import { useMemo, useState } from "react";

// atoms
import { Spin } from "~/shared/components/atoms/spin";

// molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";

// Hook
import { useTranslation } from "~/hook";

// Other
import ItemList from "./ItemList";
import { AddDeliveredItem } from "../modal/addDeliveredItem";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import dayjs from "dayjs";
import { resetDash } from "../../../redux/slices/dashboardSlice";
import {
  addEquipmentDeliveredAct,
  deleteEquipmentDeliveredAct,
} from "../../../redux/slices/equipmentsSlice";
import {
  deleteDLSubOnjonSiteApi,
  updateEquipmentsDelivered,
} from "../../../redux/action";
import { useParams } from "@remix-run/react";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { getGlobalUser } from "~/zustand/global/user/slice";

dayjs.extend(utc);
dayjs.extend(timezone);
const EquipmentItemsDeliveredCard = ({
  isReadOnly,
}: {
  isReadOnly: boolean;
}) => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();
  const [addDeliveredItemOpen, setAddDeliveredItemOpen] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [selectedEquipmentDeliId, setSelectedEquipmentDeliId] =
    useState<number>();
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const { isEquipmentTabLoading, equipmentData }: IDLEquipmentsInitialState =
    useAppDLSelector((state) => state.dailyLogeEuipments);
  const { deliveredEquipments } = equipmentData || [];
  const dispatch = useAppDLDispatch();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id = "" } = user || {};

  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  const handleSaveEquipment = async (data: ICommonOpItemDelivery) => {
    setIsLoading(true);
    const equipmentDeliFrm: IEquipmentDelivery[] = [
      {
        itemName: data?.subject || "",
        deliveredBy: data?.supplier_company_name || "",
        deliveryTime: currentDateTime.format("HH:mm A"),
        notes: data?.quantity?.toString()
          ? `Original QTY Ordered = ${data?.quantity?.toString()}`
          : "",
        isReturned: 0,
        showUntilReturned: 0,
        referenceItemId: 0,
      },
    ];

    const addSubsEmpRes = (await updateEquipmentsDelivered({
      equipmentDelivery: equipmentDeliFrm,
      logId: Number(id),
    })) as IEquipmentDeliRes;
    setIsLoading(false);

    if (addSubsEmpRes?.success) {
      dispatch(resetDash());
      setAddDeliveredItemOpen(false);
      dispatch(
        addEquipmentDeliveredAct({
          data: addSubsEmpRes?.data?.equipment_item_delivered,
          action: "add",
        })
      );
    } else {
      notification.error({
        description: addSubsEmpRes?.message,
      });
    }
  };

  const handleDeleteEquipment = (id: number) => {
    dispatch(deleteEquipmentDeliveredAct({ item_id: id }));
  };

  const handleDeleteEquipmentDeli = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteDLSubOnjonSiteApi({
        dailyLogId: Number(id),
        itemId: selectedEquipmentDeliId || 0,
      })) as IDLPeopleDetailsUpdateApiRes;

      if (deleteRes?.success) {
        dispatch(resetDash());
        handleDeleteEquipment(selectedEquipmentDeliId || 0);
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const handleAddEquipmentDeli = (data: IEquipmentDelivery) => {
    dispatch(addEquipmentDeliveredAct({ data, action: "update" }));
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Equipment Items Delivered")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-box-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
          id: "material_items_delivered_box_circle_check",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        headerRightButton={
          <AddButton
            disabled={isReadOnly}
            onClick={() => {
              if (isReadOnly) {
                return;
              }
              setAddDeliveredItemOpen(true);
            }}
          >
            {_t("Item")}
          </AddButton>
        }
        children={
          <div className="pt-2">
            {isEquipmentTabLoading && (
              <Spin className="w-full h-10 flex items-center justify-center" />
            )}
            {!isEquipmentTabLoading && (
              <div className="grid gap-3">
                {deliveredEquipments.length > 0 ? (
                  <>
                    {deliveredEquipments?.map((itmes: IDeliveredEquipments) => (
                      <ItemList
                        key={itmes?.item_id}
                        EquipmentItem={itmes}
                        isReadOnly={isReadOnly}
                        onAddEquipmentDeli={handleAddEquipmentDeli}
                        onDeleteEquipmentDeli={() => {
                          setSelectedEquipmentDeliId(Number(itmes?.item_id));
                          setIsDeleteConfirmOpen(true);
                        }}
                      />
                    ))}
                  </>
                ) : (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-equipment-items-delivered.svg`}
                  />
                )}
              </div>
            )}
          </div>
        }
      />
      {addDeliveredItemOpen && (
        <AddDeliveredItem
          isOpen={addDeliveredItemOpen}
          isLoading={isLoading}
          itemType={"equipment"}
          onSavematerialItem={handleSaveEquipment}
          onCloseModal={() => {
            setAddDeliveredItemOpen(false);
          }}
        />
      )}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteEquipmentDeli}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default EquipmentItemsDeliveredCard;
