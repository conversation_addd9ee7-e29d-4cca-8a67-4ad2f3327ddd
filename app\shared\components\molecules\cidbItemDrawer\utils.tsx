import cloneDeep from "lodash/cloneDeep";
import { defaultConfig } from "~/data";
import { initialState } from "./component/CidbData";

export function reducer(state: TInitialCIdbItem, action: SendCidbAction) {
  switch (action.type) {
    case "SET_ACTIVE_ITEM_FIELD":
      const state_active = cloneDeep(state);
      if (state_active.activeField !== action.payload) {
        state_active.activeField = action.payload;
        state_active.item[action.payload].list = [];
        state_active.item[action.payload].pageNumber = 0;
        state_active.item[action.payload].infiniteScrollhasMore = true;
        state_active.item[action.payload].error = false;
        return state_active;
      } else {
        return state_active;
      }
    case "SET_SELECTED_CIDB_ITEM":
      const contactData = action.payload;

      if (Array.isArray(contactData)) {
        const uniqueArray = contactData.filter(
          (item, index, self) =>
            index ===
            self.findIndex(
              (obj) => obj.reference_item_id === item.reference_item_id
            )
        );
        return { ...state, selectedContact: uniqueArray };
      } else {
        let tempItem = contactData;

        const checkIdIsEquel = (
          key: keyof typeof action.payload,
          selectedItem: typeof action.payload
        ) => {
          return (
            key in selectedItem &&
            selectedItem?.[key]?.toString() === tempItem?.[key]?.toString()
          );
        };

        const itemIdkeys: (
          | keyof CIDBItemSideData
          | keyof ICIDBGetAllItemsListApiResponseData
        )[] = [
          "id",
          "material_id",
          "equipment_id",
          "contractor_id",
          "labor_id",
          "item_id",
          "reference_item_id",
          "other_item_id",
        ];
        const checkIdIsEquelForRemoveItem = (
          key: keyof typeof action.payload,
          selectedItem: typeof action.payload
        ) => {
          return key in selectedItem
            ? selectedItem?.[key]?.toString() === tempItem?.[key]?.toString()
            : "reference_item_id" in selectedItem && "item_id" in tempItem
            ? selectedItem.reference_item_id?.toString() ===
              tempItem.item_id?.toString()
            : false;
        };

        let newPrev = [];
        let itemExists = false;
        if (state.activeField !== defaultConfig.group_key) {
          newPrev = state.selectedContact?.filter(
            (selectedItem) =>
              !itemIdkeys?.some((itemIdkey) =>
                checkIdIsEquel(
                  itemIdkey as keyof typeof action.payload,
                  selectedItem as typeof action.payload
                )
              )
          );
          itemExists = newPrev?.length !== state.selectedContact?.length;
          //? "} else if ("item_id" in tempItem) {" Removed this condition for https://app.clickup.com/t/86czf08er
        } else {
          tempItem.reference_item_id = tempItem?.item_id;

          const selectedContact = state.selectedContact as ICIDBGroup[];

          itemExists = selectedContact?.some((selectedItem) => {
            return itemIdkeys?.some((itemIdkey) => {
              return checkIdIsEquelForRemoveItem(
                itemIdkey as keyof typeof action.payload,
                selectedItem as typeof action.payload
              );
            });
          });

          newPrev = selectedContact?.filter((item) => {
            return !itemIdkeys?.some((itemIdkey) => {
              return checkIdIsEquel(
                itemIdkey as keyof typeof action.payload,
                item as typeof action.payload
              );
            });
          });
        }

        if (itemExists) {
          return { ...state, selectedContact: newPrev };
        } else {
          return {
            ...state,
            selectedContact: [...state.selectedContact, tempItem],
          };
        }
      }
    case "SET_INCREMENT_CIDB_ITEM_PAGE_NUMBER":
      const customerField = state.item[action.payload];
      return {
        ...state,
        item: {
          ...state.item,
          [action.payload]: {
            ...customerField,
            pageNumber: customerField.pageNumber + 1,
          },
        },
      };
    case "SET_CIDB_ITEM_SEARCH":
      const { activeField, searchText } = action.payload;
      const searchField = state.item[activeField];
      return {
        ...state,
        item: {
          ...state.item,
          [activeField]: { ...searchField, search: searchText },
        },
      };
    case "SET_CIDB_ITEM_MAIN_SEARCH":
      const { activeField: activeFieldSearch, searchText: mainSearchText } =
        action.payload;
      const searchMainField = state.item[activeFieldSearch];
      return {
        ...state,
        item: {
          ...state.item,
          [activeFieldSearch]: {
            ...searchMainField,
            pageNumber: 0,
            mainSearch: mainSearchText,
          },
        },
      };
    case "GET_CIDB_ITEM_DETAILS_PENDING":
      if (!action.payload.formState) {
        const pendingField = state.item[action.payload.tab];
        return {
          ...state,
          isEmailLoading: true,
          item: {
            ...state.item,
            [action.payload.tab]:
              action.payload.pageNumber === 0
                ? { ...pendingField, list: [], infiniteScrollhasMore: true }
                : pendingField,
          },
        };
      }
      return state;
    case "GET_CIDB_ITEM_DETAILS_FULFILLED":
      const { response, tab } = action.payload;
      let { data, success } = response;

      const state_ = cloneDeep(state);
      state_.isEmailLoading = false;

      const limit = 30;

      if (response.data?.length >= 0 && response.data?.length < limit) {
        state_.item[tab].infiniteScrollhasMore = false;
      }

      if (success && response.data?.length > 0) {
        // this is for to solve is_favorite checkbox check continuosly
        // state_.item[tab].list = data;
        state_.item[tab]?.list?.push(...data);
      }
      return state_;
    case "CHANGE_CIDB_ITEM_FAV_ACTION_FULFILLED":
      const {
        response: res_fav,
        item_id,
        is_favorite,
        key,
        groupIndex,
      } = action.payload;
      const { success: success_fav } = res_fav;
      const state_fav = cloneDeep(state);
      if (success_fav) {
        if (
          state_fav.activeField === defaultConfig.group_key &&
          groupIndex !== undefined &&
          groupIndex !== null
        ) {
          const groupData = state_fav.item[state_fav.activeField].list?.[
            groupIndex
          ] as ICIDBGroup;
          const itemIndex = groupData.items?.findIndex(
            (item: ICIDBGroupItem) =>
              (item?.reference_item_id as number) === item_id
          );
          if (itemIndex !== undefined && itemIndex >= 0) {
            groupData.items[itemIndex].is_favorite = is_favorite;
          }
          if (state_fav.item[state_fav.activeField]?.list) {
            state_fav.item[state_fav.activeField].list = state_fav.item[
              state_fav.activeField
            ].list.map((group) => {
              if (!group.items?.length) return group;

              const updatedItems = group.items.map((item) => {
                if (item?.reference_item_id === item_id) {
                  return {
                    ...item,
                    is_favorite,
                  };
                }
                return item;
              });

              return {
                ...group,
                items: updatedItems,
              };
            });
          }
          if (state.item[state.activeField].isFavorite) {
            state_fav.item[state_fav.activeField].list =
              state_fav.item.groups.list
                .map((group: any) => {
                  group.items = group.items?.filter((item: any) => {
                    return item?.[key] !== item_id;
                  });

                  return group;
                })
                .filter(
                  (group: ICIDBGroup) => group.items && group.items.length > 0
                ); // <-- remove group if items is empty https://app.clickup.com/t/86cyqmzgh
          }
        } else {
          const index = state_fav.item[state_fav.activeField].list.findIndex(
            (item: any) => {
              return (item?.[key] as number) === item_id;
            }
          );
          if (index >= 0) {
            state_fav.item[state_fav.activeField].list[index].is_favorite =
              is_favorite;
          }
          if (state.item[state.activeField].isFavorite) {
            state_fav.item[state_fav.activeField].list = state_fav.item[
              state_fav.activeField
            ].list.filter((item: any) => item?.[key] !== item_id);
          }
        }
      }
      return state_fav;
    case "CHANGE_CIDB_ITEM_IS_FAVORITE_MAIN":
      const { is_favorite: isFavorite } = action.payload;
      return {
        ...state,
        item: {
          ...state.item,
          [state.activeField]: {
            ...state.item[state.activeField],
            isFavorite: isFavorite,
            list: [],
            pageNumber: 0,
            error: false,
            infiniteScrollhasMore: true,
          },
        },
        callAgain: 1 - state.callAgain,
      };
    case "GET_CIDB_ITEM_ADD_DETAILS_FULFILLED":
      const { activeField: activeTab, addedData: newData } = action.payload;
      const newstate = cloneDeep(state);
      return {
        ...newstate,
        item: {
          ...newstate.item,
          [newstate?.activeField]: {
            ...newstate.item[newstate?.activeField],
            list: [
              newData,
              ...(newstate.item[newstate?.activeField].list || []),
            ],
          },
        },
      };
    case "RESET_CIDB_ITEM_ALL_DATA":
      return cloneDeep(initialState);
    default:
      return state;
  }
}
