import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { SubContractsFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/subContractsFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { useNavigate, useParams } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { Number, sanitizeString } from "~/helpers/helper";
import { sendMessageKeys } from "~/components/page/$url/data";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";

const SubContractTable = (props: TableProps) => {
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { authorization }: GConfig = getGConfig();
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { getGlobalModuleByKey } = useGlobalModule();
  const SubContractorModule = getGlobalModuleByKey(
    CFConfig.sub_contracts_module
  );
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const subContracts = financialData?.sub_contracts ?? [];
  const [selectedId, setSelectedId] = useState<number>(0);
  const navigate = useNavigate();
  const { id } = useParams();
  const [allSubContracts, setAllSubContracts] = useState<
    IProjectSubContractsData[]
  >([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const purchaseSubContracts = isShowingMore
    ? allSubContracts
    : allSubContracts?.slice(0, dataLimit);
  const totalCount = Number(
    financialData?.sub_contracts_count?.[0]?.number_of_sub_contract ?? 0
  );
  const totalAmount = Number(
    financialData?.sub_contracts_count?.[0]?.total ?? 0
  );

  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad) {
      setAllSubContracts(subContracts);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(
      subContracts?.map((sc) => [sc?.sub_contract_id, sc])
    );

    const mergedSubContracts = allSubContracts?.map((existing) => {
      const updated = updatedMap?.get(existing?.sub_contract_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(
      allSubContracts?.map((sc) => sc?.sub_contract_id)
    );
    const newSubContracts = subContracts?.filter(
      (sc) => !existingIds?.has(sc?.sub_contract_id)
    );

    const nextAll = [...mergedSubContracts, ...newSubContracts];

    const hasChanged =
      nextAll?.length !== allSubContracts?.length ||
      nextAll?.some(
        (sc, i) => JSON.stringify(sc) !== JSON.stringify(allSubContracts[i])
      );

    if (hasChanged) {
      setAllSubContracts(nextAll);
    }
  }, [subContracts, isInitialLoad]);

  const [collapse, setCollapse] = useState<string[]>([]);
  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "sub_contarct") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allSubContracts.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["sub_contracts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "company_sub_contract_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const scId = `SC #${HTMLEntities.decode(sanitizeString(value))}`;
        return value ? (
          <Tooltip title={scId}>
            <Typography className="table-tooltip-text">{scId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "order_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: "Subject",
      field: "subject",
      minWidth: 200,
      maxWidth: 200,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const subject = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "Subcontractor",
      field: "contractor_name",
      minWidth: 240,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) =>
        value ? (
          <Tooltip title={HTMLEntities.decode(sanitizeString(value))}>
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(value))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        ),
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formattedValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "sub_contract_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: SubContract }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_SUB_CONTRACTS.url +
                    "/" +
                    (data?.sub_contract_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <SubContractsFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              subContractId={data.sub_contract_id}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(SubContractorModule?.plural_name ?? "Sub-Contracts")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(SubContractorModule?.module_name ?? "Sub-Contracts")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_SUB_CONTRACTS.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_SUB_CONTRACTS.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={purchaseSubContracts}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-sub-contracts.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["sub_contracts", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, [
                "sub_contracts",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default SubContractTable;
