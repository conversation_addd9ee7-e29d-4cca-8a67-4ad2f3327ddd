import { useDateFormatter } from "~/hook/date-formatter-d";
import * as Yup from "yup";

const formAddBillSchema = () => {
  const dateFormatter = useDateFormatter();

  const initValues: Partial<IAddNewBill> = {
    order_date: dateFormatter({
      format: CFConfig.luxon_date_format,
      zone: true,
    }),

    vendor_id: "",
    term_id: "",
    custom_bill_id: "",
    is_billable: true,
  };

  let validateSchema = null;

  validateSchema = Yup.object().shape({
    order_date: Yup.string().required("This field is required."),
    vendor_id: Yup.string().required("This field is required."),
    custom_bill_id: Yup.string().trim().required("This field is required."),
  });

  return {
    initValues,
    validateSchema,
  };
};

export { formAddBillSchema };
