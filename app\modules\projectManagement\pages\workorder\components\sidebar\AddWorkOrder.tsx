import dayjs, { Dayjs } from "dayjs";
import * as Yup from "yup";
import { useFormik } from "formik";
import { Form, useNavigate, useSearchParams } from "@remix-run/react";
import { useEffect, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
// Other
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
//hooks
import { useDateFormatter, useTranslation } from "~/hook";
//store
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { addWorkOrderAPI } from "../../redux/action/workorderDashAction";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import {
  escapeHtmlEntities,
  getDifferenceBetweenDate,
  getFormat,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";

const AddWorkOrder = ({ drawerOpen, setDrawerOpen }: TAddWorkOrderProps) => {
  const { _t } = useTranslation();
  const { date_format, is_custom_work_orders_id }: GSettings = getGSettings();
  const { project_id, project_name }: GProject = getGProject();
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  const dateFormat = useDateFormatter();
  const [searchParams, setSearchParams] = useSearchParams();
  const isCalledProApiRef = useRef<string | null>(null);
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};

  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);

  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();

  const { componentList, loadingCustomField, setLoadingCustomFields } =
    useSideBarCustomField(
      { directory, directoryKeyValue } as IDirectoryFormCustomField,
      {
        moduleId: module_id,
      } as IRequestCustomFieldForSidebar
    );
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [isButtonDisabled, setButtonDisabled] = useState<boolean>(false);

  useEffect(() => {
    //Call the auto number api only when drawer open and
    if (Number(is_custom_work_orders_id) == 2 && drawerOpen) {
      setModuleAutoIncrementId(module_id, module_key);
    }
    if (drawerOpen) {
      setLoadingCustomFields(true);
      setTimeout(() => setLoadingCustomFields(false), 3000);
    }
  }, [is_custom_work_orders_id, drawerOpen]);

  const removeQueryParams = () => {
    if (window && window.history) {
      window.history.replaceState({}, document.title, window.location.pathname);
    }

    setSearchParams({}, { replace: true });
  };

  const initValues: IaddWorkOrderData = {
    // project_id: Number(project_id) === 0 ? null : Number(project_id),
    project_id: searchParams.get("action")?.trim() === "new" && searchParams.get("project") !== null
      ? Number(searchParams.get("project")!) : null,
    custom_work_order_id: last_primary_id && need_to_increment
      ? (Number(need_to_increment) + Number(last_primary_id)).toString()
      : "",
    subject: "",
    order_date: dayjs().format(date_format),
    end_date: dayjs().format(date_format),
    access_to_custom_fields: 0,
  };

  // Your existing logic to create dynamicValidationSchema
  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string()
        .trim()
        .required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  // Static validation schema
  const baseValidationSchema = {
    project_id: Yup.number().required("This field is required."),
    custom_work_order_id:
      is_custom_work_orders_id === 0
        ? Yup.string()
        : Yup.string().required("This field is required."),
    subject: Yup.string().trim().required("This field is required."),
  };

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...baseValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...baseValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const [initialValuesState, setInitialValuesState] =
    useState<IaddWorkOrderData>(initialFormValues);

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    validateOnChange: false,
    enableReinitialize: true,
    onSubmit: async (values, { setSubmitting }) => {
      setButtonDisabled(true);
      setSubmitting(true);

      const formData = {
        ...values,
        subject: escapeHtmlEntities(formik.values.subject).trim(),
        custom_fields:
          formik.values.custom_fields && !isNoAccessCustomField
            ? formatCustomFieldForRequest(
                formik.values.custom_fields,
                componentList,
                date_format
              ).custom_fields
            : undefined,
        access_to_custom_fields:
          componentList.length && !isNoAccessCustomField ? 1 : 0,
      };
      const startDate = getDate(values?.order_date) as string;
      const endDate = getDate(values?.end_date) as string;
      const difference = getDifferenceBetweenDate(endDate, startDate, "days");
      if (difference < 0) {
        const errorMessage =
          "End date must be greater than or equal to service date.";
        notification.error({
          description: errorMessage,
        });
        setButtonDisabled(false);
        return;
      }
      const resData = (await addWorkOrderAPI(
        getValuableObj(formData)
      )) as Partial<IResponse<IaddWorkOrderApiRes>>;
      if (resData.success) {
        EventLogger.log(
          EVENT_LOGGER_NAME.work_orders + EVENT_LOGGER_ACTION.added,
          1
        );
        if (!!resData.data?.work_order_id) {
          navigate(
            `${routes.MANAGE_WORKORDER.url}/${resData.data?.work_order_id}`
          );
          setButtonDisabled(false);
        } else {
          notification.error({
            description: resData?.message || "Something went wrong!",
          });
          setButtonDisabled(false);
        }
      } else {
        notification.error({
          description: resData?.message || "Something went wwrong!",
        });
        setButtonDisabled(false);
      }

      setSubmitting(false);
      setIsSubmit(false);
    },
  });

  const {
    handleSubmit,
    setFieldValue,
    values,
    errors,
    validateField,
    setFieldError,
  } = formik;
  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedProject([]);
    formik.resetForm();
    formik.setErrors({});
    setIsSubmit(false); // Reset other states
    setButtonDisabled(false);
    removeQueryParams();
  };
  useEffect(() => {
    if (
      Number(is_custom_work_orders_id) === 2 &&
      need_to_increment &&
      last_primary_id
    ) {
      formik.setValues({
        ...formik.values,
        custom_work_order_id: (
          Number(need_to_increment) + Number(last_primary_id)
        ).toString(),
      });
      setInitialValuesState((prevState: IaddWorkOrderData) => ({
        ...prevState,
        custom_work_order_id: (
          Number(need_to_increment) + Number(last_primary_id)
        ).toString(),
      }));
    } else if (Number(is_custom_work_orders_id) === 0) {
      formik.setValues({
        ...formik.values,
        custom_work_order_id: "", // Brijesh Kevadiya told to send "" in case if the is_custom_work_orders_id is 0 -> "Set at number"
        // custom_work_order_id: (gSetting?.custom_work_orders_id).toString(),
      });
      setInitialValuesState((prevState: IaddWorkOrderData) => ({
        ...prevState,
        custom_work_order_id: "",
      }));
    } else {
      formik.setValues({
        ...formik.values,
        custom_work_order_id: "",
      });
      setInitialValuesState((prevState: IaddWorkOrderData) => ({
        ...prevState,
        custom_work_order_id: "",
      }));
    }
  }, [
    is_custom_work_orders_id,
    need_to_increment,
    last_primary_id,
    drawerOpen,
  ]);
  useEffect(() => {
    if (
      searchParams.get("action")?.trim() === "new" &&
      searchParams.get("project")
    ) {
      if (isCalledProApiRef.current !== searchParams.get("project")) {
        isCalledProApiRef.current = searchParams.get("project");

        (async () => {
          try {
            const proResApi = (await getProjectDetails({
              start: 0,
              limit: 1,
              projects: searchParams.get("project") || "",
              need_all_projects: 0,
              global_call: true,
              is_completed: true,
              filter: { status: "0" },
            })) as IProjectDetailsRes;

            const queryPro = proResApi?.data?.projects[0];
            setFieldValue("project_id", Number(queryPro?.id));
            setSelectedProject([
              {
                id: Number(queryPro?.id),
                project_name: queryPro?.project_name,
              },
            ]);
          } catch (e) {}
        })();
      }
    } else {
      if (
        project_id &&
        project_id !== "0" &&
        isEmpty(searchParams.get("project"))
      ) {
        setFieldValue("project_id", Number(project_id));
        setSelectedProject([
          {
            id: Number(project_id),
            project_name: project_name,
          },
        ]);
      } else {
        setFieldValue("project_id", null);
        setSelectedProject([]);
      }
    }
  }, [
    drawerOpen,
    project_id,
    project_name,
    isCalledProApiRef,
    searchParams.get("action"),
    searchParams.get("project"),
    componentList,
  ]);

  const onChangeProject = (projects: IProject[]) => {
    setSelectedProject(projects);
    if (projects.length) {
      setFieldValue("project_id", projects[0].id);
    } else {
      setFieldValue("project_id", null);
    }
  };

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return dateFormat({
        date,
        dateFormat: getFormat(date_format),
        format: "yyyy-MM-dd",
      });
    }
    return undefined;
  };

  const onOrderDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const EndDate = getDate(values?.end_date) as string;
      if (!!date) {
        const startDateFormatted = date.format("YYYY-MM-DD");
        const difference = getDifferenceBetweenDate(
          EndDate,
          startDateFormatted,
          "days"
        );
        if (EndDate && difference < 0) {
          const errorMessage =
            "Service date must be less than or equal to end date.";
          notification.error({
            description: errorMessage,
          });
        } else {
          const orderDate = !!date ? date?.format(date_format) : "";
          setFieldValue("order_date", orderDate);
        }
      } else {
        setFieldValue("order_date", "");
      }
    }
  };

  const onEndDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const orderDate = getDate(values?.order_date) as string;
      if (!!date) {
        const endDateFormatted = date.format("YYYY-MM-DD");
        const difference = getDifferenceBetweenDate(
          endDateFormatted,
          orderDate,
          "days"
        );
        if (difference < 0 && orderDate) {
          const errorMessage =
            "End date must be greater than or equal to service date.";
          notification.error({
            description: errorMessage,
          });
          setFieldValue("end_date", dayjs().format(date_format));
        } else {
          const endDate = !!date ? date?.format(date_format) : "";
          setFieldValue("end_date", endDate);
        }
      } else {
        setFieldValue("end_date", "");
      }
    }
  };
  return (
    <>
      <Drawer
        open={drawerOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-lines"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Add ${
                  replaceDOMParams(sanitizeString(module_singular_name)) ??
                  "Work Order"
                }`
              )}
            </Header>
          </div>
        }
        closeIcon={
          !window.ENV.PAGE_IS_IFRAME ? (
            <CloseButton
              onClick={() => {
                handleCloseDrawer();
              }}
            />
          ) : null
        }
      >
        <Form method="post" onSubmit={handleSubmit} noValidate>
          <div className="py-4">
            <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
              <div className="grid gap-4">
                <SidebarCardBorder addGap={true}>
                  <div className="w-full">
                    <InputField
                      disabled={is_custom_work_orders_id == 0}
                      isRequired={is_custom_work_orders_id === 0 ? false : true}
                      label={_t("WO") + " #"}
                      name="custom_work_order_id"
                      id="custom_work_order_id"
                      value={
                        is_custom_work_orders_id == 0
                          ? "Save To View"
                          : HTMLEntities.decode(
                              sanitizeString(values?.custom_work_order_id)
                            )
                      }
                      maxLength={21}
                      labelPlacement="top"
                      errorMessage={errors.custom_work_order_id}
                      autoComplete="off"
                      onChange={(e) => {
                        setFieldValue(
                          "custom_work_order_id",
                          HTMLEntities.encode(
                            sanitizeString(e.target.value.trimStart())
                          )
                        );
                        if (e.target.value?.trimStart()) {
                          setFieldError("custom_work_order_id", "");
                        } else {
                          setFieldError(
                            "custom_work_order_id",
                            _t("This field is required.")
                          );
                        }
                      }}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Project")}
                      name="project_id"
                      id="project_id"
                      key="project_id"
                      labelPlacement="top"
                      onClick={() => setIsSelectProjectOpen(true)}
                      required={true}
                      value={
                        selectedProject.length
                          ? HTMLEntities.decode(
                              sanitizeString(selectedProject[0]?.project_name)
                            )
                          : ""
                      }
                      errorMessage={
                        formik.touched?.project_id && !formik.values.project_id
                          ? formik.errors.project_id
                          : ""
                      }
                      addonBefore={
                        selectedProject.length ? (
                          <ProjectFieldRedirectionIcon
                            projectId={`${selectedProject[0]?.id}`}
                          />
                        ) : null
                      }
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      name="subject"
                      id="subject"
                      label={_t("Subject")}
                      labelPlacement="top"
                      isRequired={true}
                      value={formik.values.subject || ""}
                      errorMessage={errors.subject}
                      autoComplete="off"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                        }
                      }}
                      onChange={(e) => {
                        setFieldValue("subject", e.target.value);
                        if (e.target.value) {
                          setFieldError("subject", "");
                        } else {
                          setFieldError("subject", "This field is required.");
                        }
                      }}
                    />
                  </div>
                  <div className="w-full flex gap-8">
                    <div className="w-full">
                      <DatePickerField
                        label={_t("Service Date")}
                        labelPlacement="top"
                        placeholder=""
                        name="order_date"
                        id="order_date"
                        allowClear={true}
                        value={displayDateFormat(
                          values.order_date?.toString().trim(),
                          date_format
                        )}
                        onChange={(date) => {
                          onOrderDateChange(date);
                        }}
                        format={date_format}
                      />
                    </div>
                    <div className="w-full">
                      <DatePickerField
                        labelPlacement="top"
                        label={_t("End Date")}
                        placeholder=""
                        name="end_date"
                        id="end_date"
                        value={displayDateFormat(
                          values.end_date?.toString().trim(),
                          date_format
                        )}
                        allowClear={true}
                        onChange={(date) => {
                          onEndDateChange(date);
                        }}
                        format={date_format}
                      />
                    </div>
                  </div>
                </SidebarCardBorder>
                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                />
              </div>
            </div>
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                buttonText={_t(
                  `Create ${
                    replaceDOMParams(sanitizeString(module_singular_name)) ??
                    "Work Order"
                  }`
                )}
                onClick={() => {
                  setIsSubmit(true);
                }}
                isLoading={isButtonDisabled || formik.isSubmitting}
                disabled={
                  isButtonDisabled || formik.isSubmitting || loadingCustomField
                }
              />
            </div>
          </div>
        </Form>
      </Drawer>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={false}
          module_key={module_key}
        />
      )}
    </>
  );
};

export default AddWorkOrder;
