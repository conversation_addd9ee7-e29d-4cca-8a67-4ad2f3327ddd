import { useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";

// atoms
import { Spin } from "~/shared/components/atoms/spin";

// molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";

// helper
import {
  filterOptionBySubstring,
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";

// redux slice, action and store
import { useAppDLDispatch, useAppDLSelector } from "../../../redux/store";
import { updateDLWeatherDetail } from "../../../redux/slices/dLDetailsSlice";
import { updateDLDetailApi } from "../../../redux/action";

// constants
import { DLWeatherDetailsField, fieldStatus } from "../../../utils/constasnts";
import { replaceD<PERSON><PERSON>ara<PERSON>, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";

const NotesCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const decInpRef = useRef<HTMLInputElement>(null);
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const { weatherDetails, isDetailLoading }: IDLDetailsInitialState =
    useAppDLSelector((state) => state.dailyLogDetails);
  const { jobsiteCondition }: IStatusListDataInitialState = useAppDLSelector(
    (state) => state.statusListData
  );
  const loadingStatusRef = useRef(fieldStatus);
  const [inputValues, setInputValues] = useState<IDLWeatherUpDetails>(
    DLWeatherDetailsField
  );

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);

  const jobsiteConditionList = useMemo(
    () =>
      jobsiteCondition.map((item: IJobsiteConditionSL) => ({
        label: replaceDOMParams(sanitizeString(item.display_name || item.name)),
        value: item.type_id.toString(),
      })),
    [jobsiteCondition]
  );

  // Make dynamic hooks in future
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  useEffect(() => {
    if (checkStatusLoading) {
      setInputValues(weatherDetails);
    }
  }, [weatherDetails, checkStatusLoading]);

  useEffect(() => {
    if (
      loadingStatus.length > 0 &&
      loadingStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingStatus]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: IDLDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDLWeatherUpDetails;
    setInputValues({ ...inputValues, ...data });
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateDLDetailApi({
      logId: weatherDetails?.logId || "",
      ...data,
    })) as IDLDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateDLWeatherDetail(data));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: weatherDetails[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <CrudCommonCard
      headerTitle={_t("Jobsite Condition")}
      iconProps={{
        icon: "fa-solid fa-memo",
        containerClassName:
          "bg-[linear-gradient(180deg,#b974ff1a_0%,#8308ff1a_100%)]",
        id: "notes_memo",
        colors: ["#B974FF", "#8308FF"],
      }}
      children={
        <div className="pt-2">
          {isDetailLoading ? (
            <Spin className="w-full h-[75px] flex items-center justify-center" />
          ) : (
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <SelectField
                  label={_t("Site Condition")}
                  labelClass="sm:w-[140px] sm:max-w-[175px]"
                  placeholder={_t("Select Condition")}
                  value={
                    inputValues?.jobsiteCondition?.toString() &&
                    inputValues.jobsiteCondition != "0"
                      ? inputValues.jobsiteCondition.toString()
                      : undefined
                  }
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  showSearch
                  options={jobsiteConditionList}
                  allowClear
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "jobsiteCondition"
                  )}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    const newValue =
                      typeof value === "string" ? value : value[0];
                    setInputValues({
                      ...inputValues,
                      jobsiteCondition: newValue,
                    });
                    handleUpdateField({
                      jobsiteCondition: newValue,
                    });
                  }}
                  onClear={() => {
                    setInputValues({
                      ...inputValues,
                      jobsiteCondition: "",
                    });
                    handleUpdateField({
                      jobsiteCondition: "",
                    });
                  }}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Jobsite Condition Notes")}
                  ref={decInpRef}
                  labelClass="sm:w-[140px] sm:max-w-[175px]"
                  placeholder={_t("Write Notes if any")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={inputValues?.jobsiteNotes}
                  name="jobsiteNotes"
                  fixStatus={getStatusForField(loadingStatus, "jobsiteNotes")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "jobsiteNotes",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "jobsiteNotes",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "jobsiteNotes",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== weatherDetails?.jobsiteNotes) {
                      handleUpdateField({ jobsiteNotes: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "jobsiteNotes",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        jobsiteNotes: weatherDetails?.jobsiteNotes,
                      });
                    }
                  }}
                  onClickStsIcon={() => {
                    if (
                      getStatusForField(loadingStatus, "jobsiteNotes") ===
                      "edit"
                    ) {
                      decInpRef.current?.focus();
                    }
                  }}
                />
              </li>
            </ul>
          )}
        </div>
      }
    />
  );
};

export default NotesCard;
