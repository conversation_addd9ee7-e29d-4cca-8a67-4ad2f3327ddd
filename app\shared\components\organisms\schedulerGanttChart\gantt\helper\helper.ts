import dayjs from "dayjs";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { Number } from "~/helpers/helper";

export const adjustGanttCalendarForBaseline = (
  gantt: TGanttStaticTypes,
  tasks: TGanttTaskTypes[],
  baseline_flag: boolean
) => {
  if (!baseline_flag) return;

  let startBoundary = gantt.getState().min_date;
  let endBoundary = gantt.getState().max_date;
  let repaint = false;

  tasks.forEach((task) => {
    if (task.planned_start && task.planned_end) {
      const plannedStart = new Date(task.planned_start);
      const plannedEnd = new Date(task.planned_end);

      if (!isNaN(plannedStart.getTime()) && plannedStart <= startBoundary) {
        startBoundary = gantt.date.add(plannedStart, -1, "day");
        repaint = true;
      }

      if (!isNaN(plannedEnd.getTime()) && plannedEnd >= endBoundary) {
        endBoundary = gantt.date.add(plannedEnd, 1, "day");
        repaint = true;
      }
    }
  });

  if (repaint) {
    gantt.config.start_date = startBoundary;
    gantt.config.end_date = endBoundary;
    gantt.render();
  }
};

export const getDefautlValsForLightBox = (task: TGanttTaskTypes) => {
  const assignedTo = task?.assignees_to?.map((contact) => ({
    display_name: contact?.assignee_name ?? "",
    user_id: Number(contact?.user_id ?? ""),
    image: contact.user_image ?? "",
    type: contact.user_type as number,
    type_key: getDirectaryKeyById(
      Number(contact.user_type) === 1 ? 2 : Number(contact.user_type ?? ""),
      undefined
    ),
  }));

  const emp = task?.task_users?.map((contact) => ({
    display_name: contact?.assignee_name ?? "",
    user_id: Number(contact?.user_id ?? ""),
    image: contact.user_image ?? "",
    type: contact.user_type as number,
    type_key: getDirectaryKeyById(
      Number(contact.user_type) === 1 ? 2 : Number(contact.user_type ?? ""),
      undefined
    ),
  }));

  return {
    ...task,
    contractors: assignedTo || [],
    employees: emp || [],
  };
};

export const date_to_str = (
  ganttInstance: TGanttStaticTypes,
  format: string,
  date: Date | string
) => ganttInstance.date.date_to_str(format)(date);

/**
 * Converts a given date (string or Date) to a Date object with time set to 00:00:00
 * in the local timezone.
 *
 * Example:
 *   "2025-03-26T00:00:00.000Z" → Wed Mar 26 2025 00:00:00 GMT+0530 (IST)
 */
export const normalizeToLocalMidnigh = (date: string | Date): Date => {
  const d = typeof date === "string" ? new Date(date) : date;

  if (!(d instanceof Date) || isNaN(d.getTime())) {
    return new Date();
  }

  return new Date(d.getFullYear(), d.getMonth(), d.getDate());
};

export const normalizeGanttDate = (
  date: string | Date,
  shouldFormat = true
): string | Date => {
  if (!date) return dayjs().format("YYYY-MM-DD HH:mm:ss");

  if (shouldFormat) {
    return dayjs(date, "DD-MM-YYYY HH:mm").format("YYYY-MM-DD HH:mm:ss");
  } else {
    return dayjs(date, "DD-MM-YYYY HH:mm").toDate();
  }
};

/**
 * Function to round a number to a specified number of decimal places.
 * This handles potential floating-point inaccuracies by using multiplication and division.
 * @param {number} num - The number to be rounded.
 * @param {number} places - The number of decimal places.
 * @returns {number} The rounded number.
 */
export const roundToDecimalPlaces = (num: number, places = 2) => {
  if (isNaN(num) || typeof num !== "number") {
    return 0;
  }
  if (places < 0) {
    return num;
  }
  const multiplier = Math.pow(10, places);
  return parseFloat(
    (Math.round(num * multiplier) / multiplier).toFixed(places)
  );
};

export const calculateSummaryProgress = (
  task: TGanttTaskTypes,
  gantt: TGanttStaticTypes
) => {
  if (task.type != gantt.config.types.project) return task.progress;

  var totalToDo = 0;
  var totalDone = 0;

  gantt.eachTask((child) => {
    if (child.type != gantt.config.types.project) {
      totalToDo += child.duration;
      totalDone += (child.progress || 0) * child.duration;
    }
  }, task.id);

  if (!totalToDo) return 0;
  else return totalDone / totalToDo;
};

export const invalidDateForTask = "0000-00-00 00:00:00";
