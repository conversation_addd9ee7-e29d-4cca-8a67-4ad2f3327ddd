import { useEffect, useState } from "react";
// Fortawesome
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ChangeOrderlFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/changeOrderlFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import {
  getDefaultStatuscolor,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { routes } from "~/route-services/routes";
import { useNavigate, useParams } from "@remix-run/react";
import { sendMessageKeys } from "~/components/page/$url/data";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";

const ChangeOrdersTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const ChangeOrderModule = getGlobalModuleByKey(CFConfig.change_order_module);
  const [isShowingMore, setIsShowingMore] = useState(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const changeOrders = financialData?.change_orders ?? [];
  const { formatter } = useCurrencyFormatter();
  const [selectedId, setSelectedId] = useState<number>(0);
  const { id } = useParams();
  const navigate = useNavigate();
  const [allChangeOrders, setAllChangeOrder] = useState<
    IProjectChangeOrdersData[]
  >([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const displayedChangeOrder = isShowingMore
    ? allChangeOrders
    : allChangeOrders?.slice(0, dataLimit);
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const totalCount = Number(
    financialData?.change_orders_count?.[0]?.number_of_change_order ?? 0
  );
  const totalAmount = Number(
    financialData?.change_orders_count?.[0]?.total ?? 0
  );

  useEffect(() => {
    if (isInitialLoad) {
      setAllChangeOrder(changeOrders);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(
      changeOrders?.map((c) => [c?.change_order_id, c])
    );

    const mergedChangeOrders = allChangeOrders?.map((existing) => {
      const updated = updatedMap?.get(existing?.change_order_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(
      allChangeOrders?.map((c) => c?.change_order_id)
    );
    const newChangeOrders = changeOrders?.filter(
      (c) => !existingIds?.has(c?.change_order_id)
    );

    const nextAll = [...mergedChangeOrders, ...newChangeOrders];

    const hasChanged =
      nextAll?.length !== allChangeOrders?.length ||
      nextAll.some(
        (c, i) => JSON.stringify(c) !== JSON.stringify(allChangeOrders[i])
      );

    if (hasChanged) {
      setAllChangeOrder(nextAll);
    }
  }, [changeOrders, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "change_orders") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);
  const handleShowMore = () => {
    if (allChangeOrders.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["change_orders"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "company_order_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const prefixedVal = `CO #${HTMLEntities.decode(sanitizeString(value))}`;
        return value ? (
          <Tooltip title={prefixedVal}>
            <Typography className="table-tooltip-text">
              {prefixedVal}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "order_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: _t("Subject"),
      field: "subject",
      minWidth: 320,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const subject = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      field: "billing_status_name",
      cellRenderer: ({ data }: { data: ChangeOrder }) => {
        const status = data.billing_status_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.status_color || ""
        );
        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data, value }: { data: ChangeOrder; value: string }) => {
        const formateValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        if (data?.count_in_contract_amount === 1) {
          return (
            <div className="flex gap-1 items-center justify-end overflow-hidden">
              <Tooltip
                title={_t(
                  "This CO is not included in the Contract Amount based on checkbox selected within the CO."
                )}
                placement="top"
              >
                <FontAwesomeIcon
                  className="ml-1 w-3.5 h-3.5 text-deep-orange-500 cursor-pointer"
                  icon="fa-regular fa-triangle-exclamation"
                />
              </Tooltip>
              <Tooltip title={formateValue}>
                <Typography className="table-tooltip-text">
                  {formateValue}
                </Typography>
              </Tooltip>
            </div>
          );
        }

        return formateValue ? (
          <Tooltip title={formateValue}>
            <Typography className="table-tooltip-text">
              {formateValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "change_order_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: ChangeOrder }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_CHANGE_ORDERS.url +
                    "/" +
                    (data.change_order_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <ChangeOrderlFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              changeOrderId={data.change_order_id}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(ChangeOrderModule?.plural_name ?? "Change Orders")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(ChangeOrderModule?.module_name ?? "Change Order")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_CHANGE_ORDERS.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_CHANGE_ORDERS.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={displayedChangeOrder}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-td-open-project.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["change_orders", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, [
                "change_orders",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default ChangeOrdersTable;
