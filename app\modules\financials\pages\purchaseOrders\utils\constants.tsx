import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

export const RECENT_REQUEST = [
  {
    label: "Recently Submitted",
    value: "recently_submitted",
  },
  {
    label: "Awaiting Response",
    value: "awaiting_response",
  },
];

export const SELECT_PO_STATS = [
  {
    label: "Last Month / This Month",
    value: "last_this_month",
  },
  {
    label: "Last Year / This Year",
    value: "last_this_year",
  },
];

export const PO_STATUS_OPTIONS = [
  {
    label: "Draft",
    value: "draft",
  },
  {
    label: "Pricing Requested",
    value: "pricing_requested",
  },
  {
    label: "Approved",
    value: "approved",
  },
  {
    label: "Submitted",
    value: "submitted",
  },
  {
    label: "Received",
    value: "received",
  },
  {
    label: "Closed",
    value: "closed",
  },
];

export const PO_OPTIONS = [
  {
    label: "View/Email PDF",
    icon: "fa-regular fa-file-pdf",
    key: "email-pdf",
  },
  {
    label: "Submit Pricing Request",
    icon: "fa-regular fa-money-check-dollar",
    key: "submit_pricing_request",
  },
  {
    label: "Make a Copy",
    icon: "fa-regular fa-clone",
    key: "make_copy",
  },
  {
    label: "Generate a Bill",
    icon: "fa-regular fa-file-invoice-dollar",
    key: "generate_bill",
  },
  {
    content: "Share Internal Link",
    icon: "fa-regular fa-share-nodes",
    onlyIconView: true,
    key: "share",
  },
  {
    content: (
      <>
        Status: Active <br />
        Click to Archive the item
      </>
    ),
    icon: "fa-regular fa-box-archive",
    onlyIconView: true,
    key: "archive",
  },
  {
    content: (
      <>
        Status: Archived <br />
        Click to Activate the item
      </>
    ),
    icon: "fa-regular fa-regular-active",
    onlyIconView: true,
    key: "active",
  },
  {
    content: "Delete",
    icon: "fa-regular fa-trash-can",
    onlyIconView: true,
    key: "delete",
  },
];

export const SELECT_ITEMS_FILTER_LIST: ISelectItemsFilterList[] = [
  { title: "Material", item_type: "161" },
  { title: "Labor", item_type: "163" },
  { title: "Equipment", item_type: "162" },
  { title: "Subcontractor", item_type: "164" },
  { title: "Other", item_type: "165" },
];

export const supplierOptionKeys: string[] = [
  CFConfig.vendor_key,
  CFConfig.contractor_key,
  CFConfig.misc_contact_key,
  "by_service",
  // "my_project",
];

export const ICON_MAP = {
  Material: "fa-regular fa-block-brick",
  Labor: "fa-regular fa-user-helmet-safety",
  Subcontractor: "fa-regular fa-file-signature",
  Equipment: "fa-regular fa-screwdriver-wrench",
  "Other Items": "fa-regular fa-boxes-stacked",
  default: "fa-regular fa-boxes-stacked",
};

export const PO_ITEMS_LIST_TAB = [
  {
    label: (
      <FontAwesomeIcon
        className="text-base w-3.5 h-3.5"
        icon="fa-regular fa-percent"
      />
    ),
    value: "markup_percent",
  },
  {
    label: (
      <FontAwesomeIcon
        className="text-base w-3.5 h-3.5"
        icon="fa-regular fa-dollar-sign"
      />
    ),
    value: "sales_price",
  },
];

export const BillingStatuskeytoItemId = {
  po_on_hold: "190",
  po_pricing_requested: "359",
  po_submitted: "360",
  po_approved: "189",
  po_received: "315",
  po_declined: "260",
  po_paid: "192",
  po_canceled: "193",
};

export const PO_STATUS_ICON = {
  po_on_hold: "fa-light fa-file-pen",
  po_pricing_requested: "fa-light fa-file-invoice-dollar",
  po_submitted: "fa-light fa-file-export",
  po_approved: "fa-light fa-clipboard-check",
  po_received: "fa-light fa-box-circle-check",
  // po_declined: "fa-light fa-check-double",
  po_declined: "fa-regular fa-xmark",
  po_paid: "fa-light fa-lock-keyhole",
  po_canceled: "fa-regular fa-ban",
};

export const PO_STATUS_LIST = [
  {
    icon: "fa-light fa-file-pen",
    bgcolor: "bg-[#C7A27C]",
    label: "Draft",
  },
  {
    icon: "fa-light fa-file-invoice-dollar",
    bgcolor: "bg-[#4DAFFE]",
    label: "Pricing Requested",
  },
  {
    icon: "fa-light fa-clipboard-check",
    bgcolor: "bg-[#006868]",
    label: "Approved",
  },
  {
    icon: "fa-light fa-file-export",
    bgcolor: "",
    label: "Submitted",
  },
  {
    icon: "fa-light fa-box-circle-check",
    bgcolor: "",
    label: "Received",
  },
  {
    icon: "fa-light fa-lock-keyhole",
    bgcolor: "",
    label: "Closed",
  },
];

export const SELECT_TAB_OPTIONS = [
  {
    name: "Details",
    value: "details",
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-regular fa-rectangle-list"
      />
    ),
    addButton: true,
  },
  {
    name: "Items",
    value: "items",
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-regular fa-clipboard-list-check"
      />
    ),
    addButton: true,
  },
  {
    name: "Supplier Pricing Details",
    value: "supplier_pricing_details",
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-solid fa-file-invoice-dollar"
      />
    ),
    addButton: true,
  },
  {
    name: "Bill History",
    value: "bill_history",
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-regular fa-receipt" />,
    addButton: true,
  },
  {
    name: "Files",
    value: "files",
    icon: (
      <FontAwesomeIcon className="w-5 h-5" icon="fa-regular fa-file-image" />
    ),
    addButton: true,
  },
  {
    name: "Notes",
    value: "notes",
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-regular fa-memo" />,
    addButton: true,
  },
];

export const PO_RIGHT_BUTTON = [
  {
    label: "Open",
    value: "open",
    billing_status: "190,189,260,359,315",
    billing_status_names:
      "Draft, Approved, Declined, Pricing Requested,Received-Port",
  },
  {
    label: "Closed",
    value: "closed",
    billing_status: "192,193",
    billing_status_names: "Closed, Cancelled",
  },
  {
    label: "All",
    value: "all",
    billing_status: "190,189,260,359,192,315,193,360",
    billing_status_names:
      "Draft, Approved, Declined, Pricing Requested, Closed, Received-Port, Cancelled, Submitted",
  },
];
export const addItemObject: IAddItems = {
  text: "Add Name: Type Name & Press Enter",
  icon: "fa-regular fa-plus",
};

export const PODetailsFields: IPODetailData = {
  purchase_order_id: 0,
  total: "",
  supplier_contact_id: 0,
  billing_status: "0",
  subject: "",
  ship_via: "",
  fob_point: "",
  term_name: "",
  term_key: "",
  term_id: "",
  reference_id: "",
  notes: "",
  supplier_status: "",
  po_suppliers: "0",
  ip_address: "",
  signature: "",
  company_purchase_order_id: "0",
  tax_id: "0",
  multiple_ref_bill_ids: "0",
  project_name: "",
  pro_id: 0,
  project_id_string: "",
  project_type_name: "",
  project_type: "",
  ship_to_name: "",
  emp_username: "",
  po_created_email: "",
  order_from_username: "",
  order_from_image: "",
  approved_username: "null",
  billing_status_name: "",
  ship_to_company_name: "null",
  from_company_name: "null",
  from_address1: "",
  from_address2: "",
  from_city: "",
  from_state: "",
  from_zip: "",
  from_phone: "",
  original_order_date: "",
  order_date: "",
  po_order_date: "",
  delivery_date: "",
  projectPrefix: "",
  need_prefix_project: "",
  date_added: "",
  time_added: "",
  date_supplier_status: "",
  time_supplier_status: "",
  origin_date_modified: "",
  billing_status_key: "",
  supplier_id: 0,
  supplier_details: [],
  temperature_scale: "",
  customer_id: "",
  address_from: "",
  po_address1: "",
  po_address2: "",
  po_city: "",
  po_state: "",
  po_zip: "",
  user_id: "",
  longitude: "",
  latitude: "",
  ship_to_contact: "",
  ship_to_contact_name: "",
  supplier_dir_type: "",
};

export const POBillHistoryFields: IPOBillHistory = {
  bill_id: 0,
  total: "0",
  cost: "0",
  company_bill_id: "0",
  payment_amount: 0,
  billing_status: "0",
  payment_date: "",
  payment_id: 0,
  due_date: "",
};

// export const CUSTOMER_TYPE_NAME: ICustomerTypeName = {
//   "Employee": "employee",
//   misc_contact: "Misc. Contact": "misc_contact",
//   vendor: "Vendor": "vendor",
//   customer: "Customer": "customer",
//   lead: "Lead": "lead",
//   contractor: "Contractor": "contractor",
//   2: "Employee": "employee",
//   23: "Misc. Contact": "misc_contact",
//   22: "Vendor": "vendor",
//   3: "Customer": "customer",
//   204: "Lead": "lead",
//   4: "Contractor": "contractor",
// };

export const getDirectaryIdByName = (
  directoryKey: string,
  gConfig?: GConfig
) => {
  var directoryID: string = "";
  switch (directoryKey) {
    case CFConfig.employee_text:
      directoryID = CFConfig.employee_key;
      break;
    case CFConfig.customer_text:
      directoryID = CFConfig.customer_key;
      break;
    case CFConfig.contractor_text:
      directoryID = CFConfig.contractor_key;
      break;
    case CFConfig.vendor_text:
      directoryID = CFConfig.vendor_key;
      break;
    case CFConfig.misc_contact_text:
      directoryID = CFConfig.misc_contact_key;
      break;
    case CFConfig.lead_text:
      directoryID = CFConfig.lead_key;
      break;
  }
  return directoryID;
};

export const getDirectaryNameById = (
  directoryName: string,
  gConfig?: GConfig
) => {
  var directoryID: number = 0;
  switch (directoryName) {
    case CFConfig.employee_text:
      directoryID = CFConfig.employee_id;
      break;
    case CFConfig.customer_text:
      directoryID = CFConfig.customer_id;
      break;
    case CFConfig.contractor_text:
      directoryID = CFConfig.contractor_id;
      break;
    case CFConfig.vendor_text:
      directoryID = CFConfig.vendor_id;
      break;
    case CFConfig.misc_contact_text:
      directoryID = CFConfig.misc_contact_id;
      break;
    case CFConfig.lead_text:
      directoryID = CFConfig.lead_id;
      break;
  }
  return directoryID;
};

export const shipToOption = [
  {
    label: "Company Address",
    value: "company",
  },
  {
    label: "Project Address",
    value: "project",
  },
  {
    label: "Hold",
    value: "hold",
  },
  {
    label: "Customer Address",
    value: "customer_address",
  },
  {
    label: "Pick Up",
    value: "po_pick_up",
  },
  {
    label: "Directory Contact",
    value: "directory_contact",
  },
];

export const POIcons: Record<IconKey, IFontAwesomeIconProps["icon"]> = {
  item_material: "fa-regular fa-block-brick",
  item_labour: "fa-regular fa-user-helmet-safety",
  item_sub_contractor: "fa-regular fa-file-signature",
  item_equipment: "fa-regular fa-screwdriver-wrench",
  item_other: "fa-regular fa-boxes-stacked",
};

export const OrderTypeList = [
  {
    label: "Purchase Order",
    value: "0",
  },
  {
    label: "Pricing Request",
    value: "1",
  },
];
