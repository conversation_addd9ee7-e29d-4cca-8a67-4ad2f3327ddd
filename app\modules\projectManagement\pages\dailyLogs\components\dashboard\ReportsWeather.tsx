import { useState } from "react";
import { REPORTS_FILTER } from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { fetchDashData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { calculatePct, calculatePctShow } from "~/shared/utils/helper/common";
import { useMemo } from "react";
import { useApexCharts } from "~/shared/hooks/useApexCharts";
import { useTranslation } from "~/hook";

// atoms
import { ApexChart } from "~/shared/components/atoms/chart";

// molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { ComparisonBlock } from "~/shared/components/molecules/comparisonBlock";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import BarChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarChart.skeleton";
import ComparisionBlockSkeleton from "~/shared/components/molecules/comparisonBlock/skeleton/ComparisionBlock.skeleton";

const ReportsWeather = () => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const {
    isDashLoading,
    reports_with_weather_delay,
    reports_with_weather_delay_last_refresh_time,
  }: IDailyLogIntlState = useAppDLSelector((state) => state.dashboard);

  const [selectedFilter, setSelectedFilter] =
    useState<string>("last_this_month");
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);

  const thisMonthSellYes = reports_with_weather_delay?.this_month_yes;
  const previousMonthSellYes = reports_with_weather_delay?.last_month_yes;
  const thisMonthSellNo = reports_with_weather_delay?.this_month_no;
  const previousMonthSellNo = reports_with_weather_delay?.last_month_no;

  const thisYearSellYes = reports_with_weather_delay?.this_year_yes;
  const previousYearSellYes = reports_with_weather_delay?.prev_year_yes;
  const thisYearSellNo = reports_with_weather_delay?.this_year_no;
  const previousYearSellNo = reports_with_weather_delay?.prev_year_no;

  const monthPercentageYes = calculatePct({
    thisMonthSell: thisMonthSellYes || "",
    previousMonthSell: previousMonthSellYes || "",
  });
  const monthPercentageNo = calculatePct({
    thisMonthSell: thisMonthSellNo || "",
    previousMonthSell: previousMonthSellNo || "",
  });

  const yearPercentageYes = calculatePct({
    thisMonthSell: thisYearSellYes || "",
    previousMonthSell: previousYearSellYes || "",
  });
  const yearPercentageNo = calculatePct({
    thisMonthSell: thisYearSellNo || "",
    previousMonthSell: previousYearSellNo || "",
  });

  const tempMonthPercentageYes = calculatePctShow(monthPercentageYes);
  const tempMonthPercentageNo = calculatePctShow(monthPercentageNo);
  const tempYearPercentageYes = calculatePctShow(yearPercentageYes);
  const tempYearPercentageNo = calculatePctShow(yearPercentageNo);

  const SeriesDatabar = [
    {
      name: _t("Yes"),
      data:
        selectedFilter === "last_this_month"
          ? [previousMonthSellYes, thisMonthSellYes]
          : [previousYearSellYes, thisYearSellYes],
    },
    {
      name: _t("No"),
      data:
        selectedFilter === "last_this_month"
          ? [previousMonthSellNo, thisMonthSellNo]
          : [previousYearSellNo, thisYearSellNo],
    },
  ];

  const optionsLine = useApexCharts({ type: "bar" });
  const options = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      stroke: {
        width: 0,
        colors: ["#fff"],
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: "top", // top, center, bottom
          },
          columnWidth: "40",
        },
      },
      tooltip: {
        shared: true,
        enabled: true,
        intersect: false,
      },
      grid: {
        strokeDashArray: 5,
      },
      yaxis: {
        show: false,
      },
      xaxis: {
        categories:
          selectedFilter === "last_this_month"
            ? ["Last Month", "This Month"]
            : ["Last Year", "This Year"],
        labels: {
          show: true,
          style: {
            fontSize: "12px",
            fontWeight: 600,
          },
        },
      },
      colors: ["#FF8B90", "#75CACA"],
      legend: {
        show: true,
        position: "top",
        markers: {
          radius: 100,
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
    };
  }, [selectedFilter]);

  const handleRefreshWidget = async () => {
    setIsCashLoading(true);
    await dispatch(
      fetchDashData({ refreshType: "reports_with_weather_delay" })
    );
    setIsCashLoading(false);
  };
  return (
    <>
      <DashboardCardHeader
        title={_t("Reports with Weather Delays")}
        showRefreshIcon={true}
        refreshIconTooltip={reports_with_weather_delay_last_refresh_time}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isCashLoading}
        resRefresh={true}
        rightContent={
          <SelectField
            labelPlacement="top"
            applyBorder={true}
            formInputClassName="w-[190px] overflow-visible ml-auto"
            containerClassName="overflow-visible"
            fieldClassName="before:hidden"
            className="border-select-filed rounded h-7"
            options={REPORTS_FILTER}
            name="weather_reports"
            value={selectedFilter}
            showSearch={false}
            defaultValue="last_this_month"
            popupClassName="popup-select-option-header"
            onChange={(value) => setSelectedFilter(value as string)}
          />
        }
      />
      <div className="py-2 px-2.5">
        {isDashLoading || isCashLoading ? (
          <div className="grid sm:grid-cols-2">
            <div className="flex flex-col gap-2 w-fit 2xl:w-full xl:w-fit">
              <ComparisionBlockSkeleton numbersrow={2} />
            </div>
            <BarChartSkeleton
              sizeClassName="h-[140px] py-3.5"
              yAxisShow={false}
              count={2}
              labelCountNum={0}
            />
          </div>
        ) : (
          <div className="grid sm:grid-cols-2">
            <div className="flex flex-col gap-2 sm:w-fit w-full">
              {selectedFilter === "last_this_month" ? (
                <>
                  <ComparisonBlock
                    lastValue={reports_with_weather_delay?.last_month_yes}
                    thisValue={reports_with_weather_delay?.this_month_yes}
                    per={tempMonthPercentageYes.per}
                    comparisonLable={tempMonthPercentageYes.lable}
                    subTitle="Yes"
                    title="Month"
                    progressArrowProps={{
                      progress:
                        tempMonthPercentageYes.lable === "Decrease"
                          ? "down"
                          : reports_with_weather_delay?.last_month_yes ===
                            reports_with_weather_delay?.this_month_yes
                          ? ""
                          : "up",
                      color:
                        tempMonthPercentageYes.lable === "Decrease"
                          ? "green"
                          : reports_with_weather_delay?.last_month_yes ===
                            reports_with_weather_delay?.this_month_yes
                          ? ""
                          : "red",
                    }}
                    bgColor={
                      tempMonthPercentageYes.lable === "Decrease"
                        ? "bg-[#D4F4DD]"
                        : "bg-[#F5C9C9]"
                    }
                    textColor={
                      tempMonthPercentageYes.lable === "Decrease"
                        ? "text-[#008000]"
                        : "text-[#B6141C]"
                    }
                  />
                  <ComparisonBlock
                    lastValue={reports_with_weather_delay?.last_month_no}
                    thisValue={reports_with_weather_delay?.this_month_no}
                    per={tempMonthPercentageNo.per}
                    comparisonLable={tempMonthPercentageNo.lable}
                    subTitle="No"
                    title="Month"
                    progressArrowProps={{
                      progress:
                        tempMonthPercentageNo.lable === "Decrease"
                          ? "down"
                          : reports_with_weather_delay?.last_month_no ===
                            reports_with_weather_delay?.this_month_no
                          ? ""
                          : "up",
                      color:
                        tempMonthPercentageNo.lable === "Increase"
                          ? "green"
                          : reports_with_weather_delay?.last_month_no ===
                            reports_with_weather_delay?.this_month_no
                          ? ""
                          : "red",
                    }}
                    bgColor={
                      tempMonthPercentageNo.lable === "Increase"
                        ? "bg-[#D4F4DD]"
                        : "bg-[#F5C9C9]"
                    }
                    textColor={
                      tempMonthPercentageNo.lable === "Increase"
                        ? "text-[#008000]"
                        : "text-[#B6141C]"
                    }
                  />
                </>
              ) : (
                <>
                  <ComparisonBlock
                    lastValue={reports_with_weather_delay?.prev_year_yes}
                    thisValue={reports_with_weather_delay?.this_year_yes}
                    per={tempYearPercentageYes.per}
                    comparisonLable={tempYearPercentageYes.lable}
                    subTitle="Yes"
                    title="Year"
                    progressArrowProps={{
                      progress:
                        tempYearPercentageYes.lable === "Decrease"
                          ? "down"
                          : reports_with_weather_delay?.prev_year_yes ===
                            reports_with_weather_delay?.this_year_yes
                          ? ""
                          : "up",
                      color:
                        tempYearPercentageYes.lable === "Decrease"
                          ? "green"
                          : reports_with_weather_delay?.prev_year_yes ===
                            reports_with_weather_delay?.this_year_yes
                          ? ""
                          : "red",
                    }}
                    bgColor={
                      tempYearPercentageYes.lable === "Decrease"
                        ? "bg-[#D4F4DD]"
                        : "bg-[#F5C9C9]"
                    }
                    textColor={
                      tempYearPercentageYes.lable === "Decrease"
                        ? "text-[#008000]"
                        : "text-[#B6141C]"
                    }
                  />
                  <ComparisonBlock
                    lastValue={reports_with_weather_delay?.prev_year_no}
                    thisValue={reports_with_weather_delay?.this_year_no}
                    per={tempYearPercentageNo.per}
                    comparisonLable={tempYearPercentageNo.lable}
                    subTitle="No"
                    title="Year"
                    progressArrowProps={{
                      progress:
                        tempYearPercentageNo.lable === "Decrease"
                          ? "down"
                          : reports_with_weather_delay?.prev_year_no ===
                            reports_with_weather_delay?.this_year_no
                          ? ""
                          : "up",
                      color:
                        tempYearPercentageNo.lable === "Increase"
                          ? "green"
                          : reports_with_weather_delay?.prev_year_no ===
                            reports_with_weather_delay?.this_year_no
                          ? ""
                          : "red",
                    }}
                    bgColor={
                      tempYearPercentageNo.lable === "Increase"
                        ? "bg-[#D4F4DD]"
                        : "bg-[#F5C9C9]"
                    }
                    textColor={
                      tempYearPercentageNo.lable === "Increase"
                        ? "text-[#008000]"
                        : "text-[#B6141C]"
                    }
                  />
                </>
              )}
            </div>
            <div>
              <ApexChart
                series={SeriesDatabar}
                options={options}
                type={"bar"}
                height={130}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ReportsWeather;
