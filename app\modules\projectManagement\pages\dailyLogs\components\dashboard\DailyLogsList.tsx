import {
  MutableRefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import isEqual from "lodash/isEqual";
import { Link, useNavigate } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";

// Hooks
import { useTranslation } from "~/hook";
import {
  escapeHtmlEntities,
  getDefaultStatuscolor,
  sanitizeString,
} from "~/helpers/helper";
import { getGModuleFilters } from "~/zustand";

// Redux
import { getDailyLogListApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action/dashboardAction";
import { useAppDLDispatch } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { setDailyLogListIdsAct } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";

// atoms
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { InfiniteScroll } from "~/shared/components/molecules/InfiniteScroll/InfiniteScroll";

// Other
import { routes } from "~/route-services/routes";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import LoadingDailyLogsList from "./LoadingDailyLogsList";
import { getGConfig } from "~/zustand";
import { STATUS_CODE } from "~/shared/constants";

const DailyLogsList = ({
  onLoadingList,
  filterDate,
  search,
  onContactAction,
  onLoadFirstTime,
}: IDailyLogsListProps) => {
  const { _t } = useTranslation();
  const { module_access }: GConfig = getGConfig();
  const dispatch = useAppDLDispatch();

  const listULRef: MutableRefObject<HTMLUListElement | null> =
    useRef<HTMLUListElement | null>(null);
  const [logList, setLogList] = useState<IDailyLogList[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingFirst, setIsLoadingFirst] = useState<boolean>(true);
  const [pageNo, setPageNo] = useState<number>(0);
  const [isHasMore, setIsHasMore] = useState<boolean>(true);
  const limit = 30;
  const filterSrv = getGModuleFilters() as
    | Partial<DailyLogModuleFilter>
    | undefined;

  const filter: IDLModuleFilterMemo = useMemo(() => {
    return {
      startDate: filterDate?.startDate || filterSrv?.start_date,
      endDate: filterDate?.endDate || filterSrv?.end_date,
      projectId:
        filterSrv?.project && filterSrv?.project != "0"
          ? filterSrv.project
          : undefined,
      directory: filterSrv?.directory,
      moduleStatus: filterSrv?.status || STATUS_CODE.ACTIVE,
    };
  }, [filterSrv, filterDate]);

  const previousValues = useRef({});

  const fetchDLList = async ({
    page,
    searchDL,
    filterDL,
  }: IFetchDLListParams) => {
    try {
      setIsLoading(true);

      const filterObj = !isEmpty(filterDL)
        ? getValuableObj(filterDL)
        : undefined;

      if (filterObj?.moduleStatus === STATUS_CODE.ALL) {
        delete filterObj.moduleStatus;
      }
      const params = {
        start: limit * page,
        limit,
        search: searchDL ? escapeHtmlEntities(searchDL) : undefined,
        filter: !isEmpty(filterObj) ? filterObj : undefined,
        ignoreFilter: 1,
      };

      if (page === 0) {
        if (listULRef?.current) {
          listULRef.current.scrollTop = 0;
        }
        setIsHasMore(true);
        setLogList([]);
        setIsLoadingFirst(true);
      }
      const resData = (await getDailyLogListApi({
        params,
      })) as IDailyLogListApiRes;
      if (resData?.success) {
        if (page == 0) {
          setLogList(resData?.data || []);
        } else {
          setLogList((prevLogList) => [
            ...prevLogList,
            ...(resData?.data || []),
          ]);
        }
        setIsHasMore(resData?.data?.length >= limit);
      } else {
        setIsHasMore(false);
      }
      setIsLoading(false);
      setIsLoadingFirst(false);
      onLoadFirstTime(true);
    } catch (err) {
      setIsHasMore(false);
      setIsLoading(false);
      setIsLoadingFirst(false);
      onLoadFirstTime(false);
    }
  };
  useEffect(() => {
    onLoadingList(isLoading);
  }, [isLoading]);

  useEffect(() => {
    const logIds = logList?.map((log) => log.log_id) || [];
    dispatch(setDailyLogListIdsAct(logIds));
  }, [logList]);

  useEffect(() => {
    const currentValues = JSON.stringify(filter);
    if (
      !isEqual(JSON.stringify(previousValues.current), currentValues) &&
      filterSrv
    ) {
      previousValues.current = currentValues;
      setPageNo(0);
      fetchDLList({ page: 0, searchDL: search, filterDL: filter });
    }
  }, [search, JSON.stringify(filter), JSON.stringify(filterSrv)]);

  const loadMore = useCallback(() => {
    setPageNo((prev) => prev + 1);
    fetchDLList({ page: pageNo + 1, searchDL: search, filterDL: filter });
  }, [search, pageNo, JSON.stringify(filter)]);

  return (
    <>
      <ul
        ref={listULRef}
        className={`lg:max-h-[calc(100dvh-454px)] md:max-h-[calc(100dvh-426px)] max-h-[calc(100dvh-396px)] ${
          isLoadingFirst ? "overflow-hidden" : "overflow-y-auto"
        }`}
      >
        <InfiniteScroll
          loadMore={loadMore}
          hasMore={isHasMore}
          isLoading={isLoading}
          hideLoadingComponent={!isLoading && logList.length <= 0}
          continueCall={logList?.length > 0}
          loadingComponent={
            <LoadingDailyLogsList skeleton={!logList.length ? 20 : 2} />
          }
        >
          {logList?.map((item) => {
            return (
              <ListViewList
                key={item?.log_id}
                logId={item?.log_id}
                user={{ image: item?.image, name: item?.employee }}
                avatarContent={item?.employee}
                project={HTMLEntities.decode(
                  sanitizeString(item?.project_name)
                )}
                date={item?.arrival_date}
                time={item?.arrival_time}
                status={item?.job_status_name}
                statusColor={item?.job_status_color}
              />
            );
          })}

          {!isLoading && logList.length <= 0 && !filter.startDate && (
            <NoRecords
              className="h-[calc(100vh-455px)]"
              rootClassName="w-full max-w-52"
              image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
              imageWSize="208"
              imageHSize="169"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div>
                    <Typography
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                      onClick={() => onContactAction(true)}
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}

          {!isLoading && logList.length <= 0 && filter.startDate && (
            <NoRecords
              className="h-[calc(100vh-455px)]"
              rootClassName="w-full max-w-64"
              image={`${window.ENV.CDN_URL}assets/images/no-records-date-range.svg`}
              text={_t("No Daily Logs for the Selected Period")}
            />
          )}
        </InfiniteScroll>
      </ul>
    </>
  );
};

const ListViewList = ({
  logId,
  avatarContent,
  user,
  project,
  date,
  time,
  status,
  statusColor,
}: IListViewListProps) => {
  const navigate = useNavigate();
  const { color, textColor } = getDefaultStatuscolor(statusColor || "");

  return (
    // Inside ListViewList component
    <li className="flex flex-wrap sm:flex-nowrap items-center py-[7px] px-2.5 border-b border-[#f0f0f0] justify-between last:border-b-0 dark:border-white/10 hover:bg-[#f1f1f1]">
      <Link
        to={`${routes.MANAGE_DAILYLOG.url}/${logId}`}
        className="flex items-center w-full no-underline"
        prefetch="intent"
      >
        <Tooltip title={avatarContent} placement="right">
          <div>
            <AvatarProfile
              user={user}
              iconClassName="text-[11px] font-semibold"
            />
          </div>
        </Tooltip>
        <div className="w-[calc(100%-35px)] ml-2">
          <Tooltip title={project || ""} placement="top">
            <div className="max-w-fit mb-1.5">
              <Header
                level={5}
                className="truncate !text-xs text-primary-900 dark:text-white/90 !mb-0 w-fit max-w-full"
              >
                {project || ""}
              </Header>
            </div>
          </Tooltip>
          <div className="flex items-center justify-between gap-1">
            <DateTimeCard
              format="datetime"
              date={date}
              time={time}
              iconClassName="w-3 h-3"
              textClassName="text-[11px] min-w-fit"
            />
            {!isEmpty(status) && (
              <Typography
                style={{
                  color: `${textColor}`,
                  backgroundColor: `${color}`,
                }}
                className={`${
                  textColor == "" && "text-primary-900"
                } text-center text-[11px] rounded font-semibold inline-block py-0.5 px-1 w-[75px] max-w-[75px] truncate`}
              >
                {status || ""}
              </Typography>
            )}
          </div>
        </div>
      </Link>
    </li>
  );
};
export default DailyLogsList;
