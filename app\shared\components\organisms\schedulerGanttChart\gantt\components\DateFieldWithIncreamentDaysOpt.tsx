import dayjs from "dayjs";
import React from "react";
import { Number } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { useCurrentModuleAccess } from "../../hooks/useCurrentModuleAccess";

const DateFieldWithIncreamentDaysOpt: React.FC<
  TDateFieldWithIncreamentDaysOptProps
> = ({
  date,
  duration,
  field,
  isDateDisabled,
  onChangeDate,
  onChangeDays,
  isTask,
}) => {
  const { _t } = useTranslation();
  const { enoughGanttAccess } = useCurrentModuleAccess();

  const handleScheduleDuration = (action: "add" | "subtract"): void => {
    let currentDuration = Number(duration);
    if (action === "add") {
      currentDuration++;
    } else {
      if (currentDuration > 1) {
        currentDuration--;
      } else {
        return;
      }
    }

    onChangeDays(currentDuration);
  };

  return (
    <div className="flex justify-start items-center gap-2">
      <DatePickerField
        label={_t("")}
        formInputClassName="!w-1/2"
        name={field}
        id={field}
        labelPlacement="top"
        allowClear={false}
        readOnly={!enoughGanttAccess || isDateDisabled}
        format={CFConfig.day_js_date_format}
        value={displayDateFormat(date as string, CFConfig.day_js_date_format)}
        onChange={(date) => {
          if (!Array.isArray(date)) {
            const newDate = !!date
              ? date?.format(CFConfig.day_js_date_format)
              : "";
            onChangeDate(newDate);
          }
        }}
      />

      {isTask && (
        <div className="flex justify-start items-center">
          <input
            type="button"
            className="w-[25px] p-[2px] cursor-pointer"
            style={{
              border: "1px solid #CECECE",
              borderTopLeftRadius: "4px",
              borderBottomLeftRadius: "4px",
            }}
            value="−"
            disabled={!enoughGanttAccess}
            onClick={() => handleScheduleDuration("subtract")}
          />
          <input
            type="text"
            value={Number(duration) === 0 ? "" : duration?.toString() ?? "1"}
            className="w-[35px] p-1 h-[28px] text-center"
            disabled={!enoughGanttAccess}
            onChange={(e) => {
              if (!enoughGanttAccess) return;

              const value = e.target.value;
              // const isStartsWithZero = value?.startsWith("0");

              // if (isStartsWithZero) return;

              if (/^\d*$/.test(value)) {
                onChangeDays(value);
              }
            }}
            onBlur={(e) => {
              if (!enoughGanttAccess) return;

              const value = e.target.value;
              if (value === "") {
                onChangeDays(1);
              }
            }}
            style={{
              border: "1px solid #CECECE",
              borderRight: "none",
              borderLeft: "none",
              outline: "none",
            }}
          />
          <input
            type="button"
            className="w-[25px] p-[2px] cursor-pointer"
            style={{
              border: "1px solid #CECECE",
              borderTopRightRadius: "4px",
              borderBottomRightRadius: "4px",
            }}
            value="+"
            disabled={!enoughGanttAccess}
            onClick={() => handleScheduleDuration("add")}
          />{" "}
          <div className="ml-2">Days</div>
        </div>
      )}
    </div>
  );
};

export default DateFieldWithIncreamentDaysOpt;
