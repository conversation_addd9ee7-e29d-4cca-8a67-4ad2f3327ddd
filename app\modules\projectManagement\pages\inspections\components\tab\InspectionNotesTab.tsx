import { useMemo } from "react";
import { useParams } from "@remix-run/react";
import { getGSettings, useGModules } from "~/zustand";
// Redux
import {
  addUpNotesDataAct,
  addUpNoteFileAct,
  deleteNotesDataAct,
} from "~/redux/slices/commonNoteSlice";
// Organisms
import { NoteList } from "~/shared/components/organisms/notes/noteList";
// Other
import { useInAppDispatch, useInAppSelector } from "../../redux/store";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const InspectionNotesTab = ({ projectid }: IInspectionNotesTabProps) => {
  const params: RouteParams = useParams();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useInAppDispatch();
  const { isNotesLoading, notes }: ICommonNoteInitialState = useInAppSelector(
    (state) => state.commonNoteData
  );
  const { date_format, image_resolution }: GSettings = getGSettings();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(currentModule?.module_key) === "read_only",
    [currentModule?.module_key]
  );

  const handleDeleteNote = (id: number) => {
    dispatch(deleteNotesDataAct({ note_id: id }));
  };

  const handleAddUpNote = (data: ICommonNoteAddUpRes) => {
    dispatch(addUpNotesDataAct(data));
  };

  return (
    <>
      <div className="grid gap-2.5">
        <div className="common-card py-3 px-[15px]">
          <NoteList
            projectid={projectid}
            isAddAttachAllow={!isReadOnly}
            isAttachReadOnly={isReadOnly}
            notesData={notes}
            moduleData={{
              moduleKey: currentModule?.module_key || "",
              recordId: params?.id || "",
            }}
            onAddUpNote={handleAddUpNote}
            onUpNoteFile={(data) => {
              dispatch(addUpNoteFileAct(data));
            }}
            isNotesLoading={isNotesLoading && notes?.length < 1}
            onNoteDeleted={handleDeleteNote}
            validationParams={{
              date_format: date_format,
              file_support_module_access: checkModuleAccessByKey(
                CFConfig.file_support_key
              ),
              image_resolution: image_resolution,
              module_access: currentModule?.module_access || "full_access",
              module_id: currentModule?.module_id || 0,
              module_key: currentModule?.module_key || "inspection",
            }}
          />
        </div>
      </div>
    </>
  );
};

export default InspectionNotesTab;
