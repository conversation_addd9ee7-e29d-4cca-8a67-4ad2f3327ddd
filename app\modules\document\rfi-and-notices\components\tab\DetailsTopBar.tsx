// React + ag-grid
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "@remix-run/react";
import type { MenuProps } from "antd";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import delay from "lodash/delay";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { SelectProject } from "~/shared/components/organisms/selectProject";
// Hooks, utils, helper, redux
import { getGSettings, setCommonSidebarCollapse } from "~/zustand";
import { useIframe, useTranslation } from "~/hook";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { statusColorMap, statusIconMap } from "../../utils/constasnts";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import {
  getStatusActionForField,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { sanitizeString } from "~/helpers/helper";
import { updateRFIDetails } from "../../redux/slices/rfiDetailSlice";
import {
  getRfiDetail,
  updateRfiDetailsApi,
} from "../../redux/action/rfiDetailAction";
import { useAppRFIDispatch, useAppRFISelector } from "../../redux/store";
import { RFIFieldStatus } from "~/constants/rfi-and-notices";
import { getStatusList } from "~/redux/action/getStatusListAction";
import RFIListAction from "../RFIListAction";
import { sendMessageKeys } from "~/components/page/$url/data";
import { routes } from "~/route-services/routes";
import { resetDash } from "../../redux/slices/dashboardSlice";
import OpportunityFieldRedirectionIcon from "~/shared/components/molecules/fieldRedirect/opportunityFieldRedirectionIcon/OpportunityFieldRedirectionIcon";

dayjs.extend(utc);

// Components
const DetailsTopBar = ({
  sidebarCollapse,
  onReloadDetails,
}: IRfiNoticeTopBarProps) => {
  const { _t } = useTranslation();
  const { parentPostMessage } = useIframe();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    correspondence_schedule_status,
    correspondence_compliance_status,
    correspondence_rfi,
  }: IStatusListInitialState = useAppRFISelector(
    (state) => state.getStatusList
  );
  const { module_access = "no_access", module_key = "" } = currentModule || {};
  const navigate = useNavigate();
  const [inputValues, setInputValues] = useState<Partial<IRFIDetails>>({});
  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );
  const params: RouteParams = useParams();
  const dispatch = useAppRFIDispatch();
  const gSettings: GSettings = getGSettings();

  const { isRFIDetailLoading, rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );
  const loadingStatusRef = useRef(RFIFieldStatus);
  const [loadingDataStatus, setLoadingDataStatus] =
    useState<Array<IFieldStatus>>(RFIFieldStatus);
  const [isFocused, setIsFocused] = useState(false);
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  const [rfiTyeLoading, setRfiTyeLoading] = useState<boolean>(true);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [estValues, setEstValues] = useState<string | number>(
    inputValues?.custom_correspondence_id?.toString() ||
      inputValues?.company_correspondence_id?.toString() ||
      ""
  );

  const handleInpOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };
  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingDataStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingDataStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };
  useEffect(() => {
    if (rfiDetail) {
      setInputValues(rfiDetail);
      setEstValues(
        HTMLEntities.decode(
          sanitizeString(
            isFocused
              ? rfiDetail?.custom_correspondence_id?.toString() ||
                  rfiDetail?.company_correspondence_id?.toString()
              : `Item #${
                  rfiDetail?.custom_correspondence_id?.toString() ||
                  rfiDetail?.company_correspondence_id?.toString()
                }`
          )
        )
      );
    }
  }, [rfiDetail]);
  useEffect(() => {
    if (rfiDetail) {
      setInputValues(rfiDetail);
    }
  }, [rfiDetail]);

  useEffect(() => {
    if (
      inputValues &&
      !!inputValues?.project_id &&
      inputValues?.project_id != 0 &&
      !!inputValues?.project_name &&
      inputValues?.project_name != ""
    ) {
      setSelectedProject([
        {
          id: Number(inputValues?.project_id),
          project_id: Number(inputValues?.project_id),
          project_name: inputValues?.project_name,
          prj_record_type: inputValues?.prj_type,
        },
      ]);
    } else {
      setSelectedProject([]);
    }
  }, [rfiDetail?.project_id, inputValues]);
  const handleUpdateField = async (data: IRfiDetailFieldsBoolean) => {
    const field = Object.keys(data)[0] as keyof IRFIDetails;
    let isError = false;
    let errorMsg = "";
    if (field === "project_id" && data.project_id === "") {
      errorMsg = "Project is required";
      isError = true;
    }
    if (field === "rfi_title" && data.rfi_title === "") {
      errorMsg = "title is required";
      isError = true;
    }
    if (
      field === "custom_correspondence_id" &&
      data.custom_correspondence_id === ""
    ) {
      errorMsg = "Item id is required";
      isError = true;
    }
    if (isError) {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
      return false;
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateRfiDetailsApi({
      correspondence_id: params?.id,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      dispatch(updateRFIDetails(data));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      notification.error({
        description: updateRes.message,
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleProject = (data: IProject) => {
    if (Object.keys(data).length && data.id !== rfiDetail?.project_id) {
      setSelectedProject([
        {
          project_name: data?.project_name || "",
          project_id: Number(data?.project_id),
          prj_record_type: data?.prj_record_type || "",
          id: Number(data?.project_id) || 0,
        },
      ]);
      handleUpdateField({
        project_id: Number(data.id),
        project_name: data.project_name ?? "",
        prj_record_type: data?.prj_record_type || "",
        prj_type: data?.prj_record_type || "",
      });
    }
  };

  useEffect(() => {
    if (
      loadingDataStatus.length > 0 &&
      loadingDataStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingDataStatus]);

  useEffect(() => {
    const fetchStatusList = async () => {
      if (rfiDetail?.correspondence_id) {
        setInputValues(rfiDetail);

        if (
          rfiDetail?.correspondence_key === "correspondence_schedule_notice"
        ) {
          dispatch(
            getStatusList({
              types: ["correspondence_schedule_status"],
            })
          );
        } else if (
          rfiDetail?.correspondence_key === "correspondence_complience_notice"
        ) {
          dispatch(
            getStatusList({
              types: ["correspondence_compliance_status"],
            })
          );
        } else {
          dispatch(
            getStatusList({
              types: ["correspondence_rfi"],
            })
          );
        }

        await new Promise((resolve) => setTimeout(resolve, 1500));
        setRfiTyeLoading(false);
      }
    };

    fetchStatusList();
  }, [rfiDetail?.correspondence_id]);

  const rfiStatusList: IStatusList[] = useMemo(() => {
    const sourceList =
      rfiDetail?.correspondence_key === "correspondence_schedule_notice"
        ? correspondence_schedule_status
        : rfiDetail?.correspondence_key === "correspondence_complience_notice"
        ? correspondence_compliance_status
        : rfiDetail?.correspondence_key === "correspondence_rfi"
        ? correspondence_rfi
        : [];

    return (sourceList ?? []).map((item: any) => ({
      label: HTMLEntities.decode(sanitizeString(item.display_name)),
      value: item.type_id.toString(),
      default_color: statusColorMap[item.name],
      icon: statusIconMap[item.name],
    })) as IStatusList[];
  }, [
    rfiDetail?.correspondence_key,
    correspondence_schedule_status,
    correspondence_compliance_status,
    correspondence_rfi,
  ]);

  const status = useMemo(() => {
    const statusList = rfiStatusList?.map((item) => ({
      label: HTMLEntities.decode(sanitizeString(item?.label)),
      key: item?.value?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.default_color,
          }}
        />
      ),
    }));
    const getSelectStatus = rfiStatusList?.find(
      (item) => item.value?.toString() === rfiDetail.rfi_status?.toString()
    );

    const selectStatus = (
      <Tooltip title={getSelectStatus?.label}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {getSelectStatus?.label}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.default_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [rfiStatusList, rfiDetail.rfi_status]);

  const rfiStatusVal: IStatusList | undefined = useMemo(
    () =>
      rfiStatusList?.find(
        (item: IStatusList) => item.value == rfiDetail.rfi_status
      ),
    [rfiDetail.rfi_status, rfiStatusList]
  );

  const selectedStatusInd = useMemo(() => {
    return rfiStatusList
      ?.filter((item) => item.show_in_progress_bar != 0)
      ?.findIndex((item) => item.value == rfiDetail.rfi_status);
  }, [JSON.stringify(rfiStatusList), rfiDetail.rfi_status]);

  function handleStatus(key: string) {
    if (key?.toString() !== rfiDetail?.rfi_status?.toString()) {
      const selectedOption = status?.statusList?.find(
        (option) => option.key === key?.toString()
      );

      const newStatusKey = key?.toString();
      const selectedDate = dayjs(
        rfiDetail?.response_received,
        CFConfig.day_js_date_format
      );
      const currentDate = dayjs().format(CFConfig.day_js_date_format);
      const today = dayjs().startOf("day");

      if (selectedOption) {
        if (
          key !== "326" &&
          key !== "323" &&
          rfiDetail.response_received &&
          rfiDetail.correspondence_key === "correspondence_rfi"
        ) {
          notification.info({
            description:
              "Status set to Received as there is a response received date is set.",
          });
          handleUpdateField({
            rfi_status: 326,
            response_received: rfiDetail.response_received ?? null,
          });
        } else if (newStatusKey === "326" && selectedDate.isBefore(today)) {
          handleUpdateField({
            rfi_status: 326,
            response_received: currentDate,
          });
          setInputValues((prev) => ({
            ...prev,
            response_received: currentDate,
          }));
        } else {
          const responseReceivedExists =
            rfiDetail.response_received &&
            rfiDetail.response_received.trim() !== "";
          const newResponseReceived =
            key === "326" && !responseReceivedExists
              ? dayjs().format(CFConfig.day_js_date_format)
              : rfiDetail.response_received ?? null;
          handleUpdateField({
            rfi_status: key,
            response_received: newResponseReceived,
          });
          setInputValues((prev) => ({
            ...prev,
            response_received: newResponseReceived,
          }));
        }
      } else {
        const description = "Selected option not found in statusList";
        notification.error({
          description,
        });
      }
    }
  }
  const handleStatusChange: MenuProps["onClick"] = (e) => {
    handleStatus(e.key.toString());
  };
  const handleRfiItemOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setEstValues(value);
  };
  const estValue = useMemo(() => {
    return HTMLEntities.decode(
      sanitizeString(
        isFocused
          ? inputValues?.custom_correspondence_id?.toString()?.trim() ||
              inputValues?.company_correspondence_id?.toString()?.trim()
          : `Item #${
              inputValues?.custom_correspondence_id?.toString()?.trim() ||
              inputValues?.company_correspondence_id?.toString()?.trim()
            }`
      )
    );
  }, [inputValues]);
  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isRFIDetailLoading || rfiTyeLoading ? (
              <TopBarSkeleton statusList={true} num={5} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] !mr-auto xl:w-[calc(40%-200px)] md:w-[calc(100%-150px)] w-full">
                  <div
                    className={`w-11 h-11 flex items-center justify-center rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white`}
                    style={{ backgroundColor: rfiStatusVal?.default_color }}
                  >
                    {rfiStatusVal?.icon && (
                      <FontAwesomeIcon
                        className="w-[18px] h-[18px] text-white"
                        icon={rfiStatusVal?.icon}
                      />
                    )}
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] 2xl:pr-3.5 ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <Tooltip
                      title={`Title: ${HTMLEntities.decode(
                        sanitizeString(inputValues?.rfi_title ?? "")
                      )}`}
                      placement="topLeft"
                    >
                      <div
                        className={`max-w-full  ${
                          isReadOnly ? "w-fit" : "xl:w-full sm:w-1/2 w-full"
                        }`}
                      >
                        <InputField
                          placeholder={_t("Title")}
                          labelPlacement="left"
                          name="rfi_title"
                          id="rfi_title"
                          formInputClassName="ellipsis-input-field"
                          className="h-6 py-0 text-base font-medium"
                          readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate block"
                          labelClass="hidden"
                          inputStatusClassName="!w-[15px] !h-[15px]"
                          iconClassName="!w-3 !h-3"
                          value={HTMLEntities.decode(
                            sanitizeString(inputValues?.rfi_title ?? "")
                          )}
                          maxLength={200}
                          // disabled={isReadOnly}
                          readOnly={isReadOnly}
                          editInline={true}
                          iconView={true}
                          fixStatus={getStatusForField(
                            loadingDataStatus,
                            "rfi_title"
                          )}
                          onChange={(e) => {
                            handleInpOnChange(e);
                          }}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "rfi_title",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "rfi_title",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() => {
                            handleChangeFieldStatus({
                              field: "rfi_title",
                              status: "save",
                              action: "FOCUS",
                            });
                          }}
                          onBlur={(e) => {
                            const value = e?.target?.value
                              .replace(/\s+/g, " ")
                              .trim();
                            if (value !== rfiDetail?.rfi_title) {
                              if (value == "") {
                                notification.error({
                                  description: _t("Title field is required."),
                                });
                              }
                              handleUpdateField({
                                rfi_title: value.trim(),
                                custom_correspondence_id:
                                  rfiDetail?.custom_correspondence_id,
                              });
                            } else {
                              handleChangeFieldStatus({
                                field: "rfi_title",
                                status: "button",
                                action: "BLUR",
                              });
                            }
                          }}
                        />
                      </div>
                    </Tooltip>
                    <ButtonField
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      required={true}
                      placeholder={_t("Select Project")}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-sm h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-sm font-medium"
                      readOnly={isReadOnly}
                      isDisabled={isReadOnly}
                      onClick={() => {
                        setIsSelectProjectOpen(true);
                      }}
                      value={
                        !!inputValues?.project_name && !!inputValues?.project_id
                          ? HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name || "")
                            )
                          : ""
                      }
                      headerTooltip={`${
                        inputValues?.prj_type === "project"
                          ? "Project"
                          : "Opportunity"
                      }: ${
                        !!inputValues?.project_name && !!inputValues?.project_id
                          ? HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name || "")
                            )
                          : ""
                      }`}
                      statusProps={{
                        status: getStatusForField(
                          loadingDataStatus,
                          "project_id"
                        ),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "project_id",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      // isDisabled={
                      //   module_access === "read_only" ||
                      //   getStatusForField(loadingStatus, "project_id") ===
                      //     "loading"
                      // }
                      rightIcon={
                        inputValues?.project_id &&
                        inputValues?.project_id != "" ? (
                          inputValues?.prj_type === "project" ||
                          inputValues.prj_record_type === "project" ? (
                            <ProjectFieldRedirectionIcon
                              projectId={
                                inputValues?.project_id?.toString() || ""
                              }
                              onClick={(e) => {
                                e?.stopPropagation();
                              }}
                            />
                          ) : inputValues?.prj_type === "opportunity" ||
                            inputValues.prj_record_type === "opportunity" ? (
                            <OpportunityFieldRedirectionIcon
                              projectId={
                                inputValues?.project_id?.toString() || ""
                              }
                              onClick={(e) => {
                                e?.stopPropagation();
                              }}
                            />
                          ) : (
                            <></>
                          )
                        ) : (
                          <></>
                        )
                      }
                    />
                    <div
                      className={`flex items-center gap-2 ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex gap-2 items-center">
                        <Dropdown
                          menu={{
                            items: status.statusList,
                            selectable: true,
                            selectedKeys: rfiDetail.rfi_status
                              ? [rfiDetail.rfi_status?.toString()]
                              : [],
                            onClick: handleStatusChange,
                          }}
                          disabled={isReadOnly}
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingDataStatus, "rfi_status")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(
                              loadingDataStatus,
                              "rfi_status"
                            )}
                          />
                        )}
                      </div>
                      <Tooltip title={estValue} placement="topLeft">
                        <div
                          className={`overflow-hidden ${
                            isReadOnly || !inputValues?.custom_correspondence_id
                              ? "w-fit"
                              : "w-full max-w-[230px]"
                          }`}
                        >
                          <InputField
                            placeholder={_t("Item") + " #"}
                            name="custom_correspondence_id"
                            id="custom_correspondence_id"
                            labelPlacement="left"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] !text-sm font-medium py-0"
                            readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate sm:block flex"
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            maxLength={10}
                            value={estValues}
                            editInline={true}
                            iconView={true}
                            readOnly={isReadOnly}
                            disabled={
                              module_access === "read_only" ||
                              (gSettings?.is_custom_rfi_id == 0 &&
                                rfiDetail.correspondence_key ===
                                  "correspondence_rfi") ||
                              (gSettings?.is_custom_schedule_notice_id == 0 &&
                                rfiDetail.correspondence_key ===
                                  "correspondence_schedule_notice") ||
                              (gSettings?.is_custom_compliance_notice_id == 0 &&
                                rfiDetail.correspondence_key ===
                                  "correspondence_complience_notice") ||
                              getStatusForField(
                                loadingDataStatus,
                                "custom_correspondence_id"
                              ) === "loading"
                            }
                            fixStatus={getStatusForField(
                              loadingDataStatus,
                              "custom_correspondence_id"
                            )}
                            onChange={handleRfiItemOnChange}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "custom_correspondence_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "custom_correspondence_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onFocus={() => {
                              setIsFocused(true);
                              handleChangeFieldStatus({
                                field: "custom_correspondence_id",
                                status: "save",
                                action: "FOCUS",
                              });
                              setEstValues(
                                HTMLEntities.decode(
                                  sanitizeString(
                                    rfiDetail.custom_correspondence_id?.toString() ||
                                      rfiDetail.company_correspondence_id?.toString()
                                  )
                                )
                              );
                              if (inputValues?.custom_correspondence_id) {
                                setInputValues((prev) => {
                                  return {
                                    ...prev,
                                    custom_correspondence_id:
                                      prev?.custom_correspondence_id,
                                  };
                                });
                              }
                            }}
                            onBlur={(e) => {
                              setIsFocused(false);
                              const value = e?.target?.value
                                .replace(/\s+/g, " ")
                                .trim();
                              if (value?.includes("Item #")) {
                                value?.replace("Item #", "");
                              }
                              const newValue = value?.includes("Item #")
                                ? value?.replace("Item #", "")
                                : value;
                              if (newValue === "") {
                                notification.error({
                                  description: _t("Item # field is required."),
                                });
                              }
                              if (
                                newValue !==
                                  rfiDetail?.custom_correspondence_id &&
                                newValue !== ""
                              ) {
                                setInputValues({
                                  ...inputValues,
                                  custom_correspondence_id: newValue,
                                });
                                handleUpdateField({
                                  custom_correspondence_id: newValue,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "custom_correspondence_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_correspondence_id:
                                    inputValues?.custom_correspondence_id
                                      ?.replace(/\s+/g, " ")
                                      ?.trim(),
                                });
                                setEstValues(
                                  HTMLEntities.decode(
                                    sanitizeString(
                                      !isFocused
                                        ? rfiDetail.custom_correspondence_id?.toString() ||
                                            rfiDetail.company_correspondence_id?.toString()
                                        : `Item #${
                                            rfiDetail.custom_correspondence_id?.toString() ||
                                            rfiDetail.company_correspondence_id?.toString()
                                          }`
                                    )
                                  )
                                );
                              }
                            }}
                          />
                        </div>
                        {" "}
                      </Tooltip>
                      {((gSettings?.is_custom_rfi_id == 0 &&
                        rfiDetail.correspondence_key ===
                          "correspondence_rfi") ||
                        (gSettings?.is_custom_schedule_notice_id == 0 &&
                          rfiDetail.correspondence_key ===
                            "correspondence_schedule_notice") ||
                        (gSettings?.is_custom_compliance_notice_id == 0 &&
                          rfiDetail.correspondence_key ===
                            "correspondence_complience_notice")) && (
                        <Tooltip title='Due to the "Start at Number" setting selected in the RFI & Notices configuration, you are unable to modify the RFI & Notices Item number.'>
                          <FontAwesomeIcon
                            className="text-base w-3.5 h-3.5 text-primary-900/80 dark:text-white/90"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </div>
                </div>
                <div className="w-auto flex-[0_0_auto]">
                  <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                    {rfiStatusList?.map((item: IStatusList, index: number) => {
                      const isActive =
                        selectedStatusInd != undefined &&
                        selectedStatusInd >= index;
                      return (
                        <li
                          key={index}
                          className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                            isActive
                              ? "before:bg-primary-900"
                              : "before:bg-[#ACAEAF]"
                          } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                        >
                          <ProgressBarHeader
                            option={
                              item as unknown as IProgressBarHeaderPropOption
                            }
                            isActive={isActive}
                            onClick={(data) => {
                              if (!isReadOnly) {
                                if (data?.value) {
                                  return handleStatus(data?.value);
                                }
                              } else {
                                return;
                              }
                            }}
                          />
                        </li>
                      );
                    })}
                  </ul>
                </div>
                <div className="flex justify-between xl:flex-[1_0_0%] xl:w-[calc(40%-200px)] md:w-fit w-full">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <div>
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>
                  </div>

                  <ul className="flex justify-end gap-2.5">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName={`!text-primary-900 ${
                          isRFIDetailLoading
                            ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                            : "group-hover/buttonHover:!text-deep-orange-500"
                        }`}
                        className={`!w-[34px] !h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 ${
                          isRFIDetailLoading
                            ? "hover:bg-transparent"
                            : "hover:!bg-deep-orange-500/5"
                        }`}
                        onClick={() => {
                          onReloadDetails();
                        }}
                      />
                    </li>
                    {!isReadOnly && !isRFIDetailLoading && (
                      <li>
                        <RFIListAction
                          isKanbanDropDown={false}
                          isDetailDropDown={false}
                          paramsData={rfiDetail}
                          tooltipcontent={_t("More")}
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          onActionComplete={() => {
                            dispatch(
                              getRfiDetail({
                                correspondence_id: params?.id || "",
                                add_event: true,
                                custom_correspondence_id: "",
                              })
                            );
                            if (
                              window &&
                              window.ENV &&
                              window.ENV.PAGE_IS_IFRAME
                            ) {
                              parentPostMessage(sendMessageKeys?.modal_change, {
                                open: false,
                              });
                            } else {
                              dispatch(resetDash());
                              navigate(`${routes.MANAGE_RFI_NOTICES.url}`);
                            }
                          }}
                        />
                      </li>
                    )}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          isShowProjectType={
            rfiDetail.correspondence_key === "correspondence_rfi" ? true : false
          }
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            if (data.length) {
              handleProject(data[0]);
            } else {
              notification.error({
                description: "Project Field is required",
              });
            }
          }}
          isRequired={false}
          genericProjects="project,opportunity"
          module_key={module_key}
        />
      )}
    </>
  );
};

export default DetailsTopBar;
