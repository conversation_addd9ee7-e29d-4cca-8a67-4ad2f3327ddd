import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { sanitizeString } from "~/helpers/helper";
import { getGConfig, getGModule<PERSON><PERSON><PERSON><PERSON> } from "~/zustand";
import { useAppPODispatch, useAppPOSelector } from "../../../../redux/store";
import {
  addPOItems,
  deletePOItems,
  fetchPOEstimateItemListApi,
  getPOItems,
} from "../../../../redux/action/POItemAction";
import {
  GridApi,
  GridReadyEvent,
  ICheckboxCellRendererParams,
  IRowNode,
  SelectionChangedEvent,
} from "ag-grid-community";
// import {
//   ICON_MAP,
//   SELECT_ITEMS_FILTER_LIST,
// } from "../../../../utils/constants";
import {
  resetPOEstimateFilter,
  resetSelectedItems,
  updatePOEstimateFilter,
} from "../../../../redux/slices/poItemsSlice";
import { useParams } from "@remix-run/react";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { HtmlDecode } from "../../../../utils/function";
import { getAllItems } from "../../items/common/function";
import ItemsTable from "~/shared/components/molecules/itemsTable/ItemsTable";
import isEqual from "lodash/isEqual";

const ImportItemsEstimate = ({
  importItemsEstimate,
  setImportItemsEstimate,
}: IImportItemsEstimateProps) => {
  const { _t } = useTranslation();
  const [open, setOpen] = useState<boolean>(false);
  const moduleEst: GModule | undefined = getGModuleByKey(
    CFConfig.estimate_module
  );
  const { id: purchase_order_id }: RouteParams = useParams();
  const [tableKey, setTableKey] = useState(0);

  // const {
  //   poScheduleValueItems,
  //   isPOScheduleValueLoading,
  //   isPOScheduleValueFetched,
  // }: IPOItemState = useAppPOSelector(
  //   (state) => state.purchaseOrderItems
  // );
  // const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);
  const { module_id }: GConfig = getGConfig();

  const dispatch = useAppPODispatch();
  const { formatter } = useCurrencyFormatter();
  // const { filter } = useAppPOSelector((state) => state?.comman);

  const {
    purchaseOrderItems,
    poEstimateItems,
    isPOEstitemsLoading,
    isPOEstitemsFetched,
    isPOItemsLoading,
    filter,
    purchaseOrderSectionItems,
  } = useAppPOSelector((state) => state?.purchaseOrderItems);

  const { purchaseOrderDetail: details } = useAppPOSelector(
    (state) => state.purchaseOrderDetail
  );
  const gridApprovedEstimateApiRef = useRef<{ [key: string]: GridApi | null }>(
    {}
  );
  const [defaultselectedEstimateData, setDefaultselectedEstimateData] =
    useState<IPOItemData[]>([]);
  const [selecteEstimateItems, setSelectedEstimateItems] = useState<
    IPOItemData[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // const selectFilter = (title: string) => {
  //   let updatedList = [...filter];

  //   if (!filter?.includes(title)) {
  //     updatedList = [...filter, title];
  //   } else {
  //     updatedList?.splice(filter?.indexOf(title), 1);
  //   }
  //   dispatch(updatePOEstimateFilter(updatedList));
  // };

  const filteredEstimateData = useMemo(() => {
    // Return the original data if no filter is applied
    setTableKey((prevKey) => prevKey + 1);
    if (!filter?.length) return poEstimateItems;
    // const filterSet = new Set(filter);

    // Filter the items for each element in poEstimateItems
    return poEstimateItems?.map((el: IPOItemData) => ({
      ...el,
      items:
        el?.items?.filter(
          (item) =>
            item?.item_type_key !== undefined &&
            filter?.includes(item?.item_type_key)
        ) ?? [],
      // el?.items?.filter((item) => filterSet.has(item?.item_type_key)) ?? [],
    }));
  }, [filter, poEstimateItems]);

  const POAllItems = useMemo(() => {
    return getAllItems(purchaseOrderSectionItems);
  }, [purchaseOrderSectionItems]);

  // useEffect(()=> {
  //   if(filteredEstimateData?.length == 0) return
  //   setEstimateData(filteredEstimateData)
  // },[poEstimateItems,filteredEstimateData])

  useEffect(() => {
    if (details?.pro_id && importItemsEstimate && !isPOEstitemsFetched) {
      // if (poEstimateItems?.length !== 0) return;
      dispatch(
        fetchPOEstimateItemListApi({
          // id: purchase_order_id || "",
          module_id: module_id,
          need_section: 1,
          project_id: +details?.pro_id,
          need_section_response: 1,
        })
      );
    }
  }, [details?.pro_id, importItemsEstimate]);
  useEffect(() => {
    //  For Default Selection
    const defaultEstimateSelection: IPOItemData[] =
      poEstimateItems
        ?.flatMap((jobItem: IPOItemData) => {
          return jobItem?.items?.filter((item: IPOItemData) =>
            POAllItems?.some(
              (poData: IPOItemData) =>
                poData?.reference_module_item_id?.toString() ===
                item?.item_id?.toString()
            )
          );
        })
        ?.filter((item): item is IPOItemData => item !== undefined) || [];
    setDefaultselectedEstimateData(defaultEstimateSelection);
  }, [poEstimateItems, POAllItems]);
  // const selectFilter = (title: string) => {
  //   let updatedList = [...filter];

  //   if (!filter?.includes(title)) {
  //     updatedList = [...filter, title];
  //   } else {
  //     updatedList?.splice(filter?.indexOf(title), 1);
  //   }
  //   setFilter(updatedList);
  // };

  // const onApprovedEstimateGridReady = (params: GridReadyEvent) => {
  //   const gridApiRef = params.api;
  //   // sovGridApiRef.current = params.api as GridApi;
  //   gridApiRef?.forEachNode((node: IRowNode) => {
  //     const { item_id }: { item_id: number } = node.data;
  //     if (
  //       poScheduleValueItems?.sov_section?.defaultCheckedItems?.some(
  //         (item: IPOItemData) => item.item_id == item_id
  //       )
  //     ) {
  //       node.setSelected(true);
  //     }
  //   });
  // }

  const handleEstimentItemsSelectionChanged = (
    event: SelectionChangedEvent
  ): void => {
    // setIsProgrammaticSelection(false);
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    const unSelectedNodes: Array<ISOVBudgetItem> = event.api
      .getRenderedNodes()
      .filter((node: IRowNode) => node.isSelected() === false)
      .map((node) => {
        return node.data;
      });

    setSelectedEstimateItems((prev) => {
      const updatedSelection = [
        ...prev,
        ...selected.filter(
          (newItem) => !prev?.some((item) => item.item_id === newItem.item_id)
        ),
      ];
      const selectedItem = updatedSelection.filter(
        (node) => !unSelectedNodes.some((item) => item.item_id === node.item_id)
      );
      return selectedItem;
    });
  };

  const onApprovedEstimateGridReady = (
    params: GridReadyEvent,
    sectionId: string,
    index: number
  ) => {
    if (!gridApprovedEstimateApiRef.current) {
      gridApprovedEstimateApiRef.current = {};
    }
    const gridApi = params.api;
    gridApi.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        [...defaultselectedEstimateData, ...selecteEstimateItems]?.some(
          (item) => item?.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
    // gridApprovedEstimateApiRef.current[sectionId] = params.api as GridApi;
    // poEstimateItems?.forEach((section: IPOItemData) => {
    //   const selectedNodes: IRowNode[] = [];
    //   if (section?.section_id == sectionId) {
    //     const gridToConsider = params.api;
    //     POAllItems?.forEach((row: IPOItemData) => {
    //       const index = section?.items?.findIndex(
    //         (item) => item?.item_id == row?.reference_module_item_id
    //       );
    //       const node =
    //         index !== -1 && index !== undefined
    //           ? gridToConsider?.getRowNode(index.toString())
    //           : null;
    //       if (node) {
    //         node?.setSelected(true);
    //         selectedNodes?.push(node);
    //       }
    //     });
    //     gridToConsider?.ensureNodeVisible(selectedNodes);
    //   }
    // });
  };

  const hanleSuccessSave = () => {
    setImportItemsEstimate(false);
    setIsLoading(false);
    dispatch(resetPOEstimateFilter());
    dispatch(resetSelectedItems());
  };

  const handleSave = async () => {
    const itemsToAddEstimateItem: IPOItemData[] = [];
    let itemToBeDeleteEstimateItem: IPOItemData[] = [];
    let itemToBeDeleteIdEstimateItem: number[] = [];
    try {
      setIsLoading(true);
      // Filter Estimate items
      selecteEstimateItems?.forEach((item) => {
        if (
          !defaultselectedEstimateData.some(
            (i: IPOItemData) => i.item_id === item.item_id
          )
        ) {
          itemsToAddEstimateItem.push({
            ...item,
            // reference_item_id: item.item_id,
            reference_module_item_id: item.item_id,
            total: Number(
              Number(item?.quantity ?? 0) * Number(item.unit_cost ?? 0)
            ).toString(),
          });
        }
      });

      // Deleted Estimate items logic===========================
      defaultselectedEstimateData.forEach((item: IPOItemData) => {
        if (!selecteEstimateItems.some((i) => i.item_id === item.item_id)) {
          itemToBeDeleteEstimateItem.push(item);
        }
      });

      itemToBeDeleteIdEstimateItem = itemToBeDeleteEstimateItem
        ?.map(
          (deleteItem) =>
            POAllItems?.find(
              (item) => item?.reference_module_item_id === deleteItem?.item_id
            )?.item_id
        )
        ?.filter((mainItemId) => mainItemId !== undefined);

      // ==================================================
      if (
        itemsToAddEstimateItem?.length == 0 &&
        itemToBeDeleteEstimateItem?.length == 0
      ) {
        setImportItemsEstimate(false);
        setIsLoading(false);
        return;
      }

      if (itemsToAddEstimateItem?.length > 0) {
        const addResponse = await dispatch(
          addPOItems({
            purchase_order_id: Number(purchase_order_id),
            items: itemsToAddEstimateItem,
          })
        );
        const addAPiRes = addResponse?.payload as IPOAddItemsApiRes;
        if (addAPiRes?.success && addAPiRes?.data?.items?.length) {
          // dispatch(addItems({ itemData: addAPiRes?.data?.items }));
          if (itemToBeDeleteEstimateItem?.length == 0) {
            dispatch(
              getPOItems({
                module_id: CFConfig.purchase_order_module_id,
                purchase_order_id: details?.purchase_order_id as number,
                need_section: 1,
                project_id: Number(details?.pro_id),
                isHideLoading: true,
              })
            );
            hanleSuccessSave();
          }
        } else {
          setIsLoading(false);
          notification.error({
            description: addAPiRes?.message || "Something went wrong!",
          });
        }
      }
      if (itemToBeDeleteEstimateItem?.length > 0) {
        const deleteRes = await dispatch(
          deletePOItems({
            purchase_order_id: Number(purchase_order_id),
            item_id: itemToBeDeleteIdEstimateItem
              // ?.map((item) => item?.item_id)
              ?.join(","),
          })
        );
        const delAPiRes = deleteRes?.payload as IPOItemsApiRes;
        if (delAPiRes?.success) {
          // dispatch(removeItems({ itemIds: itemToBeDeleteIdEstimateItem }));
          dispatch(
            getPOItems({
              module_id: CFConfig.purchase_order_module_id,
              purchase_order_id: details?.purchase_order_id as number,
              need_section: 1,
              project_id: Number(details?.pro_id),
              isHideLoading: true,
            })
          );
          hanleSuccessSave();
        } else {
          setIsLoading(false);
          notification.error({
            description: delAPiRes?.message || "Something went wrong!",
          });
        }
      }
      // await dispatch(getPOItems({ purchase_order_id }));
    } catch (error) {
      console.error("Error in handleSave:", error);

      //  setImportItemsEstimate(false);
      setIsLoading(false);
      // dispatch(resetPOEstimateFilter());
      // dispatch(resetSelectedItems());
    }
  };

  const isDisabled = useMemo(() => {
    if (isLoading) return true;

    const hasChanges = (() => {
      const flattenItems = (items: IPOItemData[]) =>
        Object.values(items || {})?.flatMap((section: IPOItemData) => section);

      const selectedFlattened = flattenItems(selecteEstimateItems);
      const defaultFlattened = flattenItems(defaultselectedEstimateData);

      return !isEqual(selectedFlattened, defaultFlattened);
    })();
    return (
      !hasChanges ||
      isLoading ||
      isPOItemsLoading ||
      isPOEstitemsLoading ||
      filteredEstimateData?.every(
        (section: IPOItemData) => section?.items?.length == 0
      )
    );
  }, [
    isLoading,
    isPOItemsLoading,
    isPOEstitemsLoading,
    defaultselectedEstimateData,
    selecteEstimateItems,
    filteredEstimateData,
  ]);

  const columnDefs = [
    {
      headerName: "",
      field: "checkbox",
      minWidth: 36,
      maxWidth: 36,
      checkboxSelection: true,
      headerCheckboxSelection: true,
      suppressMenu: true,
      showDisabledCheckboxes: false,
      cellRenderer: (params: ICheckboxCellRendererParams) => {
        const node = params.node;
        let getCurrentSection = POAllItems?.find(
          (item) =>
            item.reference_item_id === node?.data?.reference_item_type_id
        )?.reference_module_id;

        if (!getCurrentSection) {
          getCurrentSection = POAllItems?.find(
            (item) =>
              item.reference_item_id === node?.data?.reference_item_type_id
          )?.reference_module_id;
        }
        return (
          <>
            <div
              className={`ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper ag-disabled ${
                getCurrentSection ? "ag-checked" : ""
              }`}
            >
              <input className="ag-input-field-input ag-checkbox-input" />
            </div>
          </>
        );
      },
    },
    // {
    //   headerName: _t("Type"),
    //   field: "item_type_name",
    //   maxWidth: 50,
    //   minWidth: 50,
    //   suppressMovable: false,
    //   suppressMenu: true,
    //   headerClass: "ag-header-center",
    //   cellClass: "ag-cell-center",
    //   cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
    //     const value = params?.data?.item_type_name;
    //     return (
    //       <Tooltip title={value}>
    //         <FontAwesomeIcon
    //           className="w-4 h-4 text-primary-900 mx-auto"
    //           icon={
    //             ICON_MAP[value as keyof typeof ICON_MAP] || ICON_MAP["default"]
    //           }
    //         />
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      headerName: _t("Item Name"),
      field: "item",
      minWidth: 150,
      flex: 2,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      suppressMenu: true,
      cellRenderer: (params: { data: ISOVEstimateItem }) => {
        const { data } = params;
        const subject = `${HtmlDecode(data?.subject || "")}`;
        // const item = `${data.subject}${
        //   data.variation_name ? " " + data.variation_name : ""
        // }`;
        return !!subject ? (
          <Tooltip title={subject ?? ""}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: { data: ISOVEstimateItem }) => {
        const { data } = params;
        // const quantity = HTMLEntities.decode(sanitizeString(data?.quantity?.toString()));
        const quantity = data?.quantity;
        return !!quantity ? (
          <Tooltip title={data?.quantity ? Number(data?.quantity) : ""}>
            <Typography className="table-tooltip-text">
              {Number(quantity)}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Unit Cost"),
      field: "unit_cost",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const nValue = Number(data?.unit_cost) / 100;
        const unitCost = formatter(
          Number(nValue) == 0
            ? Number(nValue).toFixed(0)
            : Number(nValue).toFixed(2)
        ).value_with_symbol;
        return unitCost ? (
          <Tooltip title={`${unitCost}`}>
            <Typography className="table-tooltip-text">
              {`${unitCost}`}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      minWidth: 60,
      maxWidth: 60,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const Unit = data?.unit;
        return !!Unit ? (
          <Tooltip title={`${Unit ?? ""}`}>
            <Typography className="table-tooltip-text">
              {`${Unit ?? "-"}`}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: { data: ISOVEstimateItem }) => {
        const { data } = params;
        const total = formatter(
          (
            Number(
              `${Number(data?.unit_cost ?? 0) * Number(data?.quantity ?? 0)}`
            ) / 100
          ).toFixed(2)
        ).value_with_symbol;
        return total ? (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noDataTable = useMemo(() => {
    return (
      <StaticTable
        className="static-table"
        rowSelection="multiple"
        columnDefs={columnDefs}
        rowMultiSelectWithClick={true}
        suppressRowClickSelection={true}
        rowData={[]}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
          />
        )}
      />
    );
  }, [JSON.stringify(filteredEstimateData)]);

  // const estimateSectionComponent = useMemo(() => {
  //   const isAllEmpty = filteredEstimateData?.every(
  //     (section: IPOItemData) => section?.items?.length == 0
  //   );
  //   if (isAllEmpty) {
  //     return (
  //       <div className="p-2 common-card">
  //         <SidebarCardBorder addGap={true}>
  //           <div className="ag-theme-alpine">{noDataTable}</div>
  //         </SidebarCardBorder>
  //       </div>
  //     );
  //   }
  //   const components =
  //     filteredEstimateData?.length > 0 ? (
  //       filteredEstimateData?.map((section, index) => {
  //         if (
  //           section?.items?.length !== 0 ||
  //           filteredEstimateData?.length <= 1
  //         ) {
  //           return (
  //             <LazyLoadAgGridTable
  //               className="estimate_purchase_order_items"
  //               index={`${tableKey}-${section.section_id}`}
  //               key="estimate_purchase_order_items"
  //               // title={index === 0 ? _t("Approved Estimate Items") : ""}
  //               subTitle={_t(HtmlDecode(section?.section_name))}
  //               tableProps={{
  //                 key: `${tableKey}-${section.section_id}`,
  //                 rowMultiSelectWithClick: true,
  //                 suppressRowClickSelection: true,
  //                 onSelectionChanged: handleEstimentItemsSelectionChanged,
  //                 onGridReady: (e) =>
  //                   onApprovedEstimateGridReady(
  //                     e,
  //                     `${section?.section_id}`,
  //                     index
  //                   ),
  //                 rowSelection: "multiple",
  //                 columnDefs: columnDefs,
  //                 // isRowSelectable: (node) => {
  //                 //   // let getCurrentSection = purchaseOrderItems?.find(
  //                 //   //   (item) =>
  //                 //   //     item.reference_item_id ===
  //                 //   //     node?.data?.reference_module_item_id
  //                 //   // );

  //                 //   let getCurrentSection = POAllItems?.find(
  //                 //     (item) =>
  //                 //       item.reference_item_id ===
  //                 //       node?.data?.reference_module_item_id
  //                 //   );

  //                 //   return !getCurrentSection;
  //                 // },
  //                 rowData: section?.items,
  //               }}
  //             />
  //           );
  //         } else {
  //           if (!isPOItemsLoading && filteredEstimateData?.length <= 1) {
  //             return (
  //               <div className="p-2 common-card">
  //                 <SidebarCardBorder addGap={true}>
  //                   <div className="ag-theme-alpine">{noDataTable}</div>
  //                 </SidebarCardBorder>
  //               </div>
  //             );
  //           }
  //           return <></>;
  //         }
  //       })
  //     ) : (
  //       <div className="p-2 common-card">
  //         <SidebarCardBorder addGap={true}>
  //           <div className="ag-theme-alpine">{noDataTable}</div>
  //         </SidebarCardBorder>
  //       </div>
  //     );
  //   return components;
  // }, [filteredEstimateData]);
  return (
    <Drawer
      open={importItemsEstimate}
      rootClassName="drawer-open"
      width={750}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-file-import"
            />
          </div>
          <div className="flex justify-between items-center w-[calc(100%-40px)] pr-2">
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `${_t("Import Items from")} ${HtmlDecode(
                  moduleEst?.module_name || _t("Estimate")
                )}`
              )}
            </Header>
            <Tooltip
              title={_t(
                "You can only add items that are in your Approved Estimate."
              )}
              placement="bottom"
            >
              <Typography className="cursor-pointer text-[#B94A48] text-sm">
                {_t("Missing Something?")}
              </Typography>
            </Tooltip>
          </div>
        </div>
      }
      closeIcon={
        <CloseButton
          onClick={() => {
            dispatch(resetPOEstimateFilter());
            setImportItemsEstimate(false);
          }}
        />
      }
    >
      <div className="py-4">
        <div className="sidebar-body h-[calc(100dvh-132px)]">
          <div className="grid gap-4">
            <div className="px-4 h-[calc(100dvh-132px)] overflow-y-auto">
              <div className="flex items-center justify-between mb-2">
                <Header level={5} className="!text-sm !mb-0 text-[#4B4B4B]">
                  {moduleEst?.module_name
                    ? `${HTMLEntities.decode(
                        sanitizeString(moduleEst?.module_name)
                      )} ${_t("Items")}`
                    : _t("Estimate Items")}
                </Header>

                {/* Notes: Use this component and remove filter code */}
                <ModuleItemsFilter
                  onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                  filter={filter}
                  onChangeFilter={(data) => {
                    dispatch(updatePOEstimateFilter(data));
                  }}
                  openFilter={open}
                />
              </div>
              <div className="grid gap-4">
                {isPOItemsLoading || isPOEstitemsLoading ? (
                  <Spin className="w-full h-20 flex items-center justify-center" />
                ) : // estimateSectionComponent
                !filteredEstimateData?.every(
                    (section: IPOItemData) => section?.items?.length == 0
                  ) && filteredEstimateData?.length > 0 ? (
                  filteredEstimateData?.map((section, index) => {
                    if (
                      section?.items?.length !== 0 ||
                      filteredEstimateData?.length <= 1
                    ) {
                      return (
                        <SidebarCardBorder
                          addGap={true}
                          key={section?.section_id}
                        >
                          <div className="grid gap-2">
                            <ItemsTable
                              // title={index === 0 ? _t("Approved Estimate Items") : ""}
                              subTitle={_t(HtmlDecode(section?.section_name))}
                              tableProps={{
                                key: `${tableKey}-${section.section_id}`,
                                rowMultiSelectWithClick: true,
                                suppressRowClickSelection: true,
                                onSelectionChanged:
                                  handleEstimentItemsSelectionChanged,
                                onGridReady: (e) =>
                                  onApprovedEstimateGridReady(
                                    e,
                                    `${section?.section_id}`,
                                    index
                                  ),
                                rowSelection: "multiple",
                                columnDefs: columnDefs,
                                isRowSelectable: (node) => {
                                  // let getCurrentSection = purchaseOrderItems?.find(
                                  //   (item) =>
                                  //     item.reference_item_id ===
                                  //     node?.data?.reference_module_item_id
                                  // );

                                  let getCurrentSection = POAllItems?.find(
                                    (item) =>
                                      item.reference_item_id ===
                                      node?.data?.reference_module_item_id
                                  );

                                  return !getCurrentSection;
                                },
                                rowData: section?.items,
                              }}
                            />
                          </div>
                        </SidebarCardBorder>
                      );
                    } else {
                      if (
                        !isPOItemsLoading &&
                        filteredEstimateData?.length <= 1
                      ) {
                        return (
                          <SidebarCardBorder addGap={true}>
                            <div className="p-2 common-card">
                              <div className="ag-theme-alpine">
                                {noDataTable}
                              </div>
                            </div>
                          </SidebarCardBorder>
                        );
                      }
                      return <></>;
                    }
                  })
                ) : (
                  <SidebarCardBorder addGap={true}>
                    <div className="p-2 common-card">
                      <div className="ag-theme-alpine">{noDataTable}</div>
                    </div>
                  </SidebarCardBorder>
                )}
              </div>
            </div>
          </div>
          {/*
          {!isPOItemsLoading && filteredEstimateData?.length < 1 && (
            <div className="p-2 common-card">
              <div className="ag-theme-alpine">{noDataTable}</div>
            </div>
          )} */}
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            htmlType="submit"
            className="w-full justify-center primary-btn"
            onClick={() => {
              handleSave();
            }}
            isLoading={isLoading}
            disabled={isDisabled}
            buttonText={_t("Import Items")}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default ImportItemsEstimate;
