import { useEffect, useRef, useState } from "react";
import { useParams } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";
import delay from "lodash/delay";

// hooks
import { defaultConfig } from "~/data";
import { useTranslation } from "~/hook";
import {
  DLPeopleDetailsField,
  fieldStatus,
} from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";

// redux store, action and provider
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import { updateDLPeopleDetailApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  updateDLPeopleDetail,
  updateDLPeopleEmpDetailsAct,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/peopleSlice";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Switch } from "~/shared/components/atoms/switch";

// molecules
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";

const PeopleOnSiteCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { id }: RouteParams = useParams();
  const { _t } = useTranslation();
  const loadingStatusRef = useRef(fieldStatus);
  const decInpRef = useRef<HTMLInputElement>(null);
  const visitInpRef = useRef<HTMLInputElement>(null);
  const switchRef = useRef<HTMLDivElement>(null);
  const preSwitchBlur = useRef(false);

  const dispatch = useAppDLDispatch();
  const { peopleOnSite, isPeopleTabLoading }: IDLPeopleInitialState =
    useAppDLSelector((state) => state.dailyLogPeople);
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );

  const [inputValues, setInputValues] =
    useState<IDLUpPeopleOnSite>(DLPeopleDetailsField);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [isConditions, setISConditions] = useState<ISConditionsType>({
    anyVisitorOnsite: false,
  });

  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [empOnSite, setEmpOnSite] = useState<Partial<IDLEmployeeDetails[]>>([]);
  const [userNames, setUserNames] = useState<string>("");

  // Make dynamic hooks in future
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  useEffect(() => {
    if (checkStatusLoading) {
      setISConditions({
        anyVisitorOnsite: peopleOnSite?.anyVisitorOnsite === 1,
      });
      setInputValues(peopleOnSite);
    }
  }, [peopleOnSite, checkStatusLoading]);

  useEffect(() => {
    if (
      loadingStatus.length > 0 &&
      loadingStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingStatus]);

  useEffect(() => {
    if (empOnSite?.length) {
      setUserNames(empOnSite.map((item) => item?.display_name).join(", "));
    } else {
      setUserNames(peopleOnSite.employeesOnSite);
    }
  }, [peopleOnSite.employeesOnSite, empOnSite]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        checkStatus &&
        (checkStatus.status === "loading" ||
          checkStatus.status === "success" ||
          checkStatus.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (
    data: IDLDetailFields,
    empOnSiteData: IDLEmployeeDetails[] = []
  ) => {
    if (data?.empOnSite === inputValues?.empOnSite) return;
    const field = Object.keys(data)[0] as keyof IDLUpPeopleOnSite;
    setInputValues({ ...inputValues, ...data });
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateDLPeopleDetailApi({
      logId: id || "",
      dataParams: data,
    })) as IDLPeopleDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (field === "empOnSite") {
        updateDLPeopleEmpDetailsAct(empOnSiteData);
        const newEmpOnSite = empOnSiteData?.length
          ? empOnSiteData.map((item) => item?.display_name).join(", ")
          : "";
        dispatch(
          updateDLPeopleDetail({ employeesOnSite: newEmpOnSite, ...data })
        );
      } else {
        dispatch(updateDLPeopleDetail(data));
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: peopleOnSite[field] });
      setISConditions({ ...isConditions, [field]: peopleOnSite[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChange = (data: boolean) => {
    setISConditions((prev) => ({
      ...prev,
      anyVisitorOnsite: data,
    }));
    if (!isEmpty(inputValues.visitorsNotes) || !data)
      handleUpdateField({
        anyVisitorOnsite: data ? 1 : 0,
      });
  };

  const handleSelectEmp = (data: IDLEmployeeDetails[]) => {
    const userIdsString = data?.length
      ? data.map((item) => item?.user_id).join(",")
      : "";
    setEmpOnSite(data as IDLEmployeeDetails[]);
    handleUpdateField({ empOnSite: userIdsString }, data);
  };

  const handleSwitchMouseDown = () => {
    preSwitchBlur.current = true;
    setTimeout(() => {
      preSwitchBlur.current = false;
    }, 1000);
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("People on Site")}
        iconProps={{
          icon: "fa-solid fa-user-group",
          containerClassName:
            "bg-[linear-gradient(180deg,#81bbfc1a_0%,#33a9ff1a_100%)]",
          id: "people_user_group",
          colors: ["#81BBFC", "#33A9FF"],
        }}
        children={
          <div className="pt-2">
            {isPeopleTabLoading ? (
              <Spin className="w-full h-[75px] flex items-center justify-center" />
            ) : (
              <div className="flex lg:flex-row flex-col lg:gap-4 gap-2 mt-[3px] items-start">
                <ul className="w-full flex flex-col gap-1 overflow-hidden">
                  <li>
                    <ButtonField
                      label={_t("Employees on Site")}
                      placeholder={_t("Select/View Employees")}
                      labelPlacement="left"
                      readOnlyClassName="sm:block flex"
                      editInline={true}
                      readOnly={isReadOnly}
                      iconView={true}
                      onClick={() => {
                        if (empOnSite?.length < 1) {
                          setEmpOnSite(
                            peopleOnSite?.employeeDetails?.length
                              ? peopleOnSite.employeeDetails
                              : []
                          );
                        }
                        setIsOpenSelectCustomer(true);
                      }}
                      value={userNames}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "empOnSite"),
                      }}
                      disabled={
                        getStatusForField(loadingStatus, "empOnSite") ===
                        "loading"
                      }
                    />
                  </li>
                  <li>
                    <TextAreaField
                      label={_t("Employee Work Notes")}
                      placeholder={_t("Employee Work Notes")}
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      readOnly={isReadOnly}
                      value={inputValues?.empWorkNotes || ""}
                      ref={decInpRef}
                      name="empWorkNotes"
                      fixStatus={getStatusForField(
                        loadingStatus,
                        "empWorkNotes"
                      )}
                      onChange={handleInpOnChange}
                      onMouseEnter={() => {
                        handleChangeFieldStatus({
                          field: "empWorkNotes",
                          status: "edit",
                          action: "ME",
                        });
                      }}
                      onMouseLeaveDiv={() => {
                        handleChangeFieldStatus({
                          field: "empWorkNotes",
                          status: "button",
                          action: "ML",
                        });
                      }}
                      onFocus={() =>
                        handleChangeFieldStatus({
                          field: "empWorkNotes",
                          status: "save",
                          action: "FOCUS",
                        })
                      }
                      onBlur={(e) => {
                        const value = e.target.value.trim();
                        if (value !== peopleOnSite?.empWorkNotes) {
                          handleUpdateField({ empWorkNotes: value });
                        } else {
                          handleChangeFieldStatus({
                            field: "empWorkNotes",
                            status: "button",
                            action: "BLUR",
                          });
                          setInputValues({
                            ...inputValues,
                            empWorkNotes: peopleOnSite?.empWorkNotes,
                          });
                        }
                      }}
                      onClickStsIcon={() => {
                        if (
                          getStatusForField(loadingStatus, "empWorkNotes") ===
                          "edit"
                        ) {
                          decInpRef.current?.focus();
                        }
                      }}
                    />
                  </li>
                </ul>
                <ul className="w-full">
                  <li>
                    <InlineField
                      label={_t("Visitors Notes")}
                      labelClass="lg:w-[110px] lg:max-w-[110px]"
                      labelPlacement="left"
                      field={
                        <div className="flex gap-2 w-full">
                          <div
                            ref={switchRef}
                            onMouseDown={handleSwitchMouseDown}
                          >
                            <Switch
                              className="cf-switch success mt-2"
                              disabled={isReadOnly}
                              value={isConditions.anyVisitorOnsite}
                              onChange={(data) => handleChange(data as boolean)}
                            />
                          </div>
                          <TextAreaField
                            placeholder={_t(
                              "Write Notes About Visitors on Site."
                            )}
                            labelPlacement="left"
                            ref={visitInpRef}
                            editInline={true}
                            iconView={true}
                            readOnly={
                              isReadOnly || !isConditions.anyVisitorOnsite
                            }
                            value={
                              (isConditions.anyVisitorOnsite &&
                                inputValues?.visitorsNotes) ||
                              ""
                            }
                            name="visitorsNotes"
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "visitorsNotes"
                            )}
                            onChange={handleInpOnChange}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "visitorsNotes",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "visitorsNotes",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onFocus={() =>
                              handleChangeFieldStatus({
                                field: "visitorsNotes",
                                status: "save",
                                action: "FOCUS",
                              })
                            }
                            onBlur={(e) => {
                              const value = e.target.value.trim();
                              if (preSwitchBlur.current) {
                                preSwitchBlur.current = false; // Reset after switch is clicked
                                return;
                              }

                              if (value !== peopleOnSite?.visitorsNotes) {
                                if (value === "") {
                                  notification.error({
                                    description: _t(
                                      `Visitors notes cannot be empty.`
                                    ),
                                  });
                                  setInputValues({
                                    ...inputValues,
                                    visitorsNotes: peopleOnSite?.visitorsNotes,
                                  });
                                  return false;
                                }

                                handleUpdateField({
                                  visitorsNotes: value,
                                  anyVisitorOnsite: value ? 1 : 0,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "visitorsNotes",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  visitorsNotes: peopleOnSite?.visitorsNotes,
                                });
                              }
                            }}
                            onClickStsIcon={() => {
                              if (
                                getStatusForField(
                                  loadingStatus,
                                  "visitorsNotes"
                                ) === "edit"
                              ) {
                                visitInpRef.current?.focus();
                              }
                            }}
                          />
                        </div>
                      }
                    />
                  </li>
                </ul>
              </div>
            )}
          </div>
        }
      />
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          projectId={details.projectId}
          closeDrawer={() => {
            dispatch(setActiveField(defaultConfig.employee_key));
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={false}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          setCustomer={(data) => {
            handleSelectEmp(data as IDLEmployeeDetails[]);
          }}
          selectedCustomer={empOnSite as TselectedContactSendMail[]}
          groupCheckBox={true}
        />
      )}
    </>
  );
};

export default PeopleOnSiteCard;
