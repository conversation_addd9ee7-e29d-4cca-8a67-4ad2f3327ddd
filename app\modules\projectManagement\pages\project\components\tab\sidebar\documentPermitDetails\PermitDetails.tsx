// Hook
import { useTranslation } from "~/hook";

// atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
// Molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
// Other
import { IPermitDetailsProps } from "./type";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { sanitizeString } from "~/helpers/helper";

const PermitDetails = ({
  isPermitsDetails,
  setIsPermitsDetails,
  isViewOnly = false,
  formData,
  onClick,
  setIsOpenContactDetails,
  setAdditionContact,
  setcontactId,
}: IPermitDetailsProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  const permitFees = parseFloat(formData?.permit_fees) / 100;
  const formateFeesValue =
    permitFees === 0 || isNaN(permitFees)
      ? formatter(String(0)).value_with_symbol
      : formatter(permitFees.toFixed(2)).value_with_symbol;

  return (
    <Drawer
      open={isPermitsDetails}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-clipboard-list-check"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(`Permit Details`)}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setIsPermitsDetails(false)} />}
    >
      <div className="py-4">
        <div className="sidebar-body overflow-y-auto sm:h-[calc(100vh-132px)] h-[calc(100vh-173px)] px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="grid gap-3.5">
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Permit") + " #"}
                      labelPlacement="top"
                      name="permit_has"
                      value={formData?.permission || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Type")}
                      labelPlacement="top"
                      name="type"
                      value={formData?.permit_type_name || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Pulled")}
                      labelPlacement="top"
                      name="pulled"
                      value={formData?.pulled_by_date || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Approved")}
                      labelPlacement="top"
                      name="approved"
                      value={formData?.approval_date || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Expires")}
                      labelPlacement="top"
                      name="expires"
                      value={formData?.expire_date || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Must Be Pulled By")}
                      labelPlacement="top"
                      name="pulled_by"
                      value={formData?.pulled_by_date || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Fee")}
                      labelPlacement="top"
                      name="fee"
                      value={formateFeesValue}
                      disabled={true}
                      onChange={() => {}}
                    />
                  </div>
                  <div className="w-full relative">
                    <InputField
                      label={_t("Agency")}
                      labelPlacement="top"
                      name="agency"
                      value={formData?.agency_name || "-"}
                      disabled={true}
                      onChange={() => {}}
                    />

                    {formData?.agency &&
                    formData?.agency_contact_id !== null ? (
                      <div className="absolute right-0 bottom-0">
                        <ContactDetailsButton
                          onClick={async (e) => {
                            e.stopPropagation();
                            await setIsOpenContactDetails(true);
                            await setcontactId(formData?.agency as number);
                            await setAdditionContact(
                              Number(formData?.agency_contact_id)
                            );
                          }}
                        />
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Notes")}
                    labelPlacement="top"
                    name="notes"
                    value={formData?.notes || "-"}
                    disabled={true}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Ref. Inspection")}
                    labelPlacement="top"
                    name="ref_inspection"
                    value={
                      formData?.company_inspection_id?.toString()
                        ? HTMLEntities.decode(
                            sanitizeString(formData?.company_inspection_id)
                          )
                        : "-"
                    }
                    disabled={true}
                  />
                </div>
              </div>
            </SidebarCardBorder>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            className="w-full justify-center primary-btn"
            htmlType="submit"
            buttonText={_t("View Details")}
            onClick={onClick}
          ></PrimaryButton>
        </div>
      </div>
    </Drawer>
  );
};

export default PermitDetails;
