import { useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { ApexChart } from "~/shared/components/atoms/chart";
// molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { ComparisonBlock } from "~/shared/components/molecules/comparisonBlock";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import BarChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarChart.skeleton";
import ComparisionBlockSkeleton from "~/shared/components/molecules/comparisonBlock/skeleton/ComparisionBlock.skeleton";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { Number } from "~/helpers/helper";
import { setUnpaidData } from "../../../redux/slices/dashboardSlice";
import { useAppBillDispatch, useAppBillSelector } from "../../../redux/store";
import { fetchSingleDashData } from "../../../redux/action/billDashAction";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { calculatePct, calculatePctShow } from "~/shared/utils/helper/common";
import { useApexCharts } from "~/shared/hooks/useApexCharts";

const UnPaidBills = () => {
  const {
    unPaidBillsValue,
    unPaidBillsValueLastRefreshTime,
    isDashLoading,
  }: IBillDashState = useAppBillSelector((state) => state.dashboardData);
  const { _t } = useTranslation();
  const dispatch = useAppBillDispatch();
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] =
    useState<string>("last_this_month");
  const { formatter } = useCurrencyFormatter();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { name } = currentModule || {};

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    const response = await fetchSingleDashData({
      refresh_type: "unpaid_bill",
    } as IBillDashApiRes);
    dispatch(setUnpaidData(response));
    setIsCashLoading(false);
  };
  const thisMonth = unPaidBillsValue?.this_month;
  const previousMonth = unPaidBillsValue?.previous_month;

  const thisYear = unPaidBillsValue?.this_year;
  const previousYear = unPaidBillsValue?.previous_year;

  const monthPercentage = calculatePct({
    thisMonthSell: thisMonth || "",
    previousMonthSell: previousMonth || "",
  });

  const yearPercentage = calculatePct({
    thisMonthSell: thisYear || "",
    previousMonthSell: previousYear || "",
  });
  const tempMonthPercentageYes = calculatePctShow(monthPercentage);
  const tempYearPercentageYes = calculatePctShow(yearPercentage);

  const SeriesDatabar = [
    {
      name: `Unpaid ${name}`,
      data:
        selectedFilter === "last_this_month"
          ? [previousMonth, thisMonth]
          : [previousYear, thisYear],
    },
  ];
  const optionsLine = useApexCharts({ type: "bar" });

  const options = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      stroke: {
        width: 0,
        colors: ["#fff"],
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: "top", // top, center, bottom
          },
          columnWidth: "40",
        },
      },
      tooltip: {
        shared: true,
        enabled: true,
        intersect: false,
        y: {
          formatter: function (value: number) {
            return formatter(value?.toString()).value_with_symbol_and_c_type;
          },
        },
      },
      grid: {
        strokeDashArray: 5,
      },
      yaxis: {
        show: false,
      },
      xaxis: {
        categories:
          selectedFilter === "last_this_month"
            ? ["Last Month", "This Month"]
            : ["Last Year", "This Year"],
        labels: {
          show: true,
          style: {
            fontSize: "12px",
            fontWeight: 600,
          },
        },
      },
      colors: ["#95a4c1"],
      legend: {
        show: true,
        position: "top",
        markers: {
          radius: 100,
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
    };
  }, [selectedFilter]);
  return (
    <>
      <DashboardCardHeader
        title={`Unpaid ${name}`}
        isRefreshing={isCashLoading}
        showRefreshIcon={true}
        refreshIconTooltip={unPaidBillsValueLastRefreshTime}
        onClickRefresh={handleRefreshClick}
        rightContent={
          <SelectField
            labelPlacement="top"
            applyBorder={true}
            value={selectedFilter}
            formInputClassName="w-[192px] overflow-visible ml-auto"
            containerClassName="overflow-visible"
            fieldClassName="before:hidden"
            className="border-select-filed rounded h-7"
            options={UnPaidBillsFilter}
            showSearch={false}
            onChange={(value) => setSelectedFilter(value)}
            popupClassName="popup-select-option-header"
          />
        }
      />
      <div className="py-2 px-2.5">
        {!unPaidBillsValue || isDashLoading || isCashLoading ? (
          <div className="grid items-center sm:grid-cols-2">
            <div className="flex flex-col gap-3 2xl:w-full xl:w-fit w-full">
              <ComparisionBlockSkeleton />
            </div>
            <BarChartSkeleton
              sizeClassName="h-[125px] py-3.5"
              yAxisShow={false}
              count={2}
              labelCountNum={0}
            />
          </div>
        ) : (
          <div className="grid sm:grid-cols-2 grid-cols-1 items-center">
            <div className="w-fit">
              {selectedFilter === "last_this_month" ? (
                <ComparisonBlock
                  lastValue={
                    formatter(unPaidBillsValue?.previous_month?.toString())
                      .value_with_symbol_and_c_type
                  }
                  thisValue={
                    formatter(unPaidBillsValue?.this_month?.toString())
                      .value_with_symbol_and_c_type
                  }
                  per={tempMonthPercentageYes?.per}
                  comparisonLable={tempMonthPercentageYes?.lable}
                  title={"Month"}
                  progressArrowProps={{
                    progress:
                      tempMonthPercentageYes.lable === "Decrease"
                        ? "down"
                        : Number(unPaidBillsValue?.previous_month) ===
                          Number(unPaidBillsValue?.this_month)
                        ? ""
                        : "up",
                    color:
                      tempMonthPercentageYes.lable === "Decrease"
                        ? "green"
                        : unPaidBillsValue?.previous_month ===
                          unPaidBillsValue?.this_month
                        ? ""
                        : "red",
                  }}
                  bgColor={
                    tempMonthPercentageYes.lable === "Decrease"
                      ? "bg-[#D4F4DD]"
                      : "bg-[#F5C9C9]"
                  }
                  textColor={
                    tempMonthPercentageYes.lable === "Decrease"
                      ? "text-[#008000]"
                      : "text-[#B6141C]"
                  }
                />
              ) : (
                <ComparisonBlock
                  lastValue={
                    formatter(unPaidBillsValue?.previous_year?.toString())
                      .value_with_symbol_and_c_type
                  }
                  thisValue={
                    formatter(unPaidBillsValue?.this_year?.toString())
                      .value_with_symbol_and_c_type
                  }
                  per={tempYearPercentageYes.per}
                  comparisonLable={tempYearPercentageYes.lable}
                  title={"Year"}
                  progressArrowProps={{
                    progress:
                      tempYearPercentageYes.lable === "Decrease"
                        ? "down"
                        : Number(unPaidBillsValue?.previous_year) ===
                          Number(unPaidBillsValue?.this_year)
                        ? ""
                        : "up",
                    color:
                      tempYearPercentageYes.lable === "Decrease"
                        ? "green"
                        : unPaidBillsValue?.previous_year ===
                          unPaidBillsValue?.this_year
                        ? ""
                        : "red",
                  }}
                  bgColor={
                    tempYearPercentageYes.lable === "Decrease"
                      ? "bg-[#D4F4DD]"
                      : "bg-[#F5C9C9]"
                  }
                  textColor={
                    tempYearPercentageYes.lable === "Decrease"
                      ? "text-[#008000]"
                      : "text-[#B6141C]"
                  }
                />
              )}
            </div>
            <ApexChart
              series={SeriesDatabar}
              options={options}
              type={"bar"}
              height={110}
            />
          </div>
        )}
      </div>
    </>
  );
};
const UnPaidBillsFilter = [
  {
    label: "Last Month / This Month",
    value: "last_this_month",
  },
  {
    label: "Last Year / This Year",
    value: "last_this_year",
  },
];
export default UnPaidBills;
