import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { fetchDashData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";

// atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";

// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

const MaterialDeliveries = () => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppDLDispatch();
  const {
    isDashLoading,
    materialDelivery,
    material_delivery_last_refresh_time,
  }: IDailyLogIntlState = useAppDLSelector((state) => state.dashboard);
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IMaterDelivery[]>([]);
  useEffect(() => {
    if (!isCashLoading && materialDelivery) {
      setRowData(materialDelivery);
    }
  }, [materialDelivery, isCashLoading]);
  const columnDefs = [
    {
      headerName: _t("Material Name"),
      field: "material_name",
      minWidth: 130,
      maxWidth: 250,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: IDLMaterialDeliveriesCellRenderer) => {
        const materialName = data?.material_name;
        return materialName ? (
          <Tooltip title={materialName}>
            <Typography className="table-tooltip-text">
              {materialName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Delivered By"),
      field: "delivered_by",
      minWidth: 130,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: IDLMaterialDeliveriesCellRenderer) => {
        const deliveredBy = data?.delivered_by;
        return deliveredBy ? (
          <Tooltip title={deliveredBy}>
            <Typography className="table-tooltip-text">
              {deliveredBy}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Quantity"),
      field: "quantity",
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      minWidth: 100,
      maxWidth: 100,
      cellRenderer: ({ data }: IDLMaterialDeliveriesCellRenderer) => {
        const Quantity = formatter(Number(data?.quantity).toString()).value;
        return Quantity ? (
          <Tooltip title={Quantity}>
            <Typography className="table-tooltip-text">{Quantity}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const handleRefreshWidget = async () => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refreshType: "material_delivery" }));
    setIsCashLoading(false);
  };
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-material-items-delivered.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={_t("Material Deliveries")}
        showRefreshIcon={true}
        refreshIconTooltip={material_delivery_last_refresh_time}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isCashLoading}
        leftContent={
          <Tooltip
            title={_t(
              "Recently delivered material item selected from the Material tab > Material Items delivered."
            )}
            placement="top"
          >
            <FontAwesomeIcon
              className="h-[15px] w-[15px] text-primary-900 mr-auto"
              icon="fa-regular fa-circle-info"
            />
          </Tooltip>
        }
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            key={isDashLoading ? "loading" : "loaded"}
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            noRowsOverlayComponent={
              isDashLoading || isCashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};

export default MaterialDeliveries;
