import { useState, useEffect } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ExpenseFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/expenseFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { routes } from "~/route-services/routes";
import { useParams } from "@remix-run/react";
import { getGConfig } from "~/zustand";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { sendMessageKeys } from "~/components/page/$url/data";
import { Number, sanitizeString } from "~/helpers/helper";

const ExpensesTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { getGlobalModuleByKey } = useGlobalModule();
  const expenseModule = getGlobalModuleByKey(CFConfig.expense_module);
  const [isShowingMore, setIsShowingMore] = useState(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const expenses = financialData?.expenses ?? [];
  const { id } = useParams();
  const [allExpenses, setAllExpenses] = useState<IProjectExpensesData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const displayedExpenses = isShowingMore
    ? allExpenses
    : allExpenses?.slice(0, dataLimit);
  const totalCount = Number(
    financialData?.expenses_count?.[0]?.number_of_expense ?? 0
  );
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const totalAmount = Number(financialData?.expenses_count?.[0]?.amount ?? 0);
  const [collapse, setCollapse] = useState<string[]>([]);

  useEffect(() => {
    if (isInitialLoad) {
      setAllExpenses(expenses);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(expenses.map((e) => [e?.expense_id, e]));

    const mergedExpenses = allExpenses?.map((existing) => {
      const updated = updatedMap?.get(existing?.expense_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(allExpenses?.map((e) => e?.expense_id));
    const newExpenses = expenses?.filter(
      (e) => !existingIds?.has(e?.expense_id)
    );

    const nextAll = [...mergedExpenses, ...newExpenses];

    const hasChanged =
      nextAll?.length !== allExpenses?.length ||
      nextAll?.some(
        (e, i) => JSON.stringify(e) !== JSON.stringify(allExpenses[i])
      );

    if (hasChanged) {
      setAllExpenses(nextAll);
    }
  }, [expenses, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "expenses") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allExpenses.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["expenses"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "Vendor",
      field: "vendor_name",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const vendor = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={vendor}>
            <Typography className="table-tooltip-text">{vendor}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "expense_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: "Expense Name",
      field: "expense_name",
      minWidth: 440,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const expense = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={expense}>
            <Typography className="table-tooltip-text">{expense}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formattedValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "expense_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ value }: { value: number }) => (
        <div className="flex items-center gap-1.5 justify-center">
          <ButtonWithTooltip
            tooltipTitle={_t("View")}
            tooltipPlacement="top"
            icon="fa-solid fa-eye"
            onClick={async () => {
              let tempAuthorization = authorization;
              const isExpired = isExpiredAuthorization();
              if (isExpired) {
                const response = (await webWorkerApi({
                  url: "/api/auth/token",
                })) as IGetTokenFromNode;
                if (response.success) {
                  tempAuthorization = response.data.accessToken;
                  setAuthorizationExpired(response.data.accessTokenExpired);
                }
              }
              const newURL = new URL(
                routes.MANAGE_EXPENSES.url + "/" + (value?.toString() || ""),
                window.location.origin
              );
              newURL.searchParams.set("authorize_token", tempAuthorization);
              newURL.searchParams.set("iframecall", "1");
              newURL.searchParams.set("from_remix", "1");
              setIframeData({
                url: newURL.toString(),
                title: String(id),
              });
            }}
          />
          <ExpenseFieldRedirectionIcon
            iconClassName="!w-3.5 !h-3.5"
            expenseId={value?.toString() ?? ""}
          />
        </div>
      ),
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(expenseModule?.plural_name ?? "Expenses")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(expenseModule?.module_name ?? "Expense")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_EXPENSES.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_EXPENSES.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={displayedExpenses}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-expenses.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["expenses", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, ["expenses", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default ExpensesTable;
