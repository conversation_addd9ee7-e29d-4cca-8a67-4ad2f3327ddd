import { useTranslation } from "~/hook";
import { Form } from "@remix-run/react";

// atoms
import { Header } from "~/shared/components/atoms/header";
import { Drawer } from "~/shared/components/atoms/drawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";

// Other
import { defaultConfig } from "~/data";
import { useEffect, useMemo, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { addEquipmentsItems } from "~/redux/action/addCostCode";
import { addCostCodeIdAct } from "../../../../redux/slices/equipmentsSlice";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";
import DLSendEmail from "../../../DLSendEmail";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { getGConfig } from "~/zustand";

const AddEquipmentItem = ({
  addEquipmentItem,
  setAddEquipmentItem,
}: TIAddEquipmentItemProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const gConfig: GConfig = getGConfig();

  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const { codeCostData }: IGetCostCodeList = useAppDLSelector(
    (state) => state.costCode
  );

  const [operatorModalOpen, setOperatorModalOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedOperator, setSelectedOperator] = useState<
    TselectedContactSendMail[]
  >([]);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDrawerOpen, setIsConfirmDrawerOpen] =
  //   useState<boolean>(false);

  const isCustomerName = (
    item: TselectedContactSendMail
  ): item is CustomerEmail => {
    return (item as CustomerEmail).display_name !== undefined;
  };

  const isCustomerId = (
    item: TselectedContactSendMail
  ): item is CustomerEmail => {
    return (item as CustomerEmail).user_id !== undefined;
  };
  const selectedOperatorName =
    selectedOperator
      .filter(isCustomerName)
      .map((item) => item.display_name)
      .join(", ") ?? "";

  const selectedOperatorId =
    selectedOperator
      .filter(isCustomerId)
      .map((item) => item.user_id)
      .join(", ") ?? null;

  const options = codeCostData.map((item) => ({
    label:
      `${HTMLEntities.decode(sanitizeString(item?.cost_code_name))}` +
      `${
        item?.csi_code
          ? ` (${HTMLEntities.decode(sanitizeString(item?.csi_code))})`
          : ""
      }`,
    value: item.code_id,
  }));

  const initialValues: IAddEquipmentItemValues = useMemo(
    () => ({
      name: "",
      costCodeId: "",
      operatorName: selectedOperatorName || "",
      operatorId: selectedOperatorId ? Number(selectedOperatorId) : undefined,
      notes: "",
      internal_notes: "",
    }),
    [selectedOperatorName, selectedOperatorId]
  );
  
  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().required("This field is required."),
    operatorId: Yup.number().required("This field is required."),
  });

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      setIsLoading(true);
      const addEquipmentsRes = (await addEquipmentsItems({
        costCodeKey: "item_equipment",
        name: !!values.name
          ? escapeHtmlEntities(values.name?.trim())
          : undefined,
        costCodeId: values.costCodeId || undefined,
        notes: values.notes?.trim() || undefined,
        unit: null,
        itemId: 0,
        internalNotes: values.internal_notes?.trim() || undefined,
        operatorId: values.operatorId || undefined,
      })) as IAddCostCodeDataApiRes;
      if (addEquipmentsRes?.success) {
        dispatch(addCostCodeIdAct({ cost_code_id: addEquipmentsRes.data.id }));
        setIsLoading(false);
      } else {
        notification.error({
          description: addEquipmentsRes?.message,
        });
      }

      handleClose();
    },
  });
  const handleClose = () => {
    formik.resetForm();
    setSelectedOperator([]);
    setAddEquipmentItem(false);
  };

  useEffect(() => {
    formik.setValues({
      ...formik.values,
      operatorName: selectedOperatorName,
      operatorId: selectedOperatorId ? Number(selectedOperatorId) : undefined,
    });
  }, [selectedOperatorName, selectedOperatorId]);

  return (
    <>
      <Drawer
        open={addEquipmentItem}
        rootClassName="drawer-open"
        width={718}
        push={false}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-check"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t("Add Equipment Item")}
            </Header>
          </div>
        }
        closeIcon={<CloseButton onClick={handleClose} />}
      >
        <Form
          method="post"
          noValidate
          className="py-4"
          onSubmit={formik.handleSubmit}
          onKeyDown={(e) => {
            if (
              e.key === "Enter" &&
              (e.target as HTMLElement).tagName !== "TEXTAREA"
            ) {
              e.preventDefault();
            }
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Name")}
                    name="name"
                    labelPlacement="top"
                    isRequired={true}
                    value={formik?.values?.name}
                    onChange={formik.handleChange}
                    errorMessage={
                      formik?.touched?.name ? formik?.errors?.name : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    onChange={(
                      value: string | number | string[] | number[],
                      options?: DefaultOptionType | BaseOptionType
                    ) => {
                      const selectedValues = Array.isArray(value)
                        ? value
                        : value;
                      formik.setFieldValue(
                        "costCodeId",
                        selectedValues ? selectedValues : undefined
                      );
                    }}
                    options={options}
                  />
                </div>
                <div className="w-full">
                  <ButtonField
                    label={_t("Default Operator")}
                    labelPlacement="top"
                    name="operatorId"
                    value={selectedOperatorName}
                    required={true}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      formik.setFieldValue(
                        "operatorId",
                        Number(e.target.value) || undefined
                      )
                    }
                    onClick={() => setOperatorModalOpen(true)}
                    addonBefore={
                      selectedOperatorName && formik.values?.operatorId ? (
                        <div className="flex items-center gap-1">
                          <ContactDetailsButton
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              setIsOpenContactDetails(true);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            className="!w-5 !h-5"
                            directoryId={
                              selectedOperator?.[0]?.user_id?.toString() || ""
                            }
                            directoryTypeKey={getDirectaryKeyById(
                              selectedOperator?.[0]?.type?.toString() === "1"
                                ? 2
                                : Number(selectedOperator?.[0]?.type),
                              gConfig
                            )}
                          />
                        </div>
                      ) : (
                        <></>
                      )
                    }
                    errorMessage={
                      formik?.touched?.operatorId
                        ? formik?.errors?.operatorId
                        : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    name="notes"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    value={formik?.values?.notes}
                    onChange={formik.handleChange}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    name="internal_notes"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    value={formik?.values?.internal_notes}
                    onChange={formik.handleChange}
                  />
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              disabled={isLoading}
              isLoading={isLoading}
            />
          </div>
        </Form>
      </Drawer>
      {operatorModalOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setOperatorModalOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={operatorModalOpen}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          setCustomer={(data) => setSelectedOperator(data)}
          selectedCustomer={selectedOperator}
          groupCheckBox={false}
          projectId={Number(details?.projectId || "")}
        />
      )}

      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={formik.values?.operatorId || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          additional_contact_id={0} // as per PHP additional contact will not selected in assinged to field
        />
      )}
      <DLSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
          "my_project",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        projectId={Number(details?.projectId || "")}
        app_access={false}
      />
    </>
  );
};

export default AddEquipmentItem;
