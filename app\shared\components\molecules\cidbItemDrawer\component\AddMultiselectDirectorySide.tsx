import { useEffect, useMemo, useState } from "react";
import { Form } from "@remix-run/react";
import { useTranslation } from "~/hook";
import CommonCardWithBorder from "~/components/common/common-card-with-border";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import isEmpty from "lodash/isEmpty";
import { Number, sanitizeString } from "~/helpers/helper";
import { type CheckboxChangeEvent } from "antd/es/checkbox";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
// molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
// redux action
import { addEquipmentsItems } from "~/redux/action/addCostCode";
import { getCostCodeList } from "../../../../../redux/action/getCostCodeListAction";
import { ConfirmModal } from "../../confirmModal";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getGSettings } from "~/zustand";
import {
  filterOptionBySubstring,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { onEnterSelectSearchValue } from "../../selectField/units";
import { InputNumberField } from "../../inputNumberField";

const defaultError = {
  name: "",
  unitCost: "",
  unit: "",
};

const AddMultiselectItemSide = ({
  addMultiselectItemOpen,
  setAddMultiselectItemOpen,
  selectOption,
  dispatch,
  itemType,
  itemTypes = [],
  customer,
  isViewSaveAndAddAnother,
  callOnAddComplete,
  customeCheckBox = true,
}: IAddICdbItemSideProps) => {
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const {
    default_equipment_markup_percent = "",
    default_labor_markup_percent = "",
    default_material_markup_percent = "",
    default_other_item_markup_percent = "",
    default_sub_contractor_markup_percent = "",
    default_undefined_markup_percent = "",
  } = appSettings || {};
  let markupDefaultValue = "";
  if (selectOption?.value === "material") {
    markupDefaultValue = default_material_markup_percent;
  } else if (selectOption?.value === "equipment") {
    markupDefaultValue = default_equipment_markup_percent;
  } else if (selectOption?.value === "labor") {
    markupDefaultValue = default_labor_markup_percent;
  } else if (selectOption?.value === "subcontractor") {
    markupDefaultValue = default_sub_contractor_markup_percent;
  } else if (selectOption?.value === "other_items") {
    markupDefaultValue = default_other_item_markup_percent;
  } else if (default_undefined_markup_percent) {
    markupDefaultValue = default_undefined_markup_percent;
  }
  const { _t } = useTranslation();
  const defaultValue = {
    name: "",
    unitCost: "",
    unit: "",
    markup: Number(markupDefaultValue),
    hiddenMU: "",
    costCode: "",
    description: "",
    notes: "",
    addCheckbox: true,
  };

  const [formData, setFormData] =
    useState<Partial<IAddCidbFormInitialState>>(defaultValue);
  const [initialValuesData, setInitialValuesData] =
    useState<Partial<IAddCidbFormInitialState>>(formData);
  const [costCodes, setCostCode] = useState<ICostCode[]>([]);
  const [errors, setErrors] = useState<ICidbAddFormError>(defaultError);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  

  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);
  const [isLoadingInAddAnotherData, setIsLoadingInAddAnotherData] =
    useState<boolean>(false);
  const updateFormData = (key: string, value: string | number | boolean) => {
    setFormData((prevFormData: Partial<IAddCidbFormInitialState>) => ({
      ...prevFormData,
      [key]: value,
    }));
  };

  const resetForm = () => {
    setFormData(defaultValue);
    setErrors(defaultError);
  };

  let key: string = "";
  if (selectOption?.value === "subcontractor") {
    key = "contractor_id";
  } else if (selectOption?.value === "material") {
    key = "material_id";
  } else if (selectOption?.value === "labor") {
    key = "labor_id";
  } else if (selectOption?.value === "equipment") {
    key = "equipment_id";
  } else if (selectOption?.value === "other_items") {
    key = "other_item_id";
  } else {
    key = "item_other";
  }

  useEffect(() => {
    getCostCode();
    getUnit();
  }, []);

  useEffect(() => {
    if (defaultValue.markup) {
      setFormData({ ...formData, markup: defaultValue.markup });
      setInitialValuesData((prevState: Partial<IAddCidbFormInitialState>) => ({
        ...prevState,
        markup: defaultValue.markup,
      }));
    }
  }, [defaultValue.markup]);
  const getCostCode = async () => {
    const costCodeList = (await getCostCodeList({
      daily_equipment_cost_code: 1,
    })) as ICostCodeList;
    setCostCode(costCodeList?.data);
  };

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  const options: ISelectFieldProps["options"] = costCodes?.map(
    (item: ICostCode) => ({
      label:
        `${HTMLEntities.decode(sanitizeString(item?.cost_code_name))}` +
        `${
          item?.csi_code
            ? ` (${HTMLEntities.decode(sanitizeString(item?.csi_code))})`
            : ""
        }`,
      value: item.code_id,
    })
  );

  const handleNameOnchange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("name", inputValue);
    setErrors((prev: IError) => {
      if (!isEmpty(inputValue)) {
        return { ...prev, name: "" };
      }
      return prev;
    });
  };
  const handleUnit = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("unit", inputValue);
    setErrors((prev: IError) => {
      if (!isEmpty(e?.target?.value)) {
        return { ...prev, unit: "" };
      }
      return prev;
    });
  };
  const handleCost = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e;
    updateFormData("unitCost", inputValue);
    setErrors((prev: IError) => {
      if (!isEmpty(e)) {
        return { ...prev, unitCost: "" };
      }
      return prev;
    });
  };

  useEffect(() => {
    if (defaultValue?.markup) {
      updateFormData("markup", Number(defaultValue.markup));
      setInitialValuesData((prevState: Partial<IAddCidbFormInitialState>) => ({
        ...prevState,
        markup: Number(defaultValue.markup),
      }));
    }
  }, [addMultiselectItemOpen]);

  const handleTaxCheckbox = (e: CheckboxChangeEvent) => {
    const value = e?.target?.checked;
    updateFormData("addCheckbox", value);
  };

  const handleDescription = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e?.target?.value;
    updateFormData("description", value);
  };

  const handleCostCode = (
    value: string | string[],
    options?: DefaultOptionType | BaseOptionType
  ) => {
    updateFormData("costCode", value as string);
  };
  const handleNotes = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e?.target?.value;
    updateFormData("notes", value);
  };

  const handleFormSubmit = async (
    e: React.MouseEvent<HTMLButtonElement>,
    setLoading: (loading: boolean) => void,
    closeModal: () => void
  ) => {
    e.preventDefault();
    setLoading(true);
    try {
      const tempErrors = {
        // Todo this https://app.clickup.com/t/86cz7686q
        // unitCost:
        //   selectOption?.value !== "equipment"
        //     ? Number(formData.unitCost)
        //       ? ""
        //       : "This field is required."
        //     : "",
        name: formData.name?.trim() ? "" : "This field is required.",
        // Todo this https://app.clickup.com/t/86cz7686q
        // unit:
        //   selectOption?.value !== "equipment"
        //     ? formData.unit?.trim()
        //       ? ""
        //       : "This field is required."
        //     : "",
      };

      if (
        Object.values(tempErrors).some((errorMessage) => errorMessage.trim())
      ) {
        setErrors(tempErrors);
        setLoading(false);
        return;
      }

      const data = {
        costCodeKey:
          selectOption.value === "material"
            ? "item_material"
            : selectOption.value === "equipment"
            ? "item_equipment"
            : selectOption.value === "labor"
            ? "item_labour"
            : selectOption.value === "subcontractor"
            ? "item_sub_contractor"
            : "item_other",
        name: formData.name || undefined,
        unitCost: formData.unitCost ? Number(formData.unitCost) * 100 : 0,
        unit: formData.unit
          ? HTMLEntities.encode(formData.unit)
          : formData.unit,
        markup: formData.markup?.toString() || null,
        hiddenMarkup: formData.hiddenMU?.toString() || null,
        costCodeId: formData.costCode || undefined,
        notes: formData.description || formData?.notes || undefined,
        internalNotes: formData.notes || undefined,
        is_favorite: "0",
      };

      const addEquipmentsRes = (await addEquipmentsItems(
        data
      )) as IAddCostCodeDataApiRes;

      if (addEquipmentsRes.success) {
        setLoading(false);
        const addedData = {
          ...data,
          [key]: addEquipmentsRes.data.id,
          unit_cost: (Number(formData.unitCost) * 100)?.toString(),
        } as Partial<CIDBItemSideData>;
        if (callOnAddComplete) {
          callOnAddComplete(addEquipmentsRes, data);
        }
        if (formData?.addCheckbox) {
          dispatch({
            type: "SET_SELECTED_CIDB_ITEM",
            payload: { ...addedData, item_type: itemType as unknown as string },
          });
        }
        if (customer[selectOption?.value].search) {
          dispatch({
            type: "SET_CIDB_ITEM_SEARCH",
            payload: {
              activeField: selectOption?.value,
              searchText: "",
            },
          });
          dispatch({
            type: "SET_CIDB_ITEM_MAIN_SEARCH",
            payload: {
              activeField: selectOption?.value,
              searchText: "",
            },
          });
        }
        if (!customer[selectOption?.value].isFavorite) {
          dispatch({
            type: "GET_CIDB_ITEM_ADD_DETAILS_FULFILLED",
            payload: {
              activeField: selectOption?.value as ItemTabs,
              addedData,
            },
          });
        }
      } else {
        notification.error({
          description: addEquipmentsRes.message,
        });
        setLoading(false);
      }
      closeModal();
    } catch (error) {
      setLoading(false);
      console.error(
        `\n File: #AddMultiselectDirectorySide.tsx -> Line: #341 -> error: `,
        error
      );
      notification.error({
        description: "something went wrong!",
      });
    }
  };

  const handleAddFormData = (e: React.MouseEvent<HTMLButtonElement>) => {
    handleFormSubmit(e, setIsLoading, handleCloseBill);
  };

  const handleAddFormAnotherData = (e: React.MouseEvent<HTMLButtonElement>) => {
    handleFormSubmit(e, setIsLoadingInAddAnotherData, handleClose);
  };

  const handleClose = () => {
    resetForm();
    setIsLoading(false);
    setIsLoadingInAddAnotherData(false);
  };

  const handleCloseBill = () => {
    resetForm();
    setAddMultiselectItemOpen(!addMultiselectItemOpen);
    setIsLoading(false);
    setIsLoadingInAddAnotherData(false);
  };
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  // this is will NF once testing done it will be merge on dev

  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(formData) !== JSON.stringify(initialValuesData);
  // }, [formData, initialValuesData]);

  // const closeConfirmationModal = () => {
  //   addMultiselectItemOpen;
  //   setIsConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDialogOpen(false);
  //   resetForm();
  //   setAddMultiselectItemOpen(!addMultiselectItemOpen);
  //   setIsLoading(false);
  //   setIsLoadingInAddAnotherData(false);
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     resetForm();
  //     setAddMultiselectItemOpen(!addMultiselectItemOpen);
  //     setIsLoading(false);
  //     setIsLoadingInAddAnotherData(false);
  //   } else {
  //     setIsConfirmDialogOpen(true);
  //   }
  // };

  return (
    <>
      <Drawer
        open={addMultiselectItemOpen}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        rootClassName="drawer-open"
        push={false}
        width={700}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              {/* Notes: This icon Set in dynamic (ext. Employee, Lead  */}
              {selectOption?.displayIcon}
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {/* Notes: This Name Set in dynamic (ext. Add Employee, Add Lead  */}
              {_t(
                `Add ${
                  selectOption?.name === "Other Items"
                    ? "Other Items"
                    : selectOption?.name + " Item"
                }`
              )}
            </Header>
          </div>
        }
        closeIcon={<CloseButton onClick={() => handleCloseBill()} />}
        // this is will NF once testing done it will be merge on dev
        // closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
      >
        <Form method="post" noValidate className="py-4">
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <CommonCardWithBorder>
              <div className="w-full">
                <InputField
                  label={_t("Name")}
                  type="text"
                  size="middle"
                  name="name"
                  labelClass="dark:text-white/90"
                  isRequired
                  onChange={handleNameOnchange}
                  errorMessage={errors?.name}
                  value={formData?.name}
                  onPressEnter={handleEnterKeyPress}
                />
              </div>
              <div className="grid md:grid-cols-2 gap-5">
                <div className="w-full">
                  <InputNumberField
                    label={_t("Unit Cost")}
                    size="middle"
                    name="unit_cost"
                    id="unit_cost"
                    labelClass="dark:text-white/90"
                    value={formData?.unitCost}
                    onChange={(e) => handleCost(e)}
                    formatter={(value, info) => {
                      return inputFormatter(value?.toString()).value;
                    }}
                    parser={(value) => {
                      const inputValue = value?.trim();
                      if (!inputValue) return ""; // explicitly return empty string
                      return unformatted(inputValue);
                    }}
                    onKeyDown={(event) =>
                      onKeyDownCurrency(event, {
                        integerDigits: 10,
                        decimalDigits: 2,
                        unformatted,
                        allowNegative: false,
                        decimalSeparator: inputFormatter().decimal_separator,
                      })
                    }
                    // isRequired={selectOption?.value !== "equipment"} // Todo this https://app.clickup.com/t/86cz7686q
                  />
                </div>
                <div className="w-full">
                  {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                    <SelectField
                      label={_t("Unit")}
                      placeholder=""
                      name="unit"
                      value={newTypeName || formData?.unit}
                      labelPlacement="top"
                      iconView={true}
                      maxLength={15}
                      showSearch
                      options={
                        unitData?.map((type) => ({
                          label: type.name.toString(),
                          value: type.name.toString(),
                        })) ?? []
                      }
                      allowClear
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      onChange={(value) => {
                        updateFormData(
                          "unit",
                          Array.isArray(value) ? value.join(", ") : value
                        );
                        setErrors((prev) => ({ ...prev, unit: "" }));
                      }}
                      addItem={{
                        text: "Add Unit: Type Unit & Press Enter",
                        icon: "fa-regular fa-plus",
                      }}
                      // isRequired={selectOption?.value !== "equipment"} // Todo this https://app.clickup.com/t/86cz7686q
                      onInputKeyDown={(e) => {
                        const value = e?.currentTarget?.value?.trim();
                        if (e.key === "Enter") {
                          const newType = onEnterSelectSearchValue(
                            e,
                            unitData?.map((unit) => ({
                              label: unit?.name,
                              value: "",
                            })) || []
                          );
                          if (newType) {
                            setNewTypeName(newType);
                          } else if (value) {
                            notification.error({
                              description:
                                "Records already exist, no new records were added.",
                            });
                          }
                        } else if (value?.length > 14 && e.key.length === 1) {
                          e.preventDefault();
                        }
                      }}
                      onClear={() => {
                        updateFormData("unit", "");
                        setErrors((prev) => ({ ...prev, unit: "" }));
                      }}
                      // errorMessage={errors?.unit} // Todo this https://app.clickup.com/t/86cz7686q
                    />
                  ) : (
                    <InputField
                      label={_t("Unit")}
                      type="text"
                      size="middle"
                      name="unit"
                      labelClass="dark:text-white/90"
                      // isRequired={selectOption?.value !== "equipment"}
                      onChange={handleUnit}
                      maxLength={12}
                      onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                        if (e.key === '"') {
                          e.preventDefault();
                        }
                      }}
                      errorMessage={errors?.unit}
                      value={formData?.unit}
                      onPressEnter={handleEnterKeyPress}
                    />
                  )}
                </div>
              </div>
              <div className="grid md:grid-cols-2 gap-5">
                <div className="w-full">
                  <InputField
                    label={_t("Markup") + " (%)"}
                    type="number"
                    size="middle"
                    name="markup"
                    labelClass="dark:text-white/90"
                    onChange={(e) => {
                      const value = e.target.value;
                      // Only allow up to 3 digits
                      if (value.length <= 3) {
                        // Remove any decimal points
                        const integerValue = parseInt(value) || 0;
                        updateFormData("markup", integerValue);
                      }
                    }}
                    min={0}
                    max={999}
                    onKeyDown={(
                      event: React.KeyboardEvent<HTMLInputElement>
                    ) => {
                      // Prevent decimal point
                      if (event.key === "." || event.key === "Enter") {
                        event.preventDefault();
                      }
                      // Only allow numbers and control keys
                      if (
                        !/^\d$/.test(event.key) &&
                        ![
                          "Backspace",
                          "Delete",
                          "ArrowLeft",
                          "ArrowRight",
                          "Tab",
                          "ArrowUp",
                          "ArrowDown",
                        ].includes(event.key)
                      ) {
                        event.preventDefault();
                      }
                    }}
                    value={formData?.markup}
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Hidden Markup") + " (%)"}
                    type="number"
                    size="middle"
                    name="hidden_mu"
                    min={0}
                    max={999}
                    labelClass="dark:text-white/90"
                    onChange={(e) => {
                      const value = e.target.value;
                      // Only allow up to 3 digits
                      if (value.length <= 3) {
                        // Remove any decimal points
                        const integerValue = parseInt(value) || 0;
                        updateFormData("hiddenMU", integerValue);
                      }
                    }}
                    onKeyDown={(
                      event: React.KeyboardEvent<HTMLInputElement>
                    ) => {
                      // Prevent decimal point
                      if (event.key === "." || event.key === "Enter") {
                        event.preventDefault();
                      }
                      // Only allow numbers and control keys
                      if (
                        !/^\d$/.test(event.key) &&
                        ![
                          "Backspace",
                          "Delete",
                          "ArrowLeft",
                          "ArrowRight",
                          "Tab",
                          "ArrowUp",
                          "ArrowDown",
                        ].includes(event.key)
                      ) {
                        event.preventDefault();
                      }
                    }}
                    value={formData?.hiddenMU}
                  />
                </div>
              </div>
              <div className="w-full">
                <SelectField
                  name="cost_code"
                  label={_t("Cost Code")}
                  options={options}
                  showSearch={true}
                  className="text-sm"
                  allowClear
                  onChange={handleCostCode}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  value={formData?.costCode}
                  labelPlacement={"top"} // errorMessage={formData?.costCode}
                />
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Description")}
                  name="description"
                  onChange={handleDescription}
                  value={formData?.description}
                />
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Internal Notes")}
                  name="internal_note"
                  onChange={handleNotes}
                  value={formData?.notes}
                />
              </div>
              {customeCheckBox && (
                <CustomCheckBox
                  className="gap-1.5 text-primary-900 w-fit"
                  checked={formData?.addCheckbox}
                  onChange={handleTaxCheckbox}
                  value={formData?.addCheckbox}
                >
                  {_t("Add this item to my current list upon Saving")}
                </CustomCheckBox>
              )}
            </CommonCardWithBorder>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full sm:gap-5 gap-3.5 px-4 pt-4">
            <PrimaryButton
              type="primary"
              className="w-full justify-center"
              htmlType="submit"
              onClick={handleAddFormData}
              isLoading={isLoading}
              disabled={isLoading || isLoadingInAddAnotherData}
              buttonText={_t("Save")}
            />
            {isViewSaveAndAddAnother && (
              <PrimaryButton
                type="primary"
                className="w-full justify-center"
                htmlType="submit"
                onClick={handleAddFormAnotherData}
                isLoading={isLoadingInAddAnotherData}
                disabled={isLoadingInAddAnotherData || isLoading}
                buttonText={_t("Save & Add Another Item")}
              />
            )}
          </div>
        </Form>
      </Drawer>
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Unit To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                updateFormData("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeConfirmationModal}
        />
      )} */}
    </>
  );
};

export default AddMultiselectItemSide;
