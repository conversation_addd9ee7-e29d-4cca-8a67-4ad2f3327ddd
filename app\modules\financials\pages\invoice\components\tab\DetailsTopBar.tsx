import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";
import delay from "lodash/delay";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
// Hooks + redux
import { useIframe, useTranslation } from "~/hook";
import { routes } from "~/route-services/routes";
import { sendMessageKeys } from "~/components/page/$url/data";
import { resetDash } from "~/modules/financials/pages/invoice/redux/slices/dashboardSlice";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import {
  IIVDetailsField,
  invoiceChargedApprovalType,
  invoiceExlStatus,
  invoiceStatusQB,
  upadateIVFieldStatus,
} from "~/modules/financials/pages/invoice/utils/constants";
import { getGConfig, getGSettings, setCommonSidebarCollapse } from "~/zustand";
import { sanitizeString } from "~/helpers/helper";
import {
  fetchIVDetails,
  getInvoiceRetainageInfoApi,
  updateIVDetailApi,
} from "~/modules/financials/pages/invoice/redux/action/iVDetailsAction";
import InvoiceListAction from "../InvoiceListAction";
import {
  setAuthorizedList,
  updateIVDetail,
} from "~/modules/financials/pages/invoice/redux/slices/iVDetailsSlice";
import {
  fetchChangeOrderProjectAdditonalContact,
  getContactDetailsApi,
} from "~/redux/action/commonAction";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
// Other
import { getStatusForField } from "~/shared/utils/helper/common";
import { defaultConfig } from "~/data";
import InvoiceSendEmail from "../InvoiceSendEmail";
import { dirTypeKeyById } from "~/modules/people/directory/utils/constasnts";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { getGlobalUser } from "~/zustand/global/user/slice";

dayjs.extend(utc);
dayjs.extend(timezone);

const DetailsTopBar = ({
  sidebarCollapse,
  iVStatusVal,
  activeStep,
  stepStatusLoaing,
  ivStatList,
  selectedStatusInd,
  onStepClick,
  onReloadDetails,
}: IIVDetailsTopBarProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const params: RouteParams = useParams();
  const { parentPostMessage } = useIframe();
  const { module_key, module_access }: GConfig = getGConfig();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id = "" } = user || {};
  const { date_format }: GSettings = getGSettings();
  const {
    quickbook_desktop_sync,
    quickbook_sync,
    is_custom_invoice_id,
  }: GSettings = getGSettings();
  const dispatch = useAppIVDispatch();

  const {
    isDetailLoading,
    details,
    authorizedContactId,
  }: IIVDetailsInitialState = useAppIVSelector((state) => state.invoiceDetails);

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(upadateIVFieldStatus);
  const [inputValues, setInputValues] =
    useState<Partial<IIVDetails>>(IIVDetailsField);

  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject | null>({
    project_name: "",
    project_id: 0,
    id: 0,
    customer_id: 0,
  });

  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);

  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [isSelecteCustomer, setIsSelecteCustomer] = useState<boolean>(false);
  const [selectedCustomer, setSelectedCustomer] = useState<
    Partial<IDirectoryData>
  >({});
  const [isFocusedAgrField, setIsFocusedAgrField] = useState<boolean>(false);

  useEffect(() => {
    setInputValues(details);
  }, [details.invoice_id]);
  useEffect(() => {
    setSelectedProject({
      project_name: details.project_name || "",
      // project_id: details.project_id,
      id: details?.project_id || 0,
      customer_id: details?.customer_id || 0,
    });
  }, [details.project_id]);

  useEffect(() => {
    if (details?.customer_id) {
      const dirType = Object.entries(dirTypeKeyById).find(
        ([_, val]) => val == details?.customer_type
      )?.[0];

      setSelectedCustomer({
        user_id: details.customer_id,
        display_name: details.customer_name,
        image:
          !details?.customer_contact_id || details?.customer_contact_id == 0
            ? details.customer_image
            : "",
        type_key: dirType,
        contact_id: details?.customer_contact_id,
      });
    } else {
      setSelectedCustomer({
        user_id: 0,
        display_name: "",
      });
    }
  }, [details?.customer_id, details?.customer_contact_id]);

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const fetchInvoiceRetainageInfo = async (pId: number) => {
    try {
      if (!pId || pId == 0) {
        return null;
      }
      const response = (await getInvoiceRetainageInfoApi({
        project_id: Number(pId),
      })) as IIVRetainageInfoApiRes;
      if (response?.success) {
        return response?.data;
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  const handleUpdateField = async (
    data: IIVDetailFields,
    otherData?: IIVUpdateFieldOtherData,
    reqType?: string
  ) => {
    const field = Object.keys(data)[0] as keyof IIVDetails;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newData = { ...data };
    if (reqType === "customer_id") {
      newData = {
        ...data,
        project_id: "",
        authorized_by: "",
        billed_to: otherData?.billed_to || 0,
        billed_to_contact: otherData?.billed_to_contact || 0,
        customer_contact_id: otherData?.contact_id || 0,
      };
    }

    let newBillData: IIVUpdateFieldOtherData = {};
    if (field === "project_id") {
      const retainageData = await fetchInvoiceRetainageInfo(
        Number(data?.project_id || "")
      );
      newData = {
        ...data,
        invoice_retainage: retainageData?.project_retainage || "",
        billed_to: otherData?.billed_to || 0,
        billed_to_contact: otherData?.billed_to_contact || 0,
      };

      if (!otherData?.billed_to || otherData?.billed_to == 0) {
        const apiCustomerRes = (await getContactDetailsApi({
          directoryId: details?.customer_id?.toString() || "",
          request_from: "cardview",
        })) as IGenerateShareLinkApiRes;

        newBillData = apiCustomerRes?.data;
        newData = {
          ...data,
          billed_to: newBillData?.billed_to || 0,
          billed_to_contact: newBillData?.billed_to_contact || 0,
        };
      }
    }

    const updateRes = (await updateIVDetailApi({
      invoice_id: details?.invoice_id || "",
      ...newData,
    })) as IIVDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      if (field === "custom_invoice_id") {
        dispatch(updateIVDetail(data));
      }

      if (field === "project_id") {
        const commonData = {
          prefix_company_sub_contract_id:
            updateRes?.data?.prefix_company_sub_contract_id,
          projectPrefix: updateRes?.data?.projectPrefix,
          project_custom_id: updateRes?.data?.project_custom_id,
          project_name: otherData?.project_name,
          project_id: data.project_id,
          invoice_retainage: newData?.invoice_retainage || "",
        };

        const billedData = newBillData?.billed_to
          ? {
              billed_to: newBillData?.billed_to || 0,
              billed_to_contact: newBillData?.billed_to_contact || 0,
              billed_to_display_name: newBillData?.billed_to_display_name || "",
              billed_to_type_name: newBillData?.billed_to_type_name || "",
              billed_to_dir_type: newBillData?.billed_to_type || "",
              invoiced_to: {
                ...details?.invoiced_to,
                image: newBillData?.billed_to_image || "",
              },
            }
          : {
              billed_to: otherData?.billed_to || 0,
              billed_to_contact: otherData?.billed_to_contact || 0,
              billed_to_display_name: otherData?.billed_to_name || "",
              billed_to_type_name: otherData?.billed_to_type_name || "",
              billed_to_dir_type: otherData?.billed_to_dir_type || "",
              invoiced_to: {
                ...details?.invoiced_to,
                image: otherData?.billed_to_image || "",
              },
            };

        dispatch(updateIVDetail({ ...commonData, ...billedData }));
      }

      if (field === "customer_id") {
        dispatch(
          updateIVDetail({
            customer_company: otherData?.company_name,
            customer_image: otherData?.image || "",
            customer_type:
              otherData?.contact_id &&
              otherData?.contact_id != "0" &&
              otherData?.parent_type
                ? otherData?.parent_type
                : otherData?.type,
            customer_contact_id: otherData?.contact_id || 0,
            customer_name: otherData?.display_name,
            customer_id: data.customer_id,
            billed_to: otherData?.billed_to || 0,
            billed_to_contact: otherData?.billed_to_contact || 0,
            billed_to_display_name: otherData?.billed_to_display_name || "",
            billed_to_type_name: otherData?.billed_to_type_name || "",
            billed_to_dir_type: otherData?.billed_to_dir_type || "",
            invoiced_to: {
              ...details?.invoiced_to,
              image: otherData?.billed_to_image || "",
            },
          })
        );
      }
      if (reqType === "customer_id") {
        dispatch(
          updateIVDetail({
            // Reset Project
            prefix_company_sub_contract_id: "",
            projectPrefix: "",
            project_custom_id: "",
            project_name: "",
            project_id: "",
            // Reset Authorized By
            authorized_by: "",
            //  Reset Invoiced To
          })
        );
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
      if (field === "project_id") {
        setSelectedProject({
          project_name: details.project_name || "",
          // project_id: details.project_id,
          id: details?.project_id || 0,
          customer_id: details?.customer_id || 0,
        });
      }
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const invoiceDropdownStatus = useMemo(() => {
    const statusList = ivStatList
      ?.filter(
        (i) =>
          !invoiceExlStatus.includes(i.key || "") &&
          i?.value != invoiceChargedApprovalType
      )
      ?.map((item: IStatusList) => {
        const stLabel =
          (quickbook_desktop_sync == "1" || quickbook_sync == "1") &&
          invoiceStatusQB.includes(item?.key || "")
            ? `${item?.label} ${_t("(Post to QuickBooks)")}`
            : item?.label;
        return {
          label: HTMLEntities.decode(sanitizeString(stLabel)),
          key: item?.value?.toString() ?? "",
          icon: (
            <FontAwesomeIcon
              icon="fa-solid fa-square"
              className="h-3.5 w-3.5"
              style={{
                color: item?.default_color,
              }}
            />
          ),
        };
      });
    const getSelectStatus = ivStatList?.find(
      (item: ISubContractsStatusList) => item.value == activeStep
    );
    const selectStatus = (
      <Tooltip title={getSelectStatus?.label}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {getSelectStatus?.label}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.default_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus };
  }, [JSON.stringify(ivStatList), activeStep]);

  const fetchAdditionalContacts = async (
    dirId: number
  ): Promise<IAdditionalContactsRes | null> => {
    try {
      if (!dirId || dirId == 0) {
        return null;
      }
      const response = (await fetchChangeOrderProjectAdditonalContact({
        directory_id: Number(dirId),
      })) as IAdditionalContactsRes;
      if (response?.success) {
        return response;
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  useEffect(() => {
    (async () => {
      const dirId = details.customer_id;
      if (dirId && dirId != 0 && authorizedContactId != dirId) {
        const addiConRes = await fetchAdditionalContacts(dirId || 0);
        if (addiConRes) {
          const conData = addiConRes?.data;
          dispatch(
            setAuthorizedList({
              data: [conData?.directory, ...conData?.contacts],
              dirId,
            })
          );
        } else {
          dispatch(setAuthorizedList({ data: [], dirId: 0 }));
        }
      }
    })();
  }, [details?.customer_id, authorizedContactId]);

  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  const isPastDue = useMemo(() => {
    if (details?.approval_type_key !== "invoice_submitted") return false;
    const dueDate = dayjs(details?.due_date, date_format, true);
    if (!dueDate.isValid()) return false;
    return dueDate.isBefore(currentDateTime, "day");
  }, [details?.due_date, details?.approval_type_key, currentDateTime]);

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#f8f8f8] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isDetailLoading ? (
              <TopBarSkeleton statusList={true} num={4} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:max-w-[calc(100%-125px)] w-full">
                  <div
                    className="w-11 h-11 flex items-center justify-center bg-[#44539A] rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white"
                    style={{ backgroundColor: iVStatusVal?.default_color }}
                  >
                    {iVStatusVal?.icon && (
                      <FontAwesomeIcon
                        className="w-[18px] h-[18px] text-white"
                        icon={iVStatusVal.icon}
                      />
                    )}
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] 2xl:pr-3.5 ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <ButtonField
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      labelPlacement="left"
                      value={HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}
                      headerTooltip={`Project: ${HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}`}
                      editInline={true}
                      iconView={true}
                      required={true}
                      placeholder={_t("Select Project")}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-base h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-base font-medium"
                      readOnly={isReadOnly}
                      isDisabled={details?.is_disabled_project == "1"}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "project_id"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      rightIcon={
                        !isEmpty(selectedProject?.project_name) && (
                          <ProjectFieldRedirectionIcon
                            projectId={details?.project_id?.toString() || ""}
                          />
                        )
                      }
                      onClick={() => {
                        setIsSelectProOpen(true);
                      }}
                    />

                    <ButtonField
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="customer_id"
                      labelPlacement="left"
                      value={
                        details.customer_contact_id ||
                        details?.customer_contact_id !== 0
                          ? HTMLEntities.decode(
                              sanitizeString(details?.customer_name)
                            )
                          : HTMLEntities.decode(
                              sanitizeString(details?.customer_name)
                            )
                      }
                      headerTooltip={`Customer: ${
                        details.customer_contact_id ||
                        details?.customer_contact_id !== 0
                          ? HTMLEntities.decode(
                              sanitizeString(details?.customer_name)
                            )
                          : HTMLEntities.decode(
                              sanitizeString(details?.customer_name)
                            )
                      }`}
                      editInline={true}
                      iconView={true}
                      required={true}
                      placeholder={_t("Select Customer")}
                      buttonClassName="!text-sm font-medium"
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="!text-sm h-6 font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      readOnly={isReadOnly}
                      isDisabled={details?.is_disabled_project == "1"}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "customer_id"),
                        className: "right-6",
                        iconProps: {
                          className: "!w-4 !h-4",
                        },
                      }}
                      rightIcon={
                        <>
                          {selectedCustomer?.user_id &&
                          selectedCustomer?.display_name ? (
                            <div className="flex items-center gap-1">
                              <ContactDetailsButton
                                onClick={() => {
                                  setIsOpenContactDetails(true);
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                directoryId={
                                  selectedCustomer?.user_id?.toString() || ""
                                }
                                directoryTypeKey={
                                  selectedCustomer?.type_key || ""
                                }
                              />
                            </div>
                          ) : (
                            <></>
                          )}
                        </>
                      }
                      onClick={() => {
                        setIsSelecteCustomer(true);
                      }}
                    />

                    <div
                      className={`flex items-center gap-2 ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex gap-2 items-center">
                        <Dropdown
                          menu={{
                            items: invoiceDropdownStatus.statusList,
                            selectable: true,
                            selectedKeys: [activeStep.toString() as string],
                            onClick: (e) => {
                              onStepClick(e.key, "dropdown");
                            },
                          }}
                          disabled={isReadOnly}
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {invoiceDropdownStatus.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          stepStatusLoaing
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={stepStatusLoaing}
                          />
                        )}
                        {isPastDue && (
                          <Typography className="text-[#D2322D] bg-[#D2322D1D] whitespace-nowrap py-0.5 h-5 px-2.5 rounded text-xs">
                            {_t("Past Due")}
                          </Typography>
                        )}
                      </div>

                      <Tooltip
                        title={
                          isFocusedAgrField
                            ? inputValues?.custom_invoice_id?.trim()
                            : `${_t("Inv.")} #${
                                details?.projectPrefix || ""
                              }${inputValues?.custom_invoice_id?.trim()}`
                        }
                        placement="topLeft"
                      >
                        <div
                          className={`overflow-hidden ${
                            isReadOnly || !inputValues?.custom_invoice_id
                              ? "w-fit"
                              : "w-full max-w-[200px]"
                          }
                          `}
                        >
                          <InputField
                            placeholder={_t("Inv.") + " # "}
                            name="custom_invoice_id"
                            labelPlacement="left"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] !text-sm font-medium py-0"
                            readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate sm:block flex"
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            value={
                              isFocusedAgrField
                                ? HTMLEntities.decode(
                                    inputValues?.custom_invoice_id || ""
                                  )
                                : `${_t("Inv.")} #${
                                    details?.projectPrefix || ""
                                  }${HTMLEntities.decode(
                                    inputValues?.custom_invoice_id || ""
                                  )}`
                            }
                            editInline={true}
                            iconView={true}
                            readOnly={isReadOnly}
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "custom_invoice_id"
                            )}
                            maxLength={15}
                            onChange={handleInpOnChange}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "custom_invoice_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "custom_invoice_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            disabled={is_custom_invoice_id == 0}
                            onFocus={() => {
                              setIsFocusedAgrField(true);
                              handleChangeFieldStatus({
                                field: "custom_invoice_id",
                                status: "save",
                                action: "FOCUS",
                              });
                            }}
                            onBlur={(e) => {
                              let value = e?.target?.value
                                ?.replace(/\s+/g, " ")
                                ?.trim();
                              setIsFocusedAgrField(false);

                              if (value === "") {
                                notification.error({
                                  description: _t("Inv. # field is required."),
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_invoice_id: details?.custom_invoice_id,
                                });
                                return false;
                              }

                              if (value !== details?.custom_invoice_id) {
                                handleUpdateField({ custom_invoice_id: value });
                              } else {
                                handleChangeFieldStatus({
                                  field: "custom_invoice_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_invoice_id: details.custom_invoice_id
                                    ?.replace(/\s+/g, " ")
                                    ?.trim(),
                                });
                              }
                            }}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                <div className="w-auto flex-[0_0_auto]">
                  {(iVStatusVal.key === "invoice_open" ||
                    iVStatusVal.key === "invoice_on_hold" ||
                    iVStatusVal.key === "invoice_submitted" ||
                    iVStatusVal.key === "invoice_paid") && (
                    <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                      {ivStatList
                        ?.filter(
                          (item: ISubContractsStatusList) =>
                            item.show_in_progress_bar != 0
                        )
                        .map((item: ISubContractsStatusList, index: number) => {
                          const isActive =
                            selectedStatusInd != undefined &&
                            selectedStatusInd >= index;
                          return (
                            <li
                              key={index}
                              className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                                isActive
                                  ? "before:bg-primary-900"
                                  : "before:bg-[#ACAEAF]"
                              } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                            >
                              <ProgressBarHeader
                                option={item}
                                isActive={isActive || false}
                                onClick={() => {
                                  !isReadOnly &&
                                    onStepClick(item?.value || "", "progress");
                                }}
                              />
                            </li>
                          );
                        })}
                    </ul>
                  )}
                </div>
                <div className="xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:w-fit w-full">
                  <div className="flex justify-between">
                    <div className="flex gap-2.5">
                      {!window.ENV.PAGE_IS_IFRAME && (
                        <div
                          className="flex items-center cursor-pointer md:!hidden"
                          onClick={() => {
                            const params: Partial<IframeRouteParams> =
                              parseParamsFromURL(window?.location?.pathname);
                            if (params?.page && params?.id) {
                              navigate("/" + params?.page);
                            }
                          }}
                        >
                          <IconButton
                            htmlType="button"
                            variant="default"
                            className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                            icon="fa-regular fa-chevron-left"
                          />
                        </div>
                      )}
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>

                    <ul className="flex justify-end gap-2.5">
                      <li>
                        <ButtonWithTooltip
                          tooltipTitle={_t("Refresh")}
                          tooltipPlacement="top"
                          icon="fa-regular fa-arrow-rotate-right"
                          iconClassName="!text-primary-900 group-hover/buttonHover:!text-deep-orange-500"
                          className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          onClick={() => {
                            onReloadDetails();
                          }}
                        />
                      </li>
                      {!isReadOnly && !isDetailLoading && (
                        <li>
                          <InvoiceListAction
                            isKanbanDropDown={false}
                            isDetailDropDown={true}
                            paramsData={{
                              invoice_id: details?.invoice_id,
                              is_deleted: details?.is_deleted,
                              email_subject: details?.email_subject,
                              project_id: details?.project_id,
                              approval_type_key: details?.approval_type_key,
                              billed_to: details?.billed_to,
                              billed_to_contact: details?.billed_to_contact,
                              customer_id: details?.customer_id,
                              contact_id: details?.customer_contact_id,
                              due_date: details?.due_date,
                              due_balance:
                                Number(details?.total) -
                                Number(details?.payment_amount),
                              show_payment_link: details?.show_payment_link,
                              prefix_company_invoice_id:
                                details?.prefix_company_invoice_id,
                            }}
                            buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                            onActionComplete={() => {
                              dispatch(
                                fetchIVDetails({
                                  id: params?.id || "",
                                })
                              );
                              if (
                                window &&
                                window.ENV &&
                                window.ENV.PAGE_IS_IFRAME
                              ) {
                                parentPostMessage(
                                  sendMessageKeys?.modal_change,
                                  {
                                    open: false,
                                  }
                                );
                              } else {
                                dispatch(resetDash());
                                navigate(`${routes.MANAGE_INVOICE.url}`);
                              }
                            }}
                            tooltipcontent={_t("More")}
                          />
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {isSelectProOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProOpen}
          module_key={module_key}
          setOpen={setIsSelectProOpen}
          selectedProjects={
            selectedProject?.id && !!selectedProject?.project_name
              ? [selectedProject]
              : []
          }
          customer_id={details?.customer_id?.toString()}
          onProjectSelected={(data) => {
            setSelectedProject(data.length ? data[0] : null);
            handleUpdateField(
              {
                project_id: Number(data[0]?.id || ""),
              },
              data[0] as IIVUpdateFieldOtherData
            );
          }}
        />
      )}

      {isSelecteCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isSelecteCustomer}
          closeDrawer={() => {
            setIsSelecteCustomer(false);
          }}
          singleSelecte={true}
          options={[
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          setCustomer={(data) => {
            const contractorData =
              data?.length > 0 ? (data[0] as Partial<IDirectoryData>) : {};
            if (isEmpty(contractorData)) {
              notification.error({
                description: `${_t("Customer field is required.")}`,
              });
              return false;
            }
            setSelectedCustomer(contractorData);
            handleUpdateField(
              {
                customer_id: contractorData?.user_id?.toString() || "",
              },
              contractorData,
              "customer_id"
            );
          }}
          selectedCustomer={
            selectedCustomer?.user_id &&
            selectedCustomer?.user_id != 0 &&
            selectedCustomer?.display_name
              ? ([selectedCustomer] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          additionalContactDetails={1}
          projectId={details?.project_id}
        />
      )}
      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={selectedCustomer?.user_id || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isReadOnly}
          additional_contact_id={selectedCustomer?.contact_id}
        />
      )}
      <InvoiceSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
          "my_project",
        ]}
        contactId={0}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        projectId={details.project_id}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default DetailsTopBar;
