import { useMemo } from "react";
import { useTranslation } from "~/hook";
import { defaultConfig } from "~/data";
import { getGModuleByKey } from "~/zustand";
import { useAppDLSelector } from "~/modules/projectManagement/pages/dailyLogs/redux/store";

// atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { EquipmentLogsFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/equipmentLogsFieldRedirectionIcon";

// Other
import { sanitizeString } from "~/helpers/helper";

const EquipmentLogCard = () => {
  const { _t } = useTranslation();
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.equipment_log_module
  );
  const { isEquipmentTabLoading, equipmentData }: IDLEquipmentsInitialState =
    useAppDLSelector((state) => state.dailyLogeEuipments);
  const { equipmentLogs } = equipmentData;

  const equipmentLogTable = useMemo(() => {
    return (
      <StaticTable
        className="static-table"
        columnDefs={[
          {
            headerName: _t("Name"),
            field: "equipment_name",
            minWidth: 200,
            headerClass: "ag-header-left",
            cellClass: "ag-cell-left",
            resizable: true,
            suppressMovable: false,
            suppressMenu: true,
            cellRenderer: ({ data }: IEquipmentsLogsCellRenderer) => {
              const equipment_name = HTMLEntities.decode(
                sanitizeString(data.equipment_name)
              );
              return equipment_name ? (
                <Tooltip title={equipment_name}>
                  <Typography className="table-tooltip-text">
                    {equipment_name}
                  </Typography>
                </Tooltip>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: _t("Hours"),
            field: "e_hours_used",
            minWidth: 100,
            maxWidth: 100,
            headerClass: "ag-header-right",
            cellClass: "ag-cell-right",
            suppressMovable: false,
            suppressMenu: true,
            cellRenderer: ({ data }: IEquipmentsLogsCellRenderer) => {
              return data.e_hours_used ? (
                <Typography className="table-tooltip-text">
                  {data.e_hours_used}
                </Typography>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: _t("Operator"),
            field: "display_name",
            minWidth: 100,
            maxWidth: 100,
            suppressMovable: false,
            suppressMenu: true,
            cellStyle: { textAlign: "center" },
            headerClass: "ag-header-center",
            cellRenderer: ({ data }: IEquipmentsLogsCellRenderer) => {
              const { operator_id, display_name, operator_profile_image } =
                data;
              const displayName = HTMLEntities.decode(
                sanitizeString(display_name)
              );
              return operator_id && operator_id != 0 && displayName ? (
                <Tooltip title={displayName}>
                  <div className="w-fit mx-auto">
                    <AvatarProfile
                      user={{
                        image: operator_profile_image,
                        name: displayName,
                      }}
                      className="m-auto"
                      iconClassName="text-[11px] font-semibold"
                    />
                  </div>
                </Tooltip>
              ) : (
                <>-</>
              );
            },
          },
          {
            headerName: "",
            field: "",
            minWidth: 150,
            flex: 1,
            suppressMovable: false,
            suppressMenu: true,
          },
          {
            headerName: _t("Status"),
            field: "status",
            minWidth: 100,
            maxWidth: 100,
            suppressMovable: false,
            suppressMenu: true,
            cellStyle: { textAlign: "center" },
            headerClass: "ag-header-center",
            cellRenderer: ({ data }: IEquipmentsLogsCellRenderer) => {
              const statusName = data?.status_name;
              return statusName ? (
                <Tooltip title={statusName}>
                  <div className="text-center">
                    <Tag
                      color="#2235581d"
                      className="!text-primary-900 mx-auto text-13 type-badge common-tag"
                    >
                      {statusName}
                    </Tag>
                  </div>
                </Tooltip>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: "",
            field: "",
            maxWidth: 45,
            minWidth: 45,
            suppressMenu: true,
            cellRenderer: ({ data }: IEquipmentsLogsCellRenderer) => {
              return data.log_id ? (
                <div className="flex items-center gap-2 justify-center">
                  <EquipmentLogsFieldRedirectionIcon
                    equipmentLogsId={`${data.log_id}`}
                    iconClassName="!w-3.5 !h-3.5"
                  />
                </div>
              ) : (
                "-"
              );
            },
          },
        ]}
        rowData={equipmentLogs?.length ? equipmentLogs : []}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-contact.svg`}
          />
        )}
      />
    );
  }, [equipmentLogs]);
  return (
    <>
      <CrudCommonCard
        headerTitle={module?.plural_name}
        headerClassName="flex flex-col"
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#add1001a_0%,#7b920a1a_100%)]",
          id: "equipment_log_file_lines",
          colors: ["#ADD100", "#7B920A"],
        }}
        children={
          <div className="pt-2">
            {isEquipmentTabLoading ? (
              <Spin className="w-full h-10 flex items-center justify-center" />
            ) : (
              <div className="ag-theme-alpine">{equipmentLogTable}</div>
            )}
          </div>
        }
      />
    </>
  );
};

export default EquipmentLogCard;
