import { useCallback, useMemo, useRef, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputCurrencyField } from "~/shared/components/molecules/inputCurrencyField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { InputField } from "~/shared/components/molecules/inputField";
// Fortawesome
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { useAppProSelector } from "../../../redux/store";
import dayjs, { Dayjs } from "dayjs";
import {
  filterOptionBySubstring,
  getStatusForField,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { Number, sanitizeString } from "~/helpers/helper";
import { onKeyDownNumber } from "~/helpers/key-down.helper";
import ContactDetailForProject from "../common/ContactDetailForProject";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { useModuleAccess } from "../../../hook/useModuleAcces";

import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

const AdditionalDetails = () => {
  const { _t } = useTranslation();
  const [view, setView] = useState<boolean>(false);
  const { details } = useAppProSelector((state) => state.proDetails);
  const { taxDetailsList } = useAppProSelector((state) => state.taxDetails);
  const [isOpenInoicedToDrawer, setIsOpenInoicedToDrawer] =
    useState<boolean>(false);
  const wipNotesRef = useRef<HTMLDivElement | null>(null);
  const [focusedField, setFocusedField] = useState<Record<string, boolean>>({});

  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();

  const {
    hasEnoughAccessofProjectFinanceTabModule,
    projectContractAmountModuleAccess,
    projectBudgetAmountModuleAccess,
  } = useModuleAccess();

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { tax_format } = appSettings || {};

  const dateFormatToConsider = useMemo(() => {
    return CFConfig.day_js_date_format;
  }, [CFConfig.day_js_date_format]);

  const {
    handleUpdateField,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    onFocusUpdateFieldStatus,
    updateInputFieldOnBlur,
    onBlurUpdateFieldStatus,
    onChangeInputField,
    loadingStatus,
    isReadOnly,
    inputVals,
    setInputVals,
    updateDataInStore,
  } = useProjectDetail();

  const taxRateOptions = useMemo(() => {
    return (
      taxDetailsList?.map((t) => ({
        label: `${HTMLEntities.decode(sanitizeString(t.tax_name))} (${Number(
          t.tax_rate
        )}%)`,
        value: t.tax_id?.toString(),
      })) || []
    );
  }, [taxDetailsList]);

  const selectedInvoicedTo = useMemo(() => {
    return Number(details.billed_to ?? 0) && details?.billed_to_name
      ? ([
          {
            user_id: Number(details?.billed_to),
            display_name: details?.billed_to_name,
            contact_id: details?.billed_to_contact,
            type: Number(details.billed_to_type),
            type_key: getDirectaryKeyById(
              Number(details.billed_to_type) === 1
                ? 2
                : Number(details.billed_to_type),
              undefined
            ),
          },
        ] as TselectedContactSendMail[])
      : [];
  }, [
    details.billed_to,
    details?.billed_to_name,
    details?.billed_to_contact,
    details.billed_to_type,
  ]);

  const getDirId = (type_key: string) => {
    return getDirectaryIdByKey(type_key as CustomerTabs, undefined);
  };

  const getValidValue = (inputValue: string) => {
    // Allow only numbers
    if (/^\d*$/.test(inputValue)) {
      let numericValue = Number(inputValue);

      // Restrict value to max 100
      if (numericValue > 100) return null;

      // Prevent extra digits beyond 100
      if (inputValue.length > 3) return null;
      return inputValue;
    }
    return null;
  };

  const onStartDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const endDate = dayjs(details.end_date || "", dateFormatToConsider).endOf(
        "day"
      );
      const inputDate = dayjs(date).startOf("day");
      const completionDate = dayjs(
        details.completion_date || "",
        dateFormatToConsider
      ).endOf("day");

      const isValidOverEndDate = !(
        endDate?.isValid() &&
        !(inputDate?.isSame(endDate) || inputDate?.isBefore(endDate))
      );
      const isValidOverCompletionDate = !(
        completionDate?.isValid() &&
        !(
          inputDate?.isSame(completionDate) ||
          inputDate?.isBefore(completionDate)
        )
      );

      if (!date) {
        handleUpdateField({
          data_to_send_in_api: {
            start_date: "",
          },
        });
      } else if (!isValidOverEndDate) {
        const errorMessage =
          "Start date should be less than or equal to end date.";
        notification.error({
          description: errorMessage,
        });
      } else if (!isValidOverCompletionDate) {
        const errorMessage =
          "Start date should be less than or equal to completion date.";
        notification.error({
          description: errorMessage,
        });
      } else {
        const newDateToSendInApi = date ? date.format("YYYY-MM-DD") : "";
        const newDateToUpdateInStore = date
          ? date.format(dateFormatToConsider)
          : "";
        handleUpdateField({
          data_to_send_in_api: {
            start_date: newDateToSendInApi,
          },
          data_to_update_in_store: {
            start_date: newDateToUpdateInStore,
          },
        });
      }
    }
  };

  const onEndDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const startDate = dayjs(
        details.start_date || "",
        dateFormatToConsider
      ).startOf("day");
      const inputDate = dayjs(date).endOf("day");

      const isValidDate = !(
        startDate?.isValid() &&
        !(inputDate?.isSame(startDate) || inputDate?.isAfter(startDate))
      );

      if (!date) {
        handleUpdateField({
          data_to_send_in_api: {
            end_date: "",
          },
        });
      } else if (!isValidDate) {
        const errorMessage =
          "End date should be greater than or equal to start date.";
        notification.error({
          description: errorMessage,
        });
      } else {
        const newDateToSendInApi = date ? date.format("YYYY-MM-DD") : "";
        const newDateToUpdateInStore = date
          ? date.format(dateFormatToConsider)
          : "";
        handleUpdateField({
          data_to_send_in_api: {
            end_date: newDateToSendInApi,
          },
          data_to_update_in_store: {
            end_date: newDateToUpdateInStore,
          },
        });
      }
    }
  };

  const onCompletionDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const startDate = dayjs(
        details.start_date || "",
        dateFormatToConsider
      ).startOf("day");
      const warrantyStartDate = dayjs(
        details.warranty_start_date || "",
        dateFormatToConsider
      ).endOf("day");
      const inputDate = dayjs(date).startOf("day");

      const isValidOverStartDate = !(
        startDate?.isValid() &&
        !(inputDate?.isSame(startDate) || inputDate?.isAfter(startDate))
      );
      const isValidOverWarrantyStartDate = !(
        warrantyStartDate?.isValid() &&
        !(
          inputDate?.isSame(warrantyStartDate) ||
          inputDate?.isBefore(warrantyStartDate)
        )
      );
      const isPastDate = inputDate.isBefore(dayjs().startOf("day"));

      if (!date) {
        handleUpdateField({
          data_to_send_in_api: {
            completion_date: null,
          },
        });
      } else if (!isValidOverStartDate) {
        const errorMessage =
          "Completion date should be greater than or equal to start date.";
        notification.error({
          description: errorMessage,
        });
      } else if (!isValidOverWarrantyStartDate) {
        const errorMessage =
          "Completion date should be less than or equal to Warranty start date.";
        notification.error({
          description: errorMessage,
        });
      } else {
        const newDateToSendInApi = date ? date.format("YYYY-MM-DD") : "";
        const newDateToUpdateInStore = date
          ? date.format(dateFormatToConsider)
          : "";

        handleUpdateField({
          data_to_send_in_api: {
            completion_date: newDateToSendInApi,
            ...(isPastDate
              ? {
                  project_status: "completed",
                }
              : {}),
          },
          data_to_update_in_store: {
            completion_date: newDateToUpdateInStore,
            ...(isPastDate
              ? {
                  project_status_name: "Completed",
                }
              : {}),
          },
        });
      }
    }
  };

  const onWarrantyDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const completionDate = dayjs(
        details.completion_date || "",
        dateFormatToConsider
      ).startOf("day");
      const endDate = dayjs(
        details.end_date || "",
        dateFormatToConsider
      ).startOf("day");
      const startDate = dayjs(
        details.start_date || "",
        dateFormatToConsider
      ).startOf("day");
      const inputDate = dayjs(date).endOf("day");
      const isValidDate = !(
        completionDate?.isValid() &&
        !(
          inputDate?.isSame(completionDate) ||
          inputDate?.isAfter(completionDate)
        )
      );

      const isValidOverEndDate = !(
        endDate?.isValid() &&
        !(inputDate?.isSame(endDate) || inputDate?.isAfter(endDate))
      );

      const isValidOverStartDate = !(
        startDate?.isValid() &&
        !(inputDate?.isSame(startDate) || inputDate?.isAfter(startDate))
      );

      if (!date) {
        handleUpdateField({
          data_to_send_in_api: {
            warranty_start_date: "",
          },
        });
      } else if (!isValidDate) {
        const errorMessage =
          "Warranty start date should be greater than or equal to completion date.";
        notification.error({
          description: errorMessage,
        });
      } else if (!isValidOverEndDate) {
        const errorMessage =
          "Warranty start date should be greater than or equal to end date.";
        notification.error({
          description: errorMessage,
        });
      } else if (!isValidOverStartDate) {
        const errorMessage =
          "Warranty start date should be greater than or equal to start date.";
        notification.error({
          description: errorMessage,
        });
      } else {
        const newDateToSendInApi = date ? date.format("YYYY-MM-DD") : "";
        const newDateToUpdateInStore = date
          ? date.format(dateFormatToConsider)
          : "";

        handleUpdateField({
          data_to_send_in_api: {
            warranty_start_date: newDateToSendInApi,
          },
          data_to_update_in_store: {
            warranty_start_date: newDateToUpdateInStore,
          },
        });
      }
    }
  };

  const onNoticeDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const endDate = dayjs(
        details.end_date || "",
        dateFormatToConsider
      ).startOf("day");
      const inputDate = dayjs(date).endOf("day");

      const isValidDate = !(
        endDate?.isValid() && !inputDate?.isBefore(endDate)
      );

      if (!date) {
        handleUpdateField({
          data_to_send_in_api: {
            notice_to_proceed: null,
          },
        });
      } else if (!isValidDate) {
        const errorMessage =
          "Notice to proceed date should be less than to project end date.";
          
        notification.error({
          key: "notice-to-proceed-validation",
          description: errorMessage,
        });
      } else {
        const newDateToSendInApi = date ? date.format("YYYY-MM-DD") : "";
        const newDateToUpdateInStore = date
          ? date.format(dateFormatToConsider)
          : "";

        handleUpdateField({
          data_to_send_in_api: {
            notice_to_proceed: newDateToSendInApi,
          },
          data_to_update_in_store: {
            notice_to_proceed: newDateToUpdateInStore,
          },
        });
      }
    }
  };

  const getRightIconForDirectory = useCallback(
    (id_key: string, dir_type_key: string, contact_id_key?: string) => {
      const user_id = details?.[id_key as keyof IProjectDetails] as number;

      return (
        <>
          {Number(user_id ?? "0") ? (
            <ContactDetailForProject
              data={
                (details as unknown as Record<
                  string,
                  string | number | null | undefined
                >) ?? {}
              }
              id_key={id_key}
              contact_id_key={contact_id_key ?? ""}
              dir_type_key={dir_type_key}
              isReadOnly={isReadOnly}
            />
          ) : (
            <></>
          )}
        </>
      );
    },
    [details, isReadOnly]
  );

  const handleInvoicedToSelection = (
    contacts: Array<Partial<TselectedContactSendMail>>
  ) => {
    let data = contacts[0];
    let updatedContact = {
      data_to_update_in_store: {},
      data_to_send_in_api: {},
    };

    if (data?.user_id !== undefined) {
      if (data?.user_id?.toString() != details.billed_to?.toString()) {
        const invoiced_to_by_type_key =
          data.type_key !== "contact"
            ? data.type_key || ""
            : data.parent_type_key || "";

        updatedContact = {
          data_to_update_in_store: {
            billed_to_company_name: data?.company_name,
            billed_to_name: data?.display_name,
            billed_to_type: getDirId(invoiced_to_by_type_key as string),
          },
          data_to_send_in_api: {
            billed_to: data?.user_id,
            billed_to_contact:
              data?.contact_id !== "" ? Number(data.contact_id ?? "") : null,
          },
        };
      }
    } else {
      updatedContact = {
        data_to_update_in_store: {
          billed_to_company_name: "",
          billed_to_name: null,
          billed_to_type: "",
        },
        data_to_send_in_api: {
          billed_to: null,
          billed_to_contact: null,
        },
      };
    }

    if (Object.keys(updatedContact.data_to_send_in_api).length) {
      handleUpdateField(updatedContact);
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Additional Details")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-memo-circle-info",
          containerClassName:
            "bg-[linear-gradient(180deg,#FAC4501a_0%,#F8A9001a_100%)]",
          id: "additional_detail_icon",
          colors: ["#FAC450", "#F8A900"],
        }}
        headerRightButton={
          <Button
            className="!px-2 h-6 !py-1 !bg-[#EBF1F9] relative flex gap-1 items-center min-w-[92px] dark:!bg-dark-900 dark:before:!bg-dark:900 !border-0"
            onClick={() => setView((prev) => !prev)}
          >
            <FontAwesomeIcon
              className="w-3.5 h-3.5 text-primary-900 dark:text-[#dcdcdd]"
              icon="fa-solid fa-eye"
            />
            <Typography className="text-xs font-medium text-primary-900 dark:text-white/90">
              {view ? _t("View Less") : _t("View All")}
            </Typography>
          </Button>
        }
        children={
          <div className="pt-2">
            <ul className="w-full grid 2xl:grid-cols-2 lg:grid-cols-1 md:grid-cols-2 grid-cols-1 gap-1">
              <li>
                <DatePickerField
                  label={_t("Start Date")}
                  labelPlacement="left"
                  labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                  placeholder={_t("Select Start Date")}
                  editInline={true}
                  iconView={true}
                  id="start_date"
                  fixStatus={getStatusForField(loadingStatus, "start_date")}
                  value={
                    displayDateFormat(
                      details.start_date?.toString().trim(),
                      dateFormatToConsider
                    ) ?? null
                  }
                  onChange={(date) => {
                    onStartDateChange(date);
                  }}
                  format={dateFormatToConsider}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  onMouseLeave={() => {
                    onMouseLeaveUpdateFieldStatus({ field: "start_date" });
                  }}
                />
              </li>
              <li>
                <SelectField
                  label={
                    _t(`${tax_format === "tax" ? "Tax" : "GST"} Rate`) + " (%)"
                  }
                  labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                  placeholder={_t(
                    `Select ${tax_format === "tax" ? "Tax" : "GST"}`
                  )}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  showSearch
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  options={taxRateOptions}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "default_tax_rate_id"
                  )}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  value={
                    details?.default_tax_rate_id
                      ? details.default_tax_rate_id.toString()
                      : undefined
                  }
                  onSelect={(e) => {
                    const value = e?.toString();

                    if (
                      value &&
                      value !== details?.default_tax_rate_id?.toString()
                    ) {
                      const label = taxRateOptions.find(
                        (o) => o.value === value
                      )?.label;

                      handleUpdateField({
                        data_to_send_in_api: {
                          default_tax_rate_id: value,
                        },
                        data_to_update_in_store: {
                          default_tax_name: label,
                        },
                      });
                    }
                  }}
                />
              </li>
              <li>
                <DatePickerField
                  label={_t("End Date")}
                  labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                  labelPlacement="left"
                  placeholder={_t("Select End Date")}
                  editInline={true}
                  iconView={true}
                  id="end_date"
                  fixStatus={getStatusForField(loadingStatus, "end_date")}
                  value={
                    displayDateFormat(
                      details.end_date?.toString().trim(),
                      dateFormatToConsider
                    ) ?? null
                  }
                  onChange={(date) => {
                    onEndDateChange(date);
                  }}
                  format={dateFormatToConsider}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  onMouseLeave={() => {
                    onMouseLeaveUpdateFieldStatus({ field: "end_date" });
                  }}
                />
              </li>
              {projectContractAmountModuleAccess && (
                <li>
                  <InputNumberField
                    label={_t("Contract Amount")}
                    name="amount"
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    className={
                      isReadOnly ||
                      Number(details.sov_items) !== 0 ||
                      Number(details.approved_estimate_item_total) !== 0
                        ? "!cursor-not-allowed !text-primary-900 placeholder:!text-[#bdbdbd]"
                        : ""
                    }
                    placeholder="0.00"
                    prefix={inputFormatter().currency_symbol}
                    value={
                      Number(inputVals?.original_contract_amount) !== 0
                        ? Math.floor(
                            Number(inputVals?.original_contract_amount)
                          ) / 100
                        : ""
                    }
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    disabled={
                      isReadOnly ||
                      Number(details.sov_items) !== 0 ||
                      Number(details.approved_estimate_item_total) !== 0
                    }
                    readOnlyClassName="!cursor-not-allowed"
                    currencySymbol={true}
                    onChange={(value) => {
                      onChangeInputField({
                        value: value?.toString() || "",
                        field: "original_contract_amount",
                      });
                    }}
                    fixStatus={getStatusForField(
                      loadingStatus,
                      "contract_amount"
                    )}
                    onMouseEnter={() => {
                      onMouseEnterUpdateFieldStatus({
                        field: "contract_amount",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      onMouseLeaveUpdateFieldStatus({
                        field: "contract_amount",
                      });
                    }}
                    onFocus={() => {
                      onFocusUpdateFieldStatus({
                        field: "contract_amount",
                      });
                      setFocusedField((prev) => ({
                        ...prev,
                        contract_amount: true,
                      }));
                    }}
                    parser={(value) => {
                      if (!value) return "";
                      const inputValue = unformatted(value.toString());
                      return inputValue;
                    }}
                    formatter={(value, info) => {
                      const inputValue = info.input.trim();

                      const valueToFormat =
                        inputValue !== "0" && inputValue.length > 0
                          ? unformatted(inputValue)
                          : String(value);

                      return focusedField.contract_amount
                        ? inputFormatter(valueToFormat).value
                        : inputFormatter(Number(value)?.toFixed(2)).value;
                    }}
                    onBlur={(e) => {
                      const inputValue = e.target.value.trim();
                      const formattedValue = inputValue
                        ? unformatted(inputValue)
                        : "0.00";

                      const currentContractAmount =
                        Number(details?.original_contract_amount) / 100;

                      const isModified =
                        Number(formattedValue) !==
                        Number(currentContractAmount);

                      if (isModified) {
                        handleUpdateField({
                          data_to_send_in_api: {
                            contract_amount: Number(formattedValue) * 100,
                          },
                          data_to_update_in_store: {
                            original_contract_amount:
                              Number(formattedValue) * 100,
                          },
                        });
                      } else {
                        onBlurUpdateFieldStatus({ field: "contract_amount" });
                        setInputVals((prev) => ({
                          ...prev,
                          original_contract_amount:
                            currentContractAmount * 100 || "",
                        }));
                      }
                      setFocusedField((prev) => ({
                        ...prev,
                        contract_amount: false,
                      }));
                    }}
                    onKeyDown={(event) =>
                      onKeyDownCurrency(event, {
                        integerDigits: 10,
                        decimalDigits: 2,
                        unformatted,
                        decimalSeparator: inputFormatter().decimal_separator,
                      })
                    }
                  />
                </li>
              )}
              {(view === true || details.completion_date) && (
                <li>
                  <DatePickerField
                    label={_t("Completion Date")}
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    labelPlacement="left"
                    placeholder={_t("Select Completion Date")}
                    editInline={true}
                    iconView={true}
                    id="completion_date"
                    fixStatus={getStatusForField(
                      loadingStatus,
                      "completion_date"
                    )}
                    value={
                      displayDateFormat(
                        details.completion_date?.toString().trim(),
                        dateFormatToConsider
                      ) ?? null
                    }
                    onChange={(date) => {
                      onCompletionDateChange(date);
                    }}
                    format={dateFormatToConsider}
                    readOnly={isReadOnly}
                    disabled={isReadOnly}
                    onMouseLeave={() => {
                      onMouseLeaveUpdateFieldStatus({
                        field: "completion_date",
                      });
                    }}
                  />
                </li>
              )}
              {projectBudgetAmountModuleAccess &&
              !(
                Number(details.sov_items) !== 0 ||
                Number(details.approved_estimate_item_total) !== 0
              ) ? (
                <li>
                  <InputNumberField
                    label={_t("Budget Amount")}
                    name="budget_amount"
                    className={`${
                      isReadOnly ||
                      Number(details.sov_items) !== 0 ||
                      Number(details.approved_estimate_item_total) / 100 !== 0
                        ? "!text-primary-900 placeholder:!text-[#bdbdbd]"
                        : ""
                    }`}
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    placeholder="0.00"
                    value={
                      Number(inputVals.budget_amount) !== 0
                        ? Number(inputVals.budget_amount) / 100
                        : ""
                    }
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    disabled={
                      isReadOnly ||
                      Number(details.sov_items) !== 0 ||
                      Number(details.approved_estimate_item_total) / 100 !== 0
                    }
                    currencySymbol={true}
                    prefix={inputFormatter().currency_symbol}
                    onChange={(value) => {
                      onChangeInputField({
                        value: value?.toString() || "",
                        field: "budget_amount",
                      });
                    }}
                    fixStatus={getStatusForField(
                      loadingStatus,
                      "budget_amount"
                    )}
                    onMouseEnter={() => {
                      onMouseEnterUpdateFieldStatus({
                        field: "budget_amount",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      onMouseLeaveUpdateFieldStatus({
                        field: "budget_amount",
                      });
                    }}
                    onFocus={() => {
                      onFocusUpdateFieldStatus({
                        field: "budget_amount",
                      });
                      setFocusedField((prev) => ({
                        ...prev,
                        budget_amount: true,
                      }));
                    }}
                    parser={(value) => {
                      if (!value) return "";
                      const inputValue = unformatted(value.toString());
                      return inputValue;
                    }}
                    formatter={(value, info) => {
                      const inputValue = info.input.trim();

                      const valueToFormat =
                        inputValue !== "0" && inputValue.length > 0
                          ? unformatted(inputValue)
                          : String(value);

                      return focusedField.budget_amount
                        ? inputFormatter(valueToFormat).value
                        : inputFormatter(Number(value)?.toFixed(2)).value;
                    }}
                    onBlur={(e) => {
                      const inputValue = e.target.value.trim();
                      const formattedValue = inputValue
                        ? unformatted(inputValue)
                        : "0.00";

                      const currentBudgetAmount =
                        Number(details?.budget_amount) / 100;

                      const isModified =
                        Number(formattedValue) !== Number(currentBudgetAmount);

                      if (isModified) {
                        handleUpdateField({
                          data_to_send_in_api: {
                            budget_amount: formattedValue?.toString(),
                          },
                          data_to_update_in_store: {
                            budget_amount: (
                              Number(formattedValue) * 100
                            )?.toString(),
                          },
                        });
                      } else {
                        onBlurUpdateFieldStatus({ field: "budget_amount" });
                        setInputVals((prev) => ({
                          ...prev,
                          budget_amount: currentBudgetAmount * 100 || "",
                        }));
                      }
                      setFocusedField((prev) => ({
                        ...prev,
                        budget_amount: false,
                      }));
                    }}
                    onKeyDown={(event) =>
                      onKeyDownCurrency(event, {
                        integerDigits: 10,
                        decimalDigits: 2,
                        unformatted,
                        decimalSeparator: inputFormatter().decimal_separator,
                      })
                    }
                  />
                </li>
              ) : (
                <></>
              )}
              <li>
                <DatePickerField
                  label={_t("Warranty Start Date")}
                  labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                  labelPlacement="left"
                  placeholder={_t("Select Warranty Start Date")}
                  editInline={true}
                  iconView={true}
                  id="warranty_start_date"
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "warranty_start_date"
                  )}
                  value={
                    displayDateFormat(
                      details.warranty_start_date?.toString().trim(),
                      dateFormatToConsider
                    ) ?? null
                  }
                  onChange={(date) => {
                    onWarrantyDateChange(date);
                  }}
                  format={dateFormatToConsider}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  onMouseLeave={() => {
                    onMouseLeaveUpdateFieldStatus({
                      field: "warranty_start_date",
                    });
                  }}
                />
              </li>
              {/* {view === true && (
                <> */}
              {Number(details?.contract_amount_held) &&
              Number(details?.original_retention) ? (
                <li>
                  <InputField
                    label={_t("Original Retention") + " %"}
                    placeholder="0"
                    name="retention"
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    labelPlacement="left"
                    editInline={false}
                    iconView={false}
                    disabled={true}
                    value={
                      Number(details?.original_retention) !== 0
                        ? Number(details?.original_retention).toFixed(2)
                        : ""
                    }
                    onChange={() => {}}
                  />
                </li>
              ) : (
                <></>
              )}
              {/* </>
              )} */}
              {(Number(details.retention) || view === true) && (
                <li>
                  <InputField
                    label={_t("Retention") + " %"}
                    placeholder="0"
                    name="retention"
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    readOnly={isReadOnly}
                    value={
                      Number(inputVals?.retention) == 0
                        ? ""
                        : inputVals?.retention || ""
                    }
                    fixStatus={getStatusForField(loadingStatus, "retention")}
                    onMouseEnter={() => {
                      onMouseEnterUpdateFieldStatus({
                        field: "retention",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      onMouseLeaveUpdateFieldStatus({
                        field: "retention",
                      });
                    }}
                    onFocus={() =>
                      onFocusUpdateFieldStatus({
                        field: "retention",
                      })
                    }
                    onChange={(e) => {
                      const newVal = e.target.value;

                      // Allow empty input to clear the field
                      if (newVal === "") {
                        onChangeInputField({ field: "retention", value: "" });
                        return;
                      }

                      if (newVal.startsWith(".")) return;

                      // Allow only digits and a single decimal point
                      if (/^\d*\.?\d*$/.test(newVal)) {
                        if (newVal === ".") {
                          onChangeInputField({
                            field: "retention",
                            value: newVal,
                          });
                          return;
                        }

                        const numericValue = Number(newVal);

                        // Restrict value to max 100
                        if (numericValue > 100) return;

                        // Prevent more than one digit after the decimal point
                        const decimalPart = newVal.split(".")[1];
                        if (decimalPart && decimalPart.length > 1) return;

                        onChangeInputField({
                          field: "retention",
                          value: newVal,
                        });
                      }
                    }}
                    onBlur={(e) => {
                      const value = e?.target?.value.trim();
                      if (
                        details.retention === null &&
                        Boolean(value) === Boolean(details.retention)
                      ) {
                        return;
                      } else {
                        if (Number(value) === Number(details.retention)) {
                          onBlurUpdateFieldStatus({
                            field: "retention",
                          });
                          return;
                        }
                      }

                      updateInputFieldOnBlur({
                        field: "retention",
                        value: Number(value) || "",
                      });
                    }}
                  />
                </li>
              )}
              {(view === true || details.notice_to_proceed) && (
                <>
                  <li>
                    <DatePickerField
                      label={_t("Notice to Proceed")}
                      labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                      labelPlacement="left"
                      placeholder={_t("Select Notice to Proceed Date")}
                      editInline={true}
                      iconView={true}
                      id="notice_to_proceed"
                      fixStatus={getStatusForField(
                        loadingStatus,
                        "notice_to_proceed"
                      )}
                      value={
                        displayDateFormat(
                          details.notice_to_proceed?.toString().trim(),
                          dateFormatToConsider
                        ) ?? null
                      }
                      onChange={(date) => {
                        onNoticeDateChange(date);
                      }}
                      format={dateFormatToConsider}
                      readOnly={isReadOnly}
                      disabled={isReadOnly}
                      onMouseLeave={() => {
                        onMouseLeaveUpdateFieldStatus({
                          field: "notice_to_proceed",
                        });
                      }}
                    />
                  </li>
                </>
              )}
              {hasEnoughAccessofProjectFinanceTabModule &&
              (view === true ||
                (details.contract_amount_held &&
                  Number(details.contract_amount_held))) ? (
                <li>
                  <InputCurrencyField
                    label={_t("Held")}
                    name="amount"
                    placeholder="##"
                    labelPlacement="left"
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    readOnly={isReadOnly}
                    editInline={false}
                    fieldClassName="!px-1.5"
                    iconView={true}
                    value={
                      inputVals.contract_amount_held
                        ? (
                            Number(inputVals.contract_amount_held) / 100
                          ).toFixed(2)
                        : ""
                    }
                    onChange={() => {}}
                    disabled={true}
                    className="!cursor-not-allowed"
                  />
                </li>
              ) : (
                <></>
              )}
              {(view === true || inputVals?.customer_contract) && (
                <>
                  <li>
                    <InputField
                      label={_t("Customer Contract") + " #"}
                      placeholder={_t("Customer Contract") + " #"}
                      name="customer_contract"
                      labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      readOnly={isReadOnly}
                      maxLength={21}
                      value={HTMLEntities.decode(
                        sanitizeString(inputVals?.customer_contract || "")
                      )}
                      fixStatus={getStatusForField(
                        loadingStatus,
                        "customer_contract"
                      )}
                      onMouseEnter={() => {
                        onMouseEnterUpdateFieldStatus({
                          field: "customer_contract",
                        });
                      }}
                      onMouseLeaveDiv={() => {
                        onMouseLeaveUpdateFieldStatus({
                          field: "customer_contract",
                        });
                      }}
                      onFocus={() =>
                        onFocusUpdateFieldStatus({
                          field: "customer_contract",
                        })
                      }
                      onChange={(e) => {
                        const newVal = e.target.value;

                        onChangeInputField({
                          value: newVal.trimStart(),
                          field: "customer_contract",
                        });
                      }}
                      onBlur={(e) => {
                        const value = e?.target?.value.trim();
                        if (
                          details.customer_contract === null &&
                          Boolean(value) === Boolean(details.customer_contract)
                        ) {
                          onBlurUpdateFieldStatus({
                            field: "customer_contract",
                          });
                          return;
                        }

                        updateInputFieldOnBlur({
                          field: "customer_contract",
                          value,
                        });
                      }}
                    />
                  </li>
                </>
              )}
              {view === true || details.billed_to ? (
                <>
                  <li className="overflow-hidden">
                    <ButtonField
                      label={_t("Invoiced To")}
                      labelProps={{
                        labelClass: "shrink-0 sm:w-[150px] sm:max-w-[150px]",
                      }}
                      name="billed_to"
                      placeholder={_t("If Not Customer")}
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      readOnly={isReadOnly}
                      disabled={isReadOnly}
                      value={HTMLEntities.decode(
                        sanitizeString(details.billed_to_name ?? "")
                      )}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "billed_to"),
                      }}
                      rightIcon={getRightIconForDirectory(
                        "billed_to",
                        "billed_to_type",
                        "billed_to_contact"
                      )}
                      onClick={() => setIsOpenInoicedToDrawer(true)}
                    />
                  </li>
                </>
              ) : (
                <></>
              )}
              {view === true ||
              details.wip_progress ||
              Number(details.progress) * 100 ? (
                <li>
                  <InputField
                    label={_t("Work In Progress") + " %"}
                    placeholder="0"
                    className={
                      isReadOnly || Number(details.progress) * 100 > 0
                        ? "!cursor-not-allowed"
                        : ""
                    }
                    name="wip_progress"
                    labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    disabled={isReadOnly || Number(details.progress) * 100 > 0}
                    readOnly={isReadOnly}
                    value={
                      Number(details.progress || 0) * 100 > 0
                        ? Math.round(
                            Number(inputVals?.progress) * 100
                          ).toString()
                        : Math.round(Number(inputVals?.wip_progress)) || ""
                    }
                    fixStatus={getStatusForField(loadingStatus, "wip_progress")}
                    onMouseEnter={() => {
                      onMouseEnterUpdateFieldStatus({
                        field: "wip_progress",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      onMouseLeaveUpdateFieldStatus({
                        field: "wip_progress",
                      });
                    }}
                    onFocus={() =>
                      onFocusUpdateFieldStatus({
                        field: "wip_progress",
                      })
                    }
                    onChange={(e) => {
                      const newVal = e.target.value;
                      let inputValue = getValidValue(newVal);

                      if (!newVal || inputValue) {
                        onChangeInputField({
                          field: "wip_progress",
                          value: inputValue || "",
                        });
                      }
                    }}
                    onBlur={(e) => {
                      const value = e?.target?.value.trim();
                      if (
                        details.wip_progress === null &&
                        Boolean(value) === Boolean(details.wip_progress)
                      ) {
                        onBlurUpdateFieldStatus({
                          field: "wip_progress",
                        });
                        return;
                      }

                      updateInputFieldOnBlur({
                        field: "wip_progress",
                        value: value,
                      });
                    }}
                  />
                </li>
              ) : (
                <></>
              )}
              {(view === true || inputVals?.wip_notes) && (
                <>
                  <li className="2xl:col-span-2 lg:col-span-1 md:col-span-2 col-span-1">
                    <TextAreaField
                      ref={wipNotesRef}
                      label={_t("WIP Note")}
                      labelClass="shrink-0 sm:w-[150px] sm:max-w-[150px]"
                      placeholder={_t("Work In Progress Notes")}
                      labelPlacement="left"
                      name="wip_notes"
                      editInline={true}
                      iconView={true}
                      readOnly={isReadOnly}
                      value={HTMLEntities.decode(
                        sanitizeString(inputVals?.wip_notes || "")
                      )}
                      onClickStsIcon={() => {
                        wipNotesRef.current?.focus();
                      }}
                      fixStatus={getStatusForField(loadingStatus, "wip_notes")}
                      onMouseEnter={() => {
                        onMouseEnterUpdateFieldStatus({
                          field: "wip_notes",
                        });
                      }}
                      onMouseLeaveDiv={() => {
                        onMouseLeaveUpdateFieldStatus({
                          field: "wip_notes",
                        });
                      }}
                      onFocus={() =>
                        onFocusUpdateFieldStatus({
                          field: "wip_notes",
                        })
                      }
                      onChange={(e) => {
                        const newVal = e.target.value;

                        onChangeInputField({
                          value: newVal.trimStart(),
                          field: "wip_notes",
                        });
                      }}
                      onBlur={(e) => {
                        const value = e?.target?.value.trim();
                        if (
                          details.wip_notes === null &&
                          Boolean(value) === Boolean(details.wip_notes)
                        ) {
                          onBlurUpdateFieldStatus({
                            field: "wip_notes",
                          });
                          return;
                        }

                        updateInputFieldOnBlur({
                          field: "wip_notes",
                          value,
                        });
                      }}
                    />
                  </li>
                </>
              )}
            </ul>
          </div>
        }
      />

      {isOpenInoicedToDrawer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenInoicedToDrawer}
          closeDrawer={() => setIsOpenInoicedToDrawer(false)}
          singleSelecte={true}
          setCustomer={(data) => {
            handleInvoicedToSelection(data);
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
          ]}
          selectedCustomer={selectedInvoicedTo}
          groupCheckBox={true}
          projectId={Number(details?.project_id)}
          additionalContactDetails={1}
        />
      )}
    </>
  );
};

export default AdditionalDetails;
