// Hook
import { useTranslation } from "~/hook";
// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import SelectCostCode from "~/shared/components/molecules/selectCostCode/SelectCostCode";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useEffect, useMemo, useRef, useState } from "react";
import { IBudgetItemProps } from "./type";
import { type RadioChangeEvent } from "antd";
import isEmpty from "lodash/isEmpty";
import { Number, sanitizeString } from "~/helpers/helper";
import { getItemTypeIcon } from "~/shared/utils/helper/common";
import { useAppProSelector } from "../../../../redux/store";
import { Tooltip } from "~/shared/components/atoms/tooltip";

const BudgetItem = ({
  budgetItem,
  setBudgetItem,
  isViewOnly = false,
  formData,
}: IBudgetItemProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const [markup, setMarkup] = useState<string>(
    formData.is_markup_percentage === 1 ? "markup_percent" : "markup_dolar"
  );
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
    }
  };
  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [total, setTotal] = useState<string | number>("");
  const [markupAmount, setMarkupAmount] = useState<string | number>("");
  const { details } = useAppProSelector((state) => state.proDetails);
  const { itemTypes } = useAppProSelector((state) => state.projectItemTypes);

  const itemTypeOptions = useMemo(
    () =>
      itemTypes.length
        ? itemTypes
            .filter((item: IWorkOrderType) => item.type_id)
            .map((item: IWorkOrderType) => {
              return {
                // label: item?.name,
                label: (
                  <div className="flex items-center gap-1.5">
                    <FontAwesomeIcon
                      icon={getItemTypeIcon({
                        type: item?.type_id?.toString(),
                      })}
                    />
                    {item?.name}
                  </div>
                ),
                value: item.type_id?.toString(),
              };
            })
        : [],
    [itemTypes]
  );

  const PROJECT_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {formatter().currency_symbol}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];

  useEffect(() => {
    if (
      formData?.quantity?.toString() !== "" &&
      formData?.unit_cost !== "" &&
      formData?.quantity &&
      formData?.unit_cost
    ) {
      const tot =
        Number(formData?.quantity) * (Number(formData?.unit_cost) / 100);
      setTotal(tot.toString());
      setMainTotal(Number(formData?.markup_amount) + Number(total) / 100);
    } else {
      setTotal("");
      setMainTotal("");
    }
    if (
      formData?.total !== "" &&
      formData.total &&
      formData?.markup !== "" &&
      formData?.markup
    ) {
      if (markup === "markup_percent") {
        const mar = (Number(total) * Number(formData?.markup)) / 100;
        setMarkupAmount(mar);
        setMainTotal(Number(mar) + Number(total));
      } else {
        const mar = Number(formData?.markup) / 100;
        if (mar !== 0) {
          const markupPercentage = (Number(mar) * 100) / Number(total) - 100;
          setMarkupAmount(markupPercentage.toFixed(2));
          setMainTotal(mar);
        } else {
          setMarkupAmount("0");
          setMainTotal(mar);
        }
      }
    } else {
      setMarkupAmount("");
      setMainTotal(total);
    }
  }, [
    formData?.quantity,
    formData?.unit_cost,
    formData?.total,
    formData?.markup,
    formData?.is_markup_percentage,
    total,
    markupAmount,
    mainTotal,
    markup,
  ]);

  return (
    <Drawer
      open={budgetItem}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-hand-holding-circle-dollar"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t("Budget Item")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setBudgetItem(false)} />}
    >
      <form className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100vh-84px)] px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="grid gap-3.5">
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    isRequired={true}
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(formData.subject)
                    )}
                    onChange={() => {}}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      value={formData.item_type?.toString()}
                      disabled={isViewOnly}
                      options={itemTypeOptions}
                      onChange={() => {}}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      value={HTMLEntities.decode(
                        sanitizeString(formData.assignee_name)
                      )}
                      label={_t("Assigned To")}
                      disabled={isViewOnly}
                      labelPlacement="top"
                      onClick={() => {}}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(formData.assignee_name)
                          ),
                          image: formData.assigned_to_image,
                        },
                      }}
                    />
                  </div>
                </div>
                <div className="w-full">
                  <SelectCostCode
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    disabled={isViewOnly}
                    projectId={Number(details.id)}
                    costCodeId={formData.cost_code_id?.toString()}
                    onChange={() => {}}
                    loadingStatus={[]}
                  />
                </div>
              </div>
            </SidebarCardBorder>
            <SidebarCardBorder cardTitle="Pricing">
              <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Qty")}
                  </Typography>
                  <div className="sm:w-40 w-28">
                    <InputField
                      className="!p-0 !pl-1.5 text-success !h-[22px] field-text-13 !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                      placeholder={_t("Item Quantity")}
                      value={formData.quantity}
                      disabled={isViewOnly}
                      labelPlacement="left"
                      type="text"
                      onChange={() => {}}
                    />
                  </div>
                </li>
                <li>
                  <ul className="py-0.5 relative">
                    <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                      <FontAwesomeIcon
                        className="w-3 h-3 text-primary-900 dark:text-white"
                        icon="fa-regular fa-xmark"
                      />
                    </li>
                  </ul>
                </li>
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Unit Cost/Unit")}
                  </Typography>
                  <div className="sm:w-[260px] w-28 h-[22px]" ref={costUnitRef}>
                    <div
                      ref={unitCostContainerRef}
                      className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                    >
                      {isViewOnly &&
                        (!isEmpty(formData?.unit_cost) &&
                        Number(formData?.unit_cost) !== 0 &&
                        !isEmpty(formData?.unit) ? (
                          <Typography className="text-[#008000] font-medium text-13 cursor-not-allowed">
                            {
                              formatter(
                                (Number(formData?.unit_cost) / 100).toFixed(2)
                              ).value_with_symbol
                            }
                            /{formData.unit}
                          </Typography>
                        ) : (
                          <div className="flex gap-2">
                            <div className="w-[calc(100%-52px)]">
                              <InputField
                                className="!p-0 !pl-1.5 text-success !h-[22px] field-text-13 !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Item Unit Cost")}
                                type="text"
                                value={
                                  Number(formData.unit_cost || 0) !== 0
                                    ? (
                                        Number(formData.unit_cost) / 100
                                      ).toFixed(2)
                                    : ""
                                }
                                disabled={isViewOnly}
                                onChange={() => {}}
                              />
                            </div>
                            <div className="w-11">
                              <InputField
                                className="!p-0 !pl-1.5 text-success !h-[22px] field-text-13 !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Unit")}
                                value={formData.unit}
                                disabled={isViewOnly}
                                type="text"
                                onChange={() => {}}
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </li>
                <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                  <Typography className="text-13 block text-primary-900 font-semibold">
                    {_t("Total Cost")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-red-600 text-13 font-semibold"
                      disabled={true}
                    >
                      {Number(total) === 0
                        ? `${formatter("0.00").value_with_symbol}`
                        : `${
                            formatter(Number(total || 0).toFixed(2))
                              .value_with_symbol
                          }`}
                    </Typography>
                  </div>
                </li>
              </ul>
              <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-plus"
                  />
                </li>
              </ul>
              <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex items-center justify-between">
                  <div className="flex items-center gap-1.5">
                    <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                      <ListTabButton
                        value={
                          formData.is_markup_percentage === 1
                            ? "markup_percent"
                            : "markup_dolar"
                        }
                        disabled={isViewOnly}
                        options={PROJECT_ITEMS_LIST_TAB}
                        className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                        activeclassName="active:bg-[#ffffff]"
                        onChange={(e: RadioChangeEvent) => {
                          setMarkup(e.target.value);
                        }}
                      />
                    </div>
                    <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                      <Tooltip
                        title={_t(
                          `% -- Add the % amount that the item should be marked up. ${
                            formatter().currency_symbol
                          } -- Add the ${
                            formatter().currency_symbol
                          } amount that should be charged for the item.`
                        )}
                        rootClassName="!max-w-[265px]"
                      >
                        <FontAwesomeIcon
                          className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                          icon="fa-regular fa-circle-info"
                        />
                      </Tooltip>
                    </div>
                  </div>
                  <div className="sm:w-40 w-28">
                    <InputField
                      className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                      placeholder={
                        markup === "markup_percent"
                          ? _t("Item Markup") + " %"
                          : _t("Total Sales Price")
                      }
                      value={
                        formData?.markup === "0" || !formData.markup
                          ? ""
                          : formData.is_markup_percentage === 1
                          ? formData.markup
                          : Number(formData.markup) / 100
                      }
                      labelPlacement="left"
                      type="text"
                      disabled={isViewOnly}
                      onChange={() => {}}
                    />
                  </div>
                </li>
                <li className="flex items-center justify-between">
                  <Typography className="text-13 block text-primary-900">
                    {_t("Markup")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-[#008000] text-13 font-medium"
                      disabled={true}
                    >
                      {markup === "markup_percent"
                        ? markupAmount === ""
                          ? `${formatter("0.00").value_with_symbol}`
                          : `${
                              formatter(Number(markupAmount || 0).toFixed(2))
                                .value_with_symbol
                            }`
                        : markupAmount === ""
                        ? "0%"
                        : `${
                            Number(markupAmount || 0) !== 0
                              ? Number(markupAmount)?.toFixed(2)
                              : "0"
                          }%`}
                    </Typography>
                  </div>
                </li>
              </ul>
              <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-equals"
                  />
                </li>
              </ul>
              <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex items-center justify-between">
                  <Typography className="text-13 block text-primary-900 font-semibold">
                    {_t("Total Revenue")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-red-600 text-13 font-semibold"
                      disabled={true}
                    >
                      {Number(mainTotal || "") === 0
                        ? `${formatter("0.00").value_with_symbol}`
                        : `${
                            formatter(Number(mainTotal).toFixed(2))
                              .value_with_symbol
                          }`}
                    </Typography>
                  </div>
                </li>
              </ul>
            </SidebarCardBorder>
            <SidebarCardBorder addGap={true}>
              <div className="w-full">
                <TextAreaField
                  label={_t("Description")}
                  labelPlacement="top"
                  value={HTMLEntities.decode(
                    sanitizeString(formData.description)
                  )}
                  disabled={isViewOnly}
                  placeholder={_t(
                    "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                  )}
                />
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Internal Notes")}
                  labelPlacement="top"
                  value={HTMLEntities.decode(
                    sanitizeString(formData.internal_notes)
                  )}
                  disabled={isViewOnly}
                  placeholder={_t(
                    "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                  )}
                />
              </div>
              <div className="flex flex-col gap-1.5">
                <CheckBox
                  className="gap-1.5 w-fit"
                  disabled={isViewOnly}
                  checked={formData.apply_global_tax === 1}
                >
                  {_t("Collect Tax on this Item?")}
                </CheckBox>
                <CheckBox
                  className="gap-1.5 w-fit"
                  disabled={isViewOnly}
                  checked={formData.item_on_database === 1}
                >
                  {_t("Save this item into my Labor Items list?")}
                </CheckBox>
              </div>
            </SidebarCardBorder>
          </div>
        </div>
      </form>
    </Drawer>
  );
};

export default BudgetItem;
