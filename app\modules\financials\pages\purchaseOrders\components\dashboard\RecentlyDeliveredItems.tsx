// Atoms
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Hook
import { useTranslation } from "~/hook";
// Other
import { useAppPODispatch, useAppPOSelector } from "../../redux/store";
import { useEffect, useRef, useState } from "react";
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
import { fetchDashData } from "../../redux/action/dashboardAction";
import { formatValueToString } from "../../utils/function";
import { sanitizeString } from "~/helpers/helper";
import { useNavigate } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { getGConfig } from "~/zustand";

const RecentlyDeliveredItems = () => {
  const { _t } = useTranslation();
  const { module_name }: GConfig = getGConfig();
  const gridApiRef = useRef<GridApi | null>(null);
  const navigate = useNavigate();
  const {
    isDashLoading,
    recentlyDeliveredItems,
    recentlyDeliveredItemsLastRefresTime,
  }: IPODashState = useAppPOSelector((state) => state.dashboard);
  const dispatch = useAppPODispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [rowData, setRowData] = useState<IRecentlyDeliveredItems[]>([]);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  const handleRefreshWidget = async () => {
    setIsLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refresh_type: "recently_delivered_items" }));
    setIsLoading(false);
  };
  useEffect(() => {
    if (!isLoading && recentlyDeliveredItems) {
      setRowData(recentlyDeliveredItems || []);
    }
  }, [recentlyDeliveredItems, isLoading]);
  const columnDefs = [
    {
      headerName: _t("PO") + " #",
      field: "id",
      minWidth: rowData?.length > 0 ? 100 : 60,
      maxWidth: 120,
      flex: 2,
      suppressMenu: true,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: ({ data }: IPODashRecentlyDeliveredTableCellRenderer) => {
        const id = `${data?.purchase_order_id}`;
        return !!id ? (
          <Tooltip title={id}>
            <Typography className="table-tooltip-text">{id}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      maxWidth: rowData?.length > 0 ? 135 : 60,
      minWidth: rowData?.length > 0 ? 135 : 60,
      field: "date_added",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashRecentlyDeliveredTableCellRenderer) => {
        return !!data?.date_added ? (
          <DateTimeCard
            format="date"
            date={formatValueToString(data?.date_added)}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: rowData?.length > 0 ? 125 : 70,
      flex: 1,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashRecentlyDeliveredTableCellRenderer) => {
        const subject = formatValueToString(data?.subject);
        const isSupplierLength =
          data?.supplier_details?.length &&
          data?.supplier_details?.length !== 0;
        const supplierName =
          isSupplierLength &&
          HTMLEntities.decode(
            sanitizeString(
              data?.supplier_details
                ?.map((supplier: IPoMultipleSupplier) => {
                  return supplier?.company_name;
                })
                .join(", ")
            )
          );
        return !!subject ? (
          <div className="flex gap-1 items-center overflow-hidden">
            <Tooltip title={subject}>
              <Typography className="table-tooltip-text">{subject}</Typography>
            </Tooltip>
            {isSupplierLength ? (
              <Tooltip
                title={`${supplierName ? _t("Supplier: ") : ""}${
                  supplierName || ""
                }`}
              >
                <FontAwesomeIcon
                  className="text-base w-3.5 h-3.5 text-primary-900"
                  icon="fa-regular fa-tags"
                />
              </Tooltip>
            ) : (
              <></>
            )}
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project") + " #",
      field: "id",
      minWidth: rowData?.length > 0 ? 125 : 90,
      maxWidth: rowData?.length > 0 ? 125 : 90,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashRecentlyDeliveredTableCellRenderer) => {
        const id = formatValueToString(data?.project_id_string);
        return !!id ? (
          <Tooltip title={id}>
            <Typography className="table-tooltip-text">{id}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-po-recently-delivered-items.svg`}
    />
  );
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  return (
    <>
      <DashboardCardHeader
        title={_t(`Recently Delivered Items`)}
        showRefreshIcon={true}
        refreshIconTooltip={recentlyDeliveredItemsLastRefresTime}
        isRefreshing={isLoading}
        onClickRefresh={handleRefreshWidget}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            className="static-table ag-grid-cell-pointer"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            key={isDashLoading ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isDashLoading || isLoading ? noRowsOverlay : noData
            }
            enableOpenInNewTab={true}
            generateOpenInNewTabUrl={(data: { purchase_order_id?: number }) =>
              `${routes.MANAGE_PURCHASE_ORDERS.url}/${data?.purchase_order_id}`
            }
          />
        </div>
      </div>
    </>
  );
};

export default RecentlyDeliveredItems;
