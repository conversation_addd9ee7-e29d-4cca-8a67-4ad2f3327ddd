// Atoms
import { Image } from "~/shared/components/atoms/image";
import { Typography } from "~/shared/components/atoms/typography";

const CloudList = ({ image, day, temprecher }: ICloudListProps) => {
  return (
    <li className="flex items-center flex-col">
      <Typography className="text-[11px] leading-[22px] text-gray-400">
        {day}
      </Typography>
      <div className="w-[35px] h-[35px] overflow-hidden">
        <Image
          preview={false}
          rootClassName="w-full h-full"
          src={image}
          className="w-full h-full"
        />
      </div>
      <Typography className="text-[11px] leading-[22px] text-gray-400">
        {temprecher}
      </Typography>
    </li>
  );
};
export default CloudList;
