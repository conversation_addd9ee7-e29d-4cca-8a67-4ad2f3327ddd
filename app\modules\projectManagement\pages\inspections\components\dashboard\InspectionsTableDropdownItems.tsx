import { useMemo, useState } from "react";
// custom hook
import { useIframe, useTranslation } from "~/hook";
// static const
import { apiRoutes, routes } from "~/route-services/routes";
// zustand
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { setSendEmailOpenStatus } from "~/components/sidebars/multi-select/customer/zustand/action";
// helpers
import { getApiDefaultParams, Number, sanitizeString } from "~/helpers/helper";
import { getApiData } from "~/helpers/axios-api-helper";
// molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
import { PDFFilePreviewWithoutTemplate } from "~/shared/components/molecules/pdfFilePreviewWithoutTemplate";
// Other
import { useNavigate, useParams, useSearchParams } from "@remix-run/react";
import { removeFirstSlash } from "~/shared/utils/helper/common";
import { sendMessageKeys } from "~/components/page/$url/data";
import dayjs from "dayjs";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getFileUrl, handleDownload } from "../../utils/common";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { copyInspection } from "../../redux/action/inspectionDashAction";

export const InspectionsTableDropdownItems = ({
  data,
  refreshTable,
  className,
  iconClassName,
  contentClassName,
  icon,
  isDetailView = false,
  tooltipcontent,
}: IInspectionsTableDropdownItemsProps) => {
  const { _t } = useTranslation();
  const [inspectionListPdfViewOpen, setInspectionListPdfViewOpen] =
    useState<boolean>(false);
  const navigate = useNavigate();
  const { module_singular_name, page_is_iframe }: GConfig = getGConfig();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const [shareLinkUrl, setSharelinkUrl] = useState<string>("");
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    user_id = 0,
    company_id = 0,
    timezone_utc_tz_id = "",
    allow_delete_module_items = "0",
  } = user || {};
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false); // Updated state
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [deleteRecordLoading, setDeleteRecordLoading] =
    useState<boolean>(false);
  const [isCopyExpenseLoading, setIsCopyExpenseLoading] =
    useState<boolean>(false);
  const [isCopyInspectionConfirmOpen, setIsCopyInspectionConfirmOpen] =
    useState<boolean>(false);
  const { parentPostMessage } = useIframe();
  const [searchParams] = useSearchParams();
  const iFrame = searchParams?.get("iframecall")?.trim();
  const pageOpenForm = searchParams?.get("pageOpenfrom")?.trim();
  const authToken = searchParams?.get("authorize_token")?.trim();

  const { id: inspection_id }: RouteParams = useParams();
  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs(new Date()).tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  const fileUrl = useMemo(
    () =>
      getFileUrl(
        Number(inspection_id) === 0
          ? Number(data?.inspection_id)
          : Number(inspection_id) || "",
        user_id,
        company_id,
        currentDateTime
      ),
    [inspection_id, data?.inspection_id, user_id, company_id]
  );

  const options: CustomerEmailTab[] = [
    CFConfig.employee_key,
    "my_crew",
    CFConfig.customer_key,
    CFConfig.contractor_key,
    CFConfig.vendor_key,
    CFConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const dataToDownloadPdf: IDownloadInspectionDocumentWithAction = {
    action: "download",
    op: "pdf_inspection",
    inspection_id:
      Number(inspection_id) === 0
        ? Number(data?.inspection_id)
        : Number(inspection_id),
    tz: dayjs().format("ZZ"),
    tzid: Intl.DateTimeFormat().resolvedOptions().timeZone,
    curr_time: dayjs(currentDateTime).format("YYYY-MM-DD HH:mm:ss"),
  };

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IInspectionSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id:
        Number(inspection_id) === 0
          ? Number(data?.inspection_id)
          : Number(inspection_id),
      module_id: currentModule?.module_id,
      module_key: currentModule?.module_key,
      inspection_id:
        Number(inspection_id) === 0
          ? Number(data?.inspection_id)
          : Number(inspection_id),
      action: "send",
      op: "pdf_inspection",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
          setSendEmailOpenStatus(false);
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleDelete = () => {
    try {
      setDeleteRecordLoading(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key:
              Number(inspection_id) === 0
                ? Number(data?.inspection_id)
                : Number(inspection_id),
            module_key: currentModule?.module_key,
            remove_associated_data: 0,
          },
        }),
        success: (response: { message: string }) => {
          setConfirmDialogOpen(false);
          setDeleteRecordLoading(false);

          if (window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            if (Number(inspection_id) !== 0) {
              navigate(`${routes.MANAGE_INSPECTION.url}`);
              if (isDetailView) return;
            }
          }

          refreshTable();
          // dispatch(setShouldWidgetsRefresh());
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {},
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleCopyInspection = async () => {
    setIsCopyExpenseLoading(true);

    let dataForCopyInspection = {
      id:
        Number(inspection_id) === 0
          ? data?.inspection_id?.toString()
          : inspection_id?.toString(),
    };

    try {
      const resData = (await copyInspection(
        dataForCopyInspection
      )) as ICopyInspectionApiRes;

      if (resData?.success) {
        setIsCopyExpenseLoading(false);
        setIsCopyInspectionConfirmOpen(false);

        if (resData?.data?.inspection_id) {
          if (window.ENV.PAGE_IS_IFRAME) {
            navigate(
              `${routes.MANAGE_INSPECTION.url}/${resData.data.inspection_id}?iframecall=${iFrame}&pageOpenfrom=${pageOpenForm}&authorize_token=${authToken}`
            );
          } else {
            navigate(
              `${routes.MANAGE_INSPECTION.url}/${resData?.data?.inspection_id}`
            );
          }
        }
      } else {
        setIsCopyExpenseLoading(false);
        setIsCopyInspectionConfirmOpen(false);
        notification.error({
          description: resData.message || "Something went wrong!",
        });
      }
    } catch (error) {
      setIsCopyExpenseLoading(false);
      setIsCopyInspectionConfirmOpen(false);
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const shareInternalLinToMail = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const formData = {
        ...tempFormData,
        send_me_copy: ccMailCopy ? 1 : 0,
        send_custom_email: 0,
        op: "send_custom_email",
        action: "send",
      } as ISendEmailFormTodoModule;
      const responseApi = (await sendCommonEmailApi(
        formData
      )) as ISendEmailCommonRes;
      if (responseApi?.success) {
        closeSendMailSidebar();
      }
      if (!responseApi?.success) {
        notification.error({
          description: responseApi.message,
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  // const shareInternalLinToMail = (
  //   tempFormData: SendEmailFormDataWithApiDefault,
  //   closeSendMailSidebar: () => void,
  //   ccMailCopy: boolean
  // ) => {
  //   try {
  //     getApiData({
  //       url: apiRoutes.SERVICE.url,
  //       method: "post",
  //       data: {
  //         ...tempFormData,
  //         send_me_copy: ccMailCopy ? 1 : 0,
  //         send_custom_email: 0,
  //       },
  //       success: (response: { message: string; success: string | number }) => {
  //         if (response?.success.toString() === "0") {
  //           notification.error({
  //             description: response?.message,
  //           });
  //         } else if (response?.success.toString() === "1") {
  //           closeSendMailSidebar();
  //           setIsShareOpen(false); // Set the share link modal to close
  //           setSendEmailOpenStatus(false);
  //         }
  //       },
  //       error: (description) => {
  //         notification.error({
  //           description,
  //         });
  //       },
  //     });
  //   } catch (error: unknown) {
  //     notification.error({
  //       description: (error as Error)?.message,
  //     });
  //   }
  // };

  const InspectionlistViewTableOptions = [
    {
      label: _t("View/Email PDF"),
      icon: "fa-regular fa-file-pdf",
      onClick: () => {
        setInspectionListPdfViewOpen(true);
      },
    },
    {
      label: _t(`Copy ${currentModule?.singular_name}`),
      icon: "fa-regular fa-clone",
      onClick: () => {
        // handleCopyInspection();
        setIsCopyInspectionConfirmOpen(true);
      },
    },
    {
      content: _t("Share Internal Link"),
      icon: "fa-regular fa-share-nodes",
      onClick: () => {
        setIsShareOpen(true);
      },
      onlyIconView: true,
    },
    {
      content: _t("Delete"),
      icon: "fa-regular fa-trash-can",
      onClick: () => setConfirmDialogOpen(true),
      disabled: allow_delete_module_items === "0" || page_is_iframe,
      onlyIconView: true,
    },
  ];

  return (
    <>
      <DropdownMenu
        icon={icon ? icon : "fa-regular fa-ellipsis-vertical"}
        options={InspectionlistViewTableOptions}
        buttonClass={
          className ? className : "active-button hover:!bg-[#0000000f] !rounded"
        }
        iconClassName={iconClassName && iconClassName}
        contentClassName={contentClassName && contentClassName}
        tooltipcontent={tooltipcontent}
        {...(allow_delete_module_items === "0" && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />

      {inspectionListPdfViewOpen && data && (
        <>
          <PDFFilePreviewWithoutTemplate
            isOpen={inspectionListPdfViewOpen}
            onCloseModal={() => setInspectionListPdfViewOpen(false)}
            isLoading={false}
            isViewAttachment={false}
            options={options}
            id={(data as IInspectionListData)?.project_id?.toString()}
            fileUrl={fileUrl}
            moduleName={module_singular_name}
            emailSubject={
              HTMLEntities.decode(
                sanitizeString(data?.email_subject?.toString())
              ) ?? ""
            }
            handleDownload={() => handleDownload(dataToDownloadPdf)}
            handleEmailApiCall={async (...params) => {
              await handleEmailApiCall(...params);
            }}
          />
        </>
      )}

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id:
              Number(inspection_id) === 0
                ? Number(data?.inspection_id)
                : Number(inspection_id), // Adjust the key name for your data structure
            module_key: currentModule?.module_key || "",
            module_page: removeFirstSlash(routes.MANAGE_INSPECTION.url || ""),
          }}
          onEmailLinkClick={(shareLinkData) => {
            setSendEmailOpenStatus(true); // Open the email modal
            setSendEmailOpen(true);
            setIsShareOpen(false);
            setSharelinkUrl(shareLinkData);
          }}
          onCloseModal={() => {
            setIsShareOpen(false); // Close the modal
          }}
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
            setSendEmailOpenStatus(false);
          }}
          appUsers={true}
          isViewAttachment={false}
          openSendEmailSidebar={sendEmailOpen}
          options={[CFConfig.employee_key, CFConfig.contractor_key]}
          singleSelecte={false}
          emailApiCall={shareInternalLinToMail}
          customEmailData={{
            body: _t(
              `A link to a record within Contractor Foreman has been shared with you. <a href = ${shareLinkUrl}>View Details</a>.`
            ),
            subject: _t("Shared Link"),
          }}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              CFConfig.file_support_key
            ),
            image_resolution,
            module_key: currentModule?.module_key || "",
            module_id: Number(currentModule?.module_id),
            module_access: currentModule?.module_access || "full_access",
            save_a_copy_of_sent_pdf,
          }}
          canWrite={false}
          additionalContactDetails={0}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={_t("Delete")}
          modalIcon="fa-regular fa-trash-can"
          description={_t("Are you sure you want to delete this Item?")}
          isLoading={deleteRecordLoading}
          withConfirmText={true}
          onAccept={() => {
            handleDelete();
          }}
          onDecline={() => setConfirmDialogOpen(false)}
          onCloseModal={() => setConfirmDialogOpen(false)}
        />
      )}

      {isCopyInspectionConfirmOpen && (
        <ConfirmModal
          isOpen={isCopyInspectionConfirmOpen}
          modaltitle={_t("Copy")}
          description={_t(
            `Are you sure you want to generate a copy of this ${currentModule?.singular_name}?`
          )}
          modalIcon="fa-regular fa-clone"
          isLoading={isCopyExpenseLoading}
          onAccept={() => {
            handleCopyInspection();
          }}
          onDecline={() => {
            setIsCopyInspectionConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsCopyInspectionConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};
