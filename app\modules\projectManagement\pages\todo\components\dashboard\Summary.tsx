// antd + ag
import { type RadioChangeEvent } from "antd";
import { useRef, useState, useMemo } from "react";
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
// Atoms
import { ApexChart } from "~/shared/components/atoms/chart";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import PieChartSkeleton from "~/shared/components/molecules/charts/skeleton/PieChart.skeleton";
// redux
import { useAppTDDispatch, useAppTDSelector } from "../../redux/store";
// hook
import { useTranslation } from "~/hook";
import { useApexCharts } from "~/shared/hooks/useApexCharts";
// constants
import { TODO_TAB } from "../../utils/constants";
import { sanitizeString } from "~/helpers/helper";
import { getGConfig } from "~/zustand";
import { fetchDashData } from "../../redux/action/dashboardAction";

const Summary = () => {
  const gridApiRef = useRef<GridApi | null>(null);
  const [value, setValue] = useState<string>("chart");
  const { _t } = useTranslation();
  const optionsLine = useApexCharts({ type: "donut" });
  const { module_name }: GConfig = getGConfig();
  const [chartVal, setChartVal] = useState<string>("");
  const [hoverLabel, setHoverLabel] = useState<string>("Total");
  const [hoverValue, setHoverValue] = useState<number | null>(null);
  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) || _t("To Do's");
  const dispatch = useAppTDDispatch();

  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);

  const { isDashLoading, summary, summaryLastRefreshTime }: IToDoDashState =
    useAppTDSelector((state) => state.dashboard);

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    await dispatch(
      fetchDashData({
        refresh_type: "summary",
      })
    );
    setIsCashLoading(false);
  };

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  // Process the summary data
  const chartSeriesData = useMemo(() => {
    const summaryData = summary?.[0];
    if (!summaryData) {
      return [0, 0]; // Default to 0 for both "Closed" and "Open"
    }
    return Object.entries(summaryData)
      .filter(([key]) => !key.includes("total"))
      .map(([, value]) => Number(value));
  }, [summary]);

  const formattedLabels = useMemo(() => {
    const summaryData = summary?.[0];
    if (!summaryData) {
      return ["Open: 0%", "Closed: 0%"]; // Default labels for empty summary
    }
    return Object.entries(summaryData)
      .filter(([key]) => !key.includes("total"))
      .map(
        ([key, value]) => `${key}: ${parseFloat(value as string).toFixed(2)}%`
      );
  }, [summary]);

  // Calculate the total sum of total_open and total_closed
  const totalSum = useMemo(() => {
    const summaryData = summary?.[0];
    if (!summaryData) {
      return 0; // Default total of 0 for empty summary
    }
    const open = parseInt(summaryData?.total_open ?? "0", 10);
    const due = parseInt(summaryData?.total_closed ?? "0", 10);
    return open + due;
  }, [summary]);

  const chartOptions = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        offsetY: 10,
        events: {
          dataPointMouseLeave: () => {
            setChartVal(`Total: ${totalSum}`);
            setHoverLabel("Total");
            setHoverValue(totalSum);
          },
        },
      },
      labels: formattedLabels,
      legend: {
        show: true,
        position: "right",
        horizontalAlign: "center",
        offsetX: 30,
        offsetY: 40,
        onItemClick: { toggleDataSeries: false },
        onItemHover: { highlightDataSeries: true },
      },
      colors: ["#45506D", "#FE595B", "#5C9395"],
      dataLabels: { enabled: false },
      tooltip: {
        enabled: true,
        custom: ({ seriesIndex }: { seriesIndex: number }) => {
          const totalValues = [
            summary?.[0]?.total_open ?? 0,
            summary?.[0]?.total_closed ?? 0,
          ];
          const labels = ["Open", "Closed"];
          const backgroundColors = ["#45506D", "#FE595B"];

          setHoverLabel(labels[seriesIndex]);
          setHoverValue(Number(totalValues[seriesIndex]));

          return `<div class="custom-tooltip" style="background-color: ${backgroundColors[seriesIndex]}; color: white; padding: 4px; border-radius: 4px;">
                    <span>${labels[seriesIndex]}: ${totalValues[seriesIndex]}</span>
                  </div>`;
        },
      },
      title: {
        text: undefined,
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: "12px",
                color: "#000",
                offsetY: -7,
              },
              value: {
                show: true,
                fontSize: "12px",
                color: "#000",
                opacity: 1,
                offsetY: -2,
              },
              total: {
                show: true,
                label: hoverLabel,
                color: "#000",
                fontSize: "12px",
                fontWeight: "600",
                showAlways: true,
                formatter: () => hoverValue ?? `${totalSum}`,
              },
            },
            size: "60%",
          },
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
      responsive: [
        {
          breakpoint: 500, // Adjust for tablets or small screens
          options: {
            legend: {
              offsetX: -20,
            },
          },
        },
      ],
    };
  }, [hoverLabel, hoverValue, totalSum, optionsLine, formattedLabels]);

  const columnDefs = [
    {
      headerName: _t("Status"),
      field: "status",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
    },
    {
      headerName: modulePLName,
      field: "to_do",
      minWidth: 90,
      flex: 2,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMenu: true,
    },
  ];

  return (
    <>
      <DashboardCardHeader
        title="Summary (Open, Closed)"
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={summaryLastRefreshTime}
        onClickRefresh={handleRefreshClick}
        rightContent={
          <div className="flex items-center">
            <ListTabButton
              value={value}
              options={TODO_TAB}
              onChange={(e: RadioChangeEvent) => setValue(e.target.value)}
              className="first:border-r-0"
              activeclassName="!bg-[#EAE8E8]"
            />
          </div>
        }
      />

      {value === "chart" ? (
        <>
          {isDashLoading || isCashLoading ? (
            <div className="py-2 px-2.5 h-[190px] flex justify-center items-center">
              <PieChartSkeleton />
            </div>
          ) : (
            <ApexChart
              className="donut-chart legend-small-gap"
              series={chartSeriesData}
              options={chartOptions}
              type={"donut"}
              height={162}
            />
          )}
        </>
      ) : value === "table" ? (
        <div className="py-2 px-2.5">
          <div className="ag-theme-alpine h-[170px]">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              rowData={
                !(isDashLoading || isCashLoading)
                  ? summary.length > 0
                    ? [
                        { status: "Open", to_do: summary[0]?.total_open },
                        { status: "Closed", to_do: summary[0]?.total_closed },
                      ]
                    : []
                  : undefined
              }
              noRowsOverlayComponent={() =>
                isDashLoading || isCashLoading ? (
                  <StaticTableRowLoading columnDefs={columnDefs} limit={4} />
                ) : (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-td-summary.svg`}
                  />
                )
              }
            />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default Summary;
