import { useNavigate, useParams } from "@remix-run/react";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
// molecules
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
// Organisms
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
// Other
import {
  DetailsTab,
  DetailsTopBar,
} from "~/modules/projectManagement/pages/inspections/components/tab";
import {
  getCommonSidebarCollapse,
  getGSettings, // In future this code move in redux, developer change this code
} from "~/zustand";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  useInAppDispatch,
  useInAppSelector,
} from "~/modules/projectManagement/pages/inspections/redux/store";
import { getCommonNotes } from "~/redux/action/commonNotesAction";
import { getCommonAttachments } from "~/redux/action/commonAttachmentSection";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { Number } from "~/helpers/helper";
import InspectionNotesTab from "~/modules/projectManagement/pages/inspections/components/tab/InspectionNotesTab";
import InspectionFileTab from "~/modules/projectManagement/pages/inspections/components/tab/InspectionFileTab";
import InspectionStoreProvider from "~/modules/projectManagement/pages/inspections/redux/inspectionStoreProvider";
import {
  fetchInspectionDetails,
  fetchInspectionPermitLogList,
} from "~/modules/projectManagement/pages/inspections/redux/action/inspectionDetailsAction";
import { resetTDFiles } from "~/redux/slices/commonAttachmentSlice";
import { resetTDNotes } from "~/redux/slices/commonNoteSlice";
import { setNoInspectionDetailsAvail } from "~/modules/projectManagement/pages/inspections/redux/slices/inspectionDetailsSlice";
import { routes } from "~/route-services/routes";

const ManageInspectionsTabCom = () => {
  const { tab, id }: RouteParams = useParams(); // This type already declare.
  const { comment_order }: GSettings = getGSettings();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const [activeStep, setActiveStep] = useState<string | number>("");
  const handleStepClick = (value: string) => {};
  const dispatch = useInAppDispatch();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const navigate = useNavigate();
  const { details, isDetailLoading, noDetailsAvailable } = useInAppSelector(
    (state) => state.inspectionDetails
  );

  const detailApiCalls = () => {
    dispatch(
      fetchInspectionDetails({
        id,
        add_event: true,
      })
    );
  };

  const fetchData = useCallback(() => {
    if (tab === "notes") {
      dispatch(
        getCommonNotes({
          record_id: Number(id),
          module_key: currentModule?.module_key,
          order: comment_order,
        })
      );
    } else if (tab === "files") {
      const getAttachParams: ICommonAttachmentFrm = {
        record_id: Number(id),
        module_key: currentModule?.module_key,
      };
      dispatch(getCommonAttachments(getAttachParams)).unwrap();
    }
  }, [tab, id, currentModule?.module_key]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    if (id) {
      dispatch(resetTDFiles());
      dispatch(resetTDNotes());
    }
  }, [id]);

  useEffect(() => {
    if (details.project_id) {
      const permitLogListData = {
        ignore_filter: 1,
        filter: {
          status: "0",
          project: details.project_id.toString(),
        },
        show_deleted_permit: 0,
      };
      dispatch(fetchInspectionPermitLogList(permitLogListData));
    }
  }, [details.project_id]);

  const selectedDirTab = useMemo(() => {
    let selectedTabComp = <></>;
    if (tab === "details" || tab === undefined) {
      selectedTabComp = <DetailsTab />;
    } else if (tab === "notes") {
      selectedTabComp = <InspectionNotesTab projectid={details.project_id} />;
    } else if (tab === "files") {
      selectedTabComp = <InspectionFileTab />;
    }

    return { selectedTabComp };
  }, [tab]);

  const handleReloadDetails = () => {
    if (tab?.toLowerCase() === "details" || tab === undefined) {
      detailApiCalls();
    } else {
      detailApiCalls();
      fetchData();
    }

    if (details?.project_id && details.project_id != 0) {
      const permitLogListData = {
        ignore_filter: 1,
        filter: {
          status: "0",
          project: details.project_id.toString(),
        },
        show_deleted_permit: 0,
      };
      dispatch(fetchInspectionPermitLogList(permitLogListData));
    }
  };

  return (
    <>
      <div
        className={`ease-in-out duration-300 w-full overflow-y-auto ${
          sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
        }`}
      >
        <DetailsTopBar
          sidebarCollapse={sidebarCollapse}
          activeStep={activeStep}
          setActiveStep={setActiveStep}
          handleStepClick={(value) => handleStepClick(value)}
          onReloadDirDetails={handleReloadDetails}
        />
        {!isDetailLoading && (
          <ReadOnlyPermissionMsg
            className="p-4 pt-0"
            view={currentModule?.module_access === "read_only"}
          />
        )}
        <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
          <div
            className={`px-[15px] pb-[15px] ${
              currentModule?.module_access === "read_only"
                ? window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-196px)] min-h-[calc(100dvh-246px)]"
                  : "md:min-h-[calc(100dvh-327px)] min-h-[calc(100dvh-392px)]"
                : window.ENV.PAGE_IS_IFRAME
                ? "md:min-h-[calc(100dvh-164px)] min-h-[calc(100dvh-214px)]"
                : "md:min-h-[calc(100dvh-289px)] min-h-[calc(100dvh-332px)]"
            }`}
          >
            {isDetailLoading ? (
              <Spin
                className={`flex items-center justify-center ${
                  window.ENV.PAGE_IS_IFRAME
                    ? "md:h-[calc(100dvh-161px)] h-[calc(100dvh-205px)]"
                    : "md:h-[calc(100dvh-304px)] h-[calc(100dvh-357px)]"
                }`}
              />
            ) : (
              <>{selectedDirTab.selectedTabComp}</>
            )}
          </div>
          <TimeLineFooter
            data={{
              addedDate: details.date_added || "",
              addedTime: details.time_added || "",
              addedBy: details.generated_by || "",
              moduleId: currentModule?.module_id,
              recordId: Number(id),
            }}
            sidebarCollapse={sidebarCollapse}
            isLoading={isDetailLoading}
          />
        </div>
      </div>
    </>
  );
};

const ManageInspectionsTab = () => {
  return (
    <InspectionStoreProvider>
      {" "}
      <ManageInspectionsTabCom />{" "}
    </InspectionStoreProvider>
  );
};
export default ManageInspectionsTab;

export { ErrorBoundary };
