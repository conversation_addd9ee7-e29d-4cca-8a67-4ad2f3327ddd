import { ChangeEvent, useEffect, useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";

// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Other
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { Number, sanitizeString } from "~/helpers/helper";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { getStatusForField } from "~/shared/utils/helper/common";
import { getCustomerAccessCodeField } from "~/modules/people/directory/utils/accessFields";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { routes } from "~/route-services/routes";
import { fetchProjectDetailsApi } from "../../../redux/action/projectDetailsAction";

const CustomerDetails = () => {
  const { _t } = useTranslation();

  const dispatch = useAppProDispatch();

  const { details } = useAppProSelector((state) => state.proDetails);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerEmail[]>();
  const [contactAddress, setContactAddress] = useState<boolean>(false);
  const [IsOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);

  const { isNoAccessCustomerAccessCodeField } = getCustomerAccessCodeField();
  const { checkGlobalModulePermissionByKey } = useGlobalModule();

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const {
    module_key = "",
    module_id = 0,
    module_access = "full_access",
  } = currentModule || {};

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { image_resolution = "", save_a_copy_of_sent_pdf = 1 } =
    appSettings || {};

  const {
    handleUpdateField,
    loadingStatus,
    isReadOnly,
    inputVals,
    updateDataInStore,
  } = useProjectDetail();

  const fullAddress = useMemo(() => {
    return `${
      inputVals.cust_address1 && inputVals.cust_address1 !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.cust_address1)) + "\n"
        : ""
    }${
      inputVals.cust_address2 && inputVals.cust_address2 !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.cust_address2)) + "\n"
        : ""
    }${
      inputVals.cust_city && inputVals.cust_city !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.cust_city)) + ", "
        : ""
    }${
      inputVals.cust_state && inputVals.cust_state !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.cust_state)) + " "
        : ""
    }${
      inputVals.cust_zip !== "" && inputVals.cust_zip ? inputVals.cust_zip : ""
    }`;
  }, [inputVals]);

  const showGoogleMap = useMemo(() => {
    return (
      details.cust_address1 !== "" ||
      details.cust_address2 !== "" ||
      details.cust_city !== "" ||
      details.cust_state !== "" ||
      details.cust_zip !== ""
    );
  }, [details]);

  const getDirId = (type_key: string) => {
    return getDirectaryIdByKey(type_key as CustomerTabs, undefined);
  };

  const handleCustomerSelection = async (
    contacts: Array<
      CustomerEmail & {
        customer_tax_id?: string | number;
        billed_to_dir_type?: string | number;
        access_code?: string | number;
      }
    >
  ) => {
    if (contacts) {
      setSelectedCustomer(contacts);

      let data = contacts[0];
      let updatedContact = {
        data_to_update_in_store: {},
        data_to_send_in_api: {
          customer_id: details.customer_id,
          customer_contact_id: details.customer_contact_id,
          billed_to: details.billed_to,
          billed_to_contact: details.billed_to_contact,
          default_tax_rate_id: details.default_tax_rate_id,
        },
      };

      if (data?.user_id !== undefined) {
        if (
          data?.user_id?.toString() != details.customer_id?.toString() ||
          data?.contact_id?.toString() !=
            details.customer_contact_id?.toString()
        ) {
          const customer_type_key =
            data.type_key !== "contact"
              ? data.type_key || ""
              : data.parent_type_key || "";

          updatedContact = {
            data_to_update_in_store: {
              customer_company: data?.company_name,
              customer_name: data?.display_name,
              customer_name_only: `${
                data.first_name ? data.first_name + " " : ""
              }${data.last_name ? data.last_name : ""}`,
              cust_type: getDirId(customer_type_key as string),
              cust_address1: data?.user_address1 || "",
              cust_address2: data?.user_address2 || "",
              cust_city: data?.user_city || "",
              cust_state: data?.user_state || "",
              cust_zip: data?.user_zip || "",
              access_code: data?.access_code || "",
              cust_cell: data?.cell || "",
              cust_phone: data?.phone || "",
              cust_email: data?.email || "",
              cust_image: data?.image || "",
              billed_to_name: data?.billed_to_display_name,
              billed_to_contact: data?.billed_to_contact,
              billed_to_type: Number(data.billed_to_dir_type),

              // tax_name: data.tax
            },
            data_to_send_in_api: {
              customer_id: data?.user_id,
              customer_contact_id: data?.contact_id,

              billed_to: Number(data?.billed_to) || "",
              billed_to_contact: Number(data.billed_to_contact) || "",

              default_tax_rate_id:
                data?.customer_tax_id &&
                data?.customer_tax_id?.toString() !== "0"
                  ? data.customer_tax_id?.toString()
                  : "",
            },
          };
          await handleUpdateField(updatedContact);

          dispatch(
            fetchProjectDetailsApi({
              project_id: Number(details?.id),
              record_type: "project",
              skipLoading: true,
            })
          );

          setIsOpenSelectCustomer(false);
        }
      } else {
        notification.error({
          description: "Customer field is required.",
        });
        updateDataInStore(details);

        setIsOpenSelectCustomer(false);
      }

      if (
        Object.keys(updatedContact.data_to_send_in_api).length &&
        updatedContact.data_to_send_in_api?.customer_id
      ) {
      }

      // setFieldValue("customer_id", customer.user_id);
      if (
        (data.user_address1 && data.user_address1 !== "") ||
        (data.user_address2 && data.user_address2 !== "") ||
        (data.user_city && data.user_city !== "") ||
        (data.user_state && data.user_state !== "") ||
        (data.user_zip && data.user_zip !== "")
      ) {
        setContactAddress(true);
      }
    }
  };

  const copyToClipboard = () => {
    if (selectedCustomer) {
      let data = selectedCustomer[0];
      let updatedContact = {
        data_to_send_in_api: {},
      };

      if (data?.user_id !== undefined) {
        updatedContact = {
          data_to_send_in_api: {
            address1: details?.cust_address1 || "",
            address2: details?.cust_address2 || "",
            city: details?.cust_city || "",
            state: details?.cust_state || "",
            zip: details?.cust_zip || "",
          },
        };
        handleUpdateField(updatedContact);
        setContactAddress(false);
      }
    }
  };

  const customerAddress = useMemo(() => {
    return {
      address1: details.cust_address1 || "",
      address2: details.cust_address2 || "",
      cityStZip: [
        details.cust_city || "",
        details.cust_state || "",
        details.cust_zip || "",
      ]
        .filter((value) => value !== "")
        .join(", "),
    };
  }, [
    details.cust_address1,
    details.cust_address2,
    details.cust_city,
    details.cust_state,
    details.cust_zip,
  ]);

  const contactAddressChange = (newOpen: boolean) => {
    setContactAddress(newOpen);
  };

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const formData = {
        ...tempFormData,
        send_me_copy: ccMailCopy ? 1 : 0,
        send_custom_email: 0,
        op: "send_custom_email",
        action: "send",
      } as ISendEmailFormTodoModule;
      const responseApi = (await sendCommonEmailApi(
        formData
      )) as ISendEmailCommonRes;
      if (responseApi?.success) {
        closeSendMailSidebar();
      }
      if (!responseApi?.success) {
        notification.error({
          description: responseApi.message,
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const customerPhoneCellData = useMemo(() => {
    let label = "Cell";
    let cellPhone = "";
    const custPhone = !!details?.customer_contact_id
      ? details?.customer_contact_phone
      : details.cust_phone;
    const custCell = !!details?.customer_contact_id
      ? details?.customer_contact_cell
      : details.cust_cell;
    if (custCell && custCell !== "" && custPhone && custPhone !== "") {
      label = "Phone/Cell";
      cellPhone = custPhone + " / " + custCell;
    } else if (custPhone && custPhone !== "") {
      label = "Phone";
      cellPhone = custPhone;
    } else if (custCell && custCell !== "") {
      label = "Cell";
      cellPhone = custCell;
    }

    return {
      label,
      cellPhone,
    };
  }, [details.cust_cell, details.cust_phone]);

  const selectedCustomerInDrawer = useMemo(() => {
    return details?.customer_id && details?.customer_name
      ? ([
          {
            user_id: Number(details?.customer_id),
            display_name: details?.customer_name,
            contact_id: details.customer_contact_id,
            type: Number(details.cust_type),
            type_key: getDirectaryKeyById(
              Number(details.cust_type) === 1 ? 2 : Number(details.cust_type),
              undefined
            ),
          },
        ] as CustomerEmail[])
      : [];
  }, [details]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Customer Details")}
        iconProps={{
          icon: "fa-solid fa-file-invoice",
          containerClassName:
            "bg-[linear-gradient(180deg,#C285FF1a_0%,#962EFF1a_100%)]",
          id: "customer_details",
          colors: ["#C285FF", "#962EFF"],
        }}
        children={
          <ul className="pt-2 flex 2xl:flex-row flex-col gap-2">
            <li
              className={
                showGoogleMap ? "2xl:w-[calc(100%-300px)] w-full" : "w-full"
              }
            >
              <ul>
                <li className="flex sm:flex-row flex-col mb-3">
                  <Typography className="min-w-[140px] block font-semibold text-[#343a40e3]">
                    {_t("Name")}
                  </Typography>
                  <div className="flex gap-1 items-center sm:bg-transparent bg-[#f4f5f6] sm:min-h-fit min-h-[34px] sm:p-0 p-1.5">
                    <div className="relative">
                      <Typography
                        disabled={isReadOnly}
                        onClick={
                          isReadOnly
                            ? () => {}
                            : () => setIsOpenSelectCustomer(true)
                        }
                        className={`transition duration-500 ease-in-out break-all ${
                          isReadOnly ? "!cursor-default" : "!cursor-pointer"
                        } ${
                          details.customer_name_only || details.customer_name
                            ? isReadOnly
                              ? "!text-primary-900"
                              : "!text-primary-900 hover:!text-deep-orange-500"
                            : "!text-[#bdbdbd]"
                        }`}
                      >
                        {details.customer_id && details?.customer_name ? (
                          <div className="flex items-center gap-2 pl-px">
                            <AvatarProfile
                              user={{
                                name: details?.customer_name?.trim(),
                                image: details.cust_image,
                              }}
                              className="m-auto"
                              iconClassName="text-[11px] font-semibold"
                            />
                            <Typography
                              className={`text-primary-900 text-sm hover:text-deep-orange-500 ${
                                showGoogleMap
                                  ? "max-w-[calc(100%-32px)]"
                                  : "max-w-[calc(100%-32px)]"
                              }`}
                            >
                              {HTMLEntities.decode(
                                sanitizeString(
                                  details.customer_name ||
                                    details.customer_name_only
                                )
                              )}
                            </Typography>
                          </div>
                        ) : (
                          "Select/Create Customer"
                        )}
                        {/* {details.customer_id
                          ? HTMLEntities.decode(
                              sanitizeString(
                                details.customer_name_only ||
                                  details.customer_name
                              )
                            )
                          : "Select/Create Customer"} */}
                      </Typography>

                      <Popover
                        content={
                          <div className="min-w-[272px]">
                            <Typography className="block text-sm py-2 px-3.5 bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                              {_t("Copy Customer Address")}
                            </Typography>
                            <div className="py-2 px-3.5">
                              <Typography className="mb-2">
                                {_t(
                                  "Copy Customer Address to Project Address?"
                                )}
                              </Typography>
                              <Typography className="block text-13">
                                <Typography className="font-medium text-13">
                                  {_t("Street")}
                                  {": "}
                                </Typography>
                                {customerAddress.address1}
                              </Typography>
                              <Typography className="block text-13">
                                <Typography className="font-medium text-13">
                                  {_t("Street")}
                                  {" 2: "}
                                </Typography>
                                {customerAddress.address2}
                              </Typography>
                              <Typography className="block text-13">
                                <Typography className="font-medium text-13">
                                  {_t("City")}/{_t("ST")}/{_t("Zip")}
                                  {": "}
                                </Typography>
                                {customerAddress.cityStZip}
                              </Typography>
                              <div className="flex gap-2 justify-center mt-3">
                                <Button
                                  type="primary"
                                  className="w-fit"
                                  onClick={copyToClipboard}
                                >
                                  {_t("Yes")}
                                </Button>
                                <Button
                                  onClick={() => setContactAddress(false)}
                                >
                                  {_t("No")}
                                </Button>
                              </div>
                            </div>
                          </div>
                        }
                        placement="bottom"
                        trigger="click"
                        open={contactAddress}
                        onOpenChange={contactAddressChange}
                      >
                        <Button
                          type="primary"
                          className="w-full h-0 p-0 border-0 absolute"
                        ></Button>
                      </Popover>
                    </div>

                    {details.customer_id && (
                      <DirectoryFieldRedirectionIcon
                        directoryId={details.customer_id?.toString() ?? ""}
                        directoryTypeKey={
                          details.customer_id
                            ? getDirectaryKeyById(
                                details.cust_type === 1
                                  ? 2
                                  : Number(details.cust_type ?? ""),
                                undefined
                              )
                            : ""
                        }
                      />
                    )}

                    {["loading", "success", "error"].includes(
                      getStatusForField(loadingStatus, "customer_id")
                    ) && (
                      <FieldStatus
                        className="flex items-center"
                        iconProps={{
                          className: "!w-[15px] !h-[15px]",
                        }}
                        status={getStatusForField(loadingStatus, "customer_id")}
                      />
                    )}
                  </div>
                </li>
                {details.access_code && !isNoAccessCustomerAccessCodeField && (
                  <li className="flex sm:flex-row flex-col mb-3">
                    <Typography className="min-w-[140px] block font-semibold text-[#343a40e3]">
                      {_t("Access/Gate Code")}
                    </Typography>
                    <Typography className="text-primary-900 sm:bg-transparent bg-[#f4f5f6] sm:min-h-fit min-h-[34px] sm:p-0 p-1.5">
                      {HTMLEntities.decode(sanitizeString(details.access_code))}
                    </Typography>
                  </li>
                )}
                {details.customer_company && (
                  <li className="flex sm:flex-row flex-col mb-3">
                    <Typography className="min-w-[140px] block font-semibold text-[#343a40e3]">
                      {_t("Company")}
                    </Typography>
                    <Typography className="text-primary-900 sm:bg-transparent bg-[#f4f5f6] sm:min-h-fit min-h-[34px] sm:p-0 p-1.5">
                      {HTMLEntities.decode(
                        sanitizeString(details.customer_company)
                      )}
                    </Typography>
                  </li>
                )}
                {customerPhoneCellData.cellPhone && (
                  <li className="flex sm:flex-row flex-col mb-3">
                    <Typography className="min-w-[140px] block font-semibold text-[#343a40e3]">
                      {_t(customerPhoneCellData.label)}
                    </Typography>
                    <Typography className="text-primary-900 sm:bg-transparent bg-[#f4f5f6] sm:min-h-fit min-h-[34px] sm:p-0 p-1.5">
                      {HTMLEntities.decode(
                        sanitizeString(customerPhoneCellData.cellPhone)
                      )}
                    </Typography>
                  </li>
                )}
                {details.cust_email && (
                  <li className="flex sm:flex-row flex-col mb-3">
                    <Typography className="min-w-[140px] block font-semibold text-[#343a40e3]">
                      {_t("Email")}
                    </Typography>
                    <div className="flex gap-1 items-center sm:bg-transparent bg-[#f4f5f6] sm:min-h-fit min-h-[34px] sm:p-0 p-1.5">
                      <a
                        className="text-primary-900 hover:text-[#ff5400] hover:underline transition duration-500 ease-in-out cursor-pointer underline break-all"
                        href={`${routes.MANAGE_DIRECTORY.url}/${details?.customer_id}?type=${details?.cust_type}`}
                        target="_blank"
                      >
                        {details.cust_email}
                      </a>

                      <a
                        className="focus-within:bg-[#f0f0f0] rounded focus-visible:outline-none hover:!bg-[#f0f0f0]"
                        href=""
                      >
                        <Tooltip
                          title={_t("Send Email")}
                          className="cursor-pointer"
                        >
                          <div
                            onClick={(e) => {
                              e.preventDefault();
                              setSendEmailOpen(true);
                            }}
                            className="!w-6 !h-6 cursor-pointer flex items-center justify-center"
                          >
                            <FontAwesomeIcon
                              icon="fa-regular fa-envelope"
                              className="w-3.5 h-3.5 text-primary-900/80 group-hover/buttonHover:text-primary-900"
                            />
                          </div>
                        </Tooltip>
                      </a>
                    </div>
                  </li>
                )}
                {fullAddress && (
                  <li className="flex sm:flex-row flex-col">
                    <Typography className="min-w-[140px] block font-semibold text-[#343a40e3]">
                      {_t("Address")}
                    </Typography>
                    <ul className="w-full sm:bg-transparent bg-[#f4f5f6] sm:min-h-fit min-h-[34px] sm:p-0 p-1.5">
                      <li className="text-primary-900 text-sm">
                        {HTMLEntities.decode(
                          sanitizeString(customerAddress.address1)
                        )}
                      </li>
                      <li className="text-primary-900 text-sm">
                        {HTMLEntities.decode(
                          sanitizeString(customerAddress.address2)
                        )}
                      </li>
                      <li>
                        <Typography className="text-primary-900 text-sm">
                          {HTMLEntities.decode(
                            sanitizeString(customerAddress.cityStZip)
                          )}
                        </Typography>
                      </li>
                    </ul>
                  </li>
                )}
              </ul>
            </li>
            {showGoogleMap && (
              <li className="2xl:pl-0 sm:pl-[106px]">
                <GoogleMap
                  mapAddress={{
                    address1: details.cust_address1
                      ? details.cust_address1
                      : "",
                    address2: details.cust_address2
                      ? details.cust_address2
                      : "",
                    city: details.cust_city ? details.cust_city : "",
                    state: details.cust_state ? details.cust_state : "",
                    zip: details.cust_zip ? details.cust_zip : "",
                  }}
                  cssStyle={{ height: "152px", minWidth: "300px" }}
                  isEditable={false}
                  title={""}
                  id="customerAddressMap"
                  addressInfo={{}}
                  handleInputChange={function (
                    e: ChangeEvent<HTMLInputElement>
                  ): void {
                    throw new Error("Function not implemented.");
                  }}
                  handleSelectedLocation={function (
                    place: IdirAddrPlaceDetails
                  ): Promise<void> {
                    throw new Error("Function not implemented.");
                  }}
                  handleInputBlur={function (): Promise<void> {
                    throw new Error("Function not implemented.");
                  }}
                />
              </li>
            )}
          </ul>
        }
      />

      {IsOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={IsOpenSelectCustomer}
          closeDrawer={() => {
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleCustomerSelection(data as CustomerEmail[]);
          }}
          options={[CFConfig.customer_key]}
          selectedCustomer={selectedCustomerInDrawer}
          additionalContactDetails={1}
          groupCheckBox={true}
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
          }}
          isViewAttachment={false}
          selectedCustomer={selectedCustomer}
          openSendEmailSidebar={sendEmailOpen}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            "lead",
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          singleSelecte={false}
          emailApiCall={handleEmailApiCall}
          customEmailData={{
            body: "",
            subject: "",
          }}
          validationParams={{
            date_format: CFConfig.day_js_date_format,
            file_support_module_access: checkGlobalModulePermissionByKey(
              CFConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
            save_a_copy_of_sent_pdf,
          }}
        />
      )}
    </>
  );
};

export default CustomerDetails;
