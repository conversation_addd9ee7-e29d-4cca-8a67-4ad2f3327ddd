import { type RadioChangeEvent } from "antd";
import { useFormik } from "formik";
import isEmpty from "lodash/isEmpty";
import { useEffect, useMemo, useRef, useState } from "react";
import * as Yup from "yup";
import { useTranslation } from "~/hook";
// Atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { sanitizeString } from "~/helpers/helper";
import { getGConfig, getGSettings } from "~/zustand";
import type { InputRef } from "antd";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { useAppPODispatch, useAppPOSelector } from "../../../../redux/store";
import {
  filterOptionBySubstring,
  generateCostCodeLabel,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  addPOItems,
  getPOItems,
  updatePurchaseOrderItem,
} from "../../../../redux/action/POItemAction";
import { updatePODetail } from "../../../../redux/slices/poDetailSlice";
import { Form } from "@remix-run/react";
import {
  isValidId,
  toBoolean,
} from "~/modules/financials/pages/estimates/utils/common";
import { getGlobalTypes } from "~/zustand/global/types/slice";
import {
  keysOfItem,
  updatePOItems,
  updatePOItemWithID,
} from "../../../../redux/slices/poItemsSlice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { HtmlDecode } from "../../../../utils/function";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { Tooltip } from "~/shared/components/atoms/tooltip";

const PurchaseOrderItem = ({
  itemOpen,
  setItemOpen,
  isViewOnly = false,
  formData,
  isReadOnly,
  isItemAdd = false,
  isItemDetailDrawer = false,
  descEditable = false,
  onClose,
  setPOToView,
  ParentIsReadOnly = false,
  // purchaseOrderItems = [],
  selectedSectionName = "",
  ViewItemsKey,
  isTaxEnabled = false,
}: IPurchaseOrderItemProps) => {
  const { sectionKey, sectionId } = ViewItemsKey || {};
  const { _t } = useTranslation();
  const gConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const gModules = useGlobalModule();

  const { is_cidb_auto_save, has_project_based_cost_codes } = gSettings;
  const poName = HtmlDecode(gConfig?.module_singular_name) || "Purchase Order";
  const itemTypes: GType[] = getGlobalTypes();
  const itemTypesWithMarkup = useAppPOSelector(
    (state) => state.itemTypes.itemTypes
  );
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const unitCostRef = useRef<InputRef>(null);
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);

  const dispatch = useAppPODispatch();
  const { codeCostData } = useAppPOSelector((state) => state.costCode);
  const { purchaseOrderDetail: details } = useAppPOSelector(
    (state) => state.purchaseOrderDetail
  );

  const { purchaseOrderSectionItems } = useAppPOSelector(
    (state) => state?.purchaseOrderItems
  );

  const { filter } = useAppPOSelector((state) => state.comman);

  const purchaseOrderItems = useMemo(() => {
    if (!purchaseOrderSectionItems || !sectionKey) return [];

    const filterSet = new Set(filter);
    let items: IPOItemData[] = [];

    switch (sectionKey) {
      case "lumpsum":
      case "others":
        items = purchaseOrderSectionItems?.[sectionKey] ?? [];
        break;

      case "work_order":
        items =
          purchaseOrderSectionItems?.work_order?.find(
            (w) => w.work_order_id === Number(sectionId)
          )?.items ?? [];
        break;

      case "change_order":
        items =
          purchaseOrderSectionItems?.change_order?.find(
            (c) => c.change_order_id === Number(sectionId)
          )?.items ?? [];
        break;

      case "estimate":
        items =
          purchaseOrderSectionItems?.estimate?.flatMap((est) =>
            est.sections.filter((sec) => sec.section_id === String(sectionId))
          )[0]?.items ?? [];
        break;

      default:
        items = [];
    }

    const filtered = items
      ?.filter((el) =>
        !filter?.length ? true : filterSet.has(el?.item_type_key)
      )
      ?.sort((a, b) => a.purchase_order_item_no - b.purchase_order_item_no);

    return filtered;
  }, [filter, sectionKey, sectionId, purchaseOrderSectionItems]);

  const [markupType, setMarkupType] = useState<string>("markup_percent");
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);

  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [isAddItem, setIsAddItem] = useState<boolean>(isItemAdd);
  const [inputValues, setInputValues] = useState<Partial<IPOItemData>>({});
  const [submitAction, setSubmitAction] = useState<string>("");
  const [cidbItemExistPopupData, setCidbItemExistPopupData] =
    useState<ICidbItemExistPopupData | null>();
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units || []);
  };
  useEffect(() => {
    getUnit();
  }, []);

  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const validationSchema = Yup.object().shape({
    subject: Yup.string()
      .required("This field is required.")
      .test("not-blank", "This field is required.", (value) => {
        return !isEmpty(value) && !!value?.trim().length;
      }),
    item_type: Yup.string()
      .required("This field is required.")
      .test("not-blank", "This field is required.", (value) => {
        return !isEmpty(value);
      }),
  });
  const filteredItemsTypes = useMemo(
    () =>
      itemTypes?.filter(
        (item: Partial<GType>) => item?.type === "company_items"
      ),
    [itemTypes]
  );
  const reorderedItemsTypes = (() => {
    if (!Array.isArray(filteredItemsTypes)) return [];
    const items = [...filteredItemsTypes];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();
  const undefinedTypeMarkup = useMemo(() => {
    const marupUndefined = itemTypesWithMarkup?.find(
      (i: IWorkOrderType) => i.type_id?.toString() === ""
    )?.mark_up;
    return marupUndefined !== "null" && marupUndefined
      ? marupUndefined?.toString()
      : "";
  }, [itemTypesWithMarkup]);
  // const ParentIsReadOnly = useMemo(
  //   () =>
  //     Boolean(
  //       checkGlobalModulePermissionByKey(gConfig?.module_key) === "read_only"
  //     ),
  //   [gConfig?.module_key]
  // );
  const initialValues: Partial<IPOAddItem> = useMemo(() => {
    return isAddItem
      ? {
          ...inputValues,
          subject: "",
          item_type: "",
          is_markup_percentage: 1,
          add_item_to_database: is_cidb_auto_save,
          markup: undefinedTypeMarkup,
          // if needed add default values here
        }
      : {
          ...inputValues,
          is_markup_percentage: 1,
          ...formData,
          unit_cost:
            typeof formData?.unit_cost === "undefined"
              ? ""
              : (Number(formData?.unit_cost || 0) / 100)?.toFixed(2).toString(),
          markup: formData?.markup?.toString()
            ? formData.is_markup_percentage?.toString() == "0"
              ? (Number(formData.markup) / 100).toString()
              : formData.markup
            : "",
          add_item_to_database: formData?.item_on_database,
        };
  }, [isAddItem, formData, inputValues, is_cidb_auto_save]);

  useEffect(() => {
    if (!isAddItem) {
      if (formData?.is_markup_percentage === 1) {
        setMarkupType("markup_percent");
      } else {
        setMarkupType("markup_dolar");
      }
    } else {
      setMarkupType("markup_percent");
    }
  }, [formData?.is_markup_percentage, isAddItem]);

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      if (isAddItem) {
        let _markup = values?.markup?.toString()
          ? values?.markup == "-" || !values?.markup
            ? 0
            : values.markup
          : "";

        const costcodeId = values?.cost_code_id ?? "";

        const purchaseOrderItem = {
          purchase_order_id: details?.purchase_order_id,
          items: [
            {
              ...values,
              markup: _markup?.toString()
                ? values?.is_markup_percentage === 0
                  ? (Number(_markup) * 100)?.toString()
                  : _markup?.toString()
                : null,
              cost_code_id: costcodeId,
              unit_cost: Number(values?.unit_cost) * 100,
              quantity: values?.quantity ? Number(values?.quantity) : "",
              total: Number(values?.quantity) * Number(values?.unit_cost) * 100,
            },
          ],
        };

        const reqPayload = getValuableObj(purchaseOrderItem);
        const response = await dispatch(
          addPOItems(reqPayload as unknown as IAddPOItemParams)
        );
        const AddResult = response.payload as IPOAddItemsApiRes;
        if (AddResult?.success) {
          const newItem = AddResult?.data?.items?.[0];
          if (newItem) {
            // dispatch(addItems({ itemData: newItem }));
            dispatch(
              getPOItems({
                module_id: CFConfig.purchase_order_module_id,
                purchase_order_id: details?.purchase_order_id as number,
                need_section: 1,
                project_id: Number(details?.pro_id),
                isHideLoading: true,
              })
            );
          }
          if (submitAction === "save_n_close") {
            onClose(descEditable ? { ...values } : "");
          } else if (submitAction === "save_n_add_another") {
            setIsAddItem(true);
            // setCostChangeConfirmation(null);
            // setShowCostChangeTooltip(false);
          } else {
            const nextItemIndex = formik.status;
            setPOToView?.(purchaseOrderItems?.[nextItemIndex] ?? {});
          }
          // TODO: Figure out this
          // await dispatch(getEstimateItems({ estimate_id }));
          resetForm({ values: { ...initialValues } });
        } else {
          if (
            isValidId(
              (response?.payload as ICidbItemExistPopupData)?.data
                ?.reference_item_id
            )
          ) {
            setCidbItemExistPopupData(
              response?.payload as ICidbItemExistPopupData
            );
          } else {
            notification.error({
              description: (response?.payload as ICidbItemExistPopupData)
                ?.message,
            });
          }
          setSubmitting(false);
        }
      } else {
        const _markup = values?.markup?.toString()
          ? values?.markup == "-" || !values?.markup
            ? 0
            : values.markup
          : "";
        const costcodeId = values?.cost_code_id ?? "";

        const purchaseOrderItem = {
          purchase_order_id: details?.purchase_order_id ?? 0,
          items: [
            {
              ...values,
              cost_code_id: costcodeId,
              unit_cost: Number(values?.unit_cost) * 100,
              quantity: values?.quantity ? Number(values?.quantity) : "",
              markup: _markup?.toString()
                ? values?.is_markup_percentage === 0
                  ? (Number(_markup) * 100)?.toString()
                  : _markup?.toString()
                : null,
              total: Number(values?.quantity) * Number(values?.unit_cost) * 100,
            },
          ],
        };

        const reqPayload = getValuableObj(
          purchaseOrderItem
        ) as IUpdatePOItemParams;
        const response = await dispatch(updatePurchaseOrderItem(reqPayload));

        if ((response?.payload as { success: boolean })?.success) {
          const detail = response?.payload?.data?.detail;
          if (Object.keys(detail || {})?.length > 0) {
            if (detail?.billing_status_key != details?.billing_status_key) {
              dispatch(updatePODetail(detail));
            }
          }
          const updatedItem =
            (
              response?.payload as {
                data: {
                  items: IPOAddItem[];
                  detail: IPODetailData;
                };
              }
            )?.data?.items?.[0] ?? purchaseOrderItem.items[0];
          if (values?.cost_code_id) {
            const costDetailOne = codeCostData?.find((item) => {
              return (
                values?.cost_code_id?.toString() === item?.code_id?.toString()
              );
            });
            if (costDetailOne && updatedItem) {
              updatedItem.cost_code = costDetailOne.csi_code;
              updatedItem.cost_code_name = costDetailOne.cost_code_name;
            }
          } else if (!values?.cost_code_id) {
            updatedItem.cost_code_id = "";
            updatedItem.cost_code = "";
            updatedItem.cost_code_name = "";
          }
          if (updatedItem?.add_item_to_database) {
            updatedItem.item_on_database = updatedItem?.add_item_to_database;
          }
          dispatch(updatePODetail([{ updatedItem }]));
          dispatch(
            updatePOItemWithID({
              itemId: updatedItem?.item_id || 0,
              updatedItem: updatedItem,
              sectionKey: selectedSectionName || "",
              sectionID:
                selectedSectionName === keysOfItem.ESTIMATE.ITEM
                  ? updatedItem?.estimate_id
                  : selectedSectionName === keysOfItem.CHANGE_ORDER.ITEM
                  ? updatedItem?.change_order_id
                  : selectedSectionName === keysOfItem.WORK_ORDER.ITEM
                  ? updatedItem?.work_order_id
                  : 0,
              EstimateSectionId: updatedItem?.section_id,
            })
          );
          dispatch(
            updatePOItems({
              // sectionId: singleSection?.section_id,
              itemId: updatedItem?.item_id,
              updatedItem,
            })
          );
          if (submitAction === "save_n_close") {
            onClose(descEditable ? { ...values } : "");
          } else if (submitAction === "save_n_add_another") {
            resetForm({ values: { ...initialValues } });
            setIsAddItem(true);
            // setCostChangeConfirmation(null);
            // setShowCostChangeTooltip(false);
          } else {
            const nextItemIndex = formik.status;
            resetForm({ values: { ...initialValues } });
            setPOToView?.(purchaseOrderItems?.[nextItemIndex] ?? {});
          }
          // await dispatch(getEstimateItems({ estimate_id }));

          if (
            (response?.payload as { po_status_message: string })
              ?.po_status_message
          ) {
            notification.error({
              description: (response?.payload as { po_status_message: string })
                ?.po_status_message,
            });
          }
        } else {
          if (
            isValidId(
              (response?.payload as ICidbItemExistPopupData)?.data
                ?.reference_item_id
            )
          ) {
            setCidbItemExistPopupData(
              response?.payload as ICidbItemExistPopupData
            );
          } else {
            notification.error({
              description: (response?.payload as ICidbItemExistPopupData)
                ?.message,
            });
          }
        }
      }
      setSubmitting(false);
    },
  });
  const memoizedFormData = useMemo(
    () => formData,
    [formData?.reference_item_id, formData?.item_id, formData?.item_type]
  );
  const {
    handleSubmit,
    setFieldValue,
    values,
    errors,
    isSubmitting,
    resetForm,
  } = formik;
  const applyGlobalTax = useMemo(() => {
    const taxSettingsMap: Record<number, number> = {
      [CFConfig.material_teb_id]: gSettings.is_taxable_material_items,
      [CFConfig.equipment_teb_id]: gSettings.is_taxable_equipment_items,
      [CFConfig.labor_teb_id]: gSettings.is_taxable_labor_items,
      [CFConfig.subcontractor_teb_id]: gSettings.is_taxable_subcontractor_items,
      [CFConfig.other_items_teb_id]: gSettings.is_taxable_other_items,
    };
    return taxSettingsMap[Number(values.item_type)] ?? 0;
  }, [values?.item_type, gSettings]);

  useEffect(() => {
    if (isAddItem) {
      setFieldValue("apply_global_tax", applyGlobalTax);
    }
  }, [applyGlobalTax, memoizedFormData, setFieldValue]);
  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (Number(formData?.unit_cost) === 0) {
        setInputValues({
          ...formData,
          unit_cost: "",
          total: "0",
        });
      }
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values?.unit &&
        !isEmpty(values?.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  useMemo(() => {
    if (
      formik?.values?.unit_cost !== "" &&
      !isEmpty(formik?.values?.unit_cost) &&
      formik?.values?.unit_cost !== undefined &&
      formik?.values?.unit !== "" &&
      !isEmpty(formik?.values?.unit) &&
      formik?.values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [isAddItem, formData, formik?.values]);

  const currentItemIndex = useMemo(() => {
    const curItemIndex = purchaseOrderItems?.findIndex(
      (i) => i?.item_id === formData?.item_id
    );

    return curItemIndex;
  }, [purchaseOrderItems, formData]);

  const handlePrevItem = () => {
    if (Number(currentItemIndex) && Number(currentItemIndex) > 0) {
      if (formik.dirty && !isViewOnly && !ParentIsReadOnly) {
        formik.submitForm();
        handleSaveItem("save_n_next_prev");
        formik.setStatus(Number(currentItemIndex) - 1);
      } else {
        setPOToView?.(purchaseOrderItems?.[Number(currentItemIndex) - 1] ?? {});
      }
    }
  };

  const handleNextItem = () => {
    if (Number(currentItemIndex) < (purchaseOrderItems?.length ?? 0) - 1) {
      if (formik.dirty && !isViewOnly && !ParentIsReadOnly) {
        formik.submitForm();
        handleSaveItem("save_n_next_prev");
        formik.setStatus(Number(currentItemIndex) + 1);
      } else {
        setPOToView?.(purchaseOrderItems?.[Number(currentItemIndex) + 1] ?? {});
      }
    }
  };

  const handleSaveItem = (key: string) => {
    setSubmitAction(key);
  };

  useEffect(() => {
    dispatch(
      getCostCode({
        project_id:
          has_project_based_cost_codes?.toString() === "1"
            ? details?.pro_id
            : undefined,
      })
    );
  }, [details?.pro_id, has_project_based_cost_codes]);

  useEffect(() => {
    setIsAddItem(isItemAdd);
    formik.setValues(initialValues);
  }, [formData, itemOpen, isItemAdd]);

  useEffect(() => {
    if (isAddItem) resetForm({ values: { ...initialValues } });
  }, [isAddItem]);

  useEffect(() => {
    if (
      values?.quantity !== undefined &&
      values?.unit_cost !== undefined &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
      setMainTotal(Number(values?.total));
    } else {
      setFieldValue("total", "");
      setMainTotal("");
    }
    if (
      values?.total?.toString() !== "" &&
      Number(values?.total) &&
      values?.total &&
      values?.markup !== "" &&
      values?.markup
    ) {
      const _markup =
        values?.markup == "-" || !values?.markup ? 0 : values.markup;

      if (markupType === "markup_percent") {
        const markup = (Number(values?.total) * Number(_markup)) / 100;
        setFieldValue("markup_amount", markup);
        // const total = Number(values?.quantity) * Number(values?.unit_cost);
        // setMainTotal(total);
      } else {
        const markup = Number(_markup);
        const markupPercentage =
          Number(markup) === 0
            ? 0
            : (Number(markup) * 100) / (Number(values?.total) || 1) - 100;
        setFieldValue("markup_amount", markupPercentage.toFixed(2));
        // const total = Number(values?.quantity) * Number(values?.unit_cost);
        // setMainTotal(total);
      }
    } else {
      setFieldValue("markup_amount", "");
      setMainTotal(Number(values?.total));
    }
  }, [
    values?.quantity,
    values?.unit_cost,
    values?.total,
    values?.markup,
    values?.is_markup_percentage,
    markupType,
  ]);

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show = isAddItem || Number(values?.reference_item_id ?? 0) === 0;

    const disable =
      !!values?.item_on_database ||
      (!!values?.reference_item_id && Number(values?.reference_item_id) > 0);

    return { show, disable };
  }, [isAddItem, values]);

  const assignedTo = useMemo(() => {
    if (values?.assigned_to != 0 && values?.assigned_to) {
      const assigned_to = [
        {
          display_name: values?.assignee_name,
          user_id: Number(values?.assigned_to),
          // TODO:B Figure this out
          // image: values?.user_image,
          contact_id: Number(values?.assigned_to_contact_id) ?? 0,
          type: values?.assignee_type,
          type_key: getDirectaryKeyById(Number(values?.assignee_type), gConfig),
          image:
            values?.assigned_to_contact_id === 0 ||
            values?.assigned_to_contact_id === null ||
            !values?.assigned_to_contact_id
              ? values?.user_image
              : "",
          // TODO:B Figure this out
          // type_name: values?.type_name,
        },
      ];
      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [
    values?.assigned_to,
    values?.assignee_name,
    values?.assignee_type,
    values?.assigned_to_contact_id,
    // values?.type_name,
  ]);

  const costCodeOptions = useMemo(() => {
    const filteredCodeCostData = codeCostData?.filter(
      (item) =>
        (item?.cost_code_name?.toString() !== "" ||
          item?.csi_code?.toString() !== "") &&
        Number(item?.parent_id) > 0
    );
    let costCodeOpts = filteredCodeCostData?.map((item: ICostCode) => {
      return {
        // label: `${item?.cost_code_name}${
        //   item?.csi_code ? ` (${item?.csi_code})` : ""
        // }${item?.is_deleted === 1 ? ` (Archived)` : ""}`,
        label: `${generateCostCodeLabel({
          name: item?.cost_code_name,
          code: item?.csi_code,
          isArchived: false,
          isAllowCodeWithoutName: true,
        })}`,
        value: item?.code_id,
      };
    });

    if (!isAddItem && formData?.is_deleted === 1) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          // label: `${formData?.cost_code_name}${
          //   formData?.csi_code ? ` (${formData?.csi_code})` : ""
          // } (Archived)`,
          label: `${generateCostCodeLabel({
            name: formData?.cost_code_name?.toString() ?? "",
            code: formData?.cost_code?.toString() ?? "",
            isArchived: false,
          })}`,
          value: formData?.cost_code_id?.toString() ?? "",
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData]);

  const saveItemKey = useMemo(() => {
    const itemType = itemTypes?.find(
      (i: GType) => i.type_id?.toString() === values?.item_type?.toString()
    );

    return itemType?.name;
  }, [itemTypes, values?.item_type]);
  const formattedMarkup =
    markupType === "markup_percent"
      ? values?.markup_amount?.toString()
        ? formatter(Number(values.markup_amount).toFixed(2)).value_with_symbol
        : formatter("0.00").value_with_symbol
      : !values?.markup_amount || Number(values?.markup_amount) === 0
      ? "0.00%"
      : `${Number(values?.markup_amount || 0).toFixed(2)}%`;
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  const MARKUP_OPTIONS = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {formatter().currency_symbol}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];
  return (
    <>
      <Drawer
        open={itemOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-cart-circle-plus"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {!isAddItem ? _t(`${poName} Item`) : _t(`Add ${poName} Item`)}
              </Header>
              {!isAddItem ? (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  {formik.isSubmitting &&
                  submitAction === "save_n_next_prev" ? (
                    <FontAwesomeIcon
                      className="w-3.5 h-3.5 fa-spin"
                      icon="fa-duotone fa-solid fa-spinner-third"
                    />
                  ) : (
                    ""
                  )}
                  {purchaseOrderItems?.length &&
                  purchaseOrderItems.length > 1 ? (
                    <>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Previous")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-chevron-left"
                        className="item-pre-next-button disabled:bg-transparent"
                        onClick={handlePrevItem}
                        disabled={
                          Number(currentItemIndex) <= 0 || formik.isSubmitting
                        }
                      />
                      <ButtonWithTooltip
                        tooltipTitle={_t("Next")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-chevron-right"
                        className="item-pre-next-button disabled:bg-transparent"
                        onClick={handleNextItem}
                        disabled={
                          Number(currentItemIndex) >=
                            (purchaseOrderItems?.length ?? 0) - 1 ||
                          formik.isSubmitting
                        }
                      />
                    </>
                  ) : (
                    ""
                  )}
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <Form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    id="subject"
                    value={HtmlDecode(values?.subject)}
                    errorMessage={formik.touched?.subject ? errors.subject : ""}
                    disabled={isViewOnly}
                    isRequired={true}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value?.trimStart());
                    }}
                    autoComplete="off"
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                {(values?.is_freight_charge_item == 1 ||
                  values?.is_discount_item == 1) &&
                !isAddItem ? (
                  ""
                ) : (
                  <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                    <div className="w-full">
                      <SelectField
                        label={_t("Item Type")}
                        labelPlacement="top"
                        isRequired={true}
                        name="item_type"
                        id="item_type"
                        errorMessage={
                          formik.touched?.item_type ? errors.item_type : ""
                        }
                        disabled={
                          isViewOnly ||
                          !itemTypeAndSaveItemToListField.show ||
                          itemTypeAndSaveItemToListField.disable
                        }
                        value={HtmlDecode(values?.item_type?.toString())}
                        onChange={(value) => {
                          setFieldValue("item_type", value);

                          // TODO: Verify Markup Logic Once
                          // const itemType = itemTypes?.find(
                          //   (i) => i?.value?.toString() === value?.toString()
                          // );

                          // if (isAddItem && !isMuPercentFieldChanged) {
                          //   setFieldValue("markup", itemType?.mark_up || "");
                          // }
                          const itemType = itemTypesWithMarkup?.find(
                            (i: IWorkOrderType) =>
                              i.type_id?.toString() === value?.toString()
                          );

                          setFieldValue("markup", itemType?.mark_up || "");
                          setFieldValue(
                            "unit",
                            value?.toString() === "163" ? "Hrs" : ""
                          );
                        }}
                        options={reorderedItemsTypes.map((item: GType) => ({
                          label: (
                            <div className="flex items-center gap-1.5">
                              <FontAwesomeIcon
                                icon={getItemTypeIcon({
                                  type: item?.type_id?.toString(),
                                })}
                              />
                              {item?.name}
                            </div>
                          ),
                          value: item.type_id,
                          ...item,
                        }))}
                        notFoundContent={
                          <NoRecords
                            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                          />
                        }
                      />
                    </div>
                    <div className="w-full">
                      <InputField
                        label={_t("Item SKU")}
                        labelPlacement="top"
                        name="sku"
                        id="sku"
                        errorMessage={formik.touched?.sku ? errors.sku : ""}
                        disabled={isViewOnly}
                        value={HtmlDecode(values?.sku)}
                        onChange={(e) => {
                          setFieldValue("sku", e.target.value);
                        }}
                        onPressEnter={handleEnterKeyPress}
                      />
                    </div>
                  </div>
                )}
                <div className="w-full">
                  <ButtonField
                    label={_t("Assigned To")}
                    name="assigned_to"
                    labelPlacement="top"
                    disabled={isViewOnly || ParentIsReadOnly}
                    value={HtmlDecode(
                      values?.assigned_to != 0 && values?.assigned_to
                        ? String(values?.assignee_name)
                        : ""
                    )}
                    onClick={() => {
                      setIsOpenSelectAssignedTo(true);
                    }}
                    avatarProps={{
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(String(values?.assignee_name))
                        ),
                        image:
                          values?.assigned_to_contact_id === 0 ||
                          values?.assigned_to_contact_id === null ||
                          !values?.assigned_to_contact_id
                            ? values?.user_image
                            : "",
                      },
                    }}
                    // TODO: Verify, if need then uncomment this
                    addonBefore={
                      values?.assigned_to && values?.assigned_to != 0 ? (
                        <div className="flex items-center gap-1">
                          <ContactDetailsButton
                            onClick={(e) => {
                              e.stopPropagation();
                              setContactDetailDialogOpen(true);
                            }}
                          />
                          {/* <DirectoryFieldRedirectionIcon
                            directoryId={
                              values?.assigned_to?.toString() || ""
                            }
                            directoryTypeKey={values?.type_key || ""}
                          /> */}
                        </div>
                      ) : (
                        <></>
                      )
                    }
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    value={
                      isItemDetailDrawer
                        ? values?.cost_code_name
                        : values?.cost_code_id
                        ? (() => {
                            const selectedOption = costCodeOptions?.find(
                              (item) =>
                                values?.cost_code_id?.toString() ===
                                item?.value?.toString()
                            );
                            return selectedOption
                              ? selectedOption
                              : [
                                  {
                                    label: `${HtmlDecode(
                                      values?.cost_code_name
                                    )} (Archived)`,
                                    id: values?.cost_code_id,
                                  },
                                ];
                          })()
                        : []
                    }
                    onChange={(value) => {
                      setFieldValue("cost_code_id", value);
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    options={costCodeOptions}
                    disabled={
                      isItemDetailDrawer || isViewOnly || ParentIsReadOnly
                    }
                    allowClear={true}
                    onClear={() => {
                      setFieldValue("cost_code_id", "");
                      setFieldValue("cost_code", "");
                      setFieldValue("cost_code_name", "");
                    }}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Qty")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName={`!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input`}
                        placeholder={_t("Item Quantity")}
                        disabled={isViewOnly}
                        errorMessage={errors.quantity}
                        onPaste={handlePaste}
                        // min={0}
                        // max={999999}
                        // maxLength={6}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ? "flex items-center justify-end" : ""
                        }
                        defaultValue={
                          isNaN(Number(values?.quantity)) ||
                          typeof values?.quantity === "undefined" ||
                          values?.quantity === 0
                            ? ""
                            : Number(values?.quantity)
                        }
                        value={
                          isNaN(Number(values?.quantity)) ||
                          typeof values?.quantity === "undefined" ||
                          values?.quantity === 0
                            ? ""
                            : Number(values?.quantity)
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits: 6,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            // allowNegative:
                            //   initialValues?.is_discount_item?.toString() ==
                            //   "1",
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                      >
                        {!isViewOnly && (
                          <>
                            {showUnitInputs ? (
                              <div className="flex gap-2">
                                <div className="w-[calc(100%-70px)]">
                                  <InputNumberField
                                    name="unit_cost"
                                    id="unit_cost"
                                    rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                    placeholder={_t("Item Unit Cost")}
                                    disabled={isViewOnly}
                                    // min={0}
                                    // max={999999}
                                    // maxLength={10}
                                    labelPlacement="left"
                                    errorMessage={errors.unit_cost}
                                    onPaste={handlePaste}
                                    autoFocus={Boolean(
                                      values?.unit_cost &&
                                        !isEmpty(values?.unit_cost) &&
                                        values?.unit &&
                                        !isEmpty(values?.unit)
                                    )}
                                    defaultValue={
                                      isNaN(Number(values?.unit_cost)) ||
                                      typeof values?.unit_cost ===
                                        "undefined" ||
                                      values?.unit_cost == 0
                                        ? ""
                                        : Number(values?.unit_cost)
                                    }
                                    value={
                                      isNaN(Number(values?.unit_cost)) ||
                                      typeof values?.unit_cost ===
                                        "undefined" ||
                                      values?.unit_cost == 0
                                        ? ""
                                        : Number(values?.unit_cost)
                                    }
                                    onChange={(value) => {
                                      setFieldValue(
                                        "unit_cost",
                                        value?.toString()
                                      );
                                    }}
                                    formatter={(value, info) => {
                                      return inputFormatter(value?.toString())
                                        .value;
                                    }}
                                    parser={(value) => {
                                      const inputValue = value
                                        ? unformatted(value.toString())
                                        : "";
                                      return inputValue;
                                    }}
                                    onKeyDown={(event) =>
                                      onKeyDownCurrency(event, {
                                        integerDigits: 10,
                                        decimalDigits: 2,
                                        unformatted,
                                        // allowNegative:
                                        //   initialValues?.is_discount_item?.toString() ==
                                        //   "1",
                                        allowNegative: false,
                                        decimalSeparator:
                                          inputFormatter().decimal_separator,
                                      })
                                    }
                                    onBlur={handleFocusOut}
                                  />
                                </div>
                                {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                  <div className="w-[62px]">
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      labelPlacement="left"
                                      maxLength={15}
                                      value={formik?.values?.unit || null}
                                      iconView={true}
                                      disabled={isViewOnly}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitData.map((type) => ({
                                          label: type.name.toString(),
                                          value: type.name.toString(),
                                        })) ?? []
                                      }
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      onChange={(value) => {
                                        formik?.setFieldValue(
                                          "unit",
                                          value?.toString()
                                        );
                                      }}
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onInputKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          const value =
                                            e?.currentTarget?.value?.trim();
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitData?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewTypeName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        } else if (
                                          e?.currentTarget?.value?.length >
                                            14 &&
                                          e.key?.length === 1
                                        ) {
                                          e.preventDefault();
                                        }
                                      }}
                                      onClear={() => {
                                        formik.handleChange("");
                                      }}
                                      onBlur={handleFocusOut}
                                      errorMessage={errors.unit}
                                    />
                                  </div>
                                ) : (
                                  <div className="w-11">
                                    <InputField
                                      className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                        !showUnitInputs && "!hidden"
                                      }`}
                                      placeholder={_t("Unit")}
                                      labelPlacement="left"
                                      errorMessage={errors.unit}
                                      maxLength={15}
                                      onBlur={handleFocusOut}
                                      value={values?.unit}
                                      disabled={isViewOnly}
                                      onPaste={handlePaste}
                                      type="text"
                                      onChange={(e) => {
                                        setFieldValue("unit", e.target.value);
                                      }}
                                      onPressEnter={handleEnterKeyPress}
                                    />
                                  </div>
                                )}
                              </div>
                            ) : (
                              <Typography
                                className="text-[#008000] text-13 cursor-pointer font-medium"
                                onClick={handleParagraphClick}
                              >
                                {
                                  formatter(
                                    Number(values?.unit_cost).toFixed(2)
                                  ).value
                                }
                                /{values?.unit}
                              </Typography>
                            )}
                          </>
                        )}
                        {isViewOnly &&
                          (!isEmpty(values?.unit_cost) &&
                          values?.unit_cost !== 0.0 &&
                          values?.unit_cost !== "0.00" &&
                          !isEmpty(values?.unit) &&
                          !!values?.unit ? (
                            <Typography
                              className={`text-[#008000] font-medium text-13 ${
                                isViewOnly ? "cursor-no-drop" : ""
                              }`}
                            >
                              {values?.unit_cost}/{values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <InputField
                                ref={unitCostRef}
                                className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Item Unit Cost")}
                                type="number"
                                name="unit_cost"
                                id="unit_cost"
                                disabled={isViewOnly}
                                onPaste={handlePaste}
                                value={values?.unit_cost}
                                onChange={() => {}}
                                onPressEnter={handleEnterKeyPress}
                              />
                              <div className="w-[40px] min-w-[40px]">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  name="unit"
                                  id="unit"
                                  disabled={isViewOnly}
                                  onPaste={handlePaste}
                                  value={values?.unit}
                                  type="text"
                                  onChange={() => {}}
                                  onPressEnter={handleEnterKeyPress}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="my-2 border-t border-dashed border-[#ddd] relative">
                    <div className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                      <FontAwesomeIcon
                        className="w-3 h-3 text-primary-900 dark:text-white"
                        icon="fa-regular fa-equals"
                      />
                    </div>
                  </li>
                  <li className="flex justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {values?.total?.toString() === ""
                          ? `${formatter("0.00").value_with_symbol}`
                          : `${
                              formatter(Number(values?.total || 0).toFixed(2))
                                .value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="grid gap-2 mt-2.5 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          value={markupType}
                          options={MARKUP_OPTIONS}
                          disabled={isViewOnly}
                          className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                          activeclassName="active:bg-[#ffffff]"
                          onChange={(e: RadioChangeEvent) => {
                            setMarkupType(e.target.value);
                            setFieldValue("markup", "");
                            if (e.target.value === "markup_percent") {
                              setFieldValue("is_markup_percentage", 1);
                            } else {
                              setFieldValue("is_markup_percentage", 0);
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${
                              formatter().currency_symbol
                            } -- Add the ${
                              formatter().currency_symbol
                            } amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <div className="sm:w-40 w-28">
                      <InputField
                        className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                        placeholder={
                          markupType === "markup_percent"
                            ? _t("Item Markup") + " %"
                            : _t("Total Sales Price")
                        }
                        onChange={(e) => {
                          const inputValue = e.target.value;
                          if (values?.markup === "0" && inputValue === "-") {
                            return;
                          }
                          if (
                            inputValue === "" ||
                            inputValue === "-" ||
                            !isNaN(Number(inputValue))
                          ) {
                            setFieldValue("markup", inputValue);
                          }
                        }}
                        value={values?.markup}
                        onPaste={handlePaste}
                        labelPlacement="left"
                        type="text"
                        disabled={isViewOnly}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits:
                              markupType === "markup_percent" ? 3 : 8,
                            decimalDigits:
                              markupType === "markup_percent" ? 0 : 2,
                            unformatted,
                            allowNegative: false,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                      />
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("MU (Reference Only)")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={true}
                      >
                        {formattedMarkup}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    required={false}
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    disabled={
                      isViewOnly &&
                      // TODO:B Figureout  alternative of this for PO
                      // formData?.is_added_item !== 1 &&
                      !descEditable
                    }
                    value={HtmlDecode(values?.description)}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={isViewOnly}
                    value={HtmlDecode(values?.internal_notes || "")}
                    onChange={(e) => {
                      setFieldValue("internal_notes", e.target.value);
                    }}
                  />
                </div>
                <div className="grid gap-3">
                  {!isItemDetailDrawer && values.item_type && (
                    <CheckBox
                      className="gap-1.5 text-primary-900 w-fit"
                      checked={
                        !!values?.add_item_to_database ||
                        isValidId(values?.reference_item_id)
                      }
                      disabled={
                        !isAddItem &&
                        (isViewOnly ||
                          toBoolean(formData?.item_on_database) ||
                          isValidId(formData?.reference_item_id))
                        // || (toBoolean(formData?.one_build_id) &&
                        //   !isValidId(formData?.reference_item_id))
                      }
                      onChange={(event) => {
                        const valueToSet: number = event.target.checked ? 1 : 0;
                        setFieldValue("add_item_to_database", valueToSet);
                      }}
                    >
                      {_t(`Save this item into my ${saveItemKey} Items list?`)}
                    </CheckBox>
                  )}
                  {/* https://app.clickup.com/t/86cyz48w1 --> Not able to see 1 option in PHP same one is enable in remix pls take a look -- php condition -- if (in_array(strtoupper($settings['qb_country']), GLOBAL_TAX_COUNTRY) || $qb_sync_enabled == 0)*/}
                  {!isItemDetailDrawer && isTaxEnabled && (
                    <CheckBox
                      disabled={isViewOnly}
                      className="gap-1.5 text-primary-900 w-fit"
                      checked={!!values?.apply_global_tax}
                      onChange={(event) => {
                        const valueToSet: number = event.target.checked ? 1 : 0;
                        setFieldValue("apply_global_tax", valueToSet);
                      }}
                    >
                      {_t("Collect Tax on this Item?")}
                    </CheckBox>
                  )}
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          {((!isItemDetailDrawer && isAddItem) || descEditable) && (
            <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                disabled={isSubmitting || ParentIsReadOnly}
                isLoading={submitAction === "save_n_close" && isSubmitting}
                buttonText={_t("Save & Close")}
                onClick={() => handleSaveItem("save_n_close")}
              />
              {!isItemDetailDrawer && isAddItem && (
                <PrimaryButton
                  htmlType="submit"
                  buttonText={_t("Save & Add Another Item")}
                  disabled={isSubmitting || ParentIsReadOnly}
                  isLoading={
                    submitAction === "save_n_add_another" && isSubmitting
                  }
                  onClick={() => {
                    handleSaveItem("save_n_add_another");
                    setSubmitAction("save_n_add_another"); // Set the submit action
                  }}
                />
              )}
            </div>
          )}
          {isAddItem === false && (
            <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                buttonText={_t("Save & Close")}
                onClick={() => handleSaveItem("save_n_close")}
                isLoading={
                  submitAction === "save_n_close" && formik.isSubmitting
                }
                disabled={isSubmitting || ParentIsReadOnly || isReadOnly}
              />
              {!isItemDetailDrawer && (
                <PrimaryButton
                  htmlType="submit"
                  buttonText={_t("Save & Add Another Item")}
                  disabled={isSubmitting || isReadOnly || ParentIsReadOnly}
                  isLoading={
                    submitAction === "save_n_add_another" && isSubmitting
                  }
                  onClick={() => handleSaveItem("save_n_add_another")}
                />
              )}
            </div>
          )}
        </Form>
      </Drawer>

      {/* Assigned To Selection Drawer */}
      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            if (data?.[0]) {
              // const assigneeName = `${data[0].display_name} ${
              //   (data as CustomerEmail[])[0]?.company_name
              //     ? `(${(data as CustomerEmail[])[0]?.company_name})`
              //     : ""
              // }`;
              // if (data?.[0].contact_id == "0") {
              setFieldValue("assigned_to_contact_id", data[0].contact_id);
              // }
              setFieldValue("assigned_to", data[0].user_id);
              setFieldValue("assignee_name", data[0].display_name);
              setFieldValue(
                "assignee_type",
                data[0].type ||
                  getDirectaryIdByKey(data[0].type_key as CustomerTabs, gConfig)
              );
              setFieldValue("type_key", data[0].type_key);
              setFieldValue("type_name", data[0].type_name);
              setFieldValue("user_image", data?.[0]?.image);
            } else {
              setFieldValue("assigned_to", 0);
              setFieldValue("assignee_name", "");
            }
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.misc_contact_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={assignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={details?.pro_id as number}
          activeTab={CFConfig.contractor_key}
        />
      )}

      {contactDetailDialogOpen && (
        <ContactDetails
          isOpenContact={contactDetailDialogOpen}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isReadOnly}
          additional_contact_id={values?.assigned_to_contact_id}
        />
      )}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          CFConfig.employee_key,
          "my_crew",
          CFConfig.customer_key,
          CFConfig.lead_key,
          CFConfig.contractor_key,
          CFConfig.vendor_key,
          CFConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />

      {isValidId(cidbItemExistPopupData?.data?.reference_item_id) && (
        <ConfirmModal
          isOpen={isValidId(cidbItemExistPopupData?.data?.reference_item_id)}
          modaltitle={_t("This item already exists")}
          description={
            cidbItemExistPopupData?.message ??
            `There is already an item associated with this name in your CIDB. Would you like to rename the current item or import the existing item from your CIDB?`
          }
          onAccept={() => {
            setFieldValue(
              "reference_item_id",
              cidbItemExistPopupData?.data?.reference_item_id
            );
            setFieldValue("add_item_to_database", 0);
            setCidbItemExistPopupData(null);
            handleSubmit();
          }}
          yesButtonLabel="Use Existing"
          noButtonLabel="Rename"
          onDecline={() => setCidbItemExistPopupData(null)}
          onCloseModal={() => setCidbItemExistPopupData(null)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-trash-can"
        />
      )}
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
    </>
  );
};

export default PurchaseOrderItem;
