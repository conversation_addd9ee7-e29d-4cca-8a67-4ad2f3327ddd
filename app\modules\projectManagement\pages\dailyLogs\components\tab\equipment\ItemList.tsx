// Atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// Hook
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";

// Other
import { updateEquipmentsDelivered } from "../../../redux/action";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { displayTimeFormat } from "~/shared/utils/helper/defaultDateFormat";
import { fieldStatus } from "../../../utils/constasnts";
import delay from "lodash/delay";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { useAppDLDispatch } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { AddMyList } from "../sidebar/addMyList";

const ItemList = ({
  EquipmentItem,
  isReadOnly,
  onDeleteEquipmentDeli,
  onAddEquipmentDeli,
}: IDeliveredEquipmentsList) => {
  const notesInpRef = useRef<HTMLInputElement>(null);
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const loadingStatusRef = useRef(fieldStatus);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [inputValues, setInputValues] = useState<IDeliveredEquipment>();
  const [addItemsMaterialId, setAddItemsMaterialId] = useState<number>(0);
  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find((item) => item?.field === field);

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleChangeDate = (val: string) => {
    setInputValues({
      ...inputValues,
      delivery_time: val,
    });
    handleUpdateField({
      deliveryTime: val,
    });
  };

  const handleUpdateField = async (data: IDLEquipmentDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDeliveredEquipment;
    if (field !== "deliveryTime" && field !== "referenceItemId") {
      setInputValues({ ...inputValues, ...data });
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateEquipmentsDelivered({
      logId: EquipmentItem.daily_log_id.toString(),
      equipmentDelivery: [
        {
          itemId: Number(inputValues?.item_id),
          itemName: inputValues?.item_name,
          deliveredBy: inputValues?.delivered_by || "",
          deliveryTime: inputValues?.delivery_time,
          referenceItemId: inputValues?.reference_item_id || 0,
          isReturned:
            field == "isReturned" || field == "showUntilReturned"
              ? inputValues?.is_returned
              : EquipmentItem?.is_returned,
          showUntilReturned:
            field == "isReturned" || field == "showUntilReturned"
              ? inputValues?.show_until_returned
              : EquipmentItem?.show_until_returned,
          notes: inputValues?.notes || "",
          newItem: inputValues?.reference_item_id !== 0 ? 1 : 0,
          ...data,
        },
      ],
    })) as IEquipmentDeliveryRes;

    if (updateRes?.success) {
      dispatch(resetDash());
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      onAddEquipmentDeli(updateRes.data.equipment_item_delivered[0]);
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      let newField = field;

      if (field === "itemName") {
        newField = "item_name";
      } else if (field === "deliveredBy") {
        newField = "delivered_by";
      } else if (field === "deliveryTime") {
        newField = "delivery_time";
      } else if (field === "referenceItemId") {
        newField = "reference_item_id";
      }
      setInputValues({ ...inputValues, [newField]: EquipmentItem[newField] });
      notification.error({
        description: updateRes?.message,
      });
    }

    //   // Remove success icon after 3 seconds
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  useEffect(() => {
    setInputValues(EquipmentItem);
  }, []);

  const timeField = useMemo(() => {
    return (
      <TimePickerField
        label={_t("Time")}
        labelClass="sm:w-[100px] sm:max-w-[100px]"
        labelPlacement="left"
        name="deliveryTime"
        placeholder="00:00"
        editInline={true}
        allowClear={false}
        iconView={true}
        readOnly={isReadOnly}
        key={inputValues?.delivery_time}
        format="hh:mm A"
        defaultValue={displayTimeFormat(inputValues?.delivery_time?.trim())}
        fixStatus={getStatusForField(loadingStatus, "deliveryTime")}
        onChange={(_, val) => {
          if (inputValues?.delivery_time !== val) {
            handleChangeDate(val as string);
          }
        }}
      />
    );
  }, [inputValues?.delivery_time, loadingStatus]);

  return (
    <>
      <div className="grid gap-2">
        <CheckBox
          disabled={Number(inputValues?.reference_item_id) > 0 || isReadOnly}
          checked={
            Number(inputValues?.reference_item_id) > 0 ||
            (addItemsMaterialId != 0 &&
              addItemsMaterialId == EquipmentItem?.item_id)
          }
          className="w-fit gap-1.5"
          onChange={(e) => {
            setAddItemsMaterialId(Number(EquipmentItem?.item_id || 0));
          }}
        >
          {_t("Add Items to Equipment Items list for later use?")}
        </CheckBox>
        <div className="common-card p-[3px] dark:shadow-[0_4px_24px_0] dark:shadow-dark-900">
          <div className="flex items-center dark:bg-dark-900 justify-between rounded-t-md p-[3px] pr-1.5 bg-[#F8F8F8]">
            <div className="w-[calc(100%-30px)] flex md:flex-row flex-col justify-between">
              <div className="lg:w-1/2 md:w-[400px]">
                <InputField
                  placeholder={_t("Delivered Item Name")}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues?.item_name)
                  )}
                  name="item_name"
                  labelPlacement="left"
                  className="text-[15px] font-medium"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "itemName")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "itemName",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "itemName",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() => {
                    handleChangeFieldStatus({
                      field: "itemName",
                      status: "save",
                      action: "FOCUS",
                    });
                  }}
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value === "") {
                      notification.error({
                        description: _t("Item Name is required."),
                      });
                      setInputValues({
                        ...inputValues,
                        item_name: EquipmentItem?.item_name,
                      });
                      return false;
                    }
                    if (value !== EquipmentItem?.item_name) {
                      handleUpdateField({ itemName: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "itemName",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        item_name: EquipmentItem.item_name,
                      });
                    }
                  }}
                />
              </div>
            </div>
            <ButtonWithTooltip
              tooltipTitle={_t("Delete")}
              disabled={isReadOnly}
              tooltipPlacement="top"
              icon="fa-regular fa-trash-can"
              iconClassName="group-hover/buttonHover:!text-[#FF0000] group-focus-within/buttonHover:!text-[#FF0000]"
              className="focus-within:!bg-[#FF00001a] hover:!bg-[#FF00001a]"
              onClick={onDeleteEquipmentDeli}
            />
          </div>
          <ul className="grid md:grid-cols-2 gap-y-1 gap-x-4 p-2">
            <li>
              <InputField
                label={_t("Delivered By")}
                placeholder={_t("Delivered By")}
                value={inputValues?.delivered_by}
                name="delivered_by"
                labelPlacement="left"
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                editInline={true}
                iconView={true}
                readOnly={isReadOnly}
                fixStatus={getStatusForField(loadingStatus, "deliveredBy")}
                onChange={handleInpOnChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "deliveredBy",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "deliveredBy",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() => {
                  handleChangeFieldStatus({
                    field: "deliveredBy",
                    status: "save",
                    action: "FOCUS",
                  });
                }}
                onBlur={(e) => {
                  const value = e?.target?.value.trim();
                  if (value !== EquipmentItem?.delivered_by) {
                    handleUpdateField({ deliveredBy: value });
                  } else {
                    handleChangeFieldStatus({
                      field: "deliveredBy",
                      status: "button",
                      action: "BLUR",
                    });
                    setInputValues({
                      ...inputValues,
                      delivered_by: EquipmentItem.delivered_by,
                    });
                  }
                }}
              />
            </li>
            <li>{timeField}</li>
            <li>
              <TextAreaField
                label={_t("Notes")}
                placeholder={_t("Notes")}
                value={inputValues?.notes || ""}
                name="notes"
                ref={notesInpRef}
                readOnly={isReadOnly}
                labelPlacement="left"
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                editInline={true}
                iconView={true}
                fixStatus={getStatusForField(loadingStatus, "notes")}
                onChange={handleInpOnChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() => {
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "save",
                    action: "FOCUS",
                  });
                }}
                onBlur={(e) => {
                  const value = e.target.value.trim();
                  if (value !== EquipmentItem?.notes) {
                    handleUpdateField({ notes: value });
                  } else {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "button",
                      action: "BLUR",
                    });
                    setInputValues({
                      ...inputValues,
                      notes: EquipmentItem.notes,
                    });
                  }
                }}
                onClickStsIcon={() => {
                  if (getStatusForField(loadingStatus, "notes") === "edit") {
                    notesInpRef.current?.focus();
                  }
                }}
              />
            </li>
            <li className="flex items-center gap-4">
              <CustomCheckBox
                className="gap-1.5 font-medium"
                name="isReturned"
                onChange={(e) => {
                  e.target.checked
                    ? handleUpdateField({
                        isReturned: 1,
                        showUntilReturned: 0,
                      })
                    : handleUpdateField({
                        isReturned: 0,
                        showUntilReturned: 1,
                      });
                }}
                checked={EquipmentItem?.is_returned == 1}
                disabled={isReadOnly}
                loadingProps={{
                  isLoading:
                    getStatusForField(loadingStatus, "isReturned") ===
                    "loading",
                  className: "bg-[#ffffff]",
                }}
              >
                {_t("Returned?")}
              </CustomCheckBox>
              <CustomCheckBox
                className="gap-1.5 font-medium"
                name="showUntilReturned"
                onChange={(e) => {
                  e.target.checked
                    ? handleUpdateField({
                        showUntilReturned: 1,
                        isReturned: 0,
                      })
                    : handleUpdateField({
                        showUntilReturned: 0,
                        isReturned: 1,
                      });
                }}
                checked={EquipmentItem?.show_until_returned == 1}
                disabled={isReadOnly}
                loadingProps={{
                  isLoading:
                    getStatusForField(loadingStatus, "showUntilReturned") ===
                    "loading",
                  className: "bg-[#ffffff]",
                }}
              >
                {_t("Show Until Returned?")}
              </CustomCheckBox>
            </li>
          </ul>
        </div>
      </div>

      {addItemsMaterialId > 0 && (
        <AddMyList
          addMyList={addItemsMaterialId > 0}
          setAddMyList={() => {
            setAddItemsMaterialId(0);
          }}
          selecedData={EquipmentItem}
          type={"equipment"}
          onSaved={(refId) => {
            setInputValues({
              ...inputValues,
              reference_item_id: refId,
            });
            handleUpdateField({ referenceItemId: refId });
          }}
        />
      )}
    </>
  );
};

export default ItemList;
