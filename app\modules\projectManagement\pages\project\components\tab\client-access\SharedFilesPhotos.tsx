import { useState } from "react";

// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
// Hook
import { useTranslation } from "~/hook";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { FileSelect } from "~/shared/components/organisms/fileSelect";
import { addFile } from "~/redux/action/fileAttachmentAction";
import { Number } from "~/helpers/helper";
import { filterAttachmentFiles } from "~/shared/utils/helper/common";
import ProFileGallery from "./FilePhotosContainer/ProFileGallery";

const SharedFilesPhotos = () => {
  const { _t } = useTranslation();

  const { checkGlobalModulePermissionByKey } = useGlobalModule();
  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();

  const dispatch = useAppProDispatch();

  const { details } = useAppProSelector((state) => state.proDetails);

  const [selectedTab, setSelectedTab] = useState<
    TFileAttachmentTabsValues | ""
  >("");

  const [selectedFile, setSelectedFile] = useState<IFile[]>([]);
  const [load, isLoad] = useState(false);
  const [hasFiles, setHasFiles] = useState(true);
  const { isReadOnly, handleUpdateField } = useProjectDetail();

  const {
    module_access = "read_only",
    module_key = "",
    module_id = 0,
  } = currentMenuModule || {};

  const { image_resolution = "" } = appSettings || {};

  const handleCreateFile = async (files: IFile[]) => {
    const formData: TCreateCoverImage = {
      primary_id: Number(details?.id),
      module_id: module_id,
      module_key: module_key,
    };

    if (files.length) {
      const { attachImage, awsFilesUrl }: IFilterAttachmentFiles =
        filterAttachmentFiles(files);

      formData.attach_image = attachImage.length
        ? attachImage.join(",")
        : undefined;

      const updatedAwsFilesUrl = awsFilesUrl.map((file) => ({
        ...file,
        isheic: file.file_ext.toLowerCase() === "heic" ? 1 : 0, // Add 'isheic' key
      }));

      formData.files = updatedAwsFilesUrl.length
        ? updatedAwsFilesUrl
        : undefined;

      formData.project_id = details?.id;
    } else {
      return false;
    }

    const filePromise = [
      addFile(formData) as Promise<IGetAddFileRes>,
      handleUpdateField({
        data_to_send_in_api: {
          client_cover_image: files[0]?.cdnUrl || files[0]?.file_path,
        },
      }),
    ];

    const [addFilesRes] = await Promise.all(filePromise);

    if (!addFilesRes?.success) {
      notification.error({
        description: addFilesRes?.message,
      });
    }
  };

  const handleSelectImage = (imageData: IFile) => {
    setSelectedFile((prevSelectedFiles) => {
      if (
        prevSelectedFiles.some((file) => file.image_id === imageData.image_id)
      ) {
        return prevSelectedFiles.filter(
          (file) => file.image_id !== imageData.image_id
        );
      } else {
        return [...prevSelectedFiles, imageData];
      }
    });
  };

  return (
    <>
      <CollapseSingleTable
        defaultActiveKey={["1"]}
        title={_t("Shared Files & Photos")}
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div>
              <Typography className="text-base text-black">
                {_t("Cover Image")}
              </Typography>
              <div className="grid md:grid-cols-4 lg:grid-cols-6 grid-cols-2 gap-3.5 auto-rows-max px-0.5 mt-2">
                <div
                  className="relative h-[130px] bg-[#f1f1f1] rounded overflow-hidden group/shared-file cursor-pointer"
                  onClick={() => {
                    if (!isReadOnly) setSelectedTab("new");
                  }}
                >
                  <div className="flex items-center justify-center h-full">
                    <img
                      src={
                        details?.client_cover_image ||
                        `${window.ENV.CDN_URL}assets/images/gallary-image.svg`
                      }
                      className="min-w-[50px]"
                      alt="not found"
                    />
                  </div>
                  <div className="absolute top-0 w-full h-full ease-in opacity-0 before:scale-0 before:rounded group-hover/shared-file:opacity-100 group-hover/shared-file:!visible group-hover/shared-file:before:scale-100 delay-100 before:absolute before:w-full before:h-full before:top-0 before:left-0 before:bg-black/50 before:ease-in before:duration-300">
                    <div className="flex items-center justify-center h-full relative z-10">
                      <FontAwesomeIcon
                        className="h-[18px] w-[18px] text-white"
                        icon="fa-regular fa-plus"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {hasFiles && (
              <div className="mt-4">
                <Typography className="text-base text-black">
                  {_t("Shared Files")}
                </Typography>

                <div>
                  <ProFileGallery
                    setHasFiles={setHasFiles}
                    projectid={details?.id}
                    validationParams={{
                      date_format: CFConfig.day_js_date_format,
                      file_support_module_access:
                        checkGlobalModulePermissionByKey(
                          CFConfig.file_support_key
                        ),
                      image_resolution,
                      module_key,
                      module_id,
                      module_access,
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        }
      />

      {!!selectedTab && (
        <FileSelect
          projectid={details?.id ?? ""}
          options={["new", "gallery"]}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          setSelectedFileData={(data) => {
            handleCreateFile(data as IFile[]);
          }}
          handleSelectImage={handleSelectImage}
          selectedFiles={selectedFile}
          useAppSelector={useAppProSelector}
          setSelectedFiles={setSelectedFile}
          dispatch={dispatch}
          validationParams={{
            date_format: CFConfig.day_js_date_format,
            file_support_module_access: checkGlobalModulePermissionByKey(
              CFConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
          }}
          addFilesRes={{}}
          load={load}
          isLoad={isLoad}
        />
      )}
    </>
  );
};

export default SharedFilesPhotos;
