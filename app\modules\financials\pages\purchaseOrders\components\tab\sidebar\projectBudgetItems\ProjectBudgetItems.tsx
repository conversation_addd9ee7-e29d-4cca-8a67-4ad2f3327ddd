// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import ItemsList from "./ItemsList";
import { useAppPODispatch, useAppPOSelector } from "../../../../redux/store";
import { useParams } from "@remix-run/react";
import { getGConfig, getGModuleByKey } from "~/zustand";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  addPOItems,
  deletePOItems,
  fetchPOScheduleValueItem,
} from "../../../../redux/action/POItemAction";
import { GridApi, IRowNode, SelectionChangedEvent } from "ag-grid-community";
import { GridReadyEvent } from "ag-grid-community";
import { sanitizeString } from "~/helpers/helper";
import {
  addItems,
  removeItems,
  updatePOSOVItems,
} from "../../../../redux/slices/poItemsSlice";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import useFieldStatus from "../../../../utils/useFieldStatus ";
import { POfieldStatus } from "../../../../utils/common";
import { getStatusForField } from "~/shared/utils/helper/common";
import { HtmlDecode } from "../../../../utils/function";

const ProjectBudgetItems = ({
  projectBudgetItems,
  setProjectBudgetItems,
}: IPOProjectBudgetItemsProps) => {
  const { _t } = useTranslation();
  const { id: purchase_order_id }: RouteParams = useParams();
  const { module_id }: GConfig = getGConfig();
  const dispatch = useAppPODispatch();
  const { formatter } = useCurrencyFormatter();
  const noRecordsImage = `${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`;
  const moduleWO = getGModuleByKey(CFConfig?.work_order_module) as GModule;
  const moduleCO = getGModuleByKey(CFConfig?.change_order_module) as GModule;
  const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
    useFieldStatus(POfieldStatus);
  // const moduleEst = getGModuleByKey(CFConfig?.estimate_module) as GModule;
  // after api call use this ================
  const { purchaseOrderDetail: details } = useAppPOSelector(
    (state) => state.purchaseOrderDetail
  );
  const {
    poScheduleValueItems,
    isPOScheduleValueLoading,
    isPOScheduleValueFetched,
    purchaseOrderItems,
    isPOItemsLoading,
  }: IPOItemState = useAppPOSelector((state) => state.purchaseOrderItems);
  const [withChangeOrderList, setWithChangeOrderList] = useState<IPOItemData[]>(
    []
  );
  const [withoutChangeOrderList, setWithoutChangeOrderList] = useState<
    IPOItemData[]
  >([]);
  const [withWorkOrderList, setWithWorkOrderList] = useState<IPOItemData[]>([]);
  const [selectedOriginalScopeItems, setSelectedOriginalScopeItems] = useState<
    IPOItemData[]
  >([]);
  const [selectedChangeOrderItems, setSelectedChangeOrderItems] = useState<
    IPOItemData[]
  >([]);
  const [selectedWorkOrderItems, setSelectedWorkOrderItems] = useState<
    IPOItemData[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  // Refs to store grid APIs
  const sovGridApiRef = useRef<GridApi | null>(null);
  const coGridApiRef = useRef<GridApi | null>(null);
  const woGridApiRef = useRef<GridApi | null>(null);

  // Handle "Select All Items" checkbox change
  const handleSelectAllChange = (checked: boolean) => {
    handleChangeFieldStatus({
      field: "selectAllSov",
      status: "loading",
      action: "API",
    });

    setSelectAll(checked); // Immediately update the checkbox state

    // Select or deselect all rows in each grid using optimized methods
    const updateGridSelection = (
      gridApiRef: React.RefObject<GridApi | null>
    ) => {
      if (gridApiRef?.current) {
        if (checked) {
          gridApiRef.current.selectAll();
        } else {
          gridApiRef.current.deselectAll();
        }
      }
    };

    // Update selection for each grid
    updateGridSelection(sovGridApiRef);
    updateGridSelection(coGridApiRef);
    updateGridSelection(woGridApiRef);

    if (checked) {
      setSelectedOriginalScopeItems(withoutChangeOrderList);
      setSelectedChangeOrderItems(withChangeOrderList);
      setSelectedWorkOrderItems(withWorkOrderList);
    } else {
      setSelectedOriginalScopeItems([]);
      setSelectedChangeOrderItems([]);
      setSelectedWorkOrderItems([]);
    }
    handleChangeFieldStatus({
      field: "selectAllSov",
      status: "button",
      action: "API",
    });
  };
  // const handleSelectAllChange = (checked: boolean) => {
  //   setSelectAll(checked);
  //   // Defer grid selection updates to allow state change to reflect immediately
  //   // setTimeout(() => {
  //   const updateGridSelection = (
  //     gridApiRef: React.RefObject<GridApi | null>
  //   ) => {
  //     if (gridApiRef?.current) {
  //       gridApiRef.current.forEachNode((node: IRowNode) =>
  //         node.setSelected(checked)
  //       );
  //     }
  //   };
  //   // Select or deselect all rows in each grid
  //   updateGridSelection(sovGridApiRef);
  //   updateGridSelection(coGridApiRef);
  //   updateGridSelection(woGridApiRef);
  //   // // Select or deselect all rows in each grid
  //   // if (sovGridApiRef?.current) {
  //   //   sovGridApiRef?.current?.forEachNode((node: IRowNode) =>
  //   //     node?.setSelected(checked)
  //   //   );
  //   // }
  //   // if (coGridApiRef?.current) {
  //   //   coGridApiRef?.current?.forEachNode((node: IRowNode) =>
  //   //     node?.setSelected(checked)
  //   //   );
  //   // }
  //   // if (woGridApiRef?.current) {
  //   //   woGridApiRef?.current?.forEachNode((node: IRowNode) =>
  //   //     node?.setSelected(checked)
  //   //   );
  //   // }

  //   // Update state with all items or empty arrays
  //   if (checked) {
  //     setSelectedOriginalScopeItems(withoutChangeOrderList);
  //     setSelectedChangeOrderItems(withChangeOrderList);
  //     setSelectedWorkOrderItems(withWorkOrderList);
  //   } else {
  //     setSelectedOriginalScopeItems([]);
  //     setSelectedChangeOrderItems([]);
  //     setSelectedWorkOrderItems([]);
  //   }
  //   // }, 0);
  // };
  const columns = [
    {
      headerName: "",
      field: "",
      minWidth: 36,
      maxWidth: 36,
      checkboxSelection: true,
      headerCheckboxSelection: true,
      suppressMenu: true,
    },
    // {
    //   headerName: _t("Type"),
    //   maxWidth: 60,
    //   minWidth: 60,
    //   field: "item_type",
    //   suppressMenu: true,
    //   headerClass: "ag-header-center",
    //   cellClass: "ag-cell-center",
    //   cellRenderer: ({ data }: ISubContractorChangeOrderItemsCellRenderer) => {
    //     const itemType = data?.item_type_key;
    //     const itemTypeName = data?.item_type_name;
    //     const itemTypeIcon = POIcons[itemType as IconKey];
    //     return itemTypeIcon ? (
    //       <Tooltip title={itemTypeName}>
    //         <FontAwesomeIcon
    //           className="w-4 h-4 text-primary-900 mx-auto"
    //           icon={itemTypeIcon}
    //         />
    //       </Tooltip>
    //     ) : (
    //       <></>
    //     );
    //   },
    // },
    {
      headerName: _t("Source"),
      field: "source",
      minWidth: 150,
      maxWidth: 150,
      suppressMenu: true,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const source = `${data?.source_name || ""}`;
        return (
          <Tooltip title={source}>
            <Typography className="table-tooltip-text">{source}</Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "item_name",
      minWidth: 150,
      flex: 2,
      suppressMenu: true,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const subject = `${HtmlDecode(data?.subject || "")}`;
        return (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Description"),
      field: "description",
      minWidth: 150,
      flex: 2,
      suppressMenu: true,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const description = `${HtmlDecode(data?.description || "")}`;
        return !!description ? (
          <Tooltip title={description}>
            <Typography className="table-tooltip-text">
              {description}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const qty = `${data?.quantity || 0}`;
        return !!qty ? (
          <Tooltip title={qty}>
            <Typography className="table-tooltip-text">{qty}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Cost"),
      field: "unit_cost",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const nValue = Number(data?.unit_cost) / 100;
        const unitCost = formatter(
          Number(nValue) == 0
            ? Number(nValue).toFixed(0)
            : Number(nValue).toFixed(2)
        ).value_with_symbol;
        return (
          <Tooltip title={`${unitCost}`}>
            <Typography className="table-tooltip-text">
              {`${unitCost}`}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      minWidth: 60,
      maxWidth: 60,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const Unit = data?.unit;
        return !!Unit ? (
          <Tooltip title={`${Unit ?? ""}`}>
            <Typography className="table-tooltip-text">
              {`${Unit ?? "-"}`}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IPOChangeOrderItemsCellRenderer) => {
        const { data } = params;
        const total = formatter(
          (
            Number(
              `${Number(data?.unit_cost ?? 0) * Number(data?.quantity ?? 0)}`
            ) / 100
          ).toFixed(2)
        ).value_with_symbol;
        return (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        );
      },
    },
  ];
  useEffect(() => {
    setWithChangeOrderList(poScheduleValueItems?.change_order_section?.items);
    setWithoutChangeOrderList(poScheduleValueItems?.sov_section?.items);
    setWithWorkOrderList(poScheduleValueItems?.work_order_section?.items);
  }, [JSON.stringify(poScheduleValueItems)]);

  // Items (Directly Added to SOV) onSelectionChanged  =========================
  const onGridSOVReady = async (params: GridReadyEvent) => {
    // const gridApiRef = params.api;
    sovGridApiRef.current = params.api as GridApi;
    sovGridApiRef?.current?.forEachNode((node: IRowNode) => {
      const { item_id }: { item_id: number } = node.data;
      if (
        poScheduleValueItems?.sov_section?.defaultCheckedItems?.some(
          (item: IPOItemData) => item.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
  };

  const handleSelectionSOVChanged = (event: SelectionChangedEvent): void => {
    const selectedNodes = event.api.getSelectedNodes();
    const selected = selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    setSelectedOriginalScopeItems(selected);
  };

  // Change Order onSelectionChanged  =========================
  const onGridCOReady = async (params: GridReadyEvent) => {
    // const gridApiRefChangeOrder = params.api;
    coGridApiRef.current = await params.api;
    coGridApiRef.current.forEachNode((node: IRowNode) => {
      const { item_id } = node.data;
      if (
        poScheduleValueItems?.change_order_section?.defaultCheckedItems?.some(
          (item) => item.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
  };
  const handleSelectionCOChanged = (event: SelectionChangedEvent): void => {
    const selectedChangeOrderNodes = event.api.getSelectedNodes();
    const selected = selectedChangeOrderNodes.map((node: IRowNode) => {
      return node.data;
    });
    setSelectedChangeOrderItems(selected);
  };

  // Work Order onSelectionChanged  =========================
  const onGridWOReady = async (params: GridReadyEvent) => {
    // const gridApiRefWorkOrder = params.api;
    woGridApiRef.current = params.api;
    await woGridApiRef?.current?.forEachNode((node: IRowNode) => {
      const { item_id } = node.data;
      if (
        poScheduleValueItems?.work_order_section?.defaultCheckedItems?.some(
          (item) => item.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
  };
  const handleSelectionWOChanged = async (event: SelectionChangedEvent) => {
    const selectedNodes = event.api.getSelectedNodes();
    const selected = await selectedNodes.map((node: IRowNode) => {
      return node.data;
    });
    setSelectedWorkOrderItems(selected);
  };
  useEffect(() => {
    if (
      details?.pro_id &&
      (purchaseOrderItems?.length ?? 0) > 0 &&
      !isPOScheduleValueFetched
    ) {
      // if (!isPOScheduleValueFetched) {
      dispatch(
        fetchPOScheduleValueItem({
          // id: purchase_order_id || "",
          module_id: module_id,
          need_section: 1,
          project_id: +details?.pro_id,
        })
      );
    }
  }, [details?.pro_id, purchaseOrderItems]);

  useEffect(() => {
    dispatch(updatePOSOVItems());
  }, [purchaseOrderItems]);
  const handleSaveScheduleValueItems = useCallback(async () => {
    const finalItems: IPOItemData[] = [];
    const deleteItems: IPOItemData[] = [];
    setIsLoading(true);
    // For SOV
    const sovcheckedItemsMap = new Map(
      poScheduleValueItems?.sov_section?.defaultCheckedItems?.map((item) => [
        item.item_id,
        item,
      ])
    );
    selectedOriginalScopeItems?.forEach((item: IPOItemData) =>
      sovcheckedItemsMap.has(item?.item_id)
        ? sovcheckedItemsMap.delete(item?.item_id)
        : finalItems?.push({
            ...item,
            // reference_module_item_id: item?.reference_item_id,
            project_budget_item_id: item?.item_id,
            reference_item_id: Number(item?.reference_item_type_id),
            reference_module_item_id:
              item?.reference_item_id == 0
                ? item?.item_id
                : item?.reference_item_id ?? 0,
          })
    );

    // Remaining items in the map are the ones to delete
    deleteItems?.push(...sovcheckedItemsMap.values());

    //For Change Order
    const coCheckedItemsMap = new Map(
      poScheduleValueItems?.change_order_section?.defaultCheckedItems?.map(
        (item) => [item.item_id, item]
      )
    );
    selectedChangeOrderItems?.forEach((item) =>
      coCheckedItemsMap.has(item.item_id)
        ? coCheckedItemsMap.delete(item.item_id)
        : finalItems?.push({
            ...item,
            // reference_module_item_id: item?.reference_item_id,
            project_budget_item_id: item?.item_id,
            reference_item_id: Number(item?.reference_item_type_id),
            reference_module_item_id:
              item?.reference_item_id == 0
                ? item?.item_id
                : item?.reference_item_id,
          })
    );
    // Remaining items in the map are the ones to delete
    deleteItems?.push(...coCheckedItemsMap.values());

    //For Work Order
    const woCheckedItemsMap = new Map(
      poScheduleValueItems?.work_order_section?.defaultCheckedItems?.map(
        (item) => [item.item_id, item]
      )
    );
    selectedWorkOrderItems?.forEach((item) =>
      woCheckedItemsMap.has(item.item_id)
        ? woCheckedItemsMap.delete(item.item_id)
        : finalItems?.push({
            ...item,
            // reference_module_item_id: item?.reference_item_id,
            project_budget_item_id: item?.item_id,
            reference_item_id: Number(item?.reference_item_type_id),
            reference_module_item_id:
              item?.reference_item_id == 0
                ? item?.item_id
                : item?.reference_item_id,
          })
    );
    // Remaining items in the map are the ones to delete
    deleteItems?.push(...woCheckedItemsMap.values());

    if (finalItems?.length > 0) {
      const addResponse = await dispatch(
        addPOItems({
          purchase_order_id: Number(purchase_order_id),
          items: finalItems,
        })
      );
      const addAPiRes = addResponse?.payload as IPOAddItemsApiRes;
      if (addAPiRes?.success && addAPiRes?.data?.items?.length) {
        dispatch(addItems({ itemData: addAPiRes?.data?.items }));
      } else {
        notification.error({
          description: addAPiRes?.message || "Something went wrong!",
        });
        setIsLoading(false);
        return;
      }
    }
    const delIds = (purchaseOrderItems || [])
      ?.filter((item) => {
        return deleteItems?.some(
          (deleteItem) =>
            deleteItem?.reference_item_id === item?.reference_module_item_id
        );
      })
      ?.map((item) => item?.item_id);

    if (deleteItems?.length > 0) {
      const deleteRes = await dispatch(
        deletePOItems({
          purchase_order_id: Number(purchase_order_id),
          item_id: delIds?.join(","),
        })
      );
      const delAPiRes = deleteRes?.payload as IPOItemsApiRes;
      if (delAPiRes?.success) {
        dispatch(removeItems({ itemIds: delIds }));
      } else {
        notification.error({
          description: delAPiRes?.message || "Something went wrong!",
        });
        setIsLoading(false);
        return;
      }
    }
    // await dispatch(getPOItems({ purchase_order_id }));
    setProjectBudgetItems(false);
    setIsLoading(false);
  }, [
    JSON.stringify(selectedWorkOrderItems),
    JSON.stringify(selectedOriginalScopeItems),
    JSON.stringify(selectedChangeOrderItems),
  ]);
  useEffect(() => {
    const coSelList = coGridApiRef?.current?.getSelectedRows();
    const sovSelList = sovGridApiRef?.current?.getSelectedRows();
    const woSelList = woGridApiRef?.current?.getSelectedRows();
    if (
      woSelList?.length == withWorkOrderList?.length &&
      sovSelList?.length == withoutChangeOrderList?.length &&
      coSelList?.length == withChangeOrderList?.length
    ) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  }, [
    woGridApiRef?.current?.getSelectedRows(),
    sovGridApiRef?.current?.getSelectedRows(),
    coGridApiRef?.current?.getSelectedRows(),
    withWorkOrderList,
    withoutChangeOrderList,
    withChangeOrderList,
  ]);

  return (
    <Drawer
      open={projectBudgetItems}
      rootClassName="drawer-open"
      width={980}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-file-invoice-dollar"
            />
          </div>
          <div className="flex justify-between items-center w-[calc(100%-40px)] pr-2">
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Project Budget Items`)}
            </Header>
            <Tooltip
              title={_t(
                "You can only add items that have been imported into your Schedule of Values Items. To add additional items to this list, go to your Project > Schedule of Values tab > Import or Manually Add Items."
              )}
              placement="bottom"
            >
              <Typography className="cursor-pointer text-[#B94A48] text-sm">
                {_t("Missing Something?")}
              </Typography>
            </Tooltip>
          </div>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setProjectBudgetItems(false)} />}
    >
      <div className="py-4">
        <div className="sidebar-body h-[calc(100vh-132px)] ">
          <div className="grid gap-4">
            <div className="flex items-center justify-between px-4">
              {/* <CheckBox
                className="gap-1.5"
                checked={selectAll}
                onChange={(e) => handleSelectAllChange(e.target.checked)}
              >
                {_t("Select All Items")}
              </CheckBox> */}
              <CustomCheckBox
                className="gap-1.5"
                checked={selectAll}
                onChange={(e) => handleSelectAllChange(e.target.checked)}
                loadingProps={{
                  isLoading:
                    getStatusForField(loadingStatus, "selectAllSov") ===
                    "loading",
                  className: "bg-[#ffffff]",
                }}
              >
                {_t("Select All Items")}
              </CustomCheckBox>
              {/* <input
                type="checkbox"
                className="gap-1.5"
                checked={selectAll}
                // onChange={(e) => handleSelectAllChange(e.target.checked)}
              /> */}
            </div>
            <div className="px-4 flex flex-col gap-3 h-[calc(100vh-172px)] overflow-y-auto">
              <div className="grid gap-4">
                <SidebarCardBorder addGap={true}>
                  {isPOScheduleValueLoading ? (
                    <Spin className="w-full h-20 flex items-center justify-center" />
                  ) : (
                    <ItemsList
                      title={_t("Items (Directly Added to SOV)")}
                      tableProps={{
                        rowSelection: "multiple",
                        columnDefs: columns,
                        rowData: withoutChangeOrderList,
                        loadingCellRenderer: isPOScheduleValueLoading,
                        rowMultiSelectWithClick: true,
                        suppressRowClickSelection: true,
                        onSelectionChanged: handleSelectionSOVChanged,
                        onGridReady: onGridSOVReady,
                        noRowsOverlayComponent: () => (
                          <NoRecords image={noRecordsImage} />
                        ),
                      }}
                    />
                  )}
                </SidebarCardBorder>
                {!isPOScheduleValueLoading && (
                  <SidebarCardBorder addGap={true}>
                    <ItemsList
                      title={
                        moduleCO?.module_name
                          ? HTMLEntities.decode(
                              sanitizeString(moduleCO?.module_name)
                            ) + _t(" Items")
                          : _t("Change Order Items")
                      }
                      tableProps={{
                        rowSelection: "multiple",
                        columnDefs: columns,
                        rowData: withChangeOrderList,
                        loadingCellRenderer: isPOScheduleValueLoading,
                        loadingCellRendererParams: isPOScheduleValueLoading,
                        rowMultiSelectWithClick: true,
                        suppressRowClickSelection: true,
                        onSelectionChanged: handleSelectionCOChanged,
                        onGridReady: onGridCOReady,
                        noRowsOverlayComponent: () => (
                          <NoRecords image={noRecordsImage} />
                        ),
                      }}
                    />
                  </SidebarCardBorder>
                )}
                {!isPOScheduleValueLoading && (
                  <SidebarCardBorder addGap={true}>
                    <ItemsList
                      title={
                        moduleWO?.module_name
                          ? HTMLEntities.decode(
                              sanitizeString(moduleWO?.module_name)
                            ) + _t(" Items")
                          : _t("Work Order Items")
                      }
                      tableProps={{
                        rowSelection: "multiple",
                        columnDefs: columns,
                        rowData: withWorkOrderList,
                        loadingCellRenderer: isPOScheduleValueLoading,
                        loadingCellRendererParams: isPOScheduleValueLoading,
                        rowMultiSelectWithClick: true,
                        suppressRowClickSelection: true,
                        onSelectionChanged: handleSelectionWOChanged,
                        onGridReady: onGridWOReady,
                        noRowsOverlayComponent: () => (
                          <NoRecords image={noRecordsImage} />
                        ),
                      }}
                    />
                  </SidebarCardBorder>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            htmlType="submit"
            className="w-full justify-center primary-btn"
            isLoading={isLoading}
            onClick={handleSaveScheduleValueItems}
            disabled={isLoading || isPOScheduleValueLoading || isPOItemsLoading}
            buttonText={_t(`Save & Close`)}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default ProjectBudgetItems;
