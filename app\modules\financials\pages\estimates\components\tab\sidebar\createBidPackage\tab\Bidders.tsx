import { Typography } from "antd";
import { FormikProps } from "formik";
import { useEffect, useState } from "react";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import {
  deleteEstBidder,
  getEstBidding,
  getOneEstBidding,
  updateEstBidder,
} from "~/modules/financials/pages/estimates/redux/action/ESBiddingAction";
import {
  useAppESDispatch,
  useAppESSelector,
} from "~/modules/financials/pages/estimates/redux/store";
import { isValidId } from "~/modules/financials/pages/estimates/utils/common";
import { BIDDERS_BID_STATUS } from "~/modules/financials/pages/estimates/utils/constants";
import { apiRoutes } from "~/route-services/routes";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getGlobalUser } from "~/zustand/global/user/slice";

interface BiddingBidderProps {
  formik: FormikProps<ESBiddingAddParams>;
  biddingId?: number;
  isReadOnly?: boolean;
  isEdit?: boolean;
  isBidClosed?: boolean;
}

const Bidders = ({
  formik,
  biddingId,
  isEdit,
  isReadOnly,
  isBidClosed,
}: BiddingBidderProps) => {
  const { _t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedBidder, setSelectedBidder] = useState<
    TselectedContactSendMail[] | null
  >(null);
  const [isSelectCustomerOpen, setIsSelectCustomerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [confirmBidderDialogOpen, setConfirmBidderDialogOpen] =
    useState<boolean>(false);
  const [selectedBidderDelete, setSelectedBidderDelete] =
    useState<Bidder | null>(null);
  const [confirmSendBidInviteDialogOpen, setConfirmSendBidInviteDialogOpen] =
    useState<boolean>(false);
  const [selectedBidderForInvite, setSelectedBidderForInvite] =
    useState<Bidder | null>(null);
  const { formatter } = useCurrencyFormatter();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const dispatch = useAppESDispatch();
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);

  const isSendEmailEnabled =
    formik?.values?.bid_status === 298 || formik?.values?.bid_status === 299;
  const isBiddingCLosed = formik?.values?.bid_status === 300;

  const isEstimateStatusLost =
    estimateDetail?.approval_type_key === "estimate_lost";

  const uniqueBidUserIds = new Set((formik?.values?.bidders || [])?.map(bidder => bidder.bid_user_id));

  useEffect(() => {
    if (selectedBidder?.length) {
      const bidderData: Partial<Bidder>[] = selectedBidder?.filter((item) => !uniqueBidUserIds.has(item.user_id || 0))?.map((item) => {
        return {
          bid_user_id: item?.user_id,
          user_id: item?.user_id,
          bidder_company_name: item?.company_name ?? "",
          bidder_name: item?.display_name,
          bidder_name_only: `${item?.first_name ?? ""} ${item?.last_name ?? ""
            }`,
        };
      });

      updateEstBidder({
        estimate_id: estimateDetail?.estimate_id,
        bidding_id: Number(biddingId),
        bidders: bidderData
          ?.filter((bidder) => !isValidId(bidder.bidder_id))
          ?.map((item) => ({
            item_id: 0,
            user_id: item?.user_id,
          })),
      })?.then((response) => {
        const res = response as IDefaultAPIRes
        if (res?.success) {
          fetchBiddingData();
        } else {
          notification.error({
            description: res?.message,
          })
        }
      })

    }
  }, [selectedBidder]);

  const handleSendBidInvite = (bidder: Bidder) => {
    setSelectedBidderForInvite(bidder);
    setConfirmSendBidInviteDialogOpen(true);
  };

  const confirmSendBidInvite = async () => {
    if (selectedBidderForInvite?.sent_date) {
      try {
        await updateEstBidder({
          estimate_id: estimateDetail?.estimate_id,
          bidding_id: Number(biddingId),
          bidders: [{
            item_id: 0,
            user_id: selectedBidderForInvite?.bid_user_id,
          }]
        })
        const data = await fetchBiddingData();
        if (Array.isArray(data.bidder) && data.bidder.length > 0) {
          const uniqueBidders = getUniqueLatestBidders(data.bidder || []);
          const getNewlyCreatedBidder = uniqueBidders.find(
            (bidder) => bidder.bid_user_id === selectedBidderForInvite?.bid_user_id
          );
          if (getNewlyCreatedBidder?.bidder_id) {
            await sendEmailToSingleBidder(getNewlyCreatedBidder);
          }
        }
      } catch (error) {
        notification.error({
          description: "Error while resending bid invite. Please try again.",
        });
      }
    } else if (selectedBidderForInvite) {
      await sendEmailToSingleBidder(selectedBidderForInvite);
    }
    setSelectedBidderForInvite(null);
  };

  async function sendEmailToSingleBidder(bidderDetail: Partial<Bidder>) {

    if (formik?.values?.bid_items?.length === 0) {
      notification.error({
        description: "There is no item in this bid. You must add at least 1 item to send request.",
      });
      setConfirmSendBidInviteDialogOpen(false);
      return;
    }

    setIsLoading(true);
    getApiData({
      url: apiRoutes.SERVICE.url,
      method: "post",
      data: getApiDefaultParams({
        op: "send_estimate_bidding_notification",
        user,
        otherParams: {
          estimate_id: estimateDetail?.estimate_id,
          bidder_id: bidderDetail?.bidder_id,
          bidding_id: bidderDetail?.bidding_id,
        },
      }),
      success: (response: any) => {
        if (response?.success === "1") {
          // updateBiddersList(response, bidderDetail);
          fetchBiddingData();
          dispatch(getEstBidding({ estimate_id: estimateDetail?.estimate_id }));
          // notification.success({
          //   description: response?.message,
          // });
        } else {
          notification.error({
            description: response?.message,
          });
        }
        setIsLoading(false);
        setConfirmSendBidInviteDialogOpen(false);
      },
      error: (description) => {
        setIsLoading(false);
        notification.error({
          description,
        });
        setConfirmSendBidInviteDialogOpen(false);
      },
    });
  }

  async function fetchBiddingData() {
    let data = null;
    try {
      const response = await dispatch(
        getOneEstBidding({
          estimate_id: estimateDetail?.estimate_id,
          bidding_id: biddingId,
        })
      );
      if ((response?.payload as IDefaultAPIRes)?.success) {
        data = response?.payload?.data;
        formik?.setValues({
          ...formik.values,
          ...data,
          bid_items: data?.items ?? [],
          bidders: data?.bidder ?? [],
        });
      } else {
        notification.error({
          description: (response?.payload as IDefaultAPIRes)?.message,
        });
      }
    } catch (e: unknown) {
      notification.error({
        description: (e as Error).message,
      });
    }
    return data
  }

  const handleDeleteBidderData = async () => {
    setIsLoading(true);
    if (isEdit && selectedBidderDelete?.bidder_id) {
      if (selectedBidderDelete?.bidding_id) {
        const response = await deleteEstBidder({
          estimate_id: estimateDetail?.estimate_id?.toString(),
          bidder_ids: [selectedBidderDelete?.bidder_id],
        });
        if (response.success) {
          const bidders = formik?.values?.bidders?.filter(
            (bidder) => bidder?.bidder_id !== selectedBidderDelete?.bidder_id
          );
          formik?.setFieldValue("bidders", bidders);
          setSelectedBidderDelete(null);
          setConfirmBidderDialogOpen(false);
        }
      }
    } else {
      const bidders = formik?.values?.bidders?.filter(
        (bidder) => bidder?.user_id !== selectedBidderDelete?.user_id
      );
      formik?.setFieldValue("bidders", bidders);
      setSelectedBidderDelete(null);
      setConfirmBidderDialogOpen(false);
    }
    setIsLoading(false);
  };

  const bidderColDefs = [
    {
      headerName: _t("Company Name"),
      field: "bidder_company_name",
      minWidth: 130,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: Bidder }) => {
        const companyName = HTMLEntities.decode(
          sanitizeString(data?.bidder_company_name ?? "")
        );
        return (
          <Tooltip title={companyName}>
            <Typography className="table-tooltip-text">
              {companyName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Name"),
      field: "bidder_name_only",
      minWidth: 130,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: Bidder }) => {
        const name = HTMLEntities.decode(
          sanitizeString(data?.bidder_name_only ?? "")
        );
        return (
          <Tooltip title={name}>
            <Typography className="table-tooltip-text">
              {name || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Date Sent"),
      field: "sent_date",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: Bidder }) => {
        return data?.sent_date ? (
          <DateTimeCard
            format="date"
            date={data?.sent_date}
            time={data?.sent_time}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Will Submit"),
      field: "submit_status_name",
      minWidth: 100,
      maxWidth: 100,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: Bidder }) => {
        const willSubmit = data?.submit_status_name;
        return (
          <Tooltip title={willSubmit}>
            <Typography className="table-tooltip-text">
              {willSubmit || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Invited"),
      field: "invited",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: Bidder }) => {
        return data?.sent_date ? (
          <DateTimeCard
            format="date"
            date={data?.sent_date}
            time={data?.sent_time}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Submitted"),
      field: "submitted",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: Bidder }) => {
        return data?.submitted_date ? (
          <DateTimeCard
            format="date"
            date={data?.submitted_date}
            time={data?.submitted_time}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: { data: Bidder }) => {
        const totalAmount = isNaN(data?.bid_amount)
          ? "0"
          : (+data?.bid_amount / 100)?.toFixed(2);
        const total = formatter(totalAmount)?.value_with_symbol;
        return (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">
              {total || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "bid_status_name",
      minWidth: 100,
      maxWidth: 100,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: ({ data }: { data: Bidder }) => {
        const status = data?.bid_status_name;
        const color = BIDDERS_BID_STATUS.find(
          (status) => status.key === (data?.bid_status ?? "")
        )?.color;

        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color + "1d"}
                className={
                  " mx-auto text-13 type-badge common-tag " + color
                    ? ""
                    : "!text-primary-900"
                }
                style={{ color: color }}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: Bidder }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t(
                isEstimateStatusLost
                  ? "Can't send to Bid Invite email. Because this estimate status is lost."
                  : isSendEmailEnabled
                    ? `${data?.sent_date ? "Resend" : "Send"} bid invitation`
                    : "The Send Invite option is available once the Bid Package is in Final or Submitted status."
              )}
              tooltipPlacement="top"
              icon="fa-regular fa-envelope"
              className="disabled:bg-transparent"
              disabled={
                !isSendEmailEnabled || isEstimateStatusLost || isReadOnly
              }
              onClick={() => handleSendBidInvite(data)}
            />
            {!isReadOnly && (formik?.values?.bid_status === 297 || !isEdit) && (
              <ButtonWithTooltip
                tooltipTitle={_t("Delete")}
                tooltipPlacement="top"
                icon="fa-regular fa-trash-can"
                onClick={() => {
                  setConfirmBidderDialogOpen(true);
                  setSelectedBidderDelete(data);
                }}
              />
            )}
          </div>
        );
      },
    },
  ];

  function getUniqueLatestBidders(bidders: Bidder[]): Bidder[] {
    const map = new Map<string | number, Bidder>();

    for (const bidder of bidders) {
      const existing = map.get(bidder.bid_user_id);

      if (
        !existing ||
        new Date(bidder.date_modified) > new Date(existing.date_modified)
      ) {
        map.set(bidder.bid_user_id, bidder);
      }
    }

    return Array.from(map.values());
  }

  const uniqueBidders = getUniqueLatestBidders(formik?.values?.bidders || []);

  return (
    <>
      <div className="grid grid-col gap-3">
        <SidebarCardBorder>
          <div className="flex items-center justify-between">
            <Typography className="text-sm text-[#343a40e3] font-semibold">
              {_t("Bidders")}
            </Typography>
            <div className="flex items-center gap-3">
              {isSendEmailEnabled && formik?.values?.bidders?.length > 0 && (
                <Tooltip
                  title={_t(
                    isEstimateStatusLost
                      ? "Can't send to Bid Invite email. Because this estimate status is lost."
                      : isSendEmailEnabled
                        ? "Send Invitation to Bid"
                        : "The Send Invite option is available once the Bid Package is in Final or Submitted status."
                  )}
                >
                  <Button
                    className="py-0 px-2.5 border-0 bg-[#EBF1F9] text-primary-900 text-sm rounded hover:!text-white hover:!bg-primary-900"
                    disabled={
                      !isSendEmailEnabled || isEstimateStatusLost || isReadOnly
                    }
                    onClick={() => {
                      sendEmailToSingleBidder({ bidding_id: biddingId });
                    }}
                  >
                    {_t("Send ITB")}
                  </Button>
                </Tooltip>
              )}
              {!isBiddingCLosed && (
                <Button
                  disabled={isBiddingCLosed || isReadOnly}
                  className={`py-0 px-2.5 border-0 bg-[#EBF1F9] text-primary-900 text-sm rounded ${isBiddingCLosed || isReadOnly
                    ? "opacity-50 !bg-[#EBF1F9] !text-primary-900"
                    : "hover:!text-white hover:!bg-primary-900"
                    }`}
                  onClick={() => {
                    setIsSelectCustomerOpen(true);
                  }}
                >
                  {_t("Add Bidder")}
                </Button>
              )}
            </div>
          </div>
          <div className="common-card p-2 mt-2">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                rowSelection={"multiple"}
                columnDefs={bidderColDefs}
                rowMultiSelectWithClick={true}
                suppressRowClickSelection={true}
                rowData={uniqueBidders ?? []}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                  />
                )}
              />
            </div>
          </div>
        </SidebarCardBorder>
      </div>

      {isSelectCustomerOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setIsSelectCustomerOpen(false);
          }}
          singleSelecte={false}
          openSelectCustomerSidebar={isSelectCustomerOpen}
          options={[
            CFConfig?.contractor_key,
            CFConfig?.vendor_key,
            CFConfig?.misc_contact_key,
            "by_service",
          ]}
          setCustomer={(data) => {
            if (data && data?.length === 0) {
              notification.error({
                description: "Please select contact",
              });
              return;
            }

            setSelectedBidder(data)
          }}
          selectedCustomer={Array.isArray(uniqueBidders) ? uniqueBidders.map((bidder) => ({
            user_id: bidder?.bid_user_id,
            first_name: bidder?.bidder_name?.split(" ")?.[0] ?? "",
            last_name: bidder?.bidder_name?.split(" ")?.[1] ?? "",
            company_name: bidder?.bidder_company_name,
            display_name: bidder?.bidder_name,
            email: bidder?.bidder_email,
            type: bidder?.type,
            type_name: bidder?.type_name,
          })) : []}
          groupCheckBox={false}
          projectId={0}
          additionalContactDetails={0}
        />
      )}
      <ConfirmModal
        isOpen={confirmBidderDialogOpen}
        isLoading={isLoading}
        modaltitle={_t("Delete")}
        description="Are you sure you want to delete this Bidder?"
        onAccept={() => {
          handleDeleteBidderData();
        }}
        onDecline={() => {
          setSelectedBidderDelete(null);
          setConfirmBidderDialogOpen(false);
        }}
        onCloseModal={() => {
          setSelectedBidderDelete(null);
          setConfirmBidderDialogOpen(false);
        }}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-trash-can"
        zIndex={9999}
      />
      {confirmSendBidInviteDialogOpen && (
        <ConfirmModal
          isOpen={confirmSendBidInviteDialogOpen}
          isLoading={isLoading}
          modaltitle={`${selectedBidderForInvite?.sent_date ? "Resend" : "Send"} Bid Invitation`}
          description={`Do you want to ${selectedBidderForInvite?.sent_date ? "resend" : "send"} a bid invite to this contractor?`}
          onAccept={confirmSendBidInvite}
          onDecline={() => setConfirmSendBidInviteDialogOpen(false)}
          onCloseModal={() => setConfirmSendBidInviteDialogOpen(false)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-envelope"
          zIndex={9999}
        />
      )}
    </>
  );
};

export default Bidders;
