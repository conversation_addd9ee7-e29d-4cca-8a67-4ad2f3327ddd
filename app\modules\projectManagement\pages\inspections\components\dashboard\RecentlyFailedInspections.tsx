import { useEffect, useMemo, useRef, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import {
  CellClickedEvent,
  type GridApi,
  type GridReadyEvent,
} from "ag-grid-community";
import { getGConfig } from "~/zustand";
import { useInAppDispatch, useInAppSelector } from "../../redux/store";
import { fetchInspectionsDashboardApi } from "../../redux/action/inspectionDashAction";
import { routes } from "~/route-services/routes";
import { useNavigate } from "@remix-run/react";
import { sanitizeString } from "~/helpers/helper";

const RecentlyFailedInspections = () => {
  const { _t } = useTranslation();
  const { module_name }: GConfig = getGConfig();
  const dispatch = useInAppDispatch();
  const gridApiRef = useRef<GridApi | null>(null);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IInspectionsFailsInspectionsData[]>(
    []
  );
  const { get_recently_fails_inspections, isDashboardLoading } =
    useInAppSelector((state) => state.inspectionsDashboard);
  const navigate = useNavigate();

  const handleClickRefresh = async () => {
    setisCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchInspectionsDashboardApi({
        refresh_type: "get_recently_fails_inspections",
      })
    );
    setisCashLoading(false);
  };

  const hasData = useMemo(() => {
    return (
      get_recently_fails_inspections &&
      get_recently_fails_inspections?.data.length > 0
    );
  }, [get_recently_fails_inspections]);

  useEffect(() => {
    if (!isCashLoading && get_recently_fails_inspections?.data) {
      setRowData(get_recently_fails_inspections?.data);
    }
  }, [get_recently_fails_inspections, isCashLoading]);

  const columnDefs = [
    {
      headerName: _t("Insp.") + " #",
      field: "id",
      minWidth: 70,
      maxWidth: 70,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionsFailsInspectionsData }) => {
        const { data } = params;
        const idToolT = `Insp. #${data?.insp_id}`;
        return data.insp_id ? (
          <Tooltip title={idToolT}>
            <Typography className="table-tooltip-text">
              {data.insp_id}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Submitted"),
      field: "date",
      minWidth: hasData ? 135 : 90,
      maxWidth: hasData ? 135 : 90,
      suppressMenu: true,
      cellRenderer: (params: { data: IInspectionsFailsInspectionsData }) => {
        const { data } = params;
        return data.insp_date ? (
          <DateTimeCard format="date" date={data.insp_date} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: hasData ? 130 : 70,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionsFailsInspectionsData }) => {
        const { data } = params;
        const projectName = HTMLEntities.decode(
          sanitizeString(data?.project_name)
        );
        if (!projectName) return <>-</>;
        return (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "type",
      minWidth: hasData ? 120 : 60,
      maxWidth: hasData ? 120 : 60,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: { data: IInspectionsFailsInspectionsData }) => {
        const { data } = params;
        const type = HTMLEntities.decode(sanitizeString(data?.inspection_type));
        if (!type) return <>-</>;
        return (
          <Tooltip title={type}>
            <Typography className="table-tooltip-text">{type}</Typography>
          </Tooltip>
        );
      },
    },
  ];

  const noRowsOverlay = () => (
    <StaticTableRowLoading columnDefs={columnDefs} limit={5} />
  );

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-recently-failed-inspections.svg`}
    />
  );

  return (
    <>
      <DashboardCardHeader
        title={_t(`Recently Failed ${module_name}`)}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={get_recently_fails_inspections?.last_refres_time}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px] ag-grid-cell-pointer">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            key={isDashboardLoading ? "loading" : "loaded"}
            onCellClicked={(params: CellClickedEvent) => {
              const column = params.column;
              if (
                column.getColDef().field !== "" &&
                column.getColDef().field !== "email"
              ) {
                navigate(
                  `${routes.MANAGE_INSPECTION.url}/${params?.data?.inspection_id}`
                );
              }
            }}
            noRowsOverlayComponent={
              isCashLoading || isDashboardLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};
export default RecentlyFailedInspections;
