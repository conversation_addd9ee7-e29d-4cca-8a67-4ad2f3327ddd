import { faEnvelopeOpenDollar } from "@fortawesome/pro-regular-svg-icons/faEnvelopeOpenDollar";
import { faSquare } from "@fortawesome/pro-solid-svg-icons";
import { faReceipt } from "@fortawesome/pro-regular-svg-icons/faReceipt";
import { faVideo } from "@fortawesome/pro-regular-svg-icons/faVideo";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { NavigateFunction, useNavigate, useParams } from "@remix-run/react";
import {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from "react";
import { apiRoutes } from "~/route-services/routes";
import { CFButton } from "~/components/third-party/ant-design/cf-button";
import {
  CFConfirmHeader,
  CFConfirmModal,
} from "~/components/third-party/ant-design/cf-confirm-modal";
import { CFIconButton } from "~/components/third-party/ant-design/cf-iconbutton";
import CFInlineInput from "~/components/third-party/ant-design/cf-inline-input";
import { CFTooltip } from "~/components/third-party/ant-design/cf-tooltip";
import ModuleTimelineFooter from "~/components/page/common/module-timeline-footer";
import {
  getBillDetailLoading,
  getBillsDetails,
} from "~/zustand/pages/manage-bills/store";
import CFProject from "~/components/third-party/ant-design/cf-project";
import {
  getCommonSidebarCollapse,
  getGConfig,
  getGModuleByKey,
  getGSettings,
  getIsLoading,
  setCommonSidebarCollapse,
  setIsLoading,
  useGModules,
} from "~/zustand";
import isEmpty from "lodash/isEmpty";
import {
  setBillsEdit,
  setCopyBill,
  setGenerateInvoice,
  setManageBills,
  setManageBillsDetails,
  setViewBillDetailPdf,
  updateBillDetailByKey,
} from "~/zustand/pages/manage-bills/actions";
import BillDetailPdfView from "~/components/modals/pages/manage-billslist/bill-detail-pdf-view";
import PostPayment from "~/components/sidebars/post-payment";
import { DetailsTab } from "~/components/page/bills/tabs";
import { useIframe, useTranslation } from "~/hook";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import {
  Int,
  Number,
  getApiDefaultParams,
  sanitizeString,
} from "~/helpers/helper";
import { getApiData } from "~/helpers/axios-api-helper";
import ShareInternalLinkModal from "~/components/modals/share-internal-link-modal";
import { customerSideModuleWiseOptions, defaultConfig } from "~/data";
import { setSendEmailOpenStatus } from "~/components/sidebars/multi-select/customer/zustand/action";
import ConfirmDeleteModal from "~/components/modals/confirm-delete-modal";
// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// Other
import { sendMessageKeys } from "~/components/page/$url/data";
import { getProjectModules } from "~/zustand/global-project-complete/store";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
// import { useAppSelector } from "~/components/page/bills/redux/store";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { useAppESDispatch } from "~/modules/financials/pages/estimates/redux/store";
import { getStatusList } from "~/redux/action/getStatusListAction";
import { i } from "vite/dist/node/types.d-aGj9QkWt";
import FilesTab from "~/modules/financials/pages/bills/components/tab/details/FilesTab";

import {
  fieldStatus,
  statusIconMap,
} from "~/modules/financials/pages/bills/utils/constants";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { isValidStatus } from "~/modules/projectManagement/pages/todo/utils/common";
// import { useAppTDSelector } from "~/modules/projectManagement/pages/todo/redux/store";
import { updateTaskStatus } from "~/modules/projectManagement/pages/todo/components/tab/details/common";
import { defaultStatus } from "~/modules/projectManagement/pages/todo/utils/constants";
import { MenuProps, Spin } from "antd";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { getStatusForField } from "~/shared/utils/helper/common";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import BillNotes from "~/modules/financials/pages/bills/components/tab/details/BillNotes";
import AddNotesCard from "~/components/page/common/add-notes-card/add-notes-card";

import AttachmentSection from "~/shared/components/organisms/attachmentSection/AttachmentSection";
import { setPermitsAttachEdit } from "~/zustand/pages/manage-permits/actions";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { InputField } from "~/shared/components/molecules/inputField";
import { GradientIcon } from "~/shared/components/molecules/gradientIcon";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";

interface ContactDetailsProps {
  accessContactList?: boolean;
}

interface FormStatusType {
  group_name: InlineInputStatus;
  project_id: InlineInputStatus;
}

const ManageBillsTab = ({ accessContactList = true }: ContactDetailsProps) => {
  const params: RouteParams = useParams();
  const billDetails: BillDetails = getBillsDetails();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const navigate: NavigateFunction = useNavigate();
  const gConfig: GConfig = getGConfig();
  const gProjectModules: IProjectModules = getProjectModules();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const [fileUrl, setFileUrl] = useState<string>("");
  const [billsDetailPdfViewOpen, setBillsDetailPdfViewOpen] =
    useState<boolean>(false);
  const [confirmCopyBillDialogOpen, setConfirmCopyBillDialogOpen] =
    useState<boolean>(false);
  const loading = getIsLoading();
  const { _t } = useTranslation();
  const [itemBox, setItemBox] = useState<boolean>();
  const [shareLinkModalOpen, setShareLinkModalOpen] = useState<boolean>(false);
  const [shareLink, setShareLink] = useState<string>("");
  const [multiSelectOpen, setMultiSelectOpen] = useState<CustomerTabs | "">(
    defaultConfig.employee_key
  );
  const { module_name: invoiceModuleName = "Invoice" } =
    (getGModuleByKey(defaultConfig.invoice_merge_module_key) as GModule) || {};

  const { plural_name: expenseModuleName = "Expense" } =
    (getGModuleByKey(defaultConfig.expense_module) as GModule) || {};

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const { module_name: paymentModuleName = "Payment" } =
    (getGModuleByKey(defaultConfig.payment_module) as GModule) || {};
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<number>();
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const [statusChange, setStatusChange] = useState<string | number>();
  const [closeTopDropBox, setCloseTopDropBox] = useState<string>("");
  const [archiveItem, setArchiveItem] = useState<Partial<BillDetails>>();
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isLoading, setIsdeleteLoading] = useState<boolean>(false);
  const [formStatus, setFormStatus] = useState<FormStatusType>({
    group_name: {
      message: "",
      status: "button",
      isOnFocus: false,
    },
    project_id: {
      message: "",
      status: "button",
    },
  });
  const { tab, id }: RouteParams = useParams();
  const dispatch = useAppESDispatch();
  const { checkModuleAccessByKey } = useGModules();
  const [isPdfLoading, setIsPdfLoading] = useState<boolean>(true);
  const billDetailLoading: boolean = getBillDetailLoading();
  const { parentPostMessage } = useIframe();
  const [billStatus, setBillStatus] = useState<IStatusList>();
  const [activeStep, setActiveStep] = useState<string | number>("");
  const selectedBillId: string = billDetails?.bill_id;
  const gSettings: GSettings = getGSettings();
  const [selectedStatusKey, setSelectedStatusKey] = useState<string>("");
  const [defaultColor, setDefaultColor] = useState<string>("");
  const [updatedAwsFilesUrl, setUpdatedAwsFilesUrl] = useState(
    billDetails?.aws_files || []
  );
  const { module_id, module_access, module_key }: GConfig = getGConfig();

  const paymentModuleNoAccess = useMemo(
    () =>
      checkModuleAccessByKey(defaultConfig.payment_module) === "read_only" ||
      checkModuleAccessByKey(defaultConfig.payment_module) === "no_access",
    []
  );
  const hasInvoiceAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(
      defaultConfig.invoice_merge_module_key
    );
    return mAccess === "no_access" || mAccess === "read_only";
  }, []);

  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: (e: React.MouseEvent) => {
        generateInvoiceHandler((url) => {
          window.open(url, "_blank", "noopener,noreferrer");
        });
      },
    },
  ];

  const handleClick = async () => {
    setBillsDetailPdfViewOpen(true);
    await setViewBillDetailPdf({
      module_id,
      status: 1,
      record_id: billDetails?.bill_id,
    });
    setIsPdfLoading(false);
  };
  const loadingStatusChange = (
    key: keyof FormStatusType,
    value: InlineInputStatus
  ) => {
    if (key) {
      setFormStatus((prev) => ({
        ...prev,
        [key]: {
          ...prev?.[key],
          ...(value ?? {}),
        },
      }));
    }
  };

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const billStatList: IStatusList[] = useMemo(
    () =>
      (billStatus ?? [])?.map((item: ITodoStatus) => ({
        label: HTMLEntities.decode(sanitizeString(item?.display_name)),
        value: item?.type_id.toString(),
        default_color: item?.default_color,
        icon: statusIconMap[item?.name],
      })) as IStatusList[],
    [billStatus]
  );

  const BillStatusVal: IStatusList | undefined = useMemo(
    () => billStatList?.find((item: IStatusList) => item.label == activeStep),
    [activeStep, billStatList]
  );
  useEffect(() => {
    if (!billDetails?.bill_status_name) {
      if (billDetails?.due_balance <= 0 && billDetails.payments.length) {
        setActiveStep("Paid");
        setDefaultColor("#009900");
      } else {
        setActiveStep("Open");
        setDefaultColor("#FF5400");
      }
    } else {
      setActiveStep(billDetails?.bill_status_name);
    }
    setSelectedStatusKey(billDetails?.bill_status_name || "");
  }, [billDetails?.bill_status_name, billStatList]);

  useEffect(() => {
    if (id) {
      dispatch(
        getStatusList({
          types: ["bill_status"],
        })
      )
        .then((resData) => {
          if (resData?.payload) {
            setBillStatus(resData?.payload?.data?.bill_status);
          } else {
            console.error(
              "Failed to fetch bill status:",
              resData?.payload?.message
            );
          }
        })
        .catch((error) => {
          console.error("Error fetching bill status:", error);
        });
    }
  }, [id, dispatch]);

  const status = useMemo(() => {
    const statusList = billStatList?.map((item: IStatusList) => ({
      label: HTMLEntities.decode(sanitizeString(item.label)),
      key: item?.label ?? "",
      icon: (
        <FontAwesomeIcon
          icon={faSquare}
          className="h-3.5 w-3.5"
          style={{
            color: item?.default_color,
          }}
        />
      ),
    }));

    const getSelectStatus = billStatList?.find(
      (item: IStatusList) => item?.label === activeStep
    ) || { label: activeStep, default_color: defaultColor };

    const selectStatus = (
      <Tooltip title={getSelectStatus?.label}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full group/status-dropdown px-2.5`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {getSelectStatus?.label}
          </Typography>
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus };
  }, [billStatList, activeStep]);

  const handleStatusBill: MenuProps["onClick"] = (e) => {
    const sort_Value = billStatList?.find((item) => item?.label === e?.key);
    if (
      !billDetails?.items?.length &&
      activeStep === "Open" &&
      activeStep !== e.key
    ) {
      notification.error({
        description: "Please create a payment",
      });
      return;
    }

    if (billDetails?.items?.length) {
      if (
        activeStep !== e.key &&
        (!billDetails?.payments?.length || Number(billDetails?.due_balance) > 0)
      ) {
        notification.error({
          description: "The bill must be paid in full to be marked as 'Paid'",
        });
        return;
      }

      if (Number(billDetails?.due_balance) <= 0 && activeStep !== e.key) {
        notification.error({
          description: "Bill is Paid",
        });
      }
    }
  };

  useEffect(() => {
    if (formStatus?.group_name?.status !== "error") {
      loadingStatusChange("group_name", { message: "" });
    }
  }, [formStatus?.group_name?.status]);

  useEffect(() => {
    if (formStatus?.project_id?.status !== "error") {
      loadingStatusChange("project_id", { message: "" });
    }
  }, [formStatus?.project_id?.status]);

  const handleShareLink = (record_id: number | undefined) => {
    try {
      getApiData({
        url: apiRoutes.GENERATE_SHARE_LINK.url,
        method: "post",
        data: {
          record_id,
          module_key: "bills",
          module_page: "manage-bills",
        },
        // getApiDefaultParams({
        //   user: gUser,
        //   otherParams: {
        //     record_id: params?.bill_id,
        //     module_key: "bills",
        //     module_page: "manage-bills",
        //   },
        // }),
        success: (response: GenerateLinkData) => {
          setShareLink(response?.data?.short_link);
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };
  const invoiceModuleNoAccess = useMemo(() => {
    const permission = checkModuleAccessByKey(
      defaultConfig.invoice_merge_module_key
    );
    return ["read_only", "no_access"].includes(permission);
  }, []);

  const generateInvoiceHandler = useCallback(
    (navigateCustom: (url: any) => void) => {
      if (
        gSettings?.quickbook_sync !== "0" &&
        billDetails?.items
          .filter(
            (obj: { item_category: string }) =>
              obj?.item_category !== "category"
          )
          ?.some(
            (item: Partial<ItemSideData>) =>
              Number(item?.cost_code_id) === 0 &&
              item?.is_retainage_item !== "1"
          )
      ) {
        notification.error({
          description: "One or more items are missing Cost code.",
        });
      } else if (billDetails?.project_id) {
        setGenerateInvoice(billDetails?.bill_id, navigateCustom);
      } else {
        notification.error({
          description: "Please select project.",
        });
      }
    },
    [setGenerateInvoice, billDetails, gSettings?.quickbook_sync]
  );

  const customNavigation = useCallback(
    (url: string, tab: "current" | "new") => {
      if (tab === "current") {
        navigate(url);
      } else {
        window.open(url, "_blank", "noopener,noreferrer");
      }
    },
    [navigate]
  );

  const BillsOptions = [
    {
      label: "View/Email PDF",
      icon: "fa-regular fa-file-pdf",
      onClick: () => {
        handleClick();
      },
    },
    {
      label: `Generate an ${invoiceModuleName}`,
      icon: "fa-regular fa-file-invoice-dollar",
      disabled: invoiceModuleNoAccess,
      onClick: (e: any) => {
        // Extract the original DOM event
        const domEvent = e.domEvent;
        generateInvoiceHandler((url) => {
          const isNewTab =
            domEvent &&
            (domEvent?.ctrlKey || domEvent?.metaKey) &&
            billDetails?.bill_id;
          customNavigation(url, isNewTab ? "new" : "current");
        });
      },
      onAuxClick: (e: any) => {
        if (e.button === 1) {
          generateInvoiceHandler((url) => {
            customNavigation(url, "new");
          });
        }
      },
      onContextMenu: (e: React.MouseEvent<any>) => {
        e.preventDefault();
        setContextMenu({ x: e.clientX, y: e.clientY, visible: true });
      },
    },
    {
      label: `Post ${paymentModuleName}`,
      icon: faEnvelopeOpenDollar,
      onClick: () => {
        setItemBox(true);
      },
      postPayment: true,
      disabled: paymentModuleNoAccess,
    },
    {
      label: `Copy ${gConfig.module_singular_name}`,
      icon: faReceipt,
      onClick: () => setConfirmCopyBillDialogOpen(true),
    },
    {
      label: `${gConfig.module_name} vs ${expenseModuleName}`,
      icon: faVideo,
      onClick: () => {
        window.open("https://vimeo.com/399761037", "_blank");
      },
    },
    {
      content: "Share Internal Link",
      icon: "fa-regular fa-share-nodes",
      onlyIconView: true,
      onClick: () => {
        setCloseTopDropBox(new Date().valueOf().toString());
        setShareLinkModalOpen(true);
        handleShareLink(billDetails?.bill_id);
      },
    },
    {
      content:
        Int(billDetails?.is_deleted) === 0 ? (
          <>
            {_t("Status: Active")} <br />
            {_t("Click to Archive the item")}
          </>
        ) : (
          <>
            {_t("Status: Archived")} <br />
            {_t("Click to Activate the item")}
          </>
        ),
      icon:
        Int(billDetails?.is_deleted) === 0
          ? "fa-regular fa-box-archive"
          : "fa-regular fa-regular-active",
      onlyIconView: true,
      onClick: () => {
        setCloseTopDropBox(new Date().valueOf().toString());
        setArchiveItem(billDetails);
        setConfirmArchiveDialogOpen(true);
        setStatusChange(billDetails?.is_deleted);
      },
    },
    {
      content: "Delete",
      icon: "fa-regular fa-trash-can",
      onlyIconView: true,
      onClick: () => {
        setCloseTopDropBox(new Date().valueOf().toString());
        setConfirmDialogOpen(true);
        setDeleteId(billDetails?.bill_id);
      },
      disabled: allow_delete_module_items === "0" || gConfig?.page_is_iframe,
    },
  ];
  const optionwithoutPayment = (BillsOptions || [])?.filter(
    (ele) => !ele?.postPayment
  );

  const hasDueBalance = Number(billDetails?.due_balance) / 100 > 0;
  const options = hasDueBalance ? BillsOptions : optionwithoutPayment;
  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const response = (await webWorkerApi({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: {
          ...tempFormData,
          send_me_copy: ccMailCopy ? 1 : 0,
          send_custom_email: 0,
        },
      })) as Omit<IApiCallResponse, "success"> & { success: boolean | string };
      if (
        (typeof response?.success === "boolean" && response?.success) ||
        (typeof response?.success !== "boolean" &&
          response?.success?.toString() === "1")
      ) {
        closeSendMailSidebar();
        setShareLinkModalOpen(false);
        setSendEmailOpenStatus(false);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      console.error(
        `\n File: #header-link.tsx -> Line: #47 -> `,
        (error as Error)?.message
      );
      notification.error({
        description: "Something went wrong. Please try again.",
      });
    }
  };

  const handleDelete = () => {
    setIsdeleteLoading(true);
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key: deleteId,
            module_key: "bills",
            remove_associated_data: 0,
          },
        }),
        success: (response: { message: string }) => {
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            setConfirmDialogOpen(false);
            setManageBills({});
            navigate(`/manage-bills`);
          }
        },
        error: (description) => {
          setIsdeleteLoading(false);
          notification.error({
            description,
          });
        },
      });
    } catch (error: unknown) {
      setIsdeleteLoading(false);
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleStatus = (bill: Partial<BillDetails> | undefined) => {
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: bill?.bill_id,
            module_key: "bills",
            status: bill?.is_deleted?.toString() === "0" ? 1 : 0,
          },
        }),
        success: (response: { message: string }) => {
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            navigate(`/manage-bills`);
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const updateCustomBillId = (billId: string | undefined) => {
    const customBillId = billId?.trim();
    if (isEmpty(customBillId)) {
      const errorMessage = "Bill # field is required.";
      notification.error({
        description: errorMessage,
      });
      loadingStatusChange("group_name", {
        status: "error",
        message: errorMessage,
      });
    } else if (customBillId) {
      if (params && params.id) {
        setBillsEdit({
          onError: (message) => {
            loadingStatusChange("group_name", {
              status: "error",
              message,
            });
            setIsFocused(false);
          },
          module_id: gConfig?.module_id,
          billId: params.id,
          bill: {
            custom_bill_id: HTMLEntities.encode(customBillId),
          },
          setSuccess: (success: boolean) => {
            if (success) {
              setIsFocused(false);
              loadingStatusChange("group_name", {
                status: "success",
                message: "",
              });
              updateBillDetailByKey(
                "custom_bill_id",
                HTMLEntities.encode(customBillId)
              );
              setManageBillsDetails({
                id: params.id || "",
              });
            } else {
              loadingStatusChange("group_name", {
                status: "error",
                message: "Something wrong!",
              });
            }
          },
        });
      } else {
        notification.error({
          description: "Record id not available",
        });
        setIsFocused(false);
      }
    }
  };
  const permit = {
    permit_id: params?.id,
    project_id: billDetails?.project_id,
  };

  useEffect(() => {
    setUpdatedAwsFilesUrl(billDetails?.aws_files);
  }, [billDetails]);

  const onFilesChange: IAttachmentCardProps["onAddAttachment"] = (files) => {
    const updatedImage = files.filter(
      (file) =>
        !(billDetails?.aws_files || []).some(
          (exist: { image_id: number }) => exist.image_id === file.image_id
        )
    );
    const updatedAwsFiles = updatedImage.map((file) => ({
      ...file,
      isheic: file.file_ext.toLowerCase() === "heic" ? 1 : 0, // Add 'isheic' key
    }));
    setUpdatedAwsFilesUrl([...updatedAwsFiles, ...updatedAwsFilesUrl]);
    setPermitsAttachEdit(
      user,
      permit,
      Number(module_id),
      module_key,
      updatedAwsFiles,
      () => {
        setManageBillsDetails({
          id: params?.id || "",
        });
      }
    );
  };
  const deleteFile = (data: { image_id?: number }) => {
    if (!!data?.image_id) {
      setManageBillsDetails({
        id: params?.id || "",
      });
    }
  };

  const NoRecordComp = () => {
    return (
      <>
        <GradientIcon
          title={_t(`Files`)}
          svgIcons={{
            icon: "fa-solid fa-file-image",
            containerClassName:
              "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
            id: "add_attachments_icon",
            colors: ["#50EBFD", "#5996E9"],
          }}
        />

        <NoRecords
          className="mx-auto"
          image={`${window.ENV.CDN_URL}assets/images/no-records-files.svg`}
        />
      </>
    );
  };

  const renderContent = () => {
    switch (tab) {
      case "details":
      case undefined:
        return (
          <>
            <div
              className={`pb-[15px]  ${
                gConfig?.module_read_only
                  ? "md:min-h-[calc(100vh-314px)] min-h-[calc(100vh-294px)]"
                  : "lg:min-h-[calc(100vh-280px)] md:min-h-[calc(100vh-322px)] min-h-[calc(100vh-328px)]"
              }`}
            >
              {params.id && (!params.tab || params.tab === "details") && (
                <DetailsTab />
              )}
            </div>
          </>
        );

      case "notes":
        return (
          <div
            className={`py-3 md:col-span-2 px-[15px] common-card ${
              !Boolean(billDetails?.notes_data) && "min-h-[150px]"
            }`}
          >
            <Suspense fallback={<Spin />}>
              <AddNotesCard
                projectid={billDetails?.project_id}
                hideBorder={!(billDetails?.notes_data?.length > 0)}
                notesData={billDetails?.notes_data ?? []}
                id={billDetails?.bill_id}
                refreshDataFun={() => {
                  setManageBillsDetails({
                    id: params?.id || "",
                  });
                }}
              />
            </Suspense>
          </div>
        );

      case "files":
        return (
          <div
            className={`py-3 md:col-span-2 px-[15px] common-card ${
              module_access === "read_only" &&
              (!billDetails?.aws_files || billDetails?.aws_files?.length === 0)
                ? ""
                : "min-h-[150px]"
            }`}
          >
            <Suspense fallback={<Spin />}>
              {!Boolean((billDetails?.aws_files ?? []).length) &&
              module_access === "read_only" ? (
                <NoRecordComp />
              ) : (
                <AttachmentSection
                  projectid={billDetails?.project_id}
                  title="Files"
                  files={
                    (updatedAwsFilesUrl?.length == 0
                      ? billDetails?.aws_files
                      : updatedAwsFilesUrl) || []
                  }
                  onAddAttachment={onFilesChange}
                  isAddAllow={module_access !== "read_only"}
                  onDeleteFile={deleteFile}
                  onFileUpdated={(data) => {
                    const value = data as Partial<IgetUpdatedFileRes>;
                    if (
                      value &&
                      value.success &&
                      value.data?.image_id // this condition is true when pdf edit
                    ) {
                      setManageBillsDetails({
                        id: params?.id || "",
                      });
                    }
                  }}
                  isReadOnly={module_access === "read_only"}
                  options={["new", "gallery", "google", "dropbox", "url"]}
                  validationParams={{
                    date_format,
                    file_support_module_access: checkModuleAccessByKey(
                      defaultConfig.file_support_key
                    ),
                    image_resolution,
                    module_key,
                    module_id,
                    module_access,
                  }}
                />
              )}
            </Suspense>
          </div>
        );

      default:
        return <></>;
    }
  };
  return (
    <>
      {billDetailLoading || loading ? (
        <div className="ease-in-out duration-300 w-full overflow-y-auto">
          <div className="sticky top-0 z-[99] bg-[#f8f8f9] p-[15px] pb-0 mb-[15px]">
            <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
              <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
                <TopBarSkeleton num={3} />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="ease-in-out duration-300 w-full overflow-y-auto">
          <div className="sticky top-0 z-[99] bg-[#f8f8f9] p-[15px] pb-0 mb-[15px]">
            <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
              <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
                <div className="flex items-center lg:w-[calc(100%-150px)]">
                  <div
                    className={`w-11 h-11 flex items-center justify-center rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white`}
                    style={{
                      backgroundColor: BillStatusVal?.default_color,
                    }}
                  >
                    {BillStatusVal?.icon && (
                      <FontAwesomeIcon
                        className="w-[18px] h-[18px] text-white"
                        icon={BillStatusVal?.icon}
                      />
                    )}
                  </div>
                  <div className="pl-2.5 max-w-[calc(100%-44px)]">
                    <div
                      className={`${
                        !isEmpty(billDetails?.project_id) ||
                        !isEmpty(billDetails?.project_name)
                          ? "sm:!max-w-fit"
                          : "!max-w-full"
                      }`}
                    >
                      <CFProject
                        name="project_id"
                        labelPlacement="left"
                        editInline={true}
                        placeholder="Select Project"
                        value={
                          Number(billDetails?.project_id)
                            ? {
                                id: Number(billDetails?.project_id),
                              }
                            : undefined
                        }
                        headerTooltip={`Project: ${
                          Number(billDetails?.project_id) &&
                          billDetails?.project_name
                            ? billDetails.project_name
                            : undefined
                        }`}
                        iconView={true}
                        mainReadOnlyClassName="sm:w-fit"
                        className="h-6 py-0 w-full gap-0"
                        readOnlyClassName="text-base font-medium whitespace-nowrap truncate sm:block flex"
                        inputClassName="w-fit"
                        fieldClassName="w-auto"
                        spanWidthClass="w-fit"
                        buttonClassName="!text-base font-medium"
                        statusProps={{
                          className: "right-6 flex",
                          iconProps: {
                            className: "!w-[15px] !h-[15px]",
                          },
                          status: formStatus?.project_id?.status,
                        }}
                        errorMessage={formStatus?.project_id?.message}
                        setStatus={(status: IStatus) => {
                          loadingStatusChange("project_id", {
                            status: status as InputStatus,
                          });
                        }}
                        onChange={(project: Partial<Project>) => {
                          loadingStatusChange("project_id", {
                            status: "loading",
                          });
                          if (params && params.id) {
                            setBillsEdit({
                              onError: (message) => {
                                loadingStatusChange("project_id", {
                                  status: "error",
                                  message,
                                });
                              },
                              module_id: gConfig?.module_id,
                              billId: params.id,
                              bill: {
                                project_id: project?.id || 0,
                                ref_po: "0",
                              },
                              setSuccess: (success: boolean) => {
                                if (success) {
                                  loadingStatusChange("project_id", {
                                    status: "success",
                                    message: "",
                                  });
                                  setManageBillsDetails({
                                    id: params?.id || "",
                                  });
                                } else {
                                  loadingStatusChange("project_id", {
                                    status: "error",
                                    message: "Something wrong!",
                                  });
                                }
                              },
                            });
                          } else {
                            notification.error({
                              description: "Record id not available",
                            });
                          }
                        }}
                        handleError={(error: string) => {
                          notification.error({
                            description: error,
                          });
                        }}
                        rightIcon={
                          <ProjectFieldRedirectionIcon
                            projectId={billDetails?.project_id}
                          />
                        }
                        readOnly={gConfig?.module_read_only}
                        projectApiParams={{
                          is_completed:
                            Number(
                              gProjectModules?.[
                                gConfig?.module_key as keyof IProjectModules
                              ]
                            ) !== 1,
                        }}
                      />
                    </div>
                    <div
                      className={`flex items-center gap-2 mt-[3px] ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex items-center gap-2 mt-[3px]">
                        <Dropdown
                          menu={{
                            items: status?.statusList,
                            selectable: true,
                            selectedKeys: [activeStep as string],
                            onClick: handleStatusBill,
                          }}
                          disabled={true} //this will set fixed as per the https://app.clickup.com/t/86cydj85b
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "status")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(loadingStatus, "status")}
                          />
                        )}
                      </div>
                      <CFTooltip
                        content={`${_t("Bill")} #${
                          HTMLEntities.decode(
                            billDetails?.company_bill_id || ""
                          ) ??
                          HTMLEntities.decode(
                            sanitizeString(billDetails?.custom_bill_id)
                          )
                        }`}
                        placement="topLeft"
                        /*
                      open value => if isOnFocus is true then tooltip not open but isOnFocus is false then default functionality work
                    */

                        //  this code will comment to solved this bugs:- https://app.clickup.com/t/86cxk2nfy

                        // open={
                        //   formStatus?.group_name?.isOnFocus ? false : undefined
                        // }
                      >
                        <div
                          className={`max-w-full  ${
                            gConfig?.module_read_only ||
                            gSettings?.is_custom_bill_id == 0
                              ? "w-fit"
                              : "sm:min-w-[188px] md:w-full w-full"
                          }`}
                        >
                          <CFInlineInput
                            className="!h-5 !py-0 page-header-subtitle-field"
                            name="group_name"
                            inputStatusClassName="!w-4 !h-4"
                            placeholder={_t("Bill") + " #"}
                            readOnlyClassName="text-13 !h-auto"
                            maxLength={21}
                            value={
                              HTMLEntities.decode(
                                billDetails?.company_bill_id || ""
                              ) ??
                              HTMLEntities.decode(
                                sanitizeString(billDetails?.custom_bill_id)
                              )
                            }
                            onFocus={() => {
                              loadingStatusChange("group_name", {
                                isOnFocus: true,
                              });
                            }}
                            requiredField={true}
                            status={formStatus?.group_name?.status}
                            setStatus={(status: InputStatus) => {
                              loadingStatusChange("group_name", { status });
                            }}
                            onKeyDown={(
                              event: React.KeyboardEvent<HTMLInputElement>
                            ) => {
                              if (event.key === "Enter") {
                                updateCustomBillId(event.currentTarget.value);
                              }
                            }}
                            onBlur={(value: string) => {
                              loadingStatusChange("group_name", {
                                isOnFocus: false,
                              });
                              updateCustomBillId(value);
                            }}
                            readOnly={
                              gConfig?.module_read_only ||
                              gSettings?.is_custom_bill_id == 0
                            }
                            errorMessage={formStatus?.group_name?.message}
                            isTrimValue={true}
                          />
                        </div>
                      </CFTooltip>
                      {gSettings?.is_custom_bill_id == 0 && (
                        <CFTooltip content='Due to the "Start at Number" setting selected in the Bills configuration, you are unable to modify the Bill number.'>
                          <FontAwesomeIcon
                            className="text-base w-3.5 h-3.5 text-primary-900/80 dark:text-white/90"
                            icon="fa-regular fa-circle-info"
                          />
                        </CFTooltip>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex justify-between lg:w-fit w-full gap-2.5">
                  {!window.ENV.PAGE_IS_IFRAME && (
                    <div className="flex gap-2.5">
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                      <div>
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-bars"
                          onClick={() =>
                            setCommonSidebarCollapse(!sidebarCollapse)
                          }
                        />
                      </div>
                    </div>
                  )}
                  <ul className="flex items-center justify-end gap-2.5">
                    <li>
                      <CFTooltip content={"Refresh"} placement="top">
                        <CFIconButton
                          variant="default"
                          htmlType="button"
                          className={`group/refresh w-[34px] min-w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 dark:!bg-dark-900 rounded ${
                            loading || billDetailLoading
                              ? "hover:!bg-transparent"
                              : "hover:!bg-deep-orange-500/5 dark:hover:!bg-deep-orange-500/5 hover:scale-105"
                          }`}
                          iconClass={`text-primary-900 rounded ${
                            loading || billDetailLoading
                              ? "fa-spin group-hover/refresh:!text-primary-900"
                              : "group-hover/refresh:!text-deep-orange-500 group-hover/refresh:scale-105"
                          }`}
                          disabled={loading || billDetailLoading}
                          icon="fa-regular fa-arrow-rotate-right"
                          onClick={() => {
                            setIsLoading(true);
                            setManageBillsDetails({
                              id: params?.id || "",
                              callComplete: () => setIsLoading(false),
                              addEvent: true,
                            });
                          }}
                        />
                      </CFTooltip>
                    </li>
                    {!gConfig?.module_read_only ? (
                      <li>
                        <DropdownMenu
                          options={options}
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          icon="fa-regular fa-ellipsis-vertical"
                          tooltipcontent={_t("More")}
                          {...((paymentModuleNoAccess ||
                            hasInvoiceAccessShowMessages ||
                            user?.allow_delete_module_items === "0") && {
                            footerText: _t(
                              "Some actions might be unavailable depending on your privilege."
                            ),
                          })}
                        />
                      </li>
                    ) : null}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <ReadOnlyPermissionMsg
            className="p-4 pt-0"
            view={gConfig?.module_read_only}
          />
          <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
            <div
              className={`px-[15px] pb-[15px] ${
                module_access === "read_only"
                  ? window.ENV.PAGE_IS_IFRAME
                    ? "md:min-h-[calc(100dvh-198px)] min-h-[calc(100dvh-256px)]"
                    : "md:min-h-[calc(100dvh-341px)] min-h-[calc(100dvh-408px)]"
                  : window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-164px)] min-h-[calc(100dvh-210px)]"
                  : "md:min-h-[calc(100dvh-305px)] min-h-[calc(100dvh-360px)]"
              }`}
            >
              {billDetailLoading ? (
                <Spin
                  className={`flex items-center justify-center ${
                    window.ENV.PAGE_IS_IFRAME
                      ? "md:h-[calc(100vh-161px)] h-[calc(100vh-205px)]"
                      : "md:h-[calc(100vh-304px)] h-[calc(100vh-357px)]"
                  }`}
                />
              ) : (
                <>{renderContent()}</>
              )}
            </div>
            <div className="sm:pb-0 mt-6">
              <TimeLineFooter
                data={{
                  addedDate: billDetails?.date_added || "",
                  addedTime: billDetails?.time_added || "",
                  addedBy: billDetails?.emp_username || "",
                  moduleId: module_id,
                  recordId: Number(params?.id),
                  qbDateAdded: billDetails?.qb_date_added || "",
                  qbTimeAdded: billDetails?.qb_time_added || "",
                }}
                isSynced={
                  billDetails?.quickbook_bill_id &&
                  billDetails.quickbook_bill_id.toString() !== "0"
                    ? `#${billDetails.quickbook_bill_id}`
                    : ""
                }
                hrefLinkDetail={"/bill?txnId=" + billDetails?.quickbook_bill_id}
                sidebarCollapse={sidebarCollapse}
                nameId="txnId"
              />
            </div>
          </div>

          <BillDetailPdfView
            billsDetailPdfViewOpen={billsDetailPdfViewOpen}
            setBillsDetailPdfViewOpen={setBillsDetailPdfViewOpen}
            fileUrl={fileUrl}
            setFileUrl={setFileUrl}
            selectedBillId={Number(selectedBillId)}
            isLoading={isPdfLoading}
            data={billDetails as Partial<TBill>}
          />
        </div>
      )}
      <PostPayment setItemBox={setItemBox} itemBox={itemBox} />

      {shareLinkModalOpen && (
        <ShareInternalLinkModal
          shareLinkModalOpen={shareLinkModalOpen}
          shareLink={shareLink}
          onclickEmailLink={() => {
            setSendEmailOpenStatus(true);
            setSendEmailOpen(true);
            setShareLinkModalOpen(false);
          }}
          onCloseModal={() => setShareLinkModalOpen(false)}
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
            setSendEmailOpenStatus(false);
          }}
          isViewAttachment={false}
          openSendEmailSidebar={sendEmailOpen}
          options={customerSideModuleWiseOptions?.bills}
          singleSelecte={false}
          emailApiCall={emailApiCall}
          customEmailData={{
            body: `A link to a record within Contractor Foreman has been shared with you. <a href = ${shareLink}>View Details</a>.`,
            subject: "Shared Link",
          }}
          appUsers={true}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              defaultConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
            save_a_copy_of_sent_pdf,
          }}
          contactId={0}
        />
      )}

      <ConfirmDeleteModal
        setConfirmDialogOpen={setConfirmDialogOpen}
        confirmDialogOpen={confirmDialogOpen}
        onClick={handleDelete}
        loading={isLoading}
      />

      <CFConfirmModal
        open={confirmArchiveDialogOpen}
        bodyClassName="flex text-center justify-center"
        message={`Are you sure you want to ${
          statusChange == 1
            ? "Activate this data?"
            : `Archive this item? ${
                statusChange == 0
                  ? "To view it or Activate it later, set the filter to show Archived items."
                  : ""
              }`
        }`}
        handelModal={() => setConfirmArchiveDialogOpen(false)}
        header={
          <CFConfirmHeader
            title={`${statusChange == 1 ? "Active" : "Archive"}`}
            icon={
              statusChange == 1 ? (
                <FontAwesomeIcon
                  className="w-3.5 h-3.5"
                  icon={"fa-regular fa-regular-active"}
                />
              ) : (
                <FontAwesomeIcon
                  className="w-3.5 h-3.5"
                  icon="fa-regular fa-box-archive"
                />
              )
            }
            handelModal={() => setConfirmArchiveDialogOpen(false)}
          />
        }
        footer={
          <>
            <CFButton
              variant="primary"
              className="justify-center min-w-[75px] primary-btn"
              onClick={() => {
                setConfirmArchiveDialogOpen(false);
                handleStatus(archiveItem);
              }}
            >
              {_t("Yes")}
            </CFButton>
            <CFButton
              variant="default"
              className="min-w-[75px] justify-center hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
              onClick={() => {
                setConfirmArchiveDialogOpen(false);
              }}
            >
              {_t("No")}
            </CFButton>
          </>
        }
      />

      {/* Custom Context Menu */}
      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />

      {confirmCopyBillDialogOpen && (
        <CFConfirmModal
          open={confirmCopyBillDialogOpen}
          bodyClassName="flex text-center justify-center"
          message={`Are you sure you want to generate a copy of this ${gConfig.module_singular_name}?`}
          handelModal={() => setConfirmCopyBillDialogOpen(false)}
          header={
            <CFConfirmHeader
              title={"Copy"}
              icon={
                <FontAwesomeIcon
                  className="w-3.5 h-3.5"
                  icon={"fa-regular fa-clone"}
                />
              }
              handelModal={() => setConfirmCopyBillDialogOpen(false)}
            />
          }
          footer={
            <>
              <CFButton
                variant="primary"
                className="justify-center min-w-[75px] primary-btn"
                onClick={() => {
                  setCopyBill({
                    id: billDetails?.bill_id,
                    navigate,
                  });
                  setConfirmCopyBillDialogOpen(false);
                }}
              >
                {_t("Yes")}
              </CFButton>
              <CFButton
                variant="default"
                className="min-w-[75px] justify-center hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
                onClick={() => {
                  setConfirmCopyBillDialogOpen(false);
                }}
              >
                {_t("No")}
              </CFButton>
            </>
          }
        />
      )}
    </>
  );
};

export default ManageBillsTab;

export { ErrorBoundary };
