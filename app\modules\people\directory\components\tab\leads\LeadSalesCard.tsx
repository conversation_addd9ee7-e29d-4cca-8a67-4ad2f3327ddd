import { useEffect, useMemo, useRef, useState } from "react";
import dayjs from "dayjs";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
import { useParams } from "@remix-run/react";

import { useTranslation } from "~/hook";
// TODO replace this with redux.
import {
  getGConfig,
  getGModuleByKey,
  getGModuleFilters,
  getGSettings,
} from "~/zustand";
// Molecules
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Redux store
import {
  useAppSelector,
  useAppDispatch,
} from "~/modules/people/directory/redux/store";
// Redux action and slice
import { updateDirSalesDetail } from "../../../redux/slices/dirSalesSlice";
import { updateDirDetailApi } from "../../../redux/action/dirDetailsAction";
import { addCustomData } from "~/redux/action/customDataAction";
// constants
import {
  addItemObject,
  dirSalesDetailsField,
  fieldStatus,
} from "../../../utils/constasnts";
import { customDataTypesByKey } from "~/utils/constasnts";
// Helper
import {
  getStatusForField,
  filterOptionBySubstring,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { addCustomDataAct } from "~/redux/slices/customDataSlice";
import { setDataChanged } from "../../../redux/slices/leadDashSlice";
import { hasNonEmptyValue } from "../../../utils/helper";
import { getGlobalTypes } from "~/zustand/global/types/slice";
import details from "~/components/page/bills/details/details";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);
const LeadSalesCard = ({ isReadOnly = false }: { isReadOnly: boolean }) => {
  const gConfig: GConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const filter =
    (getGModuleFilters() as Partial<LeadsFilter> | undefined) || {};
  const { _t } = useTranslation();
  const params: RouteParams = useParams();

  const dispatch = useAppDispatch();
  const { salesDetail, activity }: IDirSalesInitialState = useAppSelector(
    (state) => state.dirSales
  );
  const { customDataList }: ICustomDataInitialState = useAppSelector(
    (state) => state.customData
  );

  const typesData = getGlobalTypes();
  const loadingStatusRef = useRef(fieldStatus);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  const [inputValues, setInputValues] =
    useState<IDirSalesDetails>(dirSalesDetailsField);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);

  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [assignedTo, setAssignedTo] = useState<Partial<IDirectoryData>>({});
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);

  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);

  ///fixed with https://app.clickup.com/t/86cyq048w

  useEffect(() => {
    const today = dayjs().startOf("day");

    const processedActivities = activity
      .map((item) => {
        const combinedDateTimeStr = `${item?.start_date_only} ${item?.start_time_only}`;
        const parsedDate = dayjs(
          combinedDateTimeStr,
          `${gSettings?.date_format} hh:mm A`,
          true
        );

        if (!parsedDate.isValid()) return null;

        const adjustedDate = parsedDate.isAfter(today)
          ? today.toDate()
          : parsedDate.toDate();

        return {
          ...item,
          date_for_compare: adjustedDate,
        };
      })
      .filter(
        (item) =>
          item &&
          (item.status_keyname === "lead_activity_closed" ||
            item.status_keyname === "lead_activity_completed")
      );

    // Find the one with the max final_date
    const latestActivity = processedActivities.reduce((latest, current) => {
      if (!latest) return current;
      return current?.date_for_compare &&
        current?.date_for_compare > latest.date_for_compare
        ? current
        : latest;
    }, null);

    dispatch(
      updateDirSalesDetail({
        last_contacted: latestActivity?.start_date_only || "",
      })
    );
  }, [activity, gSettings?.date_format]);

  useEffect(() => {
    setInputValues(salesDetail);
    if (salesDetail) {
      setAssignedTo({
        user_id: salesDetail.assigned_to,
        display_name: salesDetail.assigned_to_name,
        image:
          !salesDetail?.assigned_to_contact_id &&
          salesDetail.assigned_to_contact_id == "0"
            ? salesDetail?.assigned_to_image
            : "",
        type_key: salesDetail?.assigned_to_type_key
          ? salesDetail.assigned_to_type_key == "user"
            ? "employee"
            : salesDetail?.assigned_to_type_key
          : undefined,
        type_name: salesDetail?.assigned_to_type_name
          ? salesDetail.assigned_to_type_key == "user"
            ? "Employee"
            : salesDetail?.assigned_to_type_name
          : undefined,
        contact_id: !!salesDetail?.assigned_to_contact_id
          ? salesDetail.assigned_to_contact_id != "0"
            ? salesDetail.assigned_to_contact_id
            : undefined
          : undefined,
      });
    } else {
      setAssignedTo({
        user_id: 0,
        display_name: "",
      });
    }
  }, [salesDetail.type]);

  const categCusDataItems: ICategCusDataItems = useMemo(() => {
    // Extract referral sources from customDataList
    const refeSourceList = customDataList
      .filter(
        (item) =>
          item.item_type == customDataTypesByKey.leadReferralSourceID.toString()
      )
      .map((item) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.item_id.toString(),
      }));

    // Extract predefined referral source data
    const referralSourceData =
      typesData
        ?.filter((item) => item.type === "lead_referral_source")
        ?.map((item) => ({
          label: HTMLEntities.decode(sanitizeString(item.name)),
          value: item.key,
        })) || [];

    // Check if the salesDetail.referral_source exists in refeSourceList
    const isExistingReferral = [...refeSourceList, ...referralSourceData].some(
      (item) => item.value == salesDetail.referral_source
    );

    // If referral_source is missing, add it as archived
    const archivedReferral =
      salesDetail?.referral_source && !isExistingReferral
        ? [
            {
              label: `${HTMLEntities.decode(
                salesDetail?.referral_source_name || ""
              )} (Archived)`,
              value: salesDetail.referral_source,
            },
          ]
        : [];

    // Combine predefined, custom, and archived referral sources
    return {
      refeSourceList: [
        ...referralSourceData,
        ...refeSourceList,
        ...archivedReferral,
      ],
    };
  }, [salesDetail?.referral_source, customDataList, typesData]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (
    data: IDirDetailFields,
    assignedData: Partial<IDirectoryData> = {}
  ) => {
    const field = Object.keys(data)[0] as keyof IDirSalesDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newData = data;
    if (field === "assigned_to") {
      newData = {
        ...data,
        assigned_to_contact_id: assignedData?.contact_id || "",
      };
    }
    const updateRes = (await updateDirDetailApi({
      directory_id: params?.id || "",
      type: "",
      module_id: gConfig?.module_id,
      ...newData,
    })) as IDirSalesDetailsUpdateApiRes;
    if (hasNonEmptyValue(filter)) {
      dispatch(setDataChanged());
    }
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      let newData = data;

      if (field === "last_contacted") {
        newData = {
          last_contacted: data[field]
            ? dayjs(data[field], "YYYY-MM-DD").format(gSettings?.date_format)
            : "",
        };
      }

      dispatch(updateDirSalesDetail(newData));
      if (field === "assigned_to") {
        dispatch(
          updateDirSalesDetail({
            assigned_to_name: assignedData?.display_name,
            assigned_to_contact_id: assignedData?.contact_id || "",
            assigned_to_type_name: assignedData?.type_name,
            assigned_to_type: assignedData?.type_key,
            assigned_to_image: assignedData?.image,
          })
        );
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: salesDetail[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChangeDate = (dateString: string, type: string) => {
    if (dateString === "" && type === "last_contacted") {
      setInputValues({
        ...inputValues,
        last_contacted: undefined,
      });
      handleUpdateField({
        last_contacted: "",
      });
      return false;
    }
    const newValue = backendDateFormat(
      dateString.toString(),
      gSettings?.date_format
    );
    if (type === "last_contacted") {
      setInputValues({
        ...inputValues,
        last_contacted: dateString.toString(),
      });
      handleUpdateField({
        last_contacted: newValue,
      });
    }
  };

  const handleChange = ({ value, name }: ISingleSelectOption) => {
    const newValue = typeof value === "object" ? value[0] : value;
    setInputValues({
      ...inputValues,
      [name]: newValue,
    });
    handleUpdateField({
      [name]: newValue,
    });
  };

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: string
  ) => {
    if (
      event.key === "Enter" &&
      itemType == customDataTypesByKey.leadReferralSourceID.toString()
    ) {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        categCusDataItems?.refeSourceList || []
      );
      if (newType) {
        setCustomDataAdd({
          itemType,
          name: escapeHtmlEntities(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addCustomDataAct(cDataRes?.data));

        let newData = {};
        if (
          customDataAdd?.itemType ===
          customDataTypesByKey.leadReferralSourceID.toString()
        ) {
          newData = {
            referral_source: cDataRes?.data?.item_id.toString(),
            referral_source_name: customDataAdd?.name,
          };
        }

        setInputValues({
          ...inputValues,
          ...newData,
        });
        handleUpdateField(newData);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({
          description: cDataRes?.message,
        });
      }
      setIsAddingCustomData(false);
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
    setInputValues(salesDetail);
  };

  const handleAssignedTo = (data: Partial<IDirectoryData>) => {
    const assignData = !isEmpty(data) ? data : {};
    setAssignedTo(assignData);
    handleUpdateField(
      {
        assigned_to: data.user_id?.toString() || "",
        assigned_to_contact_id: data?.contact_id || "",
      },
      assignData
    );
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Sales")}
        iconProps={{
          icon: "fa-solid fa-rectangle-list",
          containerClassName:
            "bg-[linear-gradient(180deg,#7FA3FF1a_0%,#3387FD1a_100%)]",
          id: "additional_details_icon",
          colors: ["#7FA3FF", "#3387FD"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li>
                <ButtonField
                  label={_t("Sales Rep.")}
                  labelPlacement="left"
                  placeholder={_t("Select Sales Rep.")}
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  onClick={() => {
                    setIsOpenSelectCustomer(true);
                  }}
                  avatarProps={{
                    user: {
                      name: HTMLEntities.decode(
                        sanitizeString(assignedTo?.display_name)
                      ),
                      image: assignedTo?.image,
                    },
                  }}
                  value={assignedTo?.display_name}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "assigned_to"),
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "assigned_to") ===
                    "loading"
                  }
                  rightIcon={
                    assignedTo?.user_id && assignedTo?.display_name ? (
                      <>
                        <ContactDetailsButton
                          onClick={() => {
                            setIsOpenContactDetails(true);
                          }}
                        />
                        <DirectoryFieldRedirectionIcon
                          className="!w-5 !h-5"
                          directoryId={assignedTo?.user_id?.toString()}
                          directoryTypeKey={
                            assignedTo?.type_key?.toString() ??
                            assignedTo?.type_name?.toString()?.toLowerCase() ??
                            ""
                          }
                        />
                      </>
                    ) : (
                      <></>
                    )
                  }
                />
              </li>
              <li className="overflow-hidden">
                <DatePickerField
                  label={_t("Last Contacted ")}
                  name="last_contacted"
                  labelPlacement="left"
                  placeholder={_t("Last Contacted")}
                  editInline={true}
                  disabledDate={(current) =>
                    current && current > dayjs().endOf("day")
                  }
                  inputReadOnly={true}
                  iconView={true}
                  readOnly={true}
                  format={gSettings?.date_format}
                  value={
                    salesDetail?.last_contacted
                      ? dayjs(
                          salesDetail.last_contacted,
                          gSettings?.date_format
                        )
                      : undefined
                  }
                  // value={
                  //   inputValues.last_contacted
                  //     ? dayjs(
                  //         inputValues?.last_contacted,
                  //         gSettings?.date_format
                  //       )
                  //     : undefined
                  // }
                  fixStatus={getStatusForField(loadingStatus, "last_contacted")}
                  disabled={
                    getStatusForField(loadingStatus, "last_contacted") ===
                    "loading"
                  }
                  onChange={(_, dateString) =>
                    handleChangeDate(dateString as string, "last_contacted")
                  }
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("Referred By")}
                  value={inputValues?.refered_by}
                  name="refered_by"
                  placeholder={_t("Referred By")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "refered_by")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "refered_by",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "refered_by",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "refered_by",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== salesDetail?.refered_by) {
                      handleUpdateField({ refered_by: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "refered_by",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        refered_by: salesDetail.refered_by,
                      });
                    }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <SelectField
                  label={_t("Referral Source")}
                  placeholder={_t("Select Referral Source")}
                  value={
                    inputValues?.referral_source &&
                    inputValues.referral_source != "0"
                      ? inputValues.referral_source
                      : undefined
                  }
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  showSearch
                  options={categCusDataItems?.refeSourceList}
                  allowClear
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "referral_source"
                  )}
                  disabled={
                    getStatusForField(loadingStatus, "referral_source") ===
                    "loading"
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    handleChange({ value, name: "referral_source" });
                  }}
                  addItem={addItemObject}
                  onInputKeyDown={(e) =>
                    handlekeyDown(
                      e,
                      customDataTypesByKey.leadReferralSourceID.toString()
                    )
                  }
                  onClear={() => {
                    handleChange({ value: "", name: "referral_source" });
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}

      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            dispatch(setActiveField(CFConfig.employee_key));
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={true}
          options={[
            CFConfig.employee_key,
            CFConfig.contractor_key,
            CFConfig.misc_contact_key,
            "by_service",
            "my_crew",
          ]}
          setCustomer={(data) => {
            handleAssignedTo(
              data.length ? (data[0] as Partial<IDirectoryData>) : {}
            );
            dispatch(setDataChanged());
          }}
          selectedCustomer={
            assignedTo?.user_id
              ? ([assignedTo] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
        />
      )}

      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={assignedTo?.user_id || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isReadOnly}
          additional_contact_id={
            salesDetail?.assigned_to_contact_id &&
            salesDetail.assigned_to_contact_id != "0"
              ? Number(salesDetail.assigned_to_contact_id)
              : 0
          }
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          CFConfig.employee_key,
          "my_crew",
          CFConfig.customer_key,
          CFConfig.lead_key,
          CFConfig.contractor_key,
          CFConfig.vendor_key,
          CFConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default LeadSalesCard;
