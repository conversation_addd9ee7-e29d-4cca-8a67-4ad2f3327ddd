import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { CustomFieldForm } from "~/shared/components/organisms/customField";
import { Int } from "~/helpers/helper";
import { useParams } from "@remix-run/react";
import DetailsCard from "./details/DetailsCard";
import BillsItemCostsCard from "./details/BillsItemCostsCard";
import { useAppBillSelector } from "../../redux/store";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import PaymentHistoryCard from "~/modules/financials/pages/bills/components/tab/PaymentHistory";
import TotalTextAmountCard from "~/modules/financials/pages/bills/components/tab/TotalTextAmount";
import { useState } from "react";

const BillDetailsTab = ({
  handleUpdateField,
  inputValues,
  setInputValues,
  handleChangeFieldStatus,
  loadingStatus,
  isReadOnly,
}: IBillDetailsTabProps) => {
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { billDetail }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const { formatter } = useCurrencyFormatter();
  const params = useParams();
  const { module_id = 0, module_access = "no_access" } = currentModule || {};
  const { isReadOnlyCustomField, isNoAccessCustomField }: ICustomFieldAccess =
    getCustomFieldAccess();
  const [totalAmountForHeader, setTotalAmountForHeader] = useState(0);

  const columnDefs = [
    {
      headerName: "",
      minWidth: 775,
      flex: 2,
      field: "text",
    },
    {
      headerName: "",
      maxWidth: 200,
      minWidth: 200,
      field: "price",
      headerClass: "ag-header-right",
      cellClass: "ag-right-aligned-cell",
    },
    {
      headerName: "",
      field: "space",
      maxWidth: 95,
      minWidth: 95,
    },
  ];

  return (
    <>
      <div className="grid lg:grid-cols-2 gap-2.5">
        <div className="py-3 lg:col-span-1 md:col-span-2 px-[15px] common-card h-fit">
          <DetailsCard
            totalAmountForHeader={totalAmountForHeader}
            handleUpdateField={handleUpdateField}
            inputValues={inputValues}
            setInputValues={setInputValues}
            handleChangeFieldStatus={handleChangeFieldStatus}
            loadingStatus={loadingStatus}
            isReadOnly={isReadOnly}
          />
        </div>
        {!isNoAccessCustomField && (
          <div className="py-3 lg:col-span-1 md:col-span-2 px-[15px] h-fit common-card">
            <CustomFieldForm
              isReadOnly={isReadOnly || isReadOnlyCustomField}
              requestBody={{
                moduleId: module_id,
                recordId: params?.id,
              }}
            />
          </div>
        )}
        <div className="grid md:col-span-2 gap-2">
          <BillsItemCostsCard
            setTotalAmountForHeader={setTotalAmountForHeader}
            handleUpdateField={handleUpdateField}
          />

          {billDetail?.data?.items &&
            billDetail?.data.items
              ?.filter(
                (data: { is_retainage_item: string }) =>
                  data.is_retainage_item?.toString() === "1"
              )
              ?.reduce((acc: number, item: { total: string }) => {
                return acc + Int(item?.total);
              }, 0) > 0 && (
              <div className="rounded retainage-table -mb-2">
                <div className="ag-theme-alpine">
                  <StaticTable
                    className="static-table"
                    columnDefs={columnDefs}
                    rowData={[
                      {
                        text: "Retainage (This Bill): Calculated Based on SC Retainage %",
                        price: formatter(
                          Number(
                            -(
                              billDetail?.data?.items
                                ?.filter(
                                  (data: { is_retainage_item: string }) =>
                                    data.is_retainage_item?.toString() === "1"
                                )
                                ?.reduce(
                                  (acc: number, item: { total: string }) => {
                                    return acc + Number(item?.total); // Convert total to a number
                                  },
                                  0
                                ) / 100
                            ).toFixed(2) // Convert to a string with two decimal places
                          ).toFixed(2) // Ensure the result is a valid string for `formatter`
                        ).value_with_symbol,
                        space: "",
                      },
                    ]}
                  />
                </div>
              </div>
            )}
        </div>
        {billDetail?.data?.items && billDetail?.data.items.length > 0 ? (
          <TotalTextAmountCard
            totalAmountForHeader={totalAmountForHeader}
            isReadOnly={module_access === "read_only"}
          />
        ) : (
          ""
        )}

        <div className="py-3 md:col-span-2 px-[15px] common-card min-h-[140px]">
          <PaymentHistoryCard totalAmountForHeader={totalAmountForHeader} />
        </div>
      </div>
    </>
  );
};

export default BillDetailsTab;
