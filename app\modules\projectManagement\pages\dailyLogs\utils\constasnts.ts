export const REPORTS_FILTER = [
  {
    label: "Last Month / This Month",
    value: "last_this_month",
  },
  {
    label: "Last Year / This Year",
    value: "last_this_year",
  },
];

// Equipment deleivery static color
export const equiStaticColor = {
  inUse: {
    bg: "#F156421d",
    color: "#F15642",
  },
  returned: {
    bg: "#4FB91D1d",
    color: "#4FB91D",
  },
};

export const DAILYLOG_SIDEMENU: IDLSideMenuItem[] = [
  {
    name: "Details",
    value: "details",
    icon: "fa-regular fa-rectangle-list",
  },
  {
    name: "People",
    value: "people",
    icon: "fa-regular fa-user-group",
  },
  {
    name: "Material",
    value: "material",
    icon: "fa-regular fa-block-brick",
  },
  {
    name: "Equipment",
    value: "equipment",
    icon: "fa-regular fa-screwdriver-wrench",
  },
  {
    name: "Files",
    value: "files",
    icon: "fa-regular fa-file-image",
  },
  {
    name: "Notes",
    value: "notes",
    icon: "fa-regular fa-memo",
  },
];

// Daily log detail page
export const DLDetailsField: IDLUpDetails = {
  logId: 0,
  arrivalDate: "",
  arrivalTime: "",
  taskPerformed: "",
  departureTime: "",
};

// Daily log people page
export const DLPeopleDetailsField: IDLUpPeopleOnSite = {
  logId: 0,
  anyVisitorOnsite: 0,
  visitorsNotes: "",
  empWorkNotes: "",
  empOnSite: "",
  employeesOnSite: "",
};

export const DLWeatherDetailsField: IDLWeatherUpDetails = {
  logId: 0,
  weatherTemp: "",
  weatherConditioName: "string",
  weatherCondition: "",
  weatherNotes: "",
  weatherDelayNotes: "",
  scheduleDelayNotes: "",
  anyWeatherDelay: 0,
  anyScheduleDelay: 0,
  jobsiteCondition: "",
  jobsiteNotes: "",
  weatherJson: [],
};

export const IDLMaterialsField: IMaterialsData = {
  item_id: "",
  item_type: "",
  item_primary_id: "",
  daily_log_id: "",
  company_id: "",
  quantity: "",
  hours: "",
  date_added: "",
  date_modified: "",
  log_item_no: 0,
  unit: "",
  notes: "",
  cost_code_id: "",
  operator_id: "",
  item_mleso_reference_id: "",
  item_reference_module_id: "",
  unit_cost: "",
  markup: "",
  hidden_markup: "",
  is_markup_percentage: "",
  equipment_id: "",
  material_id: "",
  name: "",
  cost_code_name: "",
  cost_code_csi_code: "",
  cost_code: "",
  operator_profile_image: "",
  operator_name: "",
};

export const IDLMaterialsDeliveryField: IDeliveredMaterial = {
  item_id: "",
  daily_log_id: "",
  item_type: "",
  item_name: "",
  delivery_date: "",
  delivery_time: "",
  delivered_by: "",
  is_returned: "",
  quantity: "",
  quantity_backorderd: "",
  qty_used: "",
  notes: "",
  directory_id: "",
  emp_on_site: "",
  time_on_site: "",
  total_hours_on_site: "",
  user_id: "",
  company_id: "",
  date_added: "",
  date_modified: "",
  reference_item_id: 0,
  parent_item_id: "",
  demo_data: "",
  show_until_returned: "",
  reference_po_item_id: 0,
  type_key: "",
  directory_name: "",
  directory_company_name: "",
  dir_type: "",
  unit_cost: "",
  unit: "",
};

export const IDLMaterialItemField: IDeliveredMaterial = {
  item_id: "",
  daily_log_id: "",
  item_type: "",
  item_name: "",
  delivery_date: "",
  delivery_time: "",
  delivered_by: "",
  is_returned: "",
  quantity: "",
  quantity_backorderd: "",
  qty_used: "",
  notes: "",
  directory_id: "",
  emp_on_site: "",
  time_on_site: "",
  total_hours_on_site: "",
  user_id: "",
  company_id: "",
  date_added: "",
  date_modified: "",
  reference_item_id: 0,
  parent_item_id: "",
  demo_data: "",
  show_until_returned: "",
  reference_po_item_id: 0,
  type_key: "",
  directory_name: "",
  directory_company_name: "",
  dir_type: "",
  unit_cost: "",
  unit: "",
};

export const IDLMaterialUsedField: IMaterialsData = {
  item_id: "",
  item_type: "",
  item_primary_id: "",
  daily_log_id: "",
  company_id: "",
  quantity: "",
  hours: "",
  date_added: "",
  date_modified: "",
  log_item_no: 0,
  unit: "",
  notes: "",
  cost_code_id: "",
  operator_id: "",
  item_mleso_reference_id: "",
  item_reference_module_id: "",
  unit_cost: "",
  markup: "",
  hidden_markup: "",
  is_markup_percentage: "",
  equipment_id: "",
  material_id: "",
  name: "",
  cost_code_name: "",
  cost_code_csi_code: "",
  cost_code: "",
  operator_profile_image: "",
  operator_name: "",
};

export const DLSubsOnJobsiteField: IDLSubsonJobsite = {
  item_id: 0,
  daily_log_id: 0,
  item_type: 0,
  item_name: "",
  delivery_date: "",
  delivery_time: "",
  delivered_by: "",
  is_returned: 0,
  quantity: "",
  quantity_backorderd: "",
  qty_used: "",
  notes: "",
  directory_id: 0,
  emp_on_site: 0,
  time_on_site: "",
  total_hours_on_site: "",
  user_id: 0,
  company_id: 0,
  date_added: "",
  date_modified: "",
  reference_item_id: 0,
  parent_item_id: 0,
  demo_data: 0,
  show_until_returned: 0,
  reference_po_item_id: 0,
  type_key: "",
  directory_name: "",
  directory_company_name: "",
  dir_type: 0,
};

// Daily log detail status fields
export const fieldStatus: IFieldStatus[] = [
  {
    field: "arrivalTime",
    status: "button",
  },
  {
    field: "departureTime",
    status: "button",
  },
  {
    field: "taskPerformed",
    status: "button",
  },
  {
    field: "weatherTemp",
    status: "button",
  },
  {
    field: "weatherCondition",
    status: "button",
  },
  {
    field: "weatherNotes",
    status: "button",
  },
  {
    field: "weatherDelayNotes",
    status: "button",
  },
  {
    field: "scheduleDelayNotes",
    status: "button",
  },
  {
    field: "jobsiteNotes",
    status: "button",
  },
  {
    field: "jobsiteCondition",
    status: "button",
  },
  {
    field: "materialNotes",
    status: "button",
  },
  {
    field: "visitorsNotes",
    status: "button",
  },
  {
    field: "empWorkNotes",
    status: "button",
  },
  {
    field: "empOnSite",
    status: "button",
  },
  {
    field: "notes",
    status: "button",
  },
  {
    field: "timeOnSite",
    status: "button",
  },
  {
    field: "itemName",
    status: "button",
  },
  {
    field: "deliveredBy",
    status: "button",
  },
  {
    field: "quantityBackorderd",
    status: "button",
  },
  {
    field: "quantity",
    status: "button",
  },
  {
    field: "qtyUsed",
    status: "button",
  },
  {
    field: "unitCost",
    status: "button",
  },
  {
    field: "unit",
    status: "button",
  },
  {
    field: "deliveryTime",
    status: "button",
  },
  {
    field: "itemName",
    status: "button",
  },
  {
    field: "deliveredBy",
    status: "button",
  },
  {
    field: "unitCost",
    status: "button",
  },
  {
    field: "unit",
    status: "button",
  },
  {
    field: "notes",
    status: "button",
  },
  {
    field: "employeeEquipmentNotes",
    status: "button",
  },
  {
    field: "cost_code_id",
    status: "button",
  },
  {
    field: "isReturned",
    status: "button",
  },
  {
    field: "showUntilReturned",
    status: "button",
  },
  {
    field: "deliveryTime",
    status: "button",
  },
  {
    field: "newItem",
    status: "button",
  },
  {
    field: "jobStatus",
    status: "button",
  },
  {
    field: "projectId",
    status: "button",
  },
  {
    field: "arrivalDate",
    status: "button",
  },
];

// ? Notes >> Project Notes
export const NOTE_PROJECT_STATUS = [
  { value: "1", label: "Open" },
  { value: "0", label: "Closed" },
];
