import { useMemo } from "react";
import { useParams } from "@remix-run/react";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
// Redux
import {
  addUpNotesDataAct,
  addUpNoteFileAct,
  deleteNotesDataAct,
} from "~/redux/slices/commonNoteSlice";
// Organisms
import { NoteList } from "~/shared/components/organisms/notes/noteList";
// Other
import { useWoAppDispatch, useWoAppSelector } from "../../redux/store";
import { defaultConfig } from "~/data";

const WorkOrderNotesTab = ({ projectid }: IWorkOrderNotesTabProps) => {
  const params: RouteParams = useParams();
  const gConfig: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useWoAppDispatch();
  const { isNotesLoading, notes }: ICommonNoteInitialState = useWoAppSelector(
    (state) => state.commonNoteData
  );
  const { date_format, image_resolution }: GSettings = getGSettings();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(gConfig.module_key) === "read_only",
    [gConfig.module_key]
  );

  const handleDeleteNote = (id: number) => {
    dispatch(deleteNotesDataAct({ note_id: id }));
  };

  const handleAddUpNote = (data: ICommonNoteAddUpRes) => {
    dispatch(addUpNotesDataAct(data));
  };

  return (
    <>
      <div className="grid gap-2.5">
        <div className="common-card py-3 px-[15px]">
          <NoteList
            projectid={projectid}
            isAddAttachAllow={!isReadOnly}
            isAttachReadOnly={isReadOnly}
            notesData={notes}
            moduleData={{
              moduleKey: gConfig.module_key || "",
              recordId: params?.id || "",
            }}
            onAddUpNote={handleAddUpNote}
            onUpNoteFile={(data) => {
              dispatch(addUpNoteFileAct(data));
            }}
            isNotesLoading={isNotesLoading && notes?.length < 1}
            onNoteDeleted={handleDeleteNote}
            validationParams={{
              date_format: date_format,
              file_support_module_access: checkModuleAccessByKey(
                defaultConfig.file_support_key
              ),
              image_resolution: image_resolution,
              module_access: gConfig.module_access,
              module_id: gConfig.module_id,
              module_key: gConfig.module_key,
            }}
          />
        </div>
      </div>
    </>
  );
};

export default WorkOrderNotesTab;
