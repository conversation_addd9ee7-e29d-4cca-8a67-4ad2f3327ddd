// Molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
// Hook
import { useTranslation } from "~/hook";
// Other
import ContactsTopCard from "./ContactsTopCard";
import { useCallback, useMemo, useState } from "react";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { Number, sanitizeString } from "~/helpers/helper";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { getStatusForField } from "~/shared/utils/helper/common";
import { fetchProjectContactsApi } from "../../../redux/action/projectDetailsAction";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const ContactsTopCardList = () => {
  const { _t } = useTranslation();
  const dispatch = useAppProDispatch();
  const { details }: IProjectDetailsInitialState = useAppProSelector(
    (state) => state.proDetails
  );

  const [customerSelectDrawerkey, setCustomerSelectDrawerkey] =
    useState<TContactListKey>("");

  const { handleUpdateField, loadingStatus } = useProjectDetail();
  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_access } = currentMenuModule || {};

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const hasAdditionalContact = useMemo(() => {
    if (
      customerSelectDrawerkey === "site_manager" ||
      customerSelectDrawerkey === "safety_contact" ||
      customerSelectDrawerkey === "project_manager"
    ) {
      return true;
    } else {
      return false;
    }
  }, [customerSelectDrawerkey]);

  const drawerOptions: CustomerEmailTab[] | undefined = useMemo(() => {
    if (
      customerSelectDrawerkey === "site_manager" ||
      customerSelectDrawerkey === "safety_contact"
    ) {
      return [
        CFConfig.employee_key,
        "my_crew",
        CFConfig.customer_key,
        CFConfig.contractor_key,
        CFConfig.vendor_key,
        CFConfig.misc_contact_key,
        "by_service",
      ];
    } else if (customerSelectDrawerkey === "project_manager") {
      return [CFConfig.employee_key, CFConfig.contractor_key];
    } else if (customerSelectDrawerkey === "sales_rep") {
      return [CFConfig.employee_key, "my_crew"];
    } else if (customerSelectDrawerkey === "estimator") {
      return [CFConfig.employee_key, CFConfig.contractor_key];
    } else {
      [CFConfig.employee_key];
    }
  }, [customerSelectDrawerkey]);

  const selectedCustomerData = useMemo(() => {
    if (customerSelectDrawerkey === "site_manager") {
      return Number(details?.site_manager_id ?? 0)
        ? ([
            {
              display_name: _t(
                HTMLEntities.decode(sanitizeString(details?.site_manager_name))
              ),
              contact_id: details?.site_manager_contact_id,
              user_id: details?.site_manager_id,
              type: details.site_manager_type,
              type_key: getDirectaryKeyById(
                Number(details.site_manager_type) === 1
                  ? 2
                  : Number(details.site_manager_type),
                undefined
              ),
              image: details?.site_manager_image ?? "",
            },
          ] as TselectedContactSendMail[])
        : [];
    } else if (customerSelectDrawerkey === "project_manager") {
      return Number(details?.project_manager_id ?? 0)
        ? ([
            {
              display_name: _t(
                HTMLEntities.decode(
                  sanitizeString(details?.project_manager_name)
                )
              ),
              contact_id: 0,
              user_id: details?.project_manager_id,
              type: details.project_manager_type,
              type_key: getDirectaryKeyById(
                Number(details.project_manager_type) === 1
                  ? 2
                  : Number(details.project_manager_type),
                undefined
              ),
              image: details?.project_manager_image ?? "",
            },
          ] as TselectedContactSendMail[])
        : [];
    } else if (customerSelectDrawerkey === "safety_contact") {
      return Number(details?.safety_contact_id ?? 0)
        ? ([
            {
              display_name: _t(
                HTMLEntities.decode(
                  sanitizeString(details?.safety_contact_name)
                )
              ),
              contact_id: details?.safety_additional_contact_id,
              user_id: details?.safety_contact_id,
              type: details.safety_contact_type ?? 0,
              type_key: getDirectaryKeyById(
                Number(details.safety_contact_type ?? 0) === 1
                  ? 2
                  : Number(details.safety_contact_type ?? 0),
                undefined
              ),
              image: details?.safety_contact_image ?? "",
            },
          ] as TselectedContactSendMail[])
        : [];
    } else if (customerSelectDrawerkey === "sales_rep") {
      return Number(details?.sales_rep ?? 0)
        ? ([
            {
              display_name: _t(
                HTMLEntities.decode(sanitizeString(details?.sales_rep_name))
              ),
              // contact_id: 0,
              user_id: details?.sales_rep,
              type: details.sales_rep_type,
              type_key: getDirectaryKeyById(
                Number(details.sales_rep_type) === 1
                  ? 2
                  : Number(details.sales_rep_type),
                undefined
              ),
              image: details?.sales_rep_image ?? "",
            },
          ] as TselectedContactSendMail[])
        : [];
    } else if (customerSelectDrawerkey === "estimator") {
      return Number(details?.estimator ?? 0)
        ? ([
            {
              display_name: _t(
                HTMLEntities.decode(sanitizeString(details?.estimator_name))
              ),
              // contact_id: 0,
              user_id: details?.estimator,
              type: details.estimator_type,
              type_key: getDirectaryKeyById(
                Number(details.estimator_type) === 1
                  ? 2
                  : Number(details.estimator_type),
                undefined
              ),
              image: details?.estimator_image ?? "",
            },
          ] as TselectedContactSendMail[])
        : [];
    }
  }, [customerSelectDrawerkey, details]);

  const openDrawer = useCallback(
    (key: TContactListKey) => setCustomerSelectDrawerkey(key),
    []
  );

  const closeDrawer = useCallback(() => setCustomerSelectDrawerkey(""), []);

  const loadingState = useMemo(() => {
    return {
      isSiteManagerLoading:
        getStatusForField(loadingStatus, "site_manager_id") === "loading",
      isProjectManagetLoading:
        getStatusForField(loadingStatus, "project_manager_id") === "loading",
      isSafetyContactLoading:
        getStatusForField(loadingStatus, "safety_contact_id") === "loading",
      isSalesRepLoading:
        getStatusForField(loadingStatus, "sales_rep") === "loading",
      isEstimatorLoading:
        getStatusForField(loadingStatus, "estimator") === "loading",
    };
  }, [loadingStatus]);

  const getDirId = (type_key: string) => {
    return getDirectaryIdByKey(type_key as CustomerTabs, undefined);
  };

  const getUpdatedContact = useCallback(
    (
      currentActive: TContactListKey,
      contact: Array<Partial<TselectedContactSendMail>>
    ) => {
      let data = contact?.[0] as CustomerEmail & {
        contact_id?: string;
      };

      const type_key =
        data?.type_key !== "contact"
          ? data?.type_key || ""
          : data?.parent_type_key || "";

      if (currentActive === "site_manager") {
        if (data?.user_id !== undefined) {
          return {
            data_to_update_in_store: {
              site_manager_company_name: data?.company_name || "",
              site_manager_name: `${
                data.first_name ? data.first_name + " " : ""
              }${data.last_name ? data.last_name : ""}`,
              site_manager_type: getDirId(type_key as string),
              site_manager_image: data?.image || "",
            },
            data_to_send_in_api: {
              site_manager_id: data?.user_id,
              site_manager_contact_id:
                data?.contact_id !== "" ? Number(data.contact_id ?? "") : "",
            },
          };
        } else {
          return {
            data_to_update_in_store: {
              site_manager_company_name: "",
              site_manager_name: "",
              site_manager_type: "",
              site_manager_image: "",
            },
            data_to_send_in_api: {
              site_manager_id: "",
              site_manager_contact_id: "",
            },
          };
        }
      } else if (currentActive === "project_manager") {
        if (data?.user_id !== undefined) {
          return {
            data_to_update_in_store: {
              project_manager_name: data?.display_name,
              project_manager_type: getDirId(type_key as string),
              project_manager_image: data?.image || "",
            },
            data_to_send_in_api: {
              project_manager_id: data?.user_id,
            },
          };
        } else {
          return {
            data_to_update_in_store: {
              project_manager_name: "",
              project_manager_type: "",
              project_manager_image: "",
            },
            data_to_send_in_api: {
              project_manager_id: "",
            },
          };
        }
      } else if (currentActive === "safety_contact") {
        if (data?.user_id !== undefined) {
          return {
            data_to_update_in_store: {
              safety_contact_company_name: data?.company_name || "",
              safety_contact_name: data?.display_name,
              safety_contact_type: getDirId(type_key as string),
              safety_contact_image: data?.image || "",
            },
            data_to_send_in_api: {
              safety_contact_id: data?.user_id,
              safety_additional_contact_id:
                data?.contact_id !== "" ? Number(data.contact_id ?? "") : "",
            },
          };
        } else {
          return {
            data_to_update_in_store: {
              safety_contact_company_name: "",
              safety_contact_name: "",
              safety_contact_type: "",
              safety_contact_image: "",
            },
            data_to_send_in_api: {
              safety_contact_id: "",
              safety_additional_contact_id: "",
            },
          };
        }
      } else if (currentActive === "sales_rep") {
        if (data?.user_id !== undefined) {
          return {
            data_to_update_in_store: {
              sales_rep_name: data?.display_name,
              sales_rep_type: getDirId(type_key as string),
              sales_rep_image: data?.image || "",
            },
            data_to_send_in_api: {
              sales_rep: data?.user_id,
            },
          };
        } else {
          return {
            data_to_update_in_store: {
              sales_rep_name: "",
              sales_rep_type: "",
              sales_rep_image: "",
            },
            data_to_send_in_api: {
              sales_rep: "",
            },
          };
        }
      } else if (currentActive === "estimator") {
        if (data?.user_id !== undefined) {
          return {
            data_to_update_in_store: {
              estimator_name: data?.display_name,
              estimator_type: getDirId(type_key as string),
              estimator_image: data?.image || "",
            },
            data_to_send_in_api: {
              estimator: data?.user_id,
            },
          };
        } else {
          return {
            data_to_update_in_store: {
              estimator_name: "",
              estimator_type: "",
              estimator_image: "",
            },
            data_to_send_in_api: {
              estimator: "",
            },
          };
        }
      }
    },
    []
  );

  const handleDrawerSelection = useCallback(
    async (data: Partial<TselectedContactSendMail>[]) => {
      const updatedData = getUpdatedContact(customerSelectDrawerkey, data);

      if (updatedData) {
        await handleUpdateField(updatedData);
        dispatch(
          fetchProjectContactsApi({
            project_id: Number(details.id || "0"),
            record_type: "project",
          })
        );
      }
    },
    [customerSelectDrawerkey]
  );

  return (
    <>
      <div className="grid 2xl:flex lg:grid-cols-3 md:grid-cols-2 gap-2.5">
        <ContactsTopCard
          title={_t("Site Manager")}
          iconProps={{
            icon: "fa-solid fa-user",
            containerClassName:
              "bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)]",
            id: "site_manager_contacts_icon",
            colors: ["#ADD100", "#7B920A"],
          }}
          contantWidth="w-[calc(100%-47px)]"
          containerClassName="field-before-none"
          children={
            <ButtonField
              label=""
              placeholder="Select Site Manager"
              value={_t(
                HTMLEntities.decode(sanitizeString(details?.site_manager_name))
              )}
              labelPlacement="left"
              avatarProps={
                details?.site_manager_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(details?.site_manager_name)
                        ),
                        image: details.site_manager_image || "",
                      },
                    }
                  : undefined
              }
              className="p-0 border-0 min-h-6 h-auto"
              placeholderClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              buttonClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              iconView={true}
              onClick={() => openDrawer("site_manager")}
              loading={loadingState.isSiteManagerLoading}
              readOnly={isReadOnly}
            />
          }
        />
        <ContactsTopCard
          title={_t("Project Manager")}
          iconProps={{
            icon: "fa-solid fa-user-tie",
            containerClassName:
              "bg-[linear-gradient(180deg,#9BC3FF1a_0%,#418CFD1a_100%)]",
            id: "project_manager_contacts_icon",
            colors: ["#9BC3FF", "#418CFD"],
          }}
          contantWidth="w-[calc(100%-47px)]"
          containerClassName="field-before-none"
          children={
            <ButtonField
              label=""
              placeholder="Select Project Manager"
              value={_t(
                HTMLEntities.decode(
                  sanitizeString(details?.project_manager_name)
                )
              )}
              labelPlacement="left"
              className="p-0 border-0 min-h-6 h-auto"
              placeholderClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              buttonClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              iconView={true}
              avatarProps={
                details?.project_manager_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(details?.project_manager_name)
                        ),
                        image: details.project_manager_image || "",
                      },
                    }
                  : undefined
              }
              onClick={() => openDrawer("project_manager")}
              loading={loadingState.isProjectManagetLoading}
              readOnly={isReadOnly}
            />
          }
        />
        <ContactsTopCard
          title={_t("Safety Contact")}
          iconProps={{
            icon: "fa-solid fa-phone",
            containerClassName:
              "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
            id: "safety_contact_contacts_icon",
            colors: ["#50EBFD", "#5996E9"],
          }}
          contantWidth="w-[calc(100%-47px)]"
          containerClassName="field-before-none"
          children={
            <ButtonField
              label=""
              placeholder="Select Safety Contact"
              value={_t(
                HTMLEntities.decode(
                  sanitizeString(details?.safety_contact_name)
                )
              )}
              labelPlacement="left"
              avatarProps={
                details?.safety_contact_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(details?.safety_contact_name)
                        ),
                        image: details.safety_contact_image || "",
                      },
                    }
                  : undefined
              }
              className="p-0 border-0 min-h-6 h-auto"
              placeholderClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              buttonClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              iconView={true}
              onClick={() => openDrawer("safety_contact")}
              loading={loadingState.isSafetyContactLoading}
              readOnly={isReadOnly}
            />
          }
        />
        <ContactsTopCard
          title={_t("Sales Rep.")}
          iconProps={{
            icon: "fa-solid fa-user-tie-hair",
            containerClassName:
              "bg-[linear-gradient(180deg,#C285FF1a_0%,#962EFF1a_100%)]",
            id: "sales_rep_contacts_icon",
            colors: ["#C285FF", "#962EFF"],
          }}
          contantWidth="w-[calc(100%-47px)]"
          containerClassName="field-before-none"
          children={
            <ButtonField
              label=""
              placeholder="Select Sales Rep."
              value={_t(
                HTMLEntities.decode(sanitizeString(details?.sales_rep_name))
              )}
              labelPlacement="left"
              avatarProps={
                details?.sales_rep_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(details?.sales_rep_name)
                        ),
                        image: details?.sales_rep_image || "",
                      },
                    }
                  : undefined
              }
              className="p-0 border-0 min-h-6 h-auto"
              placeholderClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              buttonClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              iconView={true}
              onClick={() => openDrawer("sales_rep")}
              loading={loadingState.isSalesRepLoading}
              readOnly={isReadOnly}
            />
          }
        />
        <ContactsTopCard
          title={_t("Estimator")}
          iconProps={{
            icon: "fa-solid fa-user-vneck",
            containerClassName:
              "bg-[linear-gradient(180deg,#FFB2981a_0%,#FA6D3D1a_100%)]",
            id: "estimator_contacts_icon",
            colors: ["#FFB298", "#FA6D3D"],
          }}
          contantWidth="w-[calc(100%-47px)]"
          containerClassName="field-before-none"
          children={
            <ButtonField
              label=""
              placeholder="Select Estimator"
              value={_t(
                HTMLEntities.decode(sanitizeString(details?.estimator_name))
              )}
              labelPlacement="left"
              avatarProps={
                details?.estimator_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(details?.estimator_name)
                        ),
                        image: details.estimator_image,
                      },
                    }
                  : undefined
              }
              className="p-0 border-0 min-h-6 h-auto"
              placeholderClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              buttonClassName="text-primary-900 font-semibold hover:text-deep-orange-500 transition-all"
              iconView={true}
              onClick={() => openDrawer("estimator")}
              loading={loadingState.isEstimatorLoading}
              readOnly={isReadOnly}
            />
          }
        />
      </div>

      {!!customerSelectDrawerkey && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={!!customerSelectDrawerkey}
          closeDrawer={closeDrawer}
          singleSelecte={true}
          setCustomer={(data) => {
            handleDrawerSelection(data);
          }}
          options={drawerOptions}
          selectedCustomer={selectedCustomerData}
          groupCheckBox={true}
          projectId={Number(details?.id)}
          additionalContactDetails={hasAdditionalContact ? 1 : 0}
          canWrite={
            customerSelectDrawerkey === "site_manager" ||
            customerSelectDrawerkey === "safety_contact" ||
            customerSelectDrawerkey === "sales_rep"
          }
          modulesShouldNotHaveAddButton={[]}
          app_access={
            customerSelectDrawerkey === "project_manager" ||
            customerSelectDrawerkey === "estimator"
          }
        />
      )}
    </>
  );
};

export default ContactsTopCardList;
