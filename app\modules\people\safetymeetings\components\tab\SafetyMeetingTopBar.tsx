import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";
import delay from "lodash/delay";
import { routes } from "~/route-services/routes";
// Hooks + redux
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { sendMessageKeys } from "~/components/page/$url/data";
import {
  getGConfig,
  getGModuleByKey,
  setCommonSidebarCollapse,
} from "~/zustand";
import { useIframe, useTranslation } from "~/hook";
import { defaultConfig } from "~/data";
import {
  getStatusColor,
  upadateFieldStatus,
} from "~/modules/people/safetymeetings/utils/constants";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import {
  filterOptionBySubstring,
  getStatusActionForField,
  getStatusForField,
} from "~/shared/utils/helper/common";
import {
  useAppSMDispatch,
  useAppSMSelector,
} from "~/modules/people/safetymeetings/redux/store";
import { updateSMDetailApi } from "~/modules/people/safetymeetings/redux/action/sMDetailsAction";
import { updateSMDetail } from "~/modules/people/safetymeetings/redux/slices/sMDetailsSlice";
import { resetDash } from "~/modules/people/safetymeetings/redux/slices";

// Components
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Tag } from "~/shared/components/atoms/tag";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

import { IconButton } from "~/shared/components/molecules/iconButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { SelectField } from "~/shared/components/molecules/selectField";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import SMListAction from "../common/SMListAction";

const SafetyMeetingTopBar = ({
  sidebarCollapse,
  onReloadDetails,
}: ISafetyMeetingTopBarProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const { module_access, module_key }: GConfig = getGConfig();

  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.safety_meeting_module
  );

  const { parentPostMessage } = useIframe();
  const dispatch = useAppSMDispatch();

  const loadingStatusRef = useRef(upadateFieldStatus);
  const [selectedProject, setSelectedProject] = useState<IProject | null>({
    project_name: "",
    project_id: 0,
    id: 0,
  });
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(upadateFieldStatus);
  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [inputValues, setInputValues] = useState<Partial<ISMDetails>>({});

  const { isDetailLoading, details }: ISMDetailsInitialState = useAppSMSelector(
    (state) => state.safetyMeetingDetails
  );
  const { safetyTopicData }: ISMCommonInitialState = useAppSMSelector(
    (state) => state.sMCommonData
  );

  const isReadOnly = useMemo(
    () => module_access === "read_only" || details?.meeting_status == 2,
    [module_access, details?.meeting_status]
  );

  useEffect(() => {
    setInputValues(details);
  }, [details?.meeting_id]);

  useEffect(() => {
    setSelectedProject({
      project_name: details.project_name || "",
      // project_id: details.project_id,
      id: details?.project_id || 0,
    });
  }, [details.project_id]);

  const safetyTopics = useMemo(() => {
    const seenTopicIds = new Set<string>();
    const topicItems = safetyTopicData?.items;
    if (!topicItems?.length) return { list: [], archive: false };

    let topicList = topicItems.flatMap((item) => {
      const topicId = item?.topic_id?.toString();
      if (!topicId || seenTopicIds.has(topicId)) return [];
      seenTopicIds.add(topicId);
      return [
        {
          label: HTMLEntities.decode(sanitizeString(item?.title)),
          value: topicId,
        },
      ];
    });

    let tooltipText = "";
    if (details?.topic_id) {
      const existsInList = topicList.find(
        (topic) => topic.value === details.topic_id
      );
      tooltipText = existsInList?.label || "";
      if (isEmpty(existsInList)) {
        tooltipText = `${HTMLEntities.decode(
          sanitizeString(details?.title)
        )} ${_t("(Archived)")}`;
        topicList.push({
          label: tooltipText,
          value: details.topic_id,
        });
      }
    }

    return { list: topicList, tooltipText };
  }, [safetyTopicData?.items, details?.meeting_id, details?.topic_id]);

  const meetingStatus = useMemo(() => {
    const status = details?.meeting_status_name;
    const { color, textColor } = getDefaultStatuscolor(
      getStatusColor(status || "")
    );
    return { status, color, textColor };
  }, [details?.meeting_status_name]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleChange = ({ value, name }: ISingleSelectOption) => {
    const newValue = typeof value === "object" ? value[0] : value;
    setInputValues({
      ...inputValues,
      [name]: newValue,
    });
    handleUpdateField({
      [name]: newValue,
    });
  };

  const handleUpdateField = async (
    data: ISMDetailFields,
    otherData?: {
      project_name?: string;
      display_name?: string;
      company_name?: string;
      image?: string;
      type?: string;
      type_key?: string;
      parent_type?: string;
      contact_id?: number | string;
    }
  ) => {
    const field = Object.keys(data)[0] as keyof ISMDetails;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newData = { ...data };

    const updateRes = (await updateSMDetailApi({
      id: details?.meeting_id || "",
      ...newData,
    })) as ISMDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (field === "project_id") {
        dispatch(updateSMDetail({ project_name: otherData?.project_name }));
      } else if (field === "topic_id") {
        const topicObj = safetyTopicData?.items?.find(
          (item) => item?.topic_id == newData.topic_id
        );
        dispatch(updateSMDetail({ topic_description: topicObj?.note }));
      }
      dispatch(updateSMDetail(newData));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
      if (field === "project_id") {
        setSelectedProject({
          project_name: details.project_name || "",
          // project_id: details.project_id,
          id: details?.project_id || 0,
        });
      }
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div
          className={`flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md ${
            isDetailLoading ? "" : "md:h-[83px]"
          }`}
        >
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isDetailLoading ? (
              <TopBarSkeleton num={1} />
            ) : (
              <>
                <div className="flex items-center md:w-[calc(100%-150px)] w-full">
                  <div className="w-11 h-11 flex items-center justify-center bg-[#5C9395] rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white">
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon="fa-light fa-user-helmet-safety"
                    />
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] 2xl:pr-3.5 ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <Tooltip
                      title={
                        safetyTopics?.tooltipText
                          ? `${_t("Topic Name: ")} ${safetyTopics?.tooltipText}`
                          : ""
                      }
                      placement="topLeft"
                    >
                      <div
                        className={`overflow-hidden w-fit md:min-w-[330px] lg:max-w-[760px] max-w-full`}
                      >
                        <SelectField
                          className="!text-base h-6 selectfield-medium select-tooltip-none"
                          placeholder={_t("Select Topic Name")}
                          labelPlacement="left"
                          labelClass="sm:w-[190px] sm:max-w-[190px]"
                          value={
                            inputValues?.topic_id &&
                            inputValues?.topic_id != "0"
                              ? inputValues.topic_id?.toString()
                              : undefined
                          }
                          editInline={true}
                          iconView={true}
                          readOnly={
                            isReadOnly || module?.has_student_permission == "1"
                          }
                          showSearch
                          options={
                            safetyTopics?.list?.length > 0
                              ? safetyTopics?.list
                              : [
                                  {
                                    label:
                                      HTMLEntities.decode(
                                        sanitizeString(details?.title)
                                      ) || "",
                                    value:
                                      inputValues?.topic_id?.toString() || "",
                                  },
                                ]
                          }
                          inputStatusClassName="!w-[15px] !h-[15px]"
                          statusIconClassName="!top-1/2 !-translate-y-1/2"
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "topic_id"
                          )}
                          filterOption={(input, option) =>
                            filterOptionBySubstring(
                              input,
                              option?.label as string
                            )
                          }
                          disabled={
                            getStatusForField(loadingStatus, "topic_id") ===
                            "loading"
                          }
                          onChange={(value: string | string[]) => {
                            if (!value) {
                              notification.error({
                                description: "Topic is required",
                              });
                              return;
                            }
                            handleChange({
                              value,
                              name: "topic_id",
                            });
                          }}
                        />
                      </div>
                    </Tooltip>

                    <ButtonField
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      labelPlacement="left"
                      value={HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}
                      headerTooltip={`Location: ${HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}`}
                      editInline={true}
                      iconView={true}
                      required={true}
                      placeholder={_t("Select Project")}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      formInputClassName="lg:max-w-[760px] max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-sm h-6 whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-sm"
                      readOnly={isReadOnly}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "project_id"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      rightIcon={
                        !isEmpty(details?.project_name) &&
                        !isNaN(Number(details?.project_id)) && (
                          <ProjectFieldRedirectionIcon
                            projectId={details?.project_id?.toString() || ""}
                          />
                        )
                      }
                      onClick={() => {
                        setIsSelectProOpen(true);
                      }}
                    />
                    <Tooltip
                      title={HTMLEntities.decode(
                        sanitizeString(details?.meeting_status_name)
                      )}
                    >
                      <div
                        className={`overflow-hidden w-fit ${
                          isReadOnly ? "pl-1.5" : ""
                        }`}
                      >
                        <Tag
                          color={meetingStatus?.color}
                          style={{
                            color: `${meetingStatus?.textColor || ""}`,
                          }}
                          className={`${
                            meetingStatus?.textColor === "" &&
                            "!text-primary-900"
                          } text-xs h-[21px] flex items-center me-[0]`}
                        >
                          {HTMLEntities.decode(
                            sanitizeString(meetingStatus?.status)
                          )}
                        </Tag>
                      </div>
                    </Tooltip>
                  </div>
                </div>
                <div className="flex justify-between md:w-fit w-full">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <div>
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>
                  </div>

                  <ul className="flex items-center justify-end gap-2.5">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName="!text-primary-900 group-hover/buttonHover:!text-deep-orange-500"
                        className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        onClick={() => {
                          onReloadDetails();
                        }}
                      />
                    </li>
                    {module_access !== "read_only" && !isDetailLoading && (
                      <SMListAction
                        paramsData={{
                          meeting_id: details?.meeting_id,
                          is_deleted: details?.is_deleted,
                          project_id: details?.project_id || 0,
                        }}
                        buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                        tooltipcontent={_t("More")}
                        onActionComplete={() => {
                          if (
                            window &&
                            window.ENV &&
                            window.ENV.PAGE_IS_IFRAME
                          ) {
                            parentPostMessage(sendMessageKeys?.modal_change, {
                              open: false,
                            });
                          } else {
                            dispatch(resetDash());
                            navigate(`${routes.MANAGE_SAFETY_MEETINGS.url}`);
                          }
                        }}
                      />
                    )}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {isSelectProOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProOpen}
          module_key={module_key}
          setOpen={setIsSelectProOpen}
          selectedProjects={
            selectedProject?.id && !!selectedProject?.project_name
              ? [selectedProject]
              : []
          }
          onProjectSelected={(data) => {
            setSelectedProject(data.length ? data[0] : null);
            handleUpdateField(
              {
                project_id: data[0]?.id || 0,
              },
              { project_name: data[0]?.project_name || "" }
            );
          }}
          genericProjects={"project,opportunity"}
          category={"safety_meeting_project"}
        />
      )}
    </>
  );
};

export default SafetyMeetingTopBar;
