import {
  CellClickedEvent,
  EditableCallbackParams,
  GridOptions,
  IRowNode,
  RowClassParams,
  RowDragCallbackParams,
  ValueGetterParams,
  ValueSetterParams,
} from "ag-grid-community";
import React, { memo, useCallback, useMemo } from "react";
import { getValuableObj, Number, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import {
  floatWithNegativeRegex,
  qtyNumberCheck,
} from "~/shared/utils/helper/common";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useAppProDispatch, useAppProSelector } from "../../../../redux/store";
import {
  updateBudgetItems,
  updateWholeBudgetItems,
} from "../../../../redux/slices/proSovSlice";
import { updateProjectItemTodb } from "../../../../redux/action/projectSovAction";
import { RowStyle } from "ag-grid-community";
import { CellMouseOverEvent } from "ag-grid-community";
import { CellMouseOutEvent } from "ag-grid-community";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import {
  MoveCellRenderer,
  CostCellRenderer,
  InvoicedCellRenderer,
  ItemNameCellRenderer,
  QtyCellRenderer,
  TypeCellRenderer,
  UnitCellRenderer,
  RemainCellRenderer,
  TotalCellRenderer,
  TaxCellRenderer,
  ActionCellRenderer,
} from "./CellRendererComps";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

const ItemTableRender: React.FC<TItemTableRender> = ({
  details,
  otherItems,
  callSummaryApi,
  isPercentageBilling,
  setEditProjectItem,
  setItemToEdit,
  setDeleteItemId,
  gridKey,
  computeTotalRow,
}) => {
  const { _t } = useTranslation();

  const currentModule = getCurrentMenuModule();
  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppProDispatch();
  const {
    unitData,
    projectSOVItems: { budget_items = [] },
  } = useAppProSelector((state) => state.proSov);

  const { module_access = "" } = currentModule || {};

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const unitOptions = useMemo(() => {
    const formatted = unitData?.map((u) =>
      HTMLEntities.decode(sanitizeString(u.name))
    );
    return ["Select unit", ...formatted];
  }, [unitData]);

  const getNewTotalAmount = useCallback(
    (
      data: ISOVBudgetItemsData,
      qty: string | number,
      unit_cost: string | number
    ) => {
      const markupToConsider = Number(data.markup || 0);
      const isMarkupPercentage = Number(data?.is_markup_percentage);

      const totalWithoutMU = Number(qty) * Number(unit_cost);

      if (isMarkupPercentage) {
        const markupOnTotal = (totalWithoutMU * markupToConsider) / 100;
        const totalWithMU = totalWithoutMU + markupOnTotal;
        return totalWithMU;
      } else {
        return markupToConsider ? markupToConsider / 100 : totalWithoutMU;
      }
    },
    []
  );

  const handleUpdateFieldValue = async (
    updatedData: Partial<ISOVBudgetItemsData>,
    call_summary?: boolean
  ) => {
    dispatch(
      updateBudgetItems({
        item_id: updatedData?.item_id,
        updatedItem: updatedData,
      })
    );

    const payload = {
      items: [updatedData],
    };

    const updateRes = (await updateProjectItemTodb(
      Number(details.id),
      payload
    )) as DefaultResponse;

    if (!updateRes.success) {
      const oldItem = otherItems.find(
        (i) => Number(i.item_id) === Number(updatedData.item_id)
      );
      dispatch(
        updateBudgetItems({
          item_id: oldItem?.item_id,
          updatedItem: oldItem,
        })
      );
      notification.error({
        description: updateRes?.message || "Faield to update item",
      });
    } else {
      if (call_summary) {
        callSummaryApi();
      }
    }
  };

  const columnDefs = useMemo(
    () => [
      {
        headerName: "",
        maxWidth: 30,
        minWidth: 30,
        field: "move",
        suppressMenu: true,
        rowDrag: (params: RowDragCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,
        cellClass: "ag-cell-center ag-move-cell custom-move-icon-set",
        cellRenderer: MoveCellRenderer,
      },
      {
        headerName: _t("Type"),
        maxWidth: 50,
        minWidth: 50,
        field: "type",
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        cellRenderer: TypeCellRenderer,
        cellRendererParams: {
          formatter,
          _t,
        },
      },
      {
        headerName: _t("Item"),
        field: "item_name",
        minWidth: 190,
        flex: 2,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        editable: (params: EditableCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,
        cellRenderer: ItemNameCellRenderer,
        cellRendererParams: {
          formatter,
          _t,
        },
        valueGetter: (params: ValueGetterParams<ISOVBudgetItemsData>) => {
          return HTMLEntities.decode(sanitizeString(params.data?.subject));
        },
        valueSetter: (params: ValueSetterParams<ISOVBudgetItemsData>) => {
          const { data } = params;
          if (params && params.node && params.newValue) {
            const updatedData = {
              ...data,
              subject: HTMLEntities.decode(
                sanitizeString(params.newValue?.trim())
              ),
            };
            params.node.setData(updatedData);

            handleUpdateFieldValue(updatedData);
          }
          return true;
        },
      },
      {
        headerName: _t("QTY"),
        field: "quantity",
        maxWidth: 80,
        minWidth: 80,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        editable: (params: EditableCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,
        cellEditorParams: {
          maxLength: 20,
        },
        cellEditor: "agNumberCellEditor",
        cellRenderer: QtyCellRenderer,
        cellRendererParams: {
          formatter,
        },
        valueGetter: (params: ValueGetterParams<ISOVBudgetItemsData>) => {
          return params.data?.quantity || 0;
        },
        valueSetter: (params: ValueSetterParams<ISOVBudgetItemsData>) => {
          const { data } = params;
          const newVal = Number(params.newValue || 0);

          const newTotal = getNewTotalAmount(
            data,
            newVal,
            Number(data?.unit_cost) / 100
          );
          const checkNum = qtyNumberCheck(newVal);

          if (
            newVal &&
            newVal.toString().length > 6 &&
            (!newVal.toString().includes(".") || !checkNum)
          ) {
            notification.error({
              description: "Quantity should be less than or equal to 6 digits.",
            });
            return false;
          }

          if (!floatWithNegativeRegex.test(newVal?.toString())) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }

          const updatedData = {
            ...data,
            unit_cost: Number(data.unit_cost),
            quantity: newVal,
            total: newTotal * 100,
          };
          params.node?.setData(updatedData);

          handleUpdateFieldValue(updatedData, true);
          return true;
        },
      },
      {
        headerName: _t("Cost"),
        field: "cost",
        maxWidth: 130,
        minWidth: 130,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        editable: (params: EditableCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,

        cellRenderer: CostCellRenderer,
        cellRendererParams: {
          formatter,
        },
        valueGetter: (params: ValueGetterParams<ISOVBudgetItemsData>) => {
          return Number(params.data?.unit_cost || 0) / 100;
        },
        valueSetter: (params: ValueSetterParams<ISOVBudgetItemsData>) => {
          const { data } = params;
          let newUnitCost = params.newValue ? params.newValue.toString() : "";

          const [integerPart, decimalPart] = newUnitCost.split(".");

          if (Number(newUnitCost) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Unit Cost."),
            });
            return false;
          }

          if (
            integerPart?.length > 10 ||
            (decimalPart && decimalPart?.length > 2)
          ) {
            notification.error({
              description: _t("Unit cost should be less than 10 digits"),
            });
            return false;
          }

          const newTotal = getNewTotalAmount(data, data?.quantity, newUnitCost);

          const updatedData = {
            ...data,
            unit_cost: Number(newUnitCost) * 100,
            total: newTotal * 100,
          };
          params.node?.setData(updatedData);

          handleUpdateFieldValue(updatedData, true);
          return true;
        },
      },
      {
        headerName: _t("Unit"),
        field: "unit",
        maxWidth: 100,
        minWidth: 100,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: UnitCellRenderer,
        cellRendererParams: {
          formatter,
          _t,
        },

        editable: (params: EditableCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,

        cellEditor: "agRichSelectCellEditor",
        cellEditorParams: {
          values: unitOptions || [],
          filterList: true,
          searchType: "matchAny",
          allowTyping: true,
        },
        valueGetter: (params: ValueGetterParams<ISOVBudgetItemsData>) => {
          return HTMLEntities.decode(sanitizeString(params.data?.unit || ""));
        },
        valueSetter: (params: ValueSetterParams<ISOVBudgetItemsData>) => {
          const { data, node, newValue } = params;
          const valToConsider = newValue === "Select unit" ? "" : newValue;

          if (valToConsider?.toLowerCase() === params.oldValue?.toLowerCase()) {
            return false;
          }

          if (node) {
            const updatedData = {
              ...data,
              unit: HTMLEntities.decode(sanitizeString(valToConsider?.trim())),
            };
            params.node?.setData(updatedData);

            handleUpdateFieldValue(updatedData);
          }
          return true;
        },
      },
      {
        headerName: _t("MU"),
        field: "markup",
        minWidth: 80,
        maxWidth: 80,
        suppressMovable: false,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (params: BaseCellRendererProps<ICellRendererProps>) => {
          const { data } = params;
          if (params.node.rowPinned) {
            return params.value;
          }

          const is_markup_percentage = data?.is_markup_percentage ?? "";
          const total =
            Number(data?.quantity ?? 0) * (Number(data?.unit_cost ?? 0) / 100);
          const markup = Number(data?.markup);

          let muToShow = `${formatter("0").value}%`; // Default value

          if (is_markup_percentage?.toString() === "1") {
            muToShow = `${
              formatter(
                Number(markup) ? Math.round(markup ?? 0).toString() : "0"
              ).value
            }%`;
          } else {
            if (total != 0) {
              const markupPercentage = markup / total - 100;
              muToShow = `${formatter(markupPercentage.toFixed(2))?.value}%`;
            }
          }

          return (
            <div className="flex gap-1 items-center justify-end overflow-hidden">
              {Number(data?.is_markup_percentage) === 0 && !isReadOnly ? (
                <Tooltip
                  title={_t(
                    `To edit a ${
                      formatter().currency_symbol
                    } amount View and Edit the Item`
                  )}
                  placement="top"
                >
                  <FontAwesomeIcon
                    className="!w-3.5 !h-3.5 text-primary-900"
                    icon="fa-regular fa-circle-info"
                  />
                </Tooltip>
              ) : (
                ""
              )}
              {data.markup ? (
                <Tooltip title={muToShow}>
                  <Typography className="table-tooltip-text">
                    {muToShow}
                  </Typography>
                </Tooltip>
              ) : (
                <>-</>
              )}
            </div>
          );
        },
        cellEditor: "agNumberCellEditor",
        cellRendererParams: {
          formatter,
        },
        editable: (params: EditableCallbackParams) =>
          !isReadOnly &&
          Number(params?.data?.is_markup_percentage) !== 0 &&
          !params.node.rowPinned,

        valueGetter: (params: ValueGetterParams<ISOVBudgetItemsData>) => {
          if (params?.node?.rowPinned) {
            return params.data?.markup;
          }

          return Number(params.data?.markup || 0);
        },
        valueSetter: (params: ValueSetterParams<ISOVBudgetItemsData>) => {
          const { data } = params;
          let newMarkup = params.newValue ? params.newValue.toString() : "";

          const [integerPart, decimalPart] = newMarkup.split(".");

          if (Number(newMarkup) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Markup."),
            });
            return false;
          }

          if (
            integerPart?.length > 3 ||
            (decimalPart && decimalPart?.length > 2)
          ) {
            notification.error({
              description: _t(
                "Markup should be less than or equal to 3 digits."
              ),
            });
            return false;
          }
          const newData = { ...data, markup: newMarkup };
          const newTotal = getNewTotalAmount(
            newData,
            data?.quantity,
            data.unit_cost
          );

          const updatedData = {
            ...data,
            markup: newMarkup,
            unit_cost: Number(data.unit_cost),
            total: newTotal,
          };
          params.node?.setData(updatedData);

          handleUpdateFieldValue(updatedData, true);
          return true;
        },
      },
      {
        headerName: _t("Invoiced"),
        field: "invoiced",
        minWidth: 150,
        maxWidth: 150,
        suppressMovable: false,
        suppressMenu: true,
        // cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: InvoicedCellRenderer,
        cellRendererParams: { formatter, isPercentageBilling },
      },
      {
        headerName: _t("Remain"),
        field: "remain",
        minWidth: 150,
        maxWidth: 150,
        suppressMovable: false,
        suppressMenu: true,
        // cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: RemainCellRenderer,
        cellRendererParams: {
          formatter,
          isPercentageBilling,
        },
      },
      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 130,
        maxWidth: 130,
        suppressMovable: false,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: TotalCellRenderer,
        cellRendererParams: {
          formatter,
        },
      },
      {
        headerName: _t("Tax"),
        field: "tax",
        minWidth: 50,
        maxWidth: 50,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center cursor-pointer",
        editable: (params: EditableCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,
        onCellClicked: (params: CellClickedEvent) => {
          const { data } = params;
          if (isReadOnly || params.node.rowPinned) return;

          if (params && params.node) {
            const newValue = !(data as ISOVBudgetItemsData).apply_global_tax
              ? 1
              : 0;

            const updatedData: ISOVBudgetItemsData = {
              ...data,
              apply_global_tax: newValue,
            };

            params.node.setData(updatedData);
            handleUpdateFieldValue(updatedData);
          }
          return true;
        },
        cellRenderer: TaxCellRenderer,
      },
      {
        headerName: "",
        field: "",
        maxWidth: 80,
        minWidth: 80,
        suppressMenu: true,
        cellClass: "ag-cell-left",
        cellRenderer: ActionCellRenderer,
        cellRendererParams: {
          setEditProjectItem,
          setItemToEdit,
          setDeleteItemId,
          isReadOnly,
          _t,
        },
      },
    ],
    [isReadOnly, isPercentageBilling]
  );

  const handleItemDragAndDrop = useCallback(
    async (items: ISOVBudgetItemsData[]) => {
      const indexedItems = items.map(
        (row: ISOVBudgetItemsData, index: number) => ({
          ...row,
          item_no: index + 1,
        })
      );

      const remainItems = budget_items?.filter(
        (itm) =>
          !indexedItems.some((i) => Number(itm.item_id) === Number(i.item_id))
      );

      dispatch(updateWholeBudgetItems([...remainItems, ...indexedItems]));

      const projectItemsPayload = {
        need_update_item_no_only: 1,
        items: indexedItems,
      };

      const formData = getValuableObj(projectItemsPayload);

      const response = (await updateProjectItemTodb(
        Number(details.id),
        formData
      )) as IAddProjectItemApiRes;

      if (response?.success === false) {
        notification.error({
          description: response?.message || "Failed to reorder items",
        });
        dispatch(updateWholeBudgetItems(budget_items));
      }
    },
    [budget_items]
  );

  const SCROLL_THRESHOLD = 40;
  const SCROLL_SPEED = 20;

  const gridOptions: GridOptions = {
    stopEditingWhenCellsLoseFocus: true,

    onRowDragMove: (event) => {
      const container = document.querySelector(".ag-theme-alpine"); // adjust selector as needed
      if (!container) return;

      const rect = container.getBoundingClientRect();
      const mouseY = event.event.clientY;

      // Scroll Up
      if (mouseY - rect.top < SCROLL_THRESHOLD) {
        container.scrollTop -= SCROLL_SPEED;
      }

      // Scroll Down
      if (rect.bottom - mouseY < SCROLL_THRESHOLD) {
        container.scrollTop += SCROLL_SPEED;
      }
    },

    onRowDragEnd: function (event) {
      // ag grid community provide "RowNode" But not working that's why use any
      const { node, overIndex } = event as {
        node: IRowNode;
        overIndex: number;
      };
      if (!gridOptions.api || !node) return;

      const rowData: ISOVBudgetItemsData[] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));

      if (rowData.length === 1) {
        return;
      } else {
        if (node.rowIndex !== undefined && node.rowIndex !== null) {
          rowData.splice(overIndex, 0, rowData.splice(node?.rowIndex, 1)[0]);
          handleItemDragAndDrop(rowData);
        }
      }
    },
  };

  const handleCellMouseOver = (params: CellMouseOverEvent) => {
    if (params.column.getColId() === "tax") {
      params.node.setData({
        ...params.data,
        isHovered: true,
      });
    }
  };

  const handleCellMouseOut = (params: CellMouseOutEvent) => {
    if (params.column.getColId() === "tax") {
      params.node.setData({
        ...params.data,
        isHovered: false,
      });
    }
  };

  return (
    <StaticTable
      key={gridKey?.toString()}
      className="static-table"
      columnDefs={columnDefs}
      gridOptions={gridOptions}
      rowDragManaged={true}
      animateRows={true}
      cacheBlockSize={100}
      rowBuffer={80}
      rowData={otherItems}
      pinnedBottomRowData={otherItems.length ? [computeTotalRow()] : undefined}
      getRowStyle={(params: RowClassParams): RowStyle | undefined => {
        if (params.node.rowPinned) {
          return {
            backgroundColor: "#f8f8f8",
            fontWeight: "600",
            color: "#878787 !important",
            border: "none !important",
            textAlign: "right",
          };
        }
        return {};
      }}
      onCellMouseOver={handleCellMouseOver}
      onCellMouseOut={handleCellMouseOut}
      stopEditingWhenCellsLoseFocus={true}
      getRowId={(params) => params.data?.item_id}
      noRowsOverlayComponent={() => (
        <NoRecords
          image={`${window.ENV.CDN_URL}assets/images/no-records-expenses.svg`}
        />
      )}
    />
  );
};

export default memo(ItemTableRender);
