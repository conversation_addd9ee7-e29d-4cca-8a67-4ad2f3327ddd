// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { GradientIcon } from "~/shared/components/molecules/gradientIcon";

// Organisms
import AttachmentSection from "~/shared/components/organisms/attachmentSection/AttachmentSection";

// Hook
import { useTranslation } from "~/hook";

// Other
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { useAppDLSelector } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { defaultConfig } from "~/data";

const DLAttchmentAssociated = () => {
  const { _t } = useTranslation();
  const { module_id, module_access, module_key }: GConfig = getGConfig();
  const { date_format, image_resolution }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const { isFilePhotoLoading, referenceFilePhotos }: IDLFilePhotoInitialState =
    useAppDLSelector((state) => state.dLFilePhoto);

  const associatedDLTitle = _t(
    "Associated Files (Files Associated with other items such as: Project, Safety Meeting, etc)"
  );

  if (!isFilePhotoLoading && !Boolean((referenceFilePhotos ?? []).length)) {
    return (
      <>
        <GradientIcon
          title={associatedDLTitle}
          svgIcons={{
            icon: "fa-solid fa-file",
            containerClassName:
              "bg-[linear-gradient(180deg,#8B99B41a_0%,#63759A1a_100%)]",
            id: "add_associated_files_icon",
            colors: ["#8B99B4", "#63759A"],
          }}
        />

        <NoRecords
          className="mx-auto"
          image={`${window.ENV.CDN_URL}assets/images/no-records-files.svg`}
        />
      </>
    );
  }

  return (
    <AttachmentSection
      title={associatedDLTitle}
      files={referenceFilePhotos || []}
      isLoadingSection={isFilePhotoLoading && referenceFilePhotos?.length < 1}
      isReadOnly={true}
      isShowAddIcon={false}
      onAddAttachment={() => {}}
      onDeleteFile={() => {}}
      validationParams={{
        date_format,
        file_support_module_access: checkModuleAccessByKey(
          defaultConfig.file_support_key
        ),
        image_resolution,
        module_key,
        module_id,
        module_access,
      }}
      svgIcon={"fa-solid fa-file"}
      containerClassName={
        "bg-[linear-gradient(180deg,#8B99B41a_0%,#63759A1a_100%)]"
      }
      svgId={"add_associated_files_icon"}
      svgColor={["#8B99B4", "#63759A"]}
    />
  );
};

export default DLAttchmentAssociated;
