import { useMemo, useState } from "react";
import { useParams } from "@remix-run/react";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { GradientIcon } from "~/shared/components/molecules/gradientIcon";

// Organisms
import AttachmentSection from "~/shared/components/organisms/attachmentSection/AttachmentSection";

// Hook
import { useTranslation } from "~/hook";

// Other
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { addDLFilePhotoApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { addUpDelFilesAct } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLFilePhotoSlice";
import { filterAttachmentFiles } from "~/shared/utils/helper/common";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { defaultConfig } from "~/data";

const DLAttachmentSection = () => {
  const { _t } = useTranslation();
  const { id: paramId }: RouteParams = useParams();
  const { module_id, module_access, module_key }: GConfig = getGConfig();
  const { date_format, image_resolution }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const { isFilePhotoLoading, filePhotos }: IDLFilePhotoInitialState =
    useAppDLSelector((state) => state.dLFilePhoto);
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const dispatch = useAppDLDispatch();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [res, setRes] = useState({});

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const handleSaveAttachment = async (files: IFile[]) => {
    if (!isLoading) {
      const formData: IAddDLFilePhotoFrm = {
        primary_id: Number(paramId),
        module_id,
        module_key,
      };
      if (files.length) {
        const { attachImage, awsFilesUrl }: IFilterAttachmentFiles =
          filterAttachmentFiles(files);
        formData.attach_image = attachImage.length
          ? attachImage.join(",")
          : undefined;
        const updatedAwsFilesUrl = awsFilesUrl.map((file) => ({
          ...file,
          isheic: file.file_ext.toLowerCase() === "heic" ? 1 : 0, // Add 'isheic' key
        }));
        formData.files = updatedAwsFilesUrl.length
          ? updatedAwsFilesUrl
          : undefined;
        formData.project_id = details.projectId;
      } else {
        return false;
      }
      setIsLoading(true);
      const addFilesRes = (await addDLFilePhotoApi(
        formData
      )) as IAddDLFilePhotoRes;

      setRes(addFilesRes);
      if (addFilesRes?.success) {
        dispatch(resetDash());
        dispatch(
          addUpDelFilesAct({
            files: addFilesRes?.data?.aws_files,
            action: "add",
          })
        );
      } else {
        notification.error({
          description: addFilesRes?.message,
        });
      }

      setIsLoading(false);
      setRes({});
    }
  };

  if (
    !isFilePhotoLoading &&
    !Boolean((filePhotos ?? []).length) &&
    isReadOnly
  ) {
    return (
      <>
        <GradientIcon
          title={_t(`Files`)}
          svgIcons={{
            icon: "fa-solid fa-file-image",
            containerClassName:
              "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
            id: "add_attachments_icon",
            colors: ["#50EBFD", "#5996E9"],
          }}
        />

        <NoRecords
          className="mx-auto"
          image={`${window.ENV.CDN_URL}assets/images/no-records-files.svg`}
        />
      </>
    );
  }

  return (
    <AttachmentSection
      projectid={details.projectId}
      title={_t("Files")}
      files={filePhotos || []}
      isLoading={isLoading}
      isLoadingSection={isFilePhotoLoading && filePhotos?.length < 1}
      isAddAllow={!isReadOnly}
      isReadOnly={isReadOnly}
      onAddAttachment={(data) => {
        const filtered = data.filter(
          (file) =>
            !(filePhotos || []).some(
              (exist) => exist.image_id === file.image_id
            )
        );
        handleSaveAttachment(filtered);
      }}
      onFileUpdated={(data) => {
        if (!data?.success) {
          notification.error({
            description: data?.message || "Something went wrong!",
          });
          return;
        }
        dispatch(
          addUpDelFilesAct({
            files: data?.isAddNew == 1 ? [data.data] : data.data,
            action: data?.isAddNew == 1 ? "add" : "update",
          })
        );
        dispatch(resetDash());
      }}
      onDeleteFile={(data) => {
        dispatch(resetDash());
        dispatch(
          addUpDelFilesAct({
            files: data,
            action: "delete",
          })
        );
      }}
      validationParams={{
        date_format,
        file_support_module_access: checkModuleAccessByKey(
          defaultConfig.file_support_key
        ),
        image_resolution,
        module_key,
        module_id,
        module_access,
      }}
      addFilesRes={res}
    />
  );
};

export default DLAttachmentSection;
