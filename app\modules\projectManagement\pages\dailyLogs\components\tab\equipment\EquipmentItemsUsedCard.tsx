import { useMemo, useState } from "react";
import isEmpty from "lodash/isEmpty";
import { ValueSetterParams } from "ag-grid-community";
import type { Dispatch } from "@reduxjs/toolkit";
import { useParams } from "@remix-run/react";

// hook + redux
import { useTranslation } from "~/hook";
import { defaultConfig } from "~/data";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  deleteEquipmentsTimecardDetail,
  fetchDLEquipments,
  getDLEquipmentItemsApi,
  updateEquipments,
} from "~/modules/projectManagement/pages/dailyLogs/redux/action";

import {
  addEquipmentUsedAct,
  deleteEquipmentUsedAct,
  resetDLEquipment,
  resetDLEquipmentItemsAct,
  updateEquipmentLogAct,
  upEqtUsedAct,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/equipmentsSlice";
import { sanitizeString } from "~/helpers/helper";
import { convertMaskHours } from "~/shared/utils/helper/common";

// components
import { EquipmentItem } from "../sidebar/equipmentItem";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AddButton } from "~/shared/components/molecules/addButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { LoadingItems } from "~/shared/components/molecules/loadingItems";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import AnimateError from "~/shared/components/molecules/animateMark/AnimateError";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
// Other
import InputCellEditor from "~/shared/components/agGrid/InputCellEditor";
import { ColDef } from "ag-grid-community";

const EquipmentItemsUsedCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const { id: logId }: RouteParams = useParams();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [equipmentItemOpen, setEquipmentItemOpen] = useState<
    TEquipmentItemTabsValues | ""
  >("");
  const [operatorModalOpen, setOperatorModalOpen] = useState<boolean>(false);
  const [selectedOperator, setSelectedOperator] = useState<
    TselectedContactSendMail[]
  >([]);

  const [operatorData, setOperatorData] = useState<IEquipmentsUsed>();
  const [isEquipmentLoading, setIsEquipmentLoading] = useState<boolean>(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState<boolean>(false);
  const [selectedItemData, setSelectedItemData] =
    useState<IEquipmentsUsed | null>(null);

  const { isEquipmentTabLoading, equipmentData }: IDLEquipmentsInitialState =
    useAppDLSelector((state) => state.dailyLogeEuipments);
  const [loding, setLoding] = useState<boolean>(false);
  const { equipmentsUsed } = equipmentData;
  const { codeCostData }: IGetCostCodeList = useAppDLSelector(
    (state) => state.costCode
  );
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );

  const saveCheckedEquipmentItemsHandler = async (
    checkedItems: TEquipmentTimeCardsDetailData
  ) => {
    try {
      setIsEquipmentLoading(true);
      // Function to pick the required properties
      const pickRequiredProperties = (item: any) => ({
        itemId: 0,
        itemName: item.name || item.subject,
        itemPrimaryId: item.item_id || item.equipment_id || item.estimate_id,
        hours: item.hours || "00000:00",
        notes: item.notes,
        costCodeId: item.cost_code_id,
        operatorId: item.operator_id,
        operatorName: item.operator_name,
        operatorProfileImage: item.operator_profile_image,
        costCodeName: item.cost_code_name,
        itemMlesoReferenceId: item?.reference_item_id,
        itemReferenceModuleId: logId,
        unitCost: item?.unit_cost,
        markup: item?.markup,
        hiddenMarkup: item?.hidden_markup,
        isMarkupPercentage: item?.is_markup_percentage,
      });

      // Combine all equipment items
      const allItems = [
        ...checkedItems.change_order_equipment_item,
        ...checkedItems.equipment_item,
        ...checkedItems.estimate_equipment_item,
      ];

      // Remove duplicates based on equipment_id
      const filteredEquipments = allItems
        .map(pickRequiredProperties)
        .filter(
          (item, index, self) =>
            index ===
            self.findIndex((t) => t.itemPrimaryId === item.itemPrimaryId)
        );

      let response: Partial<TIEquipmentTimeCardsDetailResponseNew> = {
        data: {
          equipment_item: [],
          estimate_equipment_item: [],
          change_order_equipment_item: [],
          equipment_item_used: [],
        },
      };

      // Make the API call
      response = (await updateEquipments({
        logId: Number(logId),
        equipments: filteredEquipments,
      })) as TIEquipmentTimeCardsDetailResponseNew;

      if (response?.success) {
        const resApi = (await getDLEquipmentItemsApi({
          id: logId || "",
        })) as IDLEquipmentsApiRes;
        if (resApi?.success) {
          dispatch(
            resetDLEquipmentItemsAct({
              data: resApi?.data?.equipmentLogs,
            })
          );
        }
        dispatch(
          addEquipmentUsedAct({
            action: "add",
            data: response.data,
          })
        );
      }
      setEquipmentItemOpen("");
      setIsEquipmentLoading(false);
    } catch (error) {
      return [];
    } finally {
      setIsEquipmentLoading(false);
    }
  };

  const modalSidebarCustomer = (data: IEquipmentsUsed) => {
    if (data?.operator_id && data?.operator_id != 0) {
      setSelectedOperator([
        {
          user_id: data?.operator_id,
          display_name: data?.operator_name,
          type_key: data?.type_key,
          type_name: data?.type_name,
          image: data?.operator_profile_image,
        },
      ]);
    } else {
      setSelectedOperator([]);
    }
    setOperatorModalOpen(true);
    setOperatorData(data);
  };

  const handleCustomerSelection = async (data: IEquipmentsUsed[]) => {
    const selectedOperatorData = data[0] || {};

    const updatedData = {
      ...operatorData,
      operator_id: selectedOperatorData.user_id,
      operator_name: selectedOperatorData.display_name,
      operator_profile_image: selectedOperatorData.image,
    };

    try {
      dispatch(
        updateEquipmentLogAct({
          reference_item_id: operatorData?.item_id ?? 0,
          operator_id: selectedOperatorData.user_id,
          display_name: selectedOperatorData.display_name,
          operator_profile_image: selectedOperatorData.image,
        })
      );
      dispatch(
        addEquipmentUsedAct({
          action: "update",
          data: updatedData,
        })
      );

      const response = (await updateEquipments({
        logId: Number(logId),
        equipments: [
          {
            itemId: operatorData?.item_id ?? 0,
            operatorId: String(updatedData.operator_id),
            operatorProfileImage: updatedData?.operator_profile_image,
          },
        ],
      })) as IUpdateEquipmentRes;

      if (!response?.success) {
        dispatch(fetchDLEquipments({ id: logId, isHideLoading: true }));
        notification.error({
          description: response?.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const deleteModalOpen = (item: IEquipmentsUsed) => {
    setSelectedItemData(item);
    setIsConfirmModalOpen(true);
  };

  const onDelete = async () => {
    if (selectedItemData === null) return;

    try {
      if (
        selectedItemData &&
        selectedItemData.item_id !== undefined &&
        selectedItemData.daily_log_id !== undefined
      ) {
        setIsLoading(true);
        const responseApi = (await deleteEquipmentsTimecardDetail({
          itemId: [selectedItemData.item_id],
          logId: selectedItemData.daily_log_id,
        })) as IDeleteEquipmentTimecardRes;
        if (responseApi?.success) {
          dispatch(
            deleteEquipmentUsedAct({ item_id: selectedItemData.item_id })
          );
        } else {
          notification.error({
            description: responseApi?.message || "Something went wrong!",
          });
        }
        setIsLoading(false);
      }
    } catch (error) {
      notification.error({
        description: "An error occurred while deleting the record.",
      });
      setIsLoading(false);
    }
    setIsConfirmModalOpen(false);
  };

  const updateData = async (
    params: { data: Partial<IEquipment>; newValue: string },
    field: keyof IEquipment,
    dispatch: Dispatch,
    callback: (success: boolean) => void
  ): Promise<void> => {
    let newValue = params.newValue;

    if (field == "hours") {
      newValue = convertMaskHours(newValue);
    }

    if (field == "hours") {
      dispatch(
        upEqtUsedAct({
          item_id: params?.data?.item_id,
          hours: newValue,
        })
      );
    } else {
      dispatch(
        upEqtUsedAct({
          item_id: params?.data?.item_id,
          notes: newValue,
        })
      );
    }

    if (params.data[field] !== newValue) {
      const updatedData: Partial<IEquipment> = {
        ...params.data,
        [field]: field === "notes" ? newValue.trim() : newValue,
      };

      try {
        if (field == "hours") {
          dispatch(
            updateEquipmentLogAct({
              reference_item_id: updatedData.item_id,
              e_hours_used: updatedData.hours,
            })
          );
        }

        const response = (await updateEquipments({
          logId: Number(logId),
          equipments: [
            {
              itemId: updatedData.item_id ?? 0,
              [field]: field === "notes" ? newValue.trim() : newValue,
            },
          ],
        })) as IUpdateEquipmentRes;

        if (response?.success) {
          dispatch(resetDLEquipment());
          setLoding(false);
          callback(true);
          return;
        } else {
          // @ts-ignore: Unreachable code error
          dispatch(fetchDLEquipments({ id: logId, isHideLoading: true }));
          notification.error({
            description: response?.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error)?.message || "Something went wrong!",
        });
      }
    }
    callback(false);
  };

  const updateCostCode = async (
    costCodeId: string = "",
    costCodeName: string,
    cost_code_csi_code: string = "",
    data: IEquipmentsUsed
  ) => {
    const updatedData = {
      ...data,
      cost_code_id: costCodeId,
      cost_code_name: costCodeName,
      cost_code_csi_code: cost_code_csi_code,
    };

    try {
      const updateEquipmentData = [
        {
          itemId: updatedData.item_id as number,
          costCodeId: costCodeId as string,
        },
      ];

      dispatch(
        addEquipmentUsedAct({
          action: "update",
          data: updatedData,
        })
      );

      const response = (await updateEquipments({
        logId: Number(details?.logId),
        equipments: updateEquipmentData,
      })) as IUpdateEquipmentRes;

      if (response?.success) {
        setLoding(false);
      } else {
        dispatch(fetchDLEquipments({ id: logId, isHideLoading: true }));
        notification.error({
          description: response?.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  const filterCostCodeName = (val: string) => {
    if (val) {
      return val
        .toString()
        .replace(/\(.*?\)/g, "")
        .trim();
    }
    return "";
  };

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  const equipmentItemsUsedCardTable = useMemo(() => {
    return (
      <StaticTable
        className="static-table"
        rowDragManaged={!isReadOnly}
        columnDefs={[
          {
            headerName: _t("Name"),
            field: "name",
            minWidth: 200,
            maxWidth: 250,
            headerClass: "ag-header-left",
            cellClass: "ag-cell-left",
            resizable: true,
            suppressMovable: false,
            suppressMenu: true,
            cellRenderer: ({ data }: IEquipmentsUsedCellRenderer) => {
              const { name } = data;
              return name ? (
                <Tooltip title={name}>
                  <Typography className="table-tooltip-text truncate">
                    {HTMLEntities.decode(sanitizeString(name))}
                  </Typography>
                </Tooltip>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: _t("Hours"),
            field: "hours",
            minWidth: 100,
            maxWidth: 100,
            headerClass: "ag-header-right",
            suppressMovable: false,
            suppressMenu: true,
            editable: !isReadOnly,
            cellEditor: InputCellEditor,
            suppressKeyboardEvent,
            cellClass: "table-input-field ag-cell-right",
            cellEditorParams: {
              maskFormat: "99999:99",
              isHourCheck: true,
            },
            valueGetter: (params: IEquipmentsUsedCellRenderer) =>
              params?.data?.hours,
            valueSetter: (params: ValueSetterParams) => {
              const { newValue } = params;
              const newFiltVal = convertMaskHours(newValue);
              let isSuccess = false;
              const regex = /^\d{0,5}(\:\d{0,2})?$/;

              // Validate the new value against the regex pattern
              if (!regex.test(newFiltVal)) {
                notification.error({
                  description:
                    "Invalid value. Please enter up to 5 digits before the colon and up to 2 digits after.",
                });
                return false; // Reject the new value
              }

              const updatedData = {
                ...params.data,
                hours: newFiltVal,
              };

              if (params && params.node) {
                params.node.setData(updatedData);
              }

              // If validation passes, update the data
              updateData(params, "hours", dispatch, (success) => {
                isSuccess = success;
              });
              return true;
            },
            cellRenderer: ({ data }: IEquipmentsUsedCellRenderer) => (
              <Typography className="table-tooltip-text">
                {data.hours || "-"}
              </Typography>
            ),
          },
          {
            headerName: _t("Operator"),
            field: "operator_name",
            minWidth: 100,
            maxWidth: 100,
            suppressMovable: false,
            suppressMenu: true,
            cellStyle: { textAlign: "center" },
            headerClass: "ag-header-center",
            valueGetter: (params: IEquipmentsUsedCellRenderer) => {
              return (
                params?.data?.operator_name,
                params?.data?.operator_profile_image
              );
            },
            valueSetter: (params: {
              data: Partial<IEquipment>;
              newValue: string;
            }) => {
              return true;
            },

            cellRenderer: ({ data }: { data: IEquipmentsUsed }) => {
              const { operator_name, operator_profile_image, operator_id } =
                data;
              const operatorName = HTMLEntities.decode(
                sanitizeString(operator_name)
              );
              return (
                <>
                  {!!operator_id && operator_id != 0 ? (
                    <Tooltip title={operatorName}>
                      <div
                        onClick={
                          !isReadOnly
                            ? () => {
                                modalSidebarCustomer(data);
                              }
                            : undefined
                        }
                        className={!isReadOnly ? "cursor-pointer" : ""}
                      >
                        <AvatarProfile
                          user={{
                            image: operator_profile_image,
                            name: operatorName,
                          }}
                          className="m-auto"
                          iconClassName="text-[11px] font-semibold"
                        />
                      </div>
                    </Tooltip>
                  ) : (
                    <div className="relative">
                      <Tooltip title={_t("Add Operator")}>
                        <div
                          className={`${
                            !isReadOnly && "cursor-pointer"
                          } w-6 h-6 mx-auto rounded-full flex items-center justify-center bg-primary-900/20`}
                          onClick={() => {
                            if (!isReadOnly) {
                              modalSidebarCustomer(data);
                            }
                          }}
                        >
                          <FontAwesomeIcon
                            className="text-base w-3.5 h-3.5 !text-primary-900 dark:!text-white/90"
                            icon="fa-regular fa-plus"
                          />
                        </div>
                      </Tooltip>
                      <Tooltip
                        title={_t("Missing operator for this equipment")}
                      >
                        <div className="w-4 h-4 absolute right-0.5 top-1/2 -translate-y-1/2">
                          <AnimateError
                            color="#ef4444"
                            strokeCircleWidth="8"
                            strokeLineWidth="8"
                          />
                        </div>
                      </Tooltip>
                    </div>
                  )}
                </>
              );
            },
          },
          {
            headerName: _t("Cost Code "),
            maxWidth: 350,
            minWidth: 200,
            flex: 1,
            headerClass: "ag-header-left",
            cellClass: "ag-cell-left",
            field: "cost_code_name",
            resizable: true,
            suppressMovable: false,
            suppressMenu: true,
            cellEditor: "agRichSelectCellEditor",
            editable: !isReadOnly,
            cellEditorParams: {
              values: codeCostData?.map(
                (item: ICostCode) =>
                  `${item?.cost_code_name}${
                    item?.csi_code &&
                    item?.csi_code != "0" &&
                    item?.csi_code !== null
                      ? ` (${item?.csi_code})`
                      : ""
                  }`
              ),
              filterList: true,
              searchType: "matchAny",
              allowTyping: true,
              valueListMaxHeight:
                equipmentsUsed?.length == 1
                  ? 60
                  : equipmentsUsed?.length == 2
                  ? 90
                  : equipmentsUsed?.length == 3
                  ? 120
                  : equipmentsUsed?.length == 4
                  ? 150
                  : 180,
            },
            valueSetter: (params: ValueSetterParams) => {
              const { newValue } = params;
              const newValueFlt = filterCostCodeName(newValue);
              const fltCostCode = codeCostData.find(
                (item) => item.cost_code_name?.toString()?.trim() == newValueFlt
              );

              const updatedData = {
                ...params.data,
                cost_code_name: newValue,
              };
              if (params && params.node) {
                params.node.setData(updatedData);
              }

              if (params?.newValue !== undefined) {
                if (fltCostCode?.code_id) {
                  const newCostCodeId = fltCostCode?.code_id || "";
                  updateCostCode(
                    newCostCodeId,
                    newValueFlt,
                    fltCostCode?.csi_code,
                    params.data as IEquipmentsUsed
                  );
                }
                return true;
              }
              return false;
            },
            cellRenderer: ({ data }: IEquipmentsUsedCellRenderer) => {
              const { cost_code_name, cost_code_csi_code } = data;
              const ccName = !isEmpty(cost_code_name)
                ? `${cost_code_name}${
                    cost_code_csi_code &&
                    cost_code_csi_code != null &&
                    cost_code_csi_code != "0"
                      ? ` (${cost_code_csi_code})`
                      : ""
                  }`
                : "";
              return ccName ? (
                <Tooltip title={ccName}>
                  <Typography className="table-tooltip-text">
                    {ccName}
                  </Typography>
                </Tooltip>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: _t("Description"),
            field: "notes",
            minWidth: 180,
            flex: 1,
            headerClass: "ag-header-left",
            cellClass: "ag-cell-left",
            resizable: true,
            suppressMovable: false,
            suppressMenu: true,

            editable: !isReadOnly,
            valueGetter: (params: IEquipmentsUsedCellRenderer) =>
              params?.data?.notes,
            valueSetter: (params: ValueSetterParams) => {
              let isSuccess = false;
              const { newValue } = params;
              const updatedData = {
                ...params.data,
                notes: newValue,
              };
              if (params && params.node) {
                params.node.setData(updatedData);
              }
              updateData(params, "notes", dispatch, (success) => {
                isSuccess = success;
              });
              return true;
            },
            cellRenderer: ({ data }: IEquipmentsUsedCellRenderer) => (
              <Tooltip title={HTMLEntities.decode(sanitizeString(data.notes))}>
                <Typography className="table-tooltip-text">
                  {HTMLEntities.decode(sanitizeString(data.notes)) || "-"}
                </Typography>
              </Tooltip>
            ),
          },
          {
            headerName: "",
            field: "",
            maxWidth: 45,
            minWidth: 45,
            suppressMenu: true,
            cellRenderer: ({ data }: IEquipmentsUsedCellRenderer) => {
              return (
                <div className="flex items-center gap-2 justify-center">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Delete")}
                    disabled={isReadOnly}
                    tooltipPlacement="top"
                    icon="fa-regular fa-trash-can"
                    onClick={() => deleteModalOpen(data)}
                  />
                </div>
              );
            },
          },
        ]}
        rowData={equipmentsUsed || []}
        stopEditingWhenCellsLoseFocus={true}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-equipment-items-used.svg`}
          />
        )}
      />
    );
  }, [equipmentsUsed, codeCostData]);
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Equipment Items Used")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-file-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#ff8ae51a_0%,#fd3ed31a_100%)]",
          id: "equipment_item_used_file_check",
          colors: ["#FF8AE5", "#FD3ED3"],
        }}
        headerRightButton={
          <AddButton
            onClick={() => {
              if (isReadOnly) {
                return;
              }
              setEquipmentItemOpen("all");
            }}
            disabled={isReadOnly}
          >
            {_t("Item")}
          </AddButton>
        }
        children={
          <div className="pt-2">
            {isEquipmentTabLoading || loding ? (
              <Spin className="w-full h-10 flex items-center justify-center" />
            ) : (
              <div className="ag-theme-alpine">
                {equipmentItemsUsedCardTable}
              </div>
            )}
          </div>
        }
      />
      {equipmentItemOpen && (
        <EquipmentItem
          equipmentItemOpen={equipmentItemOpen}
          setEquipmentItemOpen={setEquipmentItemOpen}
          loadingComponent={<LoadingItems isAvatar={false} skeleton={20} />}
          projectId={details.projectId ?? 0}
          saveCheckedEquipmentItemsHandler={saveCheckedEquipmentItemsHandler}
          defaultCheckedItems={{
            equipment_item: [],
            estimate_equipment_item: [],
            change_order_equipment_item: [],
          }}
          isEquipmentLoading={isEquipmentLoading}
        />
      )}

      {operatorModalOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setOperatorModalOpen(false);
          }}
          projectId={details?.projectId}
          singleSelecte={true}
          openSelectCustomerSidebar={operatorModalOpen}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          setCustomer={(data) => {
            if (data?.length < 1) {
              notification.error({
                description: _t("Operator is required."),
              });
              return false;
            }
            setSelectedOperator(data); // Update state with selected data
            handleCustomerSelection(data as IEquipmentsUsed[]); // Handle selection immediately
          }}
          showSaveBtn={false}
          selectedCustomer={selectedOperator}
          groupCheckBox={false}
        />
      )}

      {isConfirmModalOpen && (
        <ConfirmModal
          isOpen={isConfirmModalOpen}
          isLoading={isLoading}
          modaltitle={_t("Delete")}
          modalIcon="fa-regular fa-trash-can"
          description={_t("Are you sure you want to delete this Item?")}
          yesButtonLabel={_t("Yes")}
          onAccept={onDelete}
          noButtonLabel={_t("No")}
          onDecline={() => setIsConfirmModalOpen(false)}
          onCloseModal={() => setIsConfirmModalOpen(false)}
        />
      )}
    </>
  );
};

export default EquipmentItemsUsedCard;
