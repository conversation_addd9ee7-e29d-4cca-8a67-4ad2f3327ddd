// React + ag-grid
import { useMemo, useState } from "react";
import { useRevalidator } from "@remix-run/react";
// Hook
import { useTranslation } from "~/hook";
// Molecules
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
// Other
import InvoiceSendEmail from "./InvoiceSendEmail";
import InvoiceFilePreview from "./InvoiceFilePreview";
import { PostPaymentToInvoice } from "./tab/sidebar";
// Constants, Shared & Common
import { Number, sanitizeString } from "~/helpers/helper";
import { removeFirstSlash } from "~/shared/utils/helper/common";
import { INVOICE_OPTIONS } from "../utils/constants";
// Redux
import { routes } from "~/route-services/routes";
import { getGConfig, getGModuleByKey, useGModules } from "~/zustand";
import {
  archiveInvoiceApi,
  deleteInvoiceApi,
} from "~/modules/financials/pages/invoice/redux/action";
import { defaultConfig } from "~/data";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import { getGlobalUser } from "~/zustand/global/user/slice";

const InvoiceListAction = ({
  isKanbanDropDown = false,
  isDetailDropDown = false,
  paramsData,
  onActionComplete,
  iconClassName = "text-primary-900/80 group-hover/buttonHover:text-primary-900",
  buttonClass = "m-0 hover:!bg-[#0000000f]",
  tooltipcontent,
}: IInvoiceListActionProps) => {
  const revalidator = useRevalidator();
  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();
  let { getExistingUsersWithApi } = useExistingCustomers();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const {
    module_id,
    module_key,
    module_singular_name,
    page_is_iframe,
  }: GConfig = getGConfig();
  const { is_deleted } = paramsData;
  const moduleSLName =
    _t(HTMLEntities.decode(sanitizeString(module_singular_name))) ||
    _t("Invoice");
  const { module_name: paymentModuleName = "Payment" } =
    (getGModuleByKey(CFConfig.payment_module) as GModule) || {};
  // State
  const [selectedData, setSelectedData] = useState<Partial<IInvoiceData>>({});
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [shareLink, setShareLink] = useState<string>("");
  const [viewEmailPdf, setViewEmailPdf] = useState<Partial<IInvoiceData>>({});
  const [postPaymentToInvoice, setPostPaymentToInvoice] =
    useState<boolean>(false);
  const [billedToData, setBilledToData] = useState<TselectedContactSendMail[]>(
    []
  );

  const hasPaymentAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(defaultConfig.payment_module);
    return mAccess === "no_access" || mAccess === "read_only";
  }, []);

  const handleInvoiceDelArch = async () => {
    if (!isDeleting && selectedData?.invoice_id) {
      setIsDeleting(true);
      let deleteRes = { success: false, message: "Something went wrong" };
      if (delArchConfirmOpen === "archive") {
        deleteRes = (await archiveInvoiceApi({
          formData: {
            moduleKey: module_key,
            status: 1,
            moduleId: module_id,
          },
          paramsData: {
            invoiceId: selectedData.invoice_id?.toString() || "",
          },
        })) as IDeleteInvoiceRes;
      } else {
        deleteRes = (await deleteInvoiceApi({
          formData: {
            module_key: module_key,
          },
          paramsData: {
            invoiceId: selectedData.invoice_id?.toString() || "",
          },
        })) as IDeleteInvoiceRes;
      }

      if (deleteRes?.success) {
        if (delArchConfirmOpen === "delete") {
          revalidator.revalidate();
        }
        onActionComplete(delArchConfirmOpen);
      }
      if (!deleteRes?.success) {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setSelectedData({});
      setDelArchConfirmOpen("");
      setIsDeleting(false);
    }
  };

  const handleInvoiceDelActive = async () => {
    if (!isDeleting && selectedData.invoice_id) {
      setIsDeleting(true);
      const activeRes = (await archiveInvoiceApi({
        formData: {
          moduleKey: module_key,
          status: 0,
          moduleId: module_id,
        },
        paramsData: {
          invoiceId: selectedData.invoice_id?.toString() || "",
        },
      })) as IDeleteInvoiceRes;

      if (activeRes?.success) {
        revalidator.revalidate();
        onActionComplete("active");
      } else {
        notification.error({
          description: activeRes?.message,
        });
      }
      setSelectedData({});
      setDelArchConfirmOpen("");
      setIsDeleting(false);
    }
  };

  const onCloseDelModal = () => {
    setSelectedData({});
    setDelArchConfirmOpen("");
  };

  const handleOnClick = async (type: string) => {
    setSelectedData(paramsData);

    if (type === "delete") {
      setDelArchConfirmOpen("delete");
    } else if (type === "archive") {
      setDelArchConfirmOpen("archive");
    } else if (type === "active") {
      setDelArchConfirmOpen("active");
    } else if (type === "share") {
      setIsShareOpen(true);
    } else if (type === "submit") {
      setViewEmailPdf(paramsData);
      if (
        (paramsData?.billed_to && paramsData?.billed_to != 0) ||
        (paramsData?.customer_id && paramsData?.customer_id != 0)
      ) {
        // const userIds = paramsData?.contact_id
        //   ? "0"
        //   : paramsData?.customer_id && paramsData?.billed_to
        //   ? [
        //       paramsData?.customer_id.toString(),
        //       paramsData?.billed_to.toString(),
        //     ]
        //       .filter(Boolean)
        //       .join(",")
        //   : paramsData?.customer_id && !paramsData?.billed_to
        //   ? paramsData.customer_id?.toString()
        //   : !paramsData?.customer_id && paramsData?.billed_to
        //   ? paramsData.billed_to.toString()
        //   : "";

        const userIdsArr = [
          !paramsData?.contact_id || paramsData.contact_id == 0
            ? paramsData?.customer_id
            : "0",
          !paramsData?.billed_to_contact || paramsData.billed_to_contact == 0
            ? paramsData?.billed_to
            : "0",
        ].filter(Boolean);

        getExistingUsersWithApi({
          usersIds: userIdsArr.map(String).join(","),
          contactIDs: [
            paramsData?.contact_id?.toString() || "0",
            paramsData?.billed_to_contact?.toString() || "0",
          ].join(","),
          apiDataReturn: (
            invoicedToCustomer: Partial<CustomerSelectedData>[]
          ) => {
            if (invoicedToCustomer.length > 0) {
              setBilledToData(invoicedToCustomer as TselectedContactSendMail[]);
            }
          },
        });
      }
    } else if (type === "payment") {
      setPostPaymentToInvoice(true);
    }
  };

  const updatedActionMenuOptList = useMemo(() => {
    const isHidePostPayment = paramsData.approval_type_key === "invoice_paid";
    const isHideSubmitToClientBtn = paramsData?.is_deleted;

    return INVOICE_OPTIONS.filter((option) => {
      if (
        (isHidePostPayment && option.key === "payment") ||
        (isHideSubmitToClientBtn && option.key === "submit")
      ) {
        return false;
      }

      if (option.key === "payment") {
        option.disabled =
          checkModuleAccessByKey(defaultConfig.payment_module) ===
            "read_only" ||
          checkModuleAccessByKey(defaultConfig.payment_module) === "no_access";
      }

      if (option.key === "delete") {
        option.disabled = allow_delete_module_items == "0" || page_is_iframe;
      }

      return true;
    });
  }, [
    allow_delete_module_items,
    JSON.stringify(INVOICE_OPTIONS),
    paramsData,
    checkModuleAccessByKey(defaultConfig.payment_module),
    page_is_iframe,
  ]);

  return (
    <>
      {isKanbanDropDown ? (
        <DropdownMenu
          options={updatedActionMenuOptList
            .filter(
              (item) =>
                item.key !==
                (is_deleted?.toString() === "1" ? "archive" : "active")
            )
            .map((option) => ({
              ...option,
              label:
                option.key === "payment"
                  ? `Post ${paymentModuleName} to ${moduleSLName}`
                  : option.label,
              disabled: option.disabled,
              onClick: () => handleOnClick(option.key as string),
            }))}
          icon="fa-solid fa-ellipsis-h"
          iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
          buttonClass="m-0 hover:!bg-[#0000000f] invisible group-hover/kanbanitem:visible"
          {...((hasPaymentAccessShowMessages ||
            allow_delete_module_items === "0") && {
            footerText: _t(
              "Some actions might be unavailable depending on your privilege."
            ),
          })}
        />
      ) : (
        <DropdownMenu
          options={updatedActionMenuOptList
            .filter(
              (item) =>
                item.key !==
                (is_deleted?.toString() === "1" ? "archive" : "active")
            )
            .map((option) => ({
              ...option,
              label:
                option.key === "payment"
                  ? `Post ${paymentModuleName} to ${moduleSLName}`
                  : option.label,
              disabled: option.disabled,
              onClick: () => handleOnClick(option.key as string),
            }))}
          icon="fa-regular fa-ellipsis-vertical"
          iconClassName={iconClassName}
          buttonClass={buttonClass}
          tooltipcontent={tooltipcontent}
          {...((hasPaymentAccessShowMessages ||
            allow_delete_module_items === "0") && {
            footerText: _t(
              "Some actions might be unavailable depending on your privilege."
            ),
          })}
        />
      )}

      {delArchConfirmOpen !== "" && (
        <ConfirmModal
          isOpen={delArchConfirmOpen !== ""}
          modaltitle={_t(
            delArchConfirmOpen === "delete"
              ? "Delete"
              : delArchConfirmOpen === "archive"
              ? "Archive"
              : "Active"
          )}
          description={_t(
            delArchConfirmOpen === "delete"
              ? "Are you sure you want to delete this Item?"
              : delArchConfirmOpen === "archive"
              ? "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
              : "Are you sure you want to Activate this data?"
          )}
          withConfirmText={delArchConfirmOpen === "delete"}
          modalIcon={
            delArchConfirmOpen === "delete"
              ? "fa-regular fa-trash-can"
              : delArchConfirmOpen === "archive"
              ? "fa-regular fa-box-archive"
              : "fa-regular fa-regular-active"
          }
          isLoading={isDeleting}
          onAccept={(data) => {
            if (delArchConfirmOpen === "active") {
              handleInvoiceDelActive();
            } else {
              handleInvoiceDelArch();
            }
          }}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id: Number(selectedData?.invoice_id),
            module_key: module_key,
            module_page: removeFirstSlash(routes.MANAGE_INVOICE.url || ""),
          }}
          onEmailLinkClick={(data) => {
            setIsSendEmailSidebarOpen(true);
            setShareLink(data);
            setIsShareOpen(false);
          }}
          onCloseModal={() => {
            setSelectedData({});
            setIsShareOpen(false);
            setShareLink("");
          }}
        />
      )}

      <InvoiceSendEmail
        contactId={0}
        options={[defaultConfig.employee_key, defaultConfig.contractor_key]}
        appUsers={true}
        recordId={Number(selectedData?.invoice_id)}
        isOpen={isSendEmailSidebarOpen}
        isViewAttachment={false}
        canWrite={false}
        emailData={{
          subject: "Shared Link",
          body: `A link to a record within Contractor Foreman has been shared with you. <a href="${shareLink}">View Details.</a>`,
        }}
        projectId={paramsData?.project_id}
        onSendResponse={() => {
          setSelectedData({});
          setShareLink("");
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
          setShareLink("");
        }}
        app_access={false}
      />

      <InvoiceFilePreview
        viewEmailPdf={viewEmailPdf}
        setViewEmailPdf={setViewEmailPdf}
        billedToData={billedToData}
        setBilledToData={setBilledToData}
        isDetailDropDown={isDetailDropDown}
        onActionComplete={onActionComplete}
      />

      {postPaymentToInvoice && (
        <PostPaymentToInvoice
          postPaymentToInvoice={postPaymentToInvoice}
          setPostPaymentToInvoice={setPostPaymentToInvoice}
          invoiceData={paramsData}
        />
      )}
    </>
  );
};
export default InvoiceListAction;
