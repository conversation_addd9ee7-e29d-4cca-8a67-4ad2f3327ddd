// ServiceTicket Dashboard
interface ISTDashState {
  restodayServices: IRestodayServices[];
  iteam_by_status: IIteam_by_status[];
  lastMonthStats: IStats;
  lastWeekStats: IStats;
  thisMonthStats: IStats;
  thisWeekStats: IStats;
  isDashLoading?: boolean;
  isDataFetched?: boolean;
  searchValue?: string;
  restodayServicesRefreshTime?: string;
  iteam_by_status_refresh_time?: string;
  monthlyWeeklyStatsRefreshTime?: string;
}

interface IRestodayServices {
  assignes_to: IAssignesTo[];
  service_ticket_id: number;
  service_time: string;
  title: string;
  priority_name: string;
  priority: string;
  service_techs: IAssignesTo[];
}

interface IIteam_by_status {
  no_of_count: string;
  sort_order: string;
  display_name: string;
}

interface IStats {
  Scheduled: INoCountScheduled[];
  Closed: INoCountClosed[][];
}

interface INoCountScheduled {
  no_of_count_Scheduled: string;
}

interface INoCountClosed {
  no_of_count_Closed: string;
}

interface ISTDashParams {
  refresh_type?: string;
}

interface ISTDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ISTDashState;
  refresh_type?: string;
}

interface ISTDashTodayServicesTableCellRenderer {
  data: Partial<IRestodayServices>;
}

interface ISTDashTicketsStatusTableCellRenderer {
  data: Partial<IIteam_by_status>;
}

// Invoice List

interface IServiceTicketsListProps {
  onIsDataLoading: (data: boolean) => void;
  search: string;
}

interface IServiceTicketListParmas {
  length?: number;
  page?: number;
  company_id?: number;
  user_id?: string | number;
  order_by_dir?: string;
  order_by_name?: string;
  search?: string;
  start: number;
  limit: number;
  is_own_data?: number;
  ignore_filter?: number;
  is_global_project?: number | string;
  filter?: any;
}

interface IServiceTicketData {
  assigned_to: any; // ! add array interface and remove "any"
  priority_name: string;
  contact_id: string;
  job_status_name: string;
  service_technicians: string;
  job_status_key: string;
  address_from: string;
  signature: string;
  project_name: string;
  project_id: number;
  service_duration: number;
  is_deleted: number;
  customer_contract: string;
  scheduled_by_name: string;
  service_ticket_id: number;
  customer_id: number;
  title: string;
  service_tech: string;
  service_techs: any;
  priority: string;
  job_status: string;
  service_fee: string;
  description: string;
  notes: string;
  location: string;
  cust_company: string;
  cust_street: string;
  cust_street2: string;
  cust_city: string;
  cust_state: string;
  cust_zip: string;
  cust_phone: string;
  cust_cell: string;
  cust_fax: string;
  cust_title: string;
  cust_email: string;
  cust_access_code: string;
  cust_notes: string;
  company_id: number;
  user_id: number;
  check_in_date: null;
  check_out_date: null;
  contact_id: number;
  billed_to_name: string;
  billed_to: number;
  billed_to_contact: number;
  billed_to_dir_type: number;
  customer_name: string;
  customer_name_only: string;
  dir_type: number;
  service_tech_name: string;
  service_tech_names: string;
  email_subject: string;
  counter_seconds: string;
  company_ticket_id: string;
  service_format_date: string;
  service_date_only: string;
  formatted_service_date: string;
  service_fulldate: string;
  service_time: string;
  service_end_time: string;
  date_added: string;
  kanban_date_modified: string;
  stcc_check_in_date: string;
  stcc_check_out_date: string;
  check_status: string;
  db_counter_seconds: string;
  counter_seconds?: string;
}

interface IAssignesTo {
  user_id: string;
  contact_id: string;
  user_contact_id: string;
  company_name: string;
  user_type: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assignee_company: string;
  email: string;
  image: string;
}

interface IServiceTicketApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IServiceTicketData[];
}

interface IServiceTicketTableCellRenderer {
  data: Partial<IServiceTicketData>;
}

interface IServiceTicketDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IServiceTicketDetails;
}
interface IServiceTicketNotesApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ICommonNote[];
  page?: number;
}

interface ISTServiceTechnicians {
  user_id?: number | any;
  directory_id?: number | any;
  display_name?: string;
  dir_type?: number | any;
  dir_type_name?: string;
  type_name?: string;
  image?: string;
}

interface IServiceTicketDetails {
  service_date_only?: string | undefined | null;
  service_fulldate?: string | undefined;
  service_date?: string | undefined | null;
  service_time?: string | undefined | null;
  service_end_time?: string | undefined | null;
  customer_contract?: string;
  company_ticket_id?: string;
  current_timecard_service_ticket?: string | number;
  count_in_contract_amount?: string | number;
  billed_to?: number | any;
  billed_to_contact?: number;
  billed_to_display_name?: string;
  custom_service_ticket_id?: string;
  service_duration?: number | any;
  notes?: string;
  title?: string;
  job_status?: number | any;
  job_status_name?: string;
  description?: string;
  priority?: number | any;
  priority_name?: string;
  project_id?: number | any;
  project_name?: string;
  service_tech?: number | any;
  service_techs?: string;
  customer_id?: number | any;
  customer_name?: string;
  cust_company?: string;
  cust_email?: string;
  scheduled_by_name?: string;
  check_in_date?: string;
  check_in_date_time_format?: string;
  check_in_time?: string;
  check_out_date?: string;
  check_out_date_time_format?: string;
  check_out_time?: string;
  check_status?: string;
  contact_id?: number;
  db_counter_seconds?: string;
  signature?: string;
  approval_date?: string;
  approved_by?: string;
  counter_seconds?: string;
  dir_type?: string | number;
  billed_to_dir_type?: string | number;
  cost_code_id?: string | number;
  cost_code_name?: string;
  assigned_to?: [
    {
      user_id?: number | any;
      contact_id?: string;
      user_contact_id?: string;
      company_name?: string;
      user_type?: string;
      assignee_name?: string;
      assigned_to_name_only?: string;
      assignee_company?: string;
      email?: string;
      image?: string;
    }
  ];
  customer_data?: [
    {
      image?: string;
      type_key?: string;
      parent_type_key?: string;
      email?: string;
      billed_to?: number;
      billed_to_display_name?: string;
      billed_to_image?: string;
      billed_to_type?: number;
      billed_to_type_key?: string;
      billed_to_type_name?: string;
      type_name?: string;
      billed_to_contact?: string | number;
      parent_type_key?: string;
      address1?: string;
      address2?: string;
      city?: string;
      state?: string;
      zip?: string;
    }
  ];
  service_technicians?: ISTServiceTechnicians[] | undefined;
  service_technicians_names?: string;
  timecard_mins?: string;
  timecard_hours?: string;
  timecard_employees?: [
    {
      employee_name?: string;
      image?: string;
    }
  ];
  service_ticket_id?: string | number;
  is_deleted?: number;
  user_id?: number;
  email_subject?: string;
  date_added?: string;
  time_added?: string;
  invoiced_to_data?: [
    {
      user_id: number;
      type_name: string;
      image?: string;
      type_key?: string;
      email?: string;
      parent_type_key?: key;
    }
  ];
  billed_to_image?: string;
}

interface IServiceInitialState {
  details: IServiceTicketDetails;
  notes_data: ICommonNote[];
  isDetailLoading: boolean;
  isNotesLoading: boolean;
  isDataLoading?: boolean;
  isNext?: boolean | undefined;
  page?: number | undefined;
}

interface ISTAddUpDetails {
  service_ticket_id?: number | string;
  projectId?: number | string;
  customerId?: number | string;
  serviceDate?: string;
  serviceTime?: string;
  serviceEndTime?: string;
  title?: string;
  customerContract?: string;
  serviceTech?: string;
  priority?: number | string;
  jobStatus?: number | string;
  serviceDuration?: number | string;
  description?: string;
  notes?: string;
  customServiceTicketId?: string;
  billedTo?: number | string;
}

interface ISTClockInParam {
  service_ticket_id?: string | number;
  check_in: number;
  check_pause: number;
  check_resume: number;
  check_out: number;
}

interface ICheckInTimeCardReq {
  service_ticket_id?: string | number;
  cost_code_id?: number;
  allow_service_tickets?: number;
  project_id?: string;
  type?: string;
  clockin?: number;
  clockinmultiple?: number;
  module_id?: number;
  tb_timecard_project?: string;
  tb_timecard_costcode?: number;
  user_id?: number;
  company_id?: number;
  timecard_start_time?: string;
  timecard_start_time_change?: number;
}
interface IUpdateTimeCardReq {
  cost_code_id?: number;
  action?: string;
  timecard_id?: string | number;
  service_ticket_id?: string | number;
  old_service_ticket_id?: string | number;
  allow_service_tickets?: number;
  project_id?: string;
  type?: string;
  is_dashboard_call?: number;
  user_id?: number;
  module_id?: number;
  company_id?: number;
}
interface IGetCurrentTimeCardReq {
  service_ticket_id?: string | number;
}

interface ISTCustomerInitialState {
  customerDetails: ISTCustomerTabDetails;
  isCustomerDetailLoading: boolean;
}

interface IServiceTicketCustomerApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ISTCustomerTabDetails;
}
interface ISTCustomerTabDetails {
  customer_id?: number | string;
  contact_id?: number | string;
  dir_type?: number | string;
  cust_email?: string;
  cust_name?: string;
  cust_company?: string;
  location?: string;
  cust_access_code?: string;
  address_from?: string;
  cust_address1?: string;
  cust_address2?: string;
  cust_street?: string;
  cust_street2?: string;
  cust_city?: string;
  cust_state?: string;
  cust_zip?: string;
  cust_notes?: string;
  customer_city?: string;
  customer_email?: string;
  customer_state?: string;
  customer_zip?: string;
  prj_address1?: string;
  prj_address2?: string;
  project_city?: string;
  project_state?: string;
  project_zip?: string;
  service_ticket_id?: number | string;
  customer_street?: string;
  customer_street2?: string;
  customer_city?: string;
  customer_state?: string;
  customer_zip?: string;
}
interface ISTCustomerTabReqDetails {
  customer_id?: number | string;
  customer_company?: string;
  location?: string;
  customer_access_code?: string;
  address_from?: string;
  customer_street?: string;
  customer_street2?: string;
  customer_city?: string;
  customer_state?: string;
  customer_zip?: string;
  customer_title?: string;
  customer_notes?: string;
  service_ticket_id?: number | string;
}

interface ISTItemInitialState {
  serviceItems: ISTItemDetails[];
  serviceItemCount: string | number;
  isItemDetailLoading: boolean;
}

interface IServiceTicketServiceApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    serviceItemData: ISTItemDetails[];
    serviceItemCount: string;
  };
}

interface ISTDeleteItemReqDetails {
  service_ticket_id?: string | number;
  item_id?: string[] | number[] | undefined;
}

interface ISTAddUpItemReqDetails {
  service_ticket_id?: string | number;
  is_single_item?: string | number;
  count_in_contract_amount?: string | number;
  items?: ISTItemReqData[];
}

interface ISTItemReqData {
  item_id?: string | number;
  service_ticket_id?: string | number;
  cost_code_id?: string | number;
  cost_code?: string | number;
  subject?: string;
  quantity?: string | number;
  is_deleted?: string | number;
  unit?: string | number;
  unit_cost?: string | number;
  markup?: string | number;
  description?: string;
  total?: string | number;
  service_ticket_item_no?: string | number;
  item_type?: string | number;
  reference_item_id?: string | number;
  parent_item_id?: string | number;
  is_markup_percentage?: string | number;
  markup_amount?: string | number;
  internal_notes?: string;
  item_on_database?: string | number;
  item_type_display_name?: string;
  item_type_name?: string;
  item_type_key?: string;
  assignee_name?: string;
  assigned_to_name_only?: string;
  cost_code_name?: string;
  tax_rate?: string;
  import_item_type?: string;
}
interface ISTItemDetails {
  assigned_to?: string | number;
  assigned_to_dir_type: number;
  assigned_to_contact_id?: string | number;
  assigned_to_name_only?: string;
  assigned_to_display_name?: string;
  user_image?: string;
  csi_code?: string;
  assignee_name?: string;
  cost_code?: string | number;
  cost_code_id?: string | number;
  cost_code_name?: string;
  description?: string;
  cost_code_is_deleted?: number;
  internal_notes?: string;
  internalNotes?: string;
  is_deleted?: number | string;
  is_markup_percentage?: number | string;
  item_id?: string | number;
  item_type?: number | string;
  item_type_key?: string;
  item_type_name?: string;
  dir_type?: string;
  markup?: number | string;
  markup_amount?: string | number;
  quantity?: number | string;
  reference_item_id?: number | string;
  subject?: string;
  total?: string | number;
  total_service_item?: string;
  unit_cost?: string | number;
  unit?: string;
  reference_module_id?: string;
  service_ticket_item_no?: string | number;
  reference_module_item_id?: number | string;
  item_on_database?: number | string;
  add_item_to_database?: number | string;
  original_code_id?: string | number;
  parent_id?: string | number;
  parent_code_id?: string | number;
  is_managed_level?: string | number;
  has_no_child?: string | number;
  assignee_type?: string;
}

interface ISTBillingInitialState {
  invoiceList: ISTBillingInvoiceDetails[];
  paymentList: ISTBillingPaymentDetails[];
  invoiceCount: string | number;
  paymentCount: string | number;
  isBillingDetailLoading: boolean;
}

interface IServiceTicketBillingInvoiceApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {
    invoiceData: ISTBillingInvoiceDetails[];
    invoiceCount: string;
  };
}

interface ISTBillingInvoiceDetails {
  company_invoice_id: string;
  invoice_id: number | string;
  total: string;
  customer_name: string;
  description: string;
  invoice_date: string;
  date_added: string;
  approval_type_name: string;
  is_advance_invoice: number | string;
  due_date: string;
  status: string;
  due_balance: string;
  paid_amount: string;
  total_invoice: string;
  status_color?: string;
}

interface IServiceTicketBillingPaymentApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {
    paymentData: ISTBillingPaymentDetails[];
    paymentCount: string;
  };
}

interface ISTBillingPaymentDetails {
  company_invoice_id: string;
  payment_type_name: string;
  payment_type: number | string;
  payment_status: number | string;
  payment_status_name: string;
  amount: string;
  invoice_id: number | string;
  payment_id: number | string;
  payment_notes: string[];
  payment_date: string;
  total_payment: string;
  status?: string;
}

interface ISTDocumentInitialState {
  documentHistoryList: ISTDocumentHistoryDetails[];
  timeCardList: ISTDocumentTimeCardDetails[];
  documentHistoryCount: string | number;
  timeCardCount: string | number;
  isDocumentDetailLoading: boolean;
}

interface IServiceTicketDocumentHistoryApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {
    service_ticket_history: ISTDocumentHistoryDetails[];
    serviceHistoryCount: string;
  };
}

interface ISTDocumentHistoryDetails {
  service_ticket_id: number | string;
  service_date: string;
  service_time: string;
  service_end_time: string;
  customer_id: number | string;
  project_id: number | string;
  title: string;
  service_tech: number | string;
  service_techs: string;
  service_tech_image: string;
  priority: number | string;
  job_status: number | string;
  service_fee: string;
  description: string;
  notes: string;
  location: string;
  cust_company: string;
  cust_street: string;
  cust_street2: string;
  cust_city: string;
  cust_state: string;
  cust_zip: string;
  cust_phone: string;
  cust_cell: string;
  cust_fax: string;
  cust_title: string;
  cust_email: string;
  cust_access_code: string;
  cust_notes: string;
  company_id: number | string;
  user_id: number | string;
  parent_ticket_id: number | string;
  demo_data: number | string;
  date_added: string;
  date_modified: string;
  company_ticket_id: number | string;
  check_in_date: string;
  check_out_date: string;
  total: number | string;
  signature: string;
  service_duration: number | string;
  contact_id: number | string;
  custom_service_ticket_id: number | string;
  billed_to: number | string;
  address_from: string;
  latitude: string;
  longitude: string;
  is_deleted: number | string;
  is_notes_convert: number | string;
  billed_to_contact: number | string;
  customer_contract: string;
  count_in_contract_amount: number | string;
  is_send_customer_email: number | string;
  service_tech_names: string;
  service_tech_name: string;
  job_on_time: string;
  service_format_date: string;
  service_date_only: string;
  service_fulldate: string;
  time_added: string;
  check_in_time: string;
  check_out_time: string;
  total_ticket_history: string;
}

interface IServiceTicketDocumentTimeCardApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: {
    service_timecard: ISTDocumentTimeCardDetails[];
    serviceTimecardCount: string;
  };
}

interface ISTDocumentTimeCardDetails {
  timecard_id: number | string;
  employee_name: string;
  employee_image: string;
  cost_code_name: string;
  cost_code_csi_code: string;
  code_id: string;
  clock_in_date: string;
  hours_worked: string;
  total_timecards: string;
}

interface IAddSTFilePhotoFrm {
  primary_id: number;
  module_id: number;
  module_key: string;
  attach_image?: string;
  files?: IFile[];
  project_id?: string;
}

interface IAddSTFilePhotoRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}

interface IServiceTicketItemApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: any;
}

interface ISTKanbanListParams {
  limit?: number;
  start?: number;
  search?: string;
  is_kanban?: number;
  is_own_data?: string | number;
  any_pagination_status?: string | number;
  user_id?: number | string;
  company_id?: number | string;
  ignore_filter?: number | string;
  is_global_project?: number | string;
  filter?: ISTTempFil;
}

interface ISTKanbanApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IKanbanColumnDataList[];
  kanban_estimate_type_selected: string[];
  kanban_setting: IKanbanSetting;
}

interface ISTKanbanRowDataList {
  billed_to: number;
  billed_to_contact: number;
  company_ticket_id: string;
  contact_id: string;
  customer_contract: string;
  customer_id: number;
  customer_name: string;
  date_added: string;
  description: string;
  email_subject: string;
  is_deleted: number;
  job_status: number;
  job_status_key: string;
  job_status_name: string;
  kanban_date_modified: string;
  notes: string;
  priority: number;
  priority_name: string;
  project_id: number;
  project_name: string;
  service_date_only: string;
  service_format_date: string;
  service_fulldate: string;
  service_tech: number;
  service_tech_name: string;
  service_techs: string;
  service_ticket_id: number;
  service_time: string;
  signature: string;
  time_added: string;
  email_subject?: string;
  title: string;
}

interface IKanbanColumnDataList {
  sort_order: string;
  status_color: string;
  item_id: number;
  key: string;
  is_custom_item: string;
  name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  does_sync_qb: number;
  orig_name: string;
  is_status: string;
  sorting_id: number;
  type: string;
  show_in_progress_bar: number;
  is_collapse_card: number;
  total_count: string;
  kanban_data: ISTKanbanRowDataList[];
  itemId: number;
}

interface IKanbanSetting {
  kanban_id: number;
  company_id: number;
  user_id: number;
  module_id: number;
  module_field_id: string[];
  default_view: number | string;
  co_type: string;
  date_modified: string;
  default_view_back: number;
}
interface IKanbanSTDetail {
  service_ticket_id: number | string;
  status: number;
  priority: number;
  task_name: string;
  project_name: string;
  assigned_to: string;
  status_key: string;
  due_date: string;
  assignee: string;
  user_profile_image: string;
  chosen: boolean;
  selected: boolean;
  is_check_list: string;
  is_deleted: string | number;
  user_id: string | number;
}

interface IKanbanSorting {
  module_id: number;
  kanban_sorting: IKanbanSortingArray[] | string;
}

interface IKanbanSortingArray {
  column_id: number | string;
  sort_order: number | string;
  sorting_id: number | string;
  column_name: number | string;
  type_id: number | string;
}

interface ISTTempFil {
  project?: string;
  project_names?: string;
  employee?: number[];
  employee_names?: string;
  priority?: string;
  priority_names?: string;
  job_status?: string;
  job_status_names?: string;
  job_status_kanban_names?: string;
  job_status_kanban?: string;
  assignee?: string;
  user_id?: number;
  is_my_ST?: string | number;
  start_date?: string;
  end_date?: string;
  is_own_data?: number | string;
  response_status?: number;
  status?: string;
  tab?: string;
  is_my_todo?: string | number;
  sort_due_date?: string | number;
}

interface ISTNotificationApiRes extends Omit<IDefaultAPIRes, "data"> {
  data?: any;
}
interface ISTCreateModifyApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: { id?: number | string };
}
interface ISTReminderApiRes extends Omit<IDefaultAPIRes, "data"> {
  data?: any;
}
interface ISericeTicketRes extends IEServiceTicketAPIRes {
  data: ObjType;
}
