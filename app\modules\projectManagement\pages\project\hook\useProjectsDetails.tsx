import { useEffect, useMemo, useState } from "react";
import delay from "lodash/delay";
import { useAppProDispatch, useAppProSelector } from "../redux/store";
import { useTranslation } from "~/hook";
import { updateProjectApi } from "../redux/action/proDashAction";
import {
  setLoadingStatus,
  updateProjectDetail,
} from "../redux/slices/proDetailsSlice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

export const useProjectDetail = () => {
  const { details, loadingStatus }: IProjectDetailsInitialState =
    useAppProSelector((state) => state.proDetails);
  const { checkGlobalModulePermissionByKey } = useGlobalModule();

  const currentModule = getCurrentMenuModule();
  const { module_key = "" } = currentModule || {};

  const [inputVals, setInputVals] = useState<Partial<IProjectDetails>>(
    details ?? {}
  );

  const { _t } = useTranslation();

  const dispatch = useAppProDispatch();

  useEffect(() => {
    setInputVals(details);
  }, [details]);

  const isReadOnly = useMemo(
    () => checkGlobalModulePermissionByKey(module_key) === "read_only",
    [module_key]
  );

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      dispatch(setLoadingStatus({ field, status, action }));
    }
  };

  const updateDataInStore = (dataToUpdate: Partial<IProjectDetails>) => {
    dispatch(updateProjectDetail(dataToUpdate));
  };

  const handleUpdateField = async ({
    data_to_send_in_api,
    data_to_update_in_store = {},
    successCallback,
  }: IUpdateProjectField) => {
    const fields = Object.keys(data_to_send_in_api) as Array<
      Partial<keyof IProjectDetails>
    >;

    fields.forEach((f) => {
      handleChangeFieldStatus({
        field: f,
        status: "loading",
        action: "API",
      });
    });

    updateDataInStore({ ...data_to_send_in_api, ...data_to_update_in_store });

    try {
      const updateRes = (await updateProjectApi({
        ...data_to_send_in_api,
        module_key: module_key,
        id: details.id?.toString(),
      })) as IProjectDetailUpdateApiRes;

      if (updateRes?.success) {
        fields.forEach((f) => {
          handleChangeFieldStatus({
            field: f,
            status: "success",
            action: "API",
          });
        });

        successCallback?.(fields, updateRes);
      } else {
        updateDataInStore(details);
        notification.error({
          description: updateRes?.message || "Failed to update field",
        });
      }
    } catch (error) {
      updateDataInStore(details);
      notification.error({
        description: (error as Error)?.message || "Failed to update field",
      });
    }

    delay(() => {
      fields.forEach((f) => {
        handleChangeFieldStatus({
          field: f,
          status: "button",
          action: "API",
        });
      });
    }, 2000);
  };

  const onFocusUpdateFieldStatus = async ({ field }: Partial<IFieldStatus>) => {
    handleChangeFieldStatus({
      field,
      status: "save",
      action: "FOCUS",
    });
  };

  const onMouseEnterUpdateFieldStatus = async ({
    field,
  }: Partial<IFieldStatus>) => {
    handleChangeFieldStatus({
      field,
      status: "edit",
      action: "ME",
    });
  };

  const onMouseLeaveUpdateFieldStatus = async ({
    field,
  }: Partial<IFieldStatus>) => {
    handleChangeFieldStatus({
      field,
      status: "button",
      action: "ML",
    });
  };

  const onBlurUpdateFieldStatus = async ({ field }: Partial<IFieldStatus>) => {
    handleChangeFieldStatus({
      field,
      status: "button",
      action: "BLUR",
    });
  };

  const updateInputFieldOnBlur = ({
    value,
    field,
    message = "",
    required = false,
  }: {
    field: string;
    value: string | number | undefined | null | "";
    message?: string;
    required?: boolean;
  }) => {
    const oldValue = details?.[field as keyof IProjectDetails];
    if (value === "") {
      updateDataInStore({
        [field]: required ? oldValue : "",
      });

      if (required) {
        notification.error({
          description: _t(message),
        });
        return;
      }
    }

    if (value != oldValue) {
      handleUpdateField({
        data_to_send_in_api: {
          [field]: value,
        },
        data_to_update_in_store: {
          [field]: value,
        },
      });
    } else {
      onBlurUpdateFieldStatus({ field });
    }
  };

  const onChangeInputField = ({
    value,
    field,
  }: {
    field: string;
    value: string | number | undefined | "";
  }) => {
    setInputVals({ ...inputVals, [field]: value });
  };

  const onlyIntegerRegex = /^-?\d+$/; // regex to check only integer

  return {
    loadingStatus,
    setLoadingStatus,
    handleUpdateField,
    handleChangeFieldStatus,
    onFocusUpdateFieldStatus,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    onBlurUpdateFieldStatus,
    updateInputFieldOnBlur,
    onChangeInputField,
    inputVals,
    setInputVals,
    isReadOnly,
    updateDataInStore,
    onlyIntegerRegex,
  };
};
