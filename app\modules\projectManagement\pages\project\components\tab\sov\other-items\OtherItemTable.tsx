// React
import { memo, useCallback, useEffect, useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";

// atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { useAppProDispatch, useAppProSelector } from "../../../../redux/store";
import { Number } from "~/helpers/helper";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import {
  getUpdatedSovData,
  updateBudgetItems,
} from "../../../../redux/slices/proSovSlice";
import { deleteSOVItem } from "../../../../redux/action/projectSovAction";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { AddProjectItem } from "../../sidebar";
import { getGlobalInvoicedRemainFormat } from "../../../utils/common-util";
import ItemTableRender from "./ItemTableRender";

const OtherItemTable: React.FC<TOtherItemsTableProps> = ({
  callSummaryApi,
  callProjectSOVItemsApi
}) => {
  const { _t } = useTranslation();

  const { details } = useAppProSelector((state) => state.proDetails);
  const { sov_summary } = useAppProSelector((state) => state.proSov);

  const [deleteItemId, setDeleteItemId] = useState<number>(0);
  const [editProjectItem, setEditProjectItem] = useState<boolean>(false);
  const [deleteItemLoading, setDeleteItemLoading] = useState<boolean>(false);
  const [itemToEdit, setItemToEdit] = useState<
    Partial<ISOVBudgetItemsData> | undefined
  >();

  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppProDispatch();

  const getUpdatedSOVItems = () => {
    const updatedSovData = dispatch(getUpdatedSovData());
    return updatedSovData.projectSOVItems.budget_items;
  };

  const budget_items = getUpdatedSOVItems();

  const isPercentageBilling = useMemo(
    () => details.billing_option === "percentage",
    [details.billing_option]
  );

  const otherItems = useMemo(() => {
    const itms = budget_items
      ?.slice()
      .sort((a, b) => a.item_no - b.item_no)
      ?.filter(
        (i) =>
          !(
            Number(i.estimate_id) > 0 ||
            Number(i.est_item_section_id) > 0 ||
            Number(i.reference_item_id) > 0 ||
            Number(i.change_order_id) > 0 ||
            Number(i.work_order_id) > 0
          )
      );
    return itms;
  }, [budget_items]);

  useEffect(() => {
    if (itemToEdit && itemToEdit.item_id) {
      const updatedItem = otherItems.find(
        (i) => Number(i.item_id) === Number(itemToEdit.item_id)
      );

      if (
        updatedItem &&
        JSON.stringify(updatedItem) !== JSON.stringify(itemToEdit)
      ) {
        setItemToEdit(updatedItem);
      }

      if (!updatedItem) {
        setItemToEdit(undefined);
      }
    }
  }, [otherItems, itemToEdit]);

  const deleteItem = useCallback(
    async (item_id: number) => {
      setDeleteItemLoading(true);

      const deleteRes = await deleteSOVItem({
        paramsData: { id: details.id?.toString() || "" },
        formData: { item_id: item_id?.toString() },
      });

      if (deleteRes.success) {
        callSummaryApi();
        dispatch(
          updateBudgetItems({
            item_id,
            updatedItem: null,
          })
        );
        setDeleteItemId(0);
      } else {
        notification.error({
          description: deleteRes?.message || "Failed to delete item",
        });
      }
      setDeleteItemLoading(false);
    },
    [details.id]
  );

  const summary = useMemo(() => {
    const totalBilled =
      Number(sov_summary?.project_item?.total_billed || 0) / 100;

    const totalRemain =
      Number(sov_summary?.project_item?.total_remain || 0) / 100;

    const totalProfit =
      Number(sov_summary?.project_item?.total_profit || 0) / 100;

    const subtotal = Number(sov_summary?.project_item?.sub_total || 0) / 100;

    const totalInvoicedWithCur = formatter(
      totalBilled !== 0 ? totalBilled?.toFixed(2) : "0.00"
    ).value_with_symbol;

    const totalRemainingWithCur = formatter(
      totalRemain !== 0 ? totalRemain?.toFixed(2) : "0.00"
    ).value_with_symbol;

    const subTotalWithCur = formatter(
      subtotal !== 0 ? subtotal?.toFixed(2) : "0.00"
    ).value_with_symbol;

    const totalProfitWithCur = formatter(
      totalProfit !== 0 ? totalProfit?.toFixed(2) : "0.00"
    ).value_with_symbol;

    const invoicedP =
      subtotal !== 0 ? Math.round((totalBilled * 100) / subtotal) : 0;

    const remainP =
      subtotal !== 0 ? Math.round((totalRemain * 100) / subtotal) : 0;

    const profitP = Math.round(
      Number(sov_summary?.project_item?.profit_percentage)
    );

    const invoicedU = sov_summary?.project_item?.total_billed_unit;
    const remainU = sov_summary?.project_item?.total_remained_unit;

    return {
      totalInvoicedWithCur,
      totalRemainingWithCur,
      totalProfitWithCur,
      subTotalWithCur,
      invoicedP,
      remainP,
      invoicedU,
      remainU,
      profitP,
    };
  }, [sov_summary?.project_item]);

  const computeTotalRow = useCallback(() => {
    return {
      markup: "Total",
      invoiced: summary.totalInvoicedWithCur,
      remain: summary.totalRemainingWithCur,
      total: summary.subTotalWithCur,
      isTotalRow: true,
    };
  }, [summary]);

  const handleExpandClick = (val: string[]) => {
    setIsExpanded(val.length > 0);
  };

  const sectionTotalData = useMemo(() => {
    let sectionTotalWithMarkup = 0;
    let sectionTotal = 0;

    otherItems?.forEach((item) => {
      sectionTotalWithMarkup += Number(item.total);
      sectionTotal += (Number(item.unit_cost) / 100) * Number(item.quantity);
    });

    const profit = Number(sectionTotalWithMarkup) / 100 - sectionTotal;

    const profitPercentage =
      sectionTotal === 0 ? 0 : (100 * profit) / sectionTotal;

    return {
      profitPercentage,
    };
  }, [otherItems]);

  return (
    <>
      <CollapseSingleTable
        title={_t("Other Items")}
        className="sov-proj-nowrap"
        onChange={handleExpandClick}
        leftsideContant={
          <div className="flex items-center gap-1.5 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap hide-collapse">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-duotone fa-solid fa-money-check-dollar"
            />
            <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
              {summary.subTotalWithCur} (
              {sectionTotalData?.profitPercentage
                ? Math.round(sectionTotalData?.profitPercentage)
                : 0}
              %)
            </Typography>
          </div>
        }
        leftContentClass="text-nowrap"
        rightsideContant={
          <div className="flex items-center gap-2 flex-wrap">
            <div className="py-[3px] px-2.5 border border-solid rounded border-[#008000] bg-white">
              <Typography className="text-[#008000] font-semibold text-13">
                {_t("Invoiced") + ": "}
                {getGlobalInvoicedRemainFormat(
                  isPercentageBilling,
                  summary.totalInvoicedWithCur,
                  summary.invoicedP,
                  summary.invoicedU
                )}
              </Typography>
            </div>
            <FontAwesomeIcon
              className="w-[13px] h-[13px] text-primary-900 font-semibold dark:text-white/90"
              icon="fa-regular fa-plus"
            />
            <div className="py-[3px] px-2.5 border border-solid rounded border-deep-orange-500 bg-white">
              <Typography className="text-deep-orange-500 font-semibold text-13">
                {_t("Remain") + ": "}
                {getGlobalInvoicedRemainFormat(
                  isPercentageBilling,
                  summary.totalRemainingWithCur,
                  summary.remainP,
                  summary.remainU
                )}
              </Typography>
            </div>
            <FontAwesomeIcon
              className="w-[13px] h-[13px] text-primary-900 font-semibold dark:text-white/90"
              icon="fa-regular fa-equals"
            />
            <div className="py-[3px] px-2.5 border border-solid rounded border-primary-900 bg-white">
              <Typography className="text-primary-900 font-semibold text-13">
                {_t("Sub Total") + ": "}
                {summary.subTotalWithCur}
              </Typography>
            </div>
            <div className="pl-4 border-l border-primary-900/25 sm:ml-2">
              <div className="bg-[#00800024] py-1 px-2.5 rounded-sm flex items-center gap-1 flex-wrap">
                <Typography className="text-green-700 text-13 font-semibold">
                  {_t("Est. Profit")} ({summary.profitP}%):
                </Typography>
                <Typography className="text-green-700 text-13 font-semibold">
                  {summary.totalProfitWithCur}
                </Typography>
              </div>
            </div>
          </div>
        }
        children={
          <ul className="grid gap-3">
            <li className="relative from-primary-500">
              {/* <div className="flex item-center gap-2">
                <div className="flex items-center gap-1.5 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap ml-auto">
                  <FontAwesomeIcon
                    className="w-4 h-4"
                    icon="fa-duotone fa-money-check-dollar"
                  />

                  <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
                    {`${summary.subTotalWithCur} (${summary?.profitP}%)`}
                  </Typography>
                </div>
              </div> */}
              <div
                className={`transition-all duration-300 ease-in-out ${
                  isExpanded ? "block" : "hidden"
                }`}
              >
                <div className="p-2 common-card">
                  <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px]">
                    <ItemTableRender
                      details={details}
                      otherItems={otherItems}
                      callSummaryApi={callSummaryApi}
                      isPercentageBilling={isPercentageBilling}
                      setEditProjectItem={setEditProjectItem}
                      setItemToEdit={setItemToEdit}
                      setDeleteItemId={setDeleteItemId}
                      gridKey={`OT-${otherItems?.[0]?.section_id || 1}`}
                      computeTotalRow={computeTotalRow}
                    />
                  </div>
                </div>
              </div>
            </li>
          </ul>
        }
      />

      {Boolean(deleteItemId) && (
        <ConfirmModal
          isOpen={Boolean(deleteItemId)}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          isLoading={deleteItemLoading}
          modalIcon="fa-regular fa-trash-can"
          onAccept={() => {
            deleteItem(deleteItemId);
          }}
          onDecline={() => setDeleteItemId(0)}
          onCloseModal={() => setDeleteItemId(0)}
        />
      )}

      {editProjectItem && (
        <AddProjectItem
          setAddProjectItem={setEditProjectItem}
          addProjectItem={editProjectItem}
          itemToEdit={itemToEdit}
          items={otherItems}
          setItemToEdit={setItemToEdit}
          callSummaryApi={callSummaryApi}
          callProjectSOVItemsApi={callProjectSOVItemsApi}
        />
      )}
    </>
  );
};

export default memo(OtherItemTable);
