import { useTranslation } from "~/hook";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
// Other
import { getGConfig, getGModuleByKey, useGModules } from "~/zustand";
import { defaultConfig } from "~/data";
import { useMemo } from "react";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

export const BillDetailActionsTab = ({
  buttonClass,
  iconClassName,
  onClick = () => {},
  isArchive,
  dueBalance,
}: IBillTableActionsProps) => {
  const getModuleName = (configKey: string, defaultName: string) => {
    const { module_name } = (getGModuleByKey(configKey) as GModule) || {};
    return module_name || defaultName;
  };
    const gConfig: GConfig = getGConfig();
  const { module_name: invoiceModuleName = "Invoice" } =
    (getGModuleByKey(defaultConfig.invoice_merge_module_key) as GModule) || {};
  const billModuleName = getModuleName(CFConfig.bill_module, "Bill");
  const paymentModuleName = getModuleName(CFConfig.payment_module, "Payment");
  const expenseModuleName = getGModuleByKey(CFConfig.expense_module) as GModule;

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { name } = currentModule || {};

  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();
  const user: IInitialGlobalData["user"] = getGlobalUser();

  const paymentModuleNoAccess = useMemo(
    () =>
      checkModuleAccessByKey(defaultConfig.payment_module) === "read_only" ||
      checkModuleAccessByKey(defaultConfig.payment_module) === "no_access",
    []
  );
  const hasInvoiceAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(
      defaultConfig.invoice_merge_module_key
    );
    return mAccess === "no_access" || mAccess === "read_only";
  }, []);
  const BillViewListTableOptions = [
    {
      label: _t("View/Email PDF"),
      icon: "fa-regular fa-file-pdf",
      onClick: () => onClick("view_pdf"),
    },

    {
      label: _t(`Generate an ${invoiceModuleName}`),
      icon: "fa-regular fa-file-invoice-dollar",
      onClick: () => onClick("Generate an Invoice"),
      disabled: hasInvoiceAccessShowMessages,
    },

    {
      label: _t(`Post ${paymentModuleName}`),
      icon: "fa-regular fa-envelope-open-dollar",
      onClick: () => onClick("Post Payment"),
      postPayment: true,
      disabled: paymentModuleNoAccess,
    },

    {
      label: _t(`Copy ${billModuleName}`),
      icon: "fa-regular fa-clone",
      onClick: () => onClick("Copy bill"),
    },

    {
      label: _t(`${name} vs ${expenseModuleName.plural_name}`),
      icon: "fa-regular fa-video",
      onClick: () => {
        window.open("https://vimeo.com/399761037", "_blank");
      },
    },

    {
      content: _t("Share Internal Link"),
      icon: "fa-regular fa-share-nodes",
      onClick: () => onClick("share_internal_link"),
      onlyIconView: true,
    },

    {
      label: isArchive
        ? _t("Archive this item")
        : _t("Change Status to Active"),
      icon: isArchive
        ? "fa-regular fa-box-archive"
        : "fa-regular fa-regular-active",
      content: isArchive ? (
        <>
          Status: Active <br />
          Click to Archive the item
        </>
      ) : (
        <>
          Status: Archived <br />
          Click to Activate the item
        </>
      ),
      onClick: () => onClick("change_status"),
      onlyIconView: true,
    },

    {
      content: "Delete",
      icon: "fa-regular fa-trash-can",
      disabled: gConfig?.page_is_iframe,
      onClick: () => onClick("delete"),
      onlyIconView: true,
    },
  ];
  const optionwithoutPayment = (BillViewListTableOptions || [])?.filter(
    (ele) => !ele?.postPayment
  );

  const hasDueBalance = Number(dueBalance) / 100 > 0;
  const options = hasDueBalance
    ? BillViewListTableOptions
    : optionwithoutPayment;
  return (
    <>
      <DropdownMenu
        options={options}
        buttonClass={buttonClass}
        iconClassName={iconClassName}
        icon="fa-regular fa-ellipsis-vertical"
        {...((paymentModuleNoAccess ||
          hasInvoiceAccessShowMessages ||
          user?.allow_delete_module_items === "0") && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />
    </>
  );
};
