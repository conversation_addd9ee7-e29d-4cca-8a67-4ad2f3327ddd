// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
// Other
import CostByType from "./sov/CostByType";
import InvoiceSummary from "./sov/InvoiceSummary";
import SovTopBar from "./sov/SovTopBar";
import ApproveEstimateItemTable from "./sov/ApproveEstimateItemTable";
import CoItemTable from "./sov/CoItemTable";
import WorkOrderItemTable from "./sov/WorkOrderItemTable";
import OtherItemTable from "./sov/other-items/OtherItemTable";
import TaxCalculationCard from "./sov/TaxCalculationCard";
import { useTranslation } from "~/hook";
import {
  AddProjectItem,
  BudgetItem,
  ImportItemsFromCo,
  ImportItemsFromEstimates,
  ImportItemsFromWo,
} from "./sidebar";
import { useMemo, useCallback, useState, useTransition } from "react";
import { useParams } from "@remix-run/react";
import { BUDGET_ITEM } from "../../utils/constants";
import {
  deleteSOVItem,
  fetchProjectSOVItemsApi,
  fetchSovSummaryApi,
  updateProjectItemTodb,
} from "../../redux/action/projectSovAction";
import { useAppProDispatch, useAppProSelector } from "../../redux/store";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { Number } from "~/helpers/helper";
import ImportProjectSOVtemsCsv from "./modal/importProjectSOVItems/ImportProjectSOVtemsCsv";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { updateSOVItems } from "../../redux/slices/proSovSlice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";

const SovTab = () => {
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const [importEstItem, setImportEstItem] = useState<boolean>(false);
  const [importCoItem, setImportCoItem] = useState<boolean>(false);
  const [importWoItem, setImportWoItem] = useState<boolean>(false);
  const [addProjectItem, setAddProjectItem] = useState<boolean>(false);
  const [budgetItem, setBudgetItem] = useState<boolean>(false);
  const [isCostDbItemOpen, setIsCostDbItemOpen] = useState<boolean>(false);
  const [SOVItemsToBeView, setSOVItemsToBeView] =
    useState<ISOVBudgetItemsData>(BUDGET_ITEM);
  const [isImpFromCsv, setIsImpFromCsv] = useState<boolean>(false);
  const moduleEst: IModule | undefined = getGlobalModuleByKey(
    CFConfig.estimate_module
  );
  const moduleCo: IModule | undefined = getGlobalModuleByKey(
    CFConfig.change_order_module
  );
  const moduleWo: IModule | undefined = getGlobalModuleByKey(
    CFConfig.work_order_module
  );
  // delete item
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [itemToBeDelete, setItemToBeDelete] = useState<number>();
  const [deleteRecordLoading, setDeleteRecordLoading] =
    useState<boolean>(false);

  const [_isPending, startTransition] = useTransition();

  const { isSOVItemsLoading } = useAppProSelector((state) => state.proSov);
  const currentModule = getCurrentMenuModule();
  const { module_access = "" } = currentModule || {};

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );
  const dispatch = useAppProDispatch();

  const gSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const {
    default_equipment_markup_percent = "",
    default_labor_markup_percent,
    default_material_markup_percent,
    default_other_item_markup_percent,
    default_sub_contractor_markup_percent,
    is_taxable_material_items,
    is_taxable_equipment_items,
    is_taxable_labor_items,
    is_taxable_subcontractor_items,
    is_taxable_other_items,
  } = gSettings || {};

  const { itemTypes } = useAppProSelector((state) => state.projectItemTypes);
  const { details } = useAppProSelector((state) => state.proDetails);
  const { projectSOVItems, current_filter_by } = useAppProSelector(
    (state) => state.proSov
  );

  const SOVItems = useMemo(() => {
    let workorderItems: ISOVBudgetItemsData[] = [];
    let estimateItems: ISOVBudgetItemsData[] = [];
    let changeOrderItems: ISOVBudgetItemsData[] = [];
    let otherItems: ISOVBudgetItemsData[] = [];

    projectSOVItems.budget_items.map((item) => {
      if (
        item.estimate_id > 0 &&
        item.est_item_section_id > 0 &&
        item.reference_item_id > 0
      ) {
        estimateItems.push(item);
      } else if (item.work_order_id && item.work_order_id !== 0) {
        workorderItems.push(item);
      } else if (item.change_order_id > 0) {
        changeOrderItems.push(item);
      } else {
        otherItems.push(item);
      }
    });

    return {
      approvedEstimateItems: estimateItems,
      cidbItems: otherItems.filter((item) => item.reference_item_type_id > 0),
    };
  }, [projectSOVItems]);

  const addItemToSOVOptions = [
    {
      label: "Add Item to SOV",
      value: "add_sov",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "import_cidb",
      onClick: () => {
        setIsCostDbItemOpen(true);
      },
    },
    {
      label: `Import from ${moduleEst?.module_name}`,
      value: "import_est",
      onClick: () => {
        setImportEstItem(true);
      },
    },
    {
      label: `Import from ${moduleCo?.module_name}`,
      value: "import_co",
      onClick: () => {
        setImportCoItem(true);
      },
    },
    {
      label: `Import from ${moduleWo?.module_name}`,
      value: "import_wo",
      onClick: () => {
        setImportWoItem(true);
      },
    },
    {
      label: "Add Manual Project Item",
      value: "add_manual_pro_item",
      onClick: () => {
        setAddProjectItem(true);
      },
    },
    {
      label: "Import from CSV",
      value: "import_csv",
      onClick: () => {
        setIsImpFromCsv(true);
      },
    },
  ];

  const callSummaryApi = useCallback(async () => {
    await dispatch(fetchSovSummaryApi({ project_id: Number(id) })).unwrap();
  }, [id]);

  const callProjectSOVItemsApi = useCallback(async () => {
    await dispatch(
      fetchProjectSOVItemsApi({
        id: Number(id),
        module_key: ["budget_items"],
      })
    ).unwrap();
  }, [id]);

  const costItemsFromDatabase = useMemo(() => {
    const data = SOVItems.cidbItems
      ?.filter(
        (item: ISOVBudgetItemsData) => Number(item?.reference_item_type_id) > 0
      )
      ?.map((item: ISOVBudgetItemsData) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_type_id,
          item_id: item?.item_id as unknown as string,
        };
        if (item?.item_type_key === "item_material") {
          temData.material_id = item?.reference_item_type_id;
          temData.item_type = "161";
          temData.type_name = "Material";
        } else if (item?.item_type_key === "item_labour") {
          temData.labor_id = item?.reference_item_type_id;
          temData.item_type = "163";
          temData.type_name = "Labor";
        } else if (item?.item_type_key === "item_sub_contractor") {
          temData.contractor_id = item?.reference_item_type_id;
          temData.item_type = "164";
          temData.type_name = "Subcontractor";
        } else if (item?.item_type_key === "item_equipment") {
          temData.equipment_id = item?.reference_item_type_id;
          temData.item_type = "162";
          temData.type_name = "Equipment";
        } else if (item?.item_type_key === "item_other") {
          temData.other_item_id = item?.reference_item_type_id;
          temData.item_type = "165";
          temData.type_name = "Other Items";
        }
        return temData as Partial<CIDBItemSideData>;
      });
    return data;
  }, [JSON.stringify(SOVItems)]);

  const handleItemFromCostItemDatabase = async (data: CIDBItemSideData[]) => {
    const itemsToBeAdd = data
      .filter(
        (jobItem) =>
          !(SOVItems.cidbItems || []).some((item) => {
            if (jobItem?.type_name === "Material") {
              return (
                Number(item.reference_item_type_id) ===
                Number(jobItem.material_id)
              );
            } else if (jobItem?.type_name === "Labor") {
              return (
                Number(item.reference_item_type_id) === Number(jobItem.labor_id)
              );
            } else if (jobItem?.type_name === "Subcontractor") {
              return (
                Number(item.reference_item_type_id) ===
                Number(jobItem.contractor_id)
              );
            } else if (jobItem?.type_name === "Equipment") {
              return (
                Number(item.reference_item_type_id) ===
                Number(jobItem.equipment_id)
              );
            } else if (jobItem?.type_name === "Other Items") {
              return (
                Number(item.reference_item_type_id) ===
                Number(jobItem.other_item_id)
              );
            } else if (jobItem?.type_name === "Group") {
              return (
                Number(item.reference_item_type_id) === Number(jobItem.item_id)
              );
            }
          })
      )
      .map((item) => {
        // Set markup, Prioriy (Project, Default settings, Item markup)
        let newMarkup = item.markup;
        if (item?.type_name === "Material" && default_material_markup_percent) {
          newMarkup = default_material_markup_percent;
        } else if (
          item?.type_name === "Labor" &&
          default_labor_markup_percent
        ) {
          newMarkup = default_labor_markup_percent;
        } else if (
          item?.type_name === "Subcontractor" &&
          default_sub_contractor_markup_percent
        ) {
          newMarkup = default_sub_contractor_markup_percent;
        } else if (
          item?.type_name === "Equipment" &&
          default_equipment_markup_percent
        ) {
          newMarkup = default_equipment_markup_percent;
        } else if (
          item?.type_name === "Other Items" &&
          default_other_item_markup_percent
        ) {
          newMarkup = default_other_item_markup_percent;
        }

        let applyGlobalTax: number | undefined;
        if (Number(item.item_type) === 161) {
          applyGlobalTax = is_taxable_material_items;
        } else if (Number(item.item_type) === 162) {
          applyGlobalTax = is_taxable_equipment_items;
        } else if (Number(item.item_type) === 163) {
          applyGlobalTax = is_taxable_labor_items;
        } else if (Number(item.item_type) === 164) {
          applyGlobalTax = is_taxable_subcontractor_items;
        } else if (Number(item.item_type) === 165) {
          applyGlobalTax = is_taxable_other_items;
        } else {
          applyGlobalTax = 0;
        }

        if (details?.id) {
          const matchedItemType = itemTypes.find(
            (type) => type?.type_id?.toString() == item?.item_type
          );
          if (matchedItemType) {
            newMarkup = matchedItemType.mark_up || newMarkup;
          }
        }

        let temData: Partial<IAddCIDBItemsData> = {
          directory_id: Number(details.estimator),
          apply_global_tax: applyGlobalTax,
          cost_code_id: Number(item.cost_code_id),
          description: item.description,
          internal_notes: item.internal_notes,
          markup: newMarkup,
          quantity: Number(item.quantity) || 0,
          subject: item.name,
          item_type: Number(item.item_type),
          unit: item.unit,
          unit_cost: item.unit_cost,
          total: item.total !== undefined ? item.total.toString() : undefined,
          is_markup_percentage: 1,
          item_assigned_to: 0,
          item_assigned_to_contact_id: 0,
          hidden_cost: Number(item.hidden_cost),
          hidden_markup: Number(item.hidden_markup),
          cost_code_name: item.cost_code_name,
          cost_code: item.cost_code,
          account_id: Number(item.account_id),
          item_type_display_name: item.item_type_display_name,
          import_item_type: item.item_type,
          equipment_id: Number(item.equipment_id),
          is_deleted: Number(item.is_deleted),
          item_id: Number(item.item_id),
          company_id: Number(item.company_id),
          material_id: Number(item.material_id),
          item_type_name: item.type_name,
          assignee_name: "",
          assignee_type: "",
          contractor_id: Number(item.contractor_id),
        };

        if (item?.type_name === "Material") {
          temData.reference_item_type_id = Number(item?.material_id);
        } else if (item?.type_name === "Labor") {
          temData.reference_item_type_id = Number(item?.labor_id);
        } else if (item?.type_name === "Subcontractor") {
          temData.reference_item_type_id = Number(item?.contractor_id);
        } else if (item?.type_name === "Equipment") {
          temData.reference_item_type_id = Number(item?.equipment_id);
        } else if (item?.type_name === "Other Items") {
          temData.reference_item_type_id = Number(item?.other_item_id);
        } else if (item?.type_name === "Group") {
          temData.reference_item_type_id =
            Number(item?.equipment_id) ||
            Number(item?.material_id) ||
            Number(item?.labor_id) ||
            Number(item?.contractor_id) ||
            Number(item?.other_item_id) ||
            Number(item?.item_id);
        }

        switch (item?.type_name) {
          case "Material":
            temData.reference_item_id =
              item.material_id !== undefined
                ? Number(item.material_id)
                : undefined;
            break;
          case "Labor":
            temData.reference_item_id =
              item.labor_id !== undefined ? Number(item.labor_id) : undefined;
            break;
          case "Subcontractor":
            temData.reference_item_id =
              item.contractor_id !== undefined
                ? Number(item.contractor_id)
                : undefined;
            break;
          case "Equipment":
            temData.reference_item_id =
              item.equipment_id !== undefined
                ? Number(item.equipment_id)
                : undefined;
            break;
          case "Other Items":
            temData.reference_item_id =
              item.other_item_id !== undefined
                ? Number(item.other_item_id)
                : undefined;
            break;
          case "Group": {
            const refId =
              item.equipment_id ??
              item.material_id ??
              item.labor_id ??
              item.contractor_id ??
              item.other_item_id ??
              item.item_id;
            temData.reference_item_id =
              refId !== undefined ? Number(refId) : undefined;
            break;
          }
          default:
            break;
        }
        return temData;
      });

    if (itemsToBeAdd.length > 0) {
      const payload = {
        items: itemsToBeAdd,
      };

      try {
        const updateRes = (await updateProjectItemTodb(
          Number(details.id),
          payload
        )) as DefaultResponse;

        if (updateRes.success) {
          await Promise.all([callSummaryApi(), callProjectSOVItemsApi()]);
          setIsCostDbItemOpen(false);
        } else {
          notification.error({
            description: updateRes.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "Something went wrong!",
        });

        setIsCostDbItemOpen(false);
      }
    } else {
      setIsCostDbItemOpen(false);
    }
  };

  const handleDeleteClick = useCallback((id: number) => {
    setTimeout(() => {
      setItemToBeDelete(id);
      setConfirmDialogOpen(true);
    }, 50);
  }, []);

  const handleSOVItemDelete = useCallback(
    async (id: string) => {
      setDeleteRecordLoading(true);
      const deleteItemParams: IDeleteSOVItemParams = {
        formData: {
          item_id: id || "0",
        },
        paramsData: {
          id: details.id?.toString() || "",
        },
      };
      const response = (await deleteSOVItem(
        deleteItemParams as IDeleteSOVItemParams
      )) as IDeleteInvoiceRes;
      if (response?.success) {
        setDeleteRecordLoading(false);
        setConfirmDialogOpen(false);
        const newItems = projectSOVItems.budget_items.filter(
          (item) => Number(item.item_id) !== Number(id)
        );
        callSummaryApi();
        dispatch(updateSOVItems({ items: newItems }));
      } else {
        notification.error({
          description: _t(
            response?.message ||
              `Something went wrong while deleting an item with ${id}`
          ),
        });
        setConfirmDialogOpen(false);
      }
    },
    [details.id]
  );

  const handleDelete = useCallback(() => {
    try {
      setDeleteRecordLoading(true);
      if (itemToBeDelete) {
        handleSOVItemDelete(itemToBeDelete.toString());
      }
    } catch (error: unknown) {
      notification.error({
        description: _t((error as Error)?.message),
      });
    }
  }, [itemToBeDelete]);

  return (
    <>
      <div className="grid gap-3 sov-content">
        <div className="flex sm:flex-row sm:gap-0 gap-2 common-card justify-between py-1.5 px-[15px]">
          <CrudCommonCard
            headerTitle={_t("Schedule of Values")}
            hideBorder={true}
            headerProps={{
              containerClassName: "!flex-row !items-center",
            }}
            titleText="text-lg"
            iconOuterClass="h-[34px] w-[34px]"
            className="h-[18px] w-[18px]"
            iconProps={{
              icon: "fa-solid fa-calendar-lines",
              id: "schedule_values",
              containerClassName:
                "bg-[linear-gradient(180deg,#FFB2981a_0%,#FA6D3D1a_100%)]",
              colors: ["#FFB298", "#FA6D3D"],
            }}
            headerRightButton={
              <>
                <SovTopBar />
              </>
            }
          />
        </div>
        <div className="grid lg:grid-cols-12 grid-cols-1 gap-2.5">
          <div className="common-card py-1.5 px-[15px] lg:col-span-4 col-span-1">
            <CostByType />
          </div>
          <div className="common-card py-1.5 px-[15px] lg:col-span-8 col-span-1">
            <InvoiceSummary />
          </div>
        </div>
        {isSOVItemsLoading ? (
          <Spin
            className={`flex items-center justify-center ${
              window.ENV.PAGE_IS_IFRAME
                ? "md:h-[calc(100dvh-161px)] h-[calc(100dvh-205px)]"
                : "2xl:h-[calc(100dvh-550px)] xl:h-[250px] md:h-[236px] h-20"
            }`}
          />
        ) : (
          <>
            <div className="flex gap-2 w-full justify-end text-end">
              <div className="sm:w-fit w-full text-end">
                {!isReadOnly && details.project_status !== "completed" && (
                  <DropdownMenu
                    options={addItemToSOVOptions}
                    buttonClass="w-fit h-auto"
                    contentClassName="add-items-drop-down"
                    placement="bottomRight"
                  >
                    <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
                      <Typography className="text-primary-900 text-sm">
                        {_t("Add Item to SOV")}
                      </Typography>
                      <FontAwesomeIcon
                        className="w-3 h-3 text-primary-900"
                        icon="fa-regular fa-chevron-down"
                      />
                    </div>
                  </DropdownMenu>
                )}
              </div>
            </div>

            {(current_filter_by === "all" ||
              current_filter_by === "estimate") && (
              <ApproveEstimateItemTable
                setSOVItemsToBeView={setSOVItemsToBeView}
                setBudgetItem={setBudgetItem}
                callSummaryApi={callSummaryApi}
                handleDeleteClick={handleDeleteClick}
              />
            )}
            {(current_filter_by === "all" ||
              !isNaN(Number(current_filter_by))) && (
              <CoItemTable
                setSOVItemsToBeView={setSOVItemsToBeView}
                setBudgetItem={setBudgetItem}
                callSummaryApi={callSummaryApi}
                handleDeleteClick={handleDeleteClick}
              />
            )}
            {(current_filter_by === "all" ||
              current_filter_by === "work_order") && (
              <WorkOrderItemTable
                setSOVItemsToBeView={setSOVItemsToBeView}
                setBudgetItem={setBudgetItem}
                callSummaryApi={callSummaryApi}
                handleDeleteClick={handleDeleteClick}
              />
            )}
            {(current_filter_by === "all" ||
              current_filter_by === "other_items") && (
              <OtherItemTable
                callSummaryApi={callSummaryApi}
                callProjectSOVItemsApi={callProjectSOVItemsApi}
              />
            )}
            <TaxCalculationCard callSummaryApi={callSummaryApi} callProjectSOVItemsApi={callProjectSOVItemsApi}/>
          </>
        )}

        <ImportItemsFromEstimates
          setImportEstItem={setImportEstItem}
          importEstItem={importEstItem}
          estimateItems={SOVItems.approvedEstimateItems}
          callSummaryApi={callSummaryApi}
          callProjectSOVItemsApi={callProjectSOVItemsApi}
        />

        {importCoItem && (
          <ImportItemsFromCo
            setImportCoItem={setImportCoItem}
            importCoItem={importCoItem}
            callSummaryApi={callSummaryApi}
            callProjectSOVItemsApi={callProjectSOVItemsApi}
          />
        )}

        {importWoItem && (
          <ImportItemsFromWo
            setImportWoItem={setImportWoItem}
            importWoItem={importWoItem}
            callSummaryApi={callSummaryApi}
            callProjectSOVItemsApi={callProjectSOVItemsApi}
          />
        )}

        <AddProjectItem
          setAddProjectItem={setAddProjectItem}
          addProjectItem={addProjectItem}
          isProjectItemAdd={true}
          callSummaryApi={callSummaryApi}
          callProjectSOVItemsApi={callProjectSOVItemsApi}
        />

        {/* <ImportSovItemsFromCsv
        isOpen={isImpSovFromCsv}
        onClose={() => {
          setIsImpSovFromCsv(false);
        }}
      /> */}

        {isCostDbItemOpen && (
          <CidbItemDrawer
            closeDrawer={() => {
              setIsCostDbItemOpen(false);
            }}
            options={[
              "material",
              "labor",
              "equipment",
              "subcontractor",
              "other_items",
              "groups",
            ]}
            singleSelecte={false}
            addItem={(data) => {
              handleItemFromCostItemDatabase(data as CIDBItemSideData[]);
            }}
            itemTypes={itemTypes?.map((item) => {
              return {
                ...item,
                default_color: item.default_color?.toString(),
              };
            })}
            openSendEmailSidebar={isCostDbItemOpen}
            data={costItemsFromDatabase as Partial<CIDBItemSideData>[]}
            initialSubMaterial={[] as CIDBItemSideData[]}
            cidbModuleVIseIdAndValue={{
              [CFConfig.material_key]: {
                id: CFConfig.material_teb_id,
                value: CFConfig.material_key,
              },
              [CFConfig.equipment_key]: {
                id: CFConfig.equipment_teb_id,
                value: CFConfig.equipment_key,
              },
              [CFConfig.labor_key]: {
                id: CFConfig.labor_teb_id,
                value: CFConfig.labor_key,
              },
              [CFConfig.subcontractor_key]: {
                id: CFConfig.subcontractor_teb_id,
                value: CFConfig.subcontractor_key,
              },
              [CFConfig.other_items_key]: {
                id: CFConfig.other_items_teb_id,
                value: CFConfig.other_items_key,
              },
            }}
            isHiddenMarkupApply={true}
          />
        )}

        {budgetItem && (
          <BudgetItem
            setBudgetItem={setBudgetItem}
            budgetItem={budgetItem}
            formData={SOVItemsToBeView}
            isViewOnly={true}
          />
        )}

        {isImpFromCsv && (
          <ImportProjectSOVtemsCsv
            isOpen={isImpFromCsv}
            onClose={() => {
              setIsImpFromCsv(false);
            }}
            callSummaryApi={callSummaryApi}
            callProjectSOVItemsApi={callProjectSOVItemsApi}
          />
        )}

        {confirmDialogOpen && (
          <ConfirmModal
            isOpen={confirmDialogOpen}
            modaltitle={_t("Remove Item from Schedule of Values")}
            description={_t(
              "Are you sure you want to remove this item from your Schedule of Values?"
            )}
            isLoading={deleteRecordLoading}
            modalIcon="fa-regular fa-trash-can"
            onAccept={() => {
              handleDelete();
            }}
            onDecline={() => setConfirmDialogOpen(false)}
            onCloseModal={() => setConfirmDialogOpen(false)}
          />
        )}
      </div>
    </>
  );
};

export default SovTab;
