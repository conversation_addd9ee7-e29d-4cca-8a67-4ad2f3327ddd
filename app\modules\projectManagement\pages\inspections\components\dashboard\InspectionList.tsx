// hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
// Other
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useEffect, useRef } from "react";
import { getGModuleDashboard, getGModuleFilters } from "~/zustand";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { useNavigate } from "@remix-run/react";
import isEqual from "lodash/isEqual";
import isEmpty from "lodash/isEmpty";
import {
  CellClickedEvent,
  GridReadyEvent,
  SortChangedEvent,
} from "ag-grid-community";
import { routes } from "~/route-services/routes";
import { getInspectionListApi } from "../../redux/action/inspectionDashAction";
import { sanitizeString } from "~/helpers/helper";
import { InspectionsTableDropdownItems } from "./InspectionsTableDropdownItems";

let timeout: NodeJS.Timeout;

const InspectionList = ({ setAddInspectionOpen }: IInspectionListProps) => {
  const { _t } = useTranslation();
  const filterSrv = getGModuleFilters() as
    | Partial<InspectionsFilter>
    | undefined;
  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    CFConfig.inspection_module
  );
  const currentModule = getCurrentMenuModule();

  const { datasource, gridRowParams } = useTableGridData();
  const navigate = useNavigate();
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        fetchInspectionList();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  const fetchInspectionList = async () => {
    let gridData: { rowCount?: number; rowData: IInspectionListData[] } = {
      rowData: [],
    };

    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const { start, order_by_name, order_by_dir } = changeGridParams || {};
    const limit = 20;

    const tempFil: IInspectionTempFil = {
      inspection_status: filterSrv?.inspection_status ?? "",
    };

    if (!filterSrv) {
      return;
    }
    if (filterSrv?.project) {
      tempFil.project = filterSrv.project;
    }
    if (filterSrv?.inspection_status) {
      tempFil.inspection_status = filterSrv.inspection_status;
    }
    if (tempFil.inspection_status?.toString() === "") {
      delete tempFil.inspection_status;
    }
    if (filterSrv?.start_date) {
      tempFil.start_date = filterSrv.start_date || "";
    }
    if (filterSrv?.end_date) {
      tempFil.end_date = filterSrv.end_date || "";
    }

    const startPagination = !!start ? Math.floor(start / limit) : 0;

    let dataParams: IInspectionListParams = {
      limit,
      filter: tempFil,
      order_by_name,
      order_by_dir,
      page: startPagination,
      search: HTMLEntities.encode(search),
      ignore_filter: 1, // without data-base
    };
    if (isEmpty(tempFil)) {
      delete dataParams.filter;
    }
    if (isEmpty(HTMLEntities.encode(search))) {
      delete dataParams.search;
    }
    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_name;
      delete dataParams.order_by_dir;
    }

    try {
      gridParams?.api.hideOverlay();
      const resData = (await getInspectionListApi(
        dataParams
      )) as IInspectionListApiRes;

      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.length < limit) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData?.data?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data ?? [] };
      gridParams?.success(gridData);
      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.page === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData.rowData.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const previousValues = useRef({
    filter: JSON.stringify(filterSrv),
    search,
  });

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filterSrv),
      search,
    };

    if (filterSrv && !isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [JSON.stringify(filterSrv), search]);

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const columnDefs = [
    {
      headerName: _t("Insp.") + " #",
      field: "company_inspection_id",
      minWidth: 130,
      maxWidth: 200,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;
        const idToolT = `Insp. #${data.company_inspection_id}`;
        return (
          <>
            {data.company_inspection_id ? (
              <Tooltip title={idToolT}>
                <Typography className="table-tooltip-text text-center">
                  {data.company_inspection_id}
                </Typography>
              </Tooltip>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "inspection_type",
      minWidth: 130,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;

        return (
          <>
            {data.inspection_type ? (
              <Tooltip
                title={HTMLEntities.decode(
                  sanitizeString(data.inspection_type)
                )}
              >
                <Typography className="table-tooltip-text text-center">
                  {HTMLEntities.decode(sanitizeString(data.inspection_type))}
                </Typography>
              </Tooltip>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Project Manager"),
      field: "assignee",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center ad-call-pr-0",
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;
        const projectManagerName = HTMLEntities.decode(
          sanitizeString(data?.project_manager_name)
        );
        const projectManagerImage = data?.project_manager_profile_image || "";

        return projectManagerName ? (
          <Tooltip title={projectManagerName} placement="top">
            <div className="flex justify-center">
              <AvatarProfile
                user={{
                  name: projectManagerName,
                  image: projectManagerImage,
                }}
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;

        return (
          <>
            {data.project_name ? (
              <Tooltip
                title={HTMLEntities.decode(sanitizeString(data.project_name))}
              >
                <Typography className="table-tooltip-text text-center">
                  {HTMLEntities.decode(sanitizeString(data.project_name))}
                </Typography>
              </Tooltip>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Agency"),
      field: "inspection_agency_name",
      minWidth: 130,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;

        return (
          <>
            {data.inspection_agency_name ? (
              <Tooltip
                title={HTMLEntities.decode(
                  sanitizeString(data.inspection_agency_name)
                )}
              >
                <Typography className="table-tooltip-text text-center">
                  {HTMLEntities.decode(
                    sanitizeString(data.inspection_agency_name)
                  )}
                </Typography>
              </Tooltip>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Inspected By"),
      field: "inspected_by",
      minWidth: 130,
      maxWidth: 200,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;
        const inspectedDetail = HTMLEntities.decode(
          sanitizeString(data?.inspected_by)
        );
        if (!inspectedDetail) return <>-</>;
        return (
          <>
            <Tooltip title={inspectedDetail}>
              <Typography className="table-tooltip-text text-center">
                {inspectedDetail}
              </Typography>
            </Tooltip>
          </>
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "inspection_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;

        return data.inspection_date ? (
          <DateTimeCard format="date" date={data.inspection_date} />
        ) : (
          <div className="table-tooltip-text">-</div>
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "inspection_status_name",
      maxWidth: 120,
      minWidth: 120,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;
        let moduleStatus: ModuleStatus | undefined =
          gModuleDashboard?.module_setting?.module_status?.find(
            (moduleStatus: ModuleStatus) => {
              return (
                moduleStatus?.item_id === data?.inspection_status?.toString()
              );
            }
          );
        return (
          <>
            {moduleStatus ? (
              <Tooltip title={moduleStatus?.name}>
                <div className="text-center overflow-hidden">
                  <Tag
                    color={`${moduleStatus?.status_color}24`}
                    className={`!text-[#${moduleStatus?.status_color}] mx-auto text-13 type-badge common-tag`}
                    style={{
                      color: moduleStatus?.status_color,
                    }}
                  >
                    {moduleStatus?.name}
                  </Tag>
                </div>
              </Tooltip>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: (params: { data: IInspectionListData }) => {
        const { data } = params;
        return (
          <div className="flex items-center gap-3 justify-center">
            {(currentModule?.module_access === "full_access" ||
              currentModule?.module_access === "own_data_access") &&
            data ? (
              <InspectionsTableDropdownItems
                iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                data={data}
                className="m-0 hover:!bg-[#0000000f]"
                icon="fa-regular fa-ellipsis-vertical"
                refreshTable={() => {
                  refreshAgGrid();
                }}
              />
            ) : null}
          </div>
        );
      },
    },
  ];

  return (
    <div className="md:h-[calc(100vh-260px)] h-[calc(100vh-230px)] list-view-table ag-grid-cell-pointer ag-theme-alpine">
      <DynamicTable
        columnDefs={columnDefs}
        onGridReady={onGridReady}
        onSortChanged={onSortChanged}
        onCellClicked={(params: CellClickedEvent) => {
          const column = params.column;
          if (
            column.getColDef().field !== "" &&
            column.getColDef().field !== "email"
          ) {
            navigate(
              `${routes.MANAGE_INSPECTION.url}/${params?.data?.inspection_id}`
            );
          }
        }}
        noRowsOverlayComponent={() => (
          <NoRecords
            rootClassName="w-full max-w-[280px]"
            image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
            text={
              currentModule?.module_access === "full_access" ||
              currentModule?.module_access === "own_data_access" ? (
                <div>
                  <Typography
                    className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    onClick={() => {
                      setAddInspectionOpen(true);
                    }}
                  >
                    {_t("Click here ")}
                  </Typography>
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("to Create a New Record")}
                  </Typography>
                </div>
              ) : (
                <Typography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </Typography>
              )
            }
          />
        )}
      />
    </div>
  );
};

export default InspectionList;
