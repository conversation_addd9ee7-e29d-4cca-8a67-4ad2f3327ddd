import {
  ColDef,
  ValueGetterParams,
  ValueSetterParams,
} from "ag-grid-community";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import {
  floatWithNegativeRegex,
  generateCostCodeLabel,
  qtyNumberCheck,
} from "~/shared/utils/helper/common";
import { ICON_MAP } from "../../../../utils/constants";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { HtmlDecode } from "../../../../utils/function";
import { formatAmount } from "~/helpers/helper";

export const getCommonColumns = ({
  _t = () => "",
  isReadOnly = false,
  isParentReadOnly = false,
  updateItemField = () => Promise.resolve(),
  filteredCodeCostData = [],
  // DeliveredHeader,
  // suppressKeyboardEvent,
  setItemsData,
  formatter = () => ({ value_with_symbol: "", value: "" }),
  inputFormatter = () => ({ value: "", value_with_symbol: "" }),
  isLoadingCheckBox = {},
  // handleDeleteSectionItem,
  handleDeleteItemOpen = () => {},
  handleDeleteItemClose = () => {},
  handleDeleteItem = () => {},
  handleViewItem = () => {},
  handleAllClick = () => {},
  sectionName = "",
  rowData = [],
  ViewItemsKey,
  isTaxEnabled = false,
}: IPOCommonColumns) => {
  return [
    {
      headerName: "",
      maxWidth: 30,
      minWidth: 30,
      field: "move",
      rowDrag: true,
      suppressMenu: true,
      hide: isReadOnly,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center ag-move-cell custom-move-icon-set",
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        return (
          <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
            <FontAwesomeIcon
              className="w-4 h-4 text-[#4b5a76]"
              icon="fa-solid fa-grip-dots"
            />
          </div>
        );
      },
    },
    {
      headerName: _t("Type"),
      maxWidth: 50,
      minWidth: 50,
      field: "type",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        const value = data?.item_type_name;
        return value ? (
          <Tooltip title={value}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              icon={
                ICON_MAP[value as keyof typeof ICON_MAP] || ICON_MAP["default"]
              }
            />
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "item_name",
      minWidth: 130,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      editable: !isReadOnly,
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        const subject = HtmlDecode(data?.subject);
        return !!subject ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
      valueGetter: (params: IIVLumpSumTableCellRenderer) =>
        HtmlDecode(params?.data?.subject),
      valueSetter: (params: ValueSetterParams) => {
        if (params && params?.node) {
          if (!params?.newValue.trim().length) {
            notification.error({
              description: _t("Item Name is required."),
            });
            return false;
          }
          const updatedData = {
            ...params?.data,
            subject: params?.newValue?.trim(),
          };
          params?.node?.setData(updatedData);
          updateItemField({
            itemId: updatedData?.item_id,
            itemData: params?.data,
            updatedItem: {
              subject: HtmlDecode(params?.newValue?.trim()),
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code",
      minWidth: 130,
      maxWidth: 250,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellEditor: "agRichSelectCellEditor",
      editable: !isReadOnly,
      cellEditorParams: {
        values: filteredCodeCostData?.map((item) => item?.label),
        filterList: true,
        searchType: "matchAny",
        allowTyping: true,
        // need to check
        valueListMaxHeight:
          rowData?.length == 1
            ? 60
            : rowData?.length == 2
            ? 90
            : rowData?.length == 3
            ? 120
            : rowData?.length == 4
            ? 150
            : 180,
      },
      // (details?.approval_type_key === "invoice_open" ||
      //   details?.approval_type_key === "invoice_on_hold"),
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? generateCostCodeLabel({
              name: params.data?.cost_code_name,
              code: params.data?.cost_code,
              isArchived: false,
              isAllowCodeWithoutName: true,
            })
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const costCodelabel = params.newValue;
          const costCode = filteredCodeCostData?.find(
            (el) => el?.label?.trim() === costCodelabel?.trim()
          );
          if (costCode) {
            const updatedData = {
              ...params.data,
              cost_code_name: costCode?.cost_code_name || "",
              cost_code_id: costCode?.code_id,
              cost_code: costCode?.csi_code,
              code_is_deleted: 0,
            };
            params.node.setData(updatedData);
            updateItemField({
              itemId: updatedData.item_id,
              itemData: params.data,
              updatedItem: {
                cost_code_id: Number(costCode?.code_id),
                cost_code_name: costCode?.cost_code_name,
                cost_code: costCode?.csi_code,
                code_is_deleted: 0,
              },
            });
          }
        }
        return true;
      },
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        // const costCode = data?.cost_code_name ?? "";
        const costCode = generateCostCodeLabel({
          name: data?.cost_code_name || "",
          code: data?.cost_code,
          isArchived:
            data?.code_is_deleted == 1 ||
            filteredCodeCostData?.findIndex(
              (el) => el?.code_id?.toString() === data?.cost_code_id?.toString()
            ) == -1,
          isAllowCodeWithoutName: true,
        });
        const htmlDecode = HtmlDecode(costCode);

        return costCode ? (
          <Tooltip title={htmlDecode}>
            <Typography className="table-tooltip-text">
              {htmlDecode || "-"}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 80,
      minWidth: 80,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      editable: !isReadOnly,
      cellEditor: "agNumberCellEditor",
      suppressKeyboardEvent,
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        const quantity = data?.quantity ?? 0;
        const quantityUnit = 
           formatter(formatAmount(Number(quantity), { isQuantity: true }))
              .value || 0
         
        return (
          <>
            <Tooltip title={quantityUnit}>
              <Typography className="table-tooltip-text">
                {quantityUnit}
              </Typography>
            </Tooltip>
          </>
        ); 
      },
      valueGetter: (params: ValueGetterParams) => {
        return Number(params.data?.quantity);
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (!nVal || nVal == null) {
            const _updateData = {
              ...params.data,
              quantity: 0,
            };
            params.node.setData(_updateData);
            updateItemField({
              itemId: _updateData.item_id,
              itemData: params.data,
              updatedItem: {
                quantity: 0,
              },
            });
            return true;
          }
          const maxLengthAllow = 6;
          const checkNum = qtyNumberCheck(nVal);
          const cleanedValue =
            nVal != null
              ? nVal?.toString()?.split(".")[0]?.replace("-", "")
              : "";
          if (
            (cleanedValue &&
              cleanedValue != null &&
              cleanedValue?.length > maxLengthAllow) ||
            !checkNum
          ) {
            notification.error({
              description: `Quantity should be less than or equal to ${maxLengthAllow} digits.`,
            });
            return false;
          }

          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }
          const updatedData = {
            ...params.data,
            quantity: Number(nVal),
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              quantity: Number(nVal),
            },
          });
        }
        return true;
      },
    },
    // {
    //   headerName: _t("Billed"),
    //   field: "billed",
    //   maxWidth: 100,
    //   minWidth: 100,
    //   suppressMovable: false,
    //   suppressMenu: true,
    //   headerClass: "ag-header-right",
    //   cellClass: "ag-cell-right",
    //   cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
    //     const billedQuantity = data?.billed_quantity ?? "";
    //     // const formattQ = formatter(Number(billedQuantity).toFixed(2)).value
    //     const formattQ = inputFormatter(
    //       Number(billedQuantity)?.toString()
    //     ).value;
    //     return billedQuantity ? (
    //       <>
    //         <Tooltip title={!!billedQuantity ? formattQ : ""}>
    //           <Typography className="table-tooltip-text">
    //             {billedQuantity ? formattQ : "-"}
    //           </Typography>
    //         </Tooltip>
    //       </>
    //     ) : (
    //       <Typography className="table-tooltip-text">
    //         -
    //       </Typography>
    //     );
    //   },
    // },
    {
      headerName: _t("Delivered"),
      field: "delivered",
      minWidth: 125,
      maxWidth: 140,
      flex: 1,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellEditor: "agNumberCellEditor",
      headerComponent: DeliveredHeader,
      headerComponentParams: {
        _t,
        handleAllClick,
        sectionName,
        rowData,
        isReadOnly,
        isParentReadOnly,
      },
      suppressKeyboardEvent,
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        const deliveredQuantity = data?.delivered_quantity ?? "";
        // const formattQ = formatter(Number(deliveredQuantity).toFixed(2)).value
        const formattQ = inputFormatter(
          Number(deliveredQuantity)?.toString()
        ).value;
        const htmlDecode = HtmlDecode(formattQ);

        // deliveredQuantity ? (
        return (
          <>
            <Tooltip title={!!deliveredQuantity ? htmlDecode : "0"}>
              <Typography className="table-tooltip-text">
                {deliveredQuantity ? htmlDecode : "0"}
              </Typography>
            </Tooltip>
          </>
        );
        // ) : (
        //   <Typography className="table-tooltip-text">
        //     -
        //   </Typography>
        // );
      },
      valueGetter: (params: ValueGetterParams) => {
        return Number(params.data?.delivered_quantity);
      },
      editable: !isParentReadOnly,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          if (!params.newValue) {
            const _updateData = {
              ...params.data,
              delivered_quantity: "",
            };
            params.node.setData(_updateData);
            updateItemField({
              itemId: _updateData.item_id,
              itemData: params.data,
              updatedItem: {
                delivered_quantity: 0,
              },
            });
            return true;
          }

          const cleanedValue = params.newValue
            .toString()
            .split(".")[0]
            .replace("-", "");
          if (cleanedValue?.length > 6) {
            notification.error({
              description: _t(
                "Delivered quantity should be less than or equal to 6 digits"
              ),
            });
            return false;
          }
          // if (Number(params.newValue) < 0) {
          //   notification.error({
          //     description: _t("Quantity should be more then 0"),
          //   });
          //   return false;
          // }

          if (!floatWithNegativeRegex.test(params.newValue)) {
            if (params.newValue.toString().includes("e")) {
              notification.error({
                description: _t(
                  "Delivered quantity should be less than or equal to 6 digits."
                ),
              });
            } else {
              notification.error({
                description: _t(
                  "Decimal part should be less than or equal to 2 digits"
                ),
              });
            }
            return false;
          }

          const updatedData = {
            ...params.data,
            delivered_quantity: Number(params.newValue),
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              delivered_quantity: Number(params.newValue),
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("Unit Cost"),
      field: "unit_cost",
      minWidth: 130,
      maxWidth: 150,
      cellEditor: "agNumberCellEditor",
      flex: 1,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      editable: !isReadOnly,
      suppressKeyboardEvent,
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        // const unitCost = data?.unit_cost ?? "$0";
        const totalFloat = Number(data?.unit_cost);
        const unitCost = formatter(
          formatAmount((totalFloat || 0) / 100)
        )?.value_with_symbol;

        return (
          <div className="flex gap-1 items-center justify-end overflow-hidden">
            <Tooltip title={unitCost}>
              <Typography className="table-tooltip-text">{unitCost}</Typography>
            </Tooltip>
          </div>
        );
      },
      valueGetter: (params: ValueGetterParams) => params.data?.unit_cost / 100,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (!nVal || nVal == null) {
            const _updateData = {
              ...params.data,
              unit_cost: "0.0",
            };
            params.node.setData(_updateData);
            updateItemField({
              itemId: _updateData.item_id,
              itemData: params.data,
              updatedItem: {
                unit_cost: "0.0",
              },
            });
            return true;
          }

          if (
            Number(params.newValue) < 0
            // &&
            // params?.data?.is_discount_item != 1
          ) {
            notification.error({
              description: _t("Negative values are not allowed for Unit Cost."),
            });
            return false;
          }
          const maxLengthAllow = 10;
          // const maxLengthAllow =
          //   params?.data?.is_discount_item != 1 ? 10 : 11;
          const checkNum = qtyNumberCheck(nVal);
          const fullstr = BigInt(
            Math.floor(Number(params.newValue))
          ).toString();

          if (fullstr.length > maxLengthAllow || !checkNum) {
            notification.error({
              description:
                "Unit cost should be less than or equal to 10 digits.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }
          const updatedData = {
            ...params.data,
            unit_cost: Number(nVal) * 100,
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              unit_cost: Number(nVal) * 100,
            },
          });
        }
        return true;
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      maxWidth: 60,
      minWidth: 60,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      // editable: !isReadOnly,
      cellEditorParams: {
        maxLength: 20,
      },
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        const unit =
          !!data?.unit && data?.unit?.trim()?.length ? data?.unit : "-";
        return (
          <>
            <Tooltip
              title={!!data?.unit && data?.unit?.trim()?.length ? unit : ""}
            >
              <Typography className="table-tooltip-text">{unit}</Typography>
            </Tooltip>
          </>
        );
      },
      // valueSetter: (params: ValueSetterParams) => {
      //   if (params && params.node) {
      //     const cleanedValue = (
      //       params?.newValue || ""
      //     )?.toString();
      //     if (cleanedValue?.length > 15) {
      //       notification.error({
      //         description: _t(
      //           "Unit should be less than or equal to 15 characters"
      //         ),
      //       });
      //       return false;
      //     }
      //     const updatedData = {
      //       ...params.data,
      //       unit: params.newValue,
      //     };
      //     params.node.setData(updatedData);
      //     // updateItemField({
      //     //   itemId: updatedData.item_id,
      //     //   itemData: params.data,
      //     //   updatedItem: {
      //     //     unit: params.newValue,
      //     //   },
      //     // });
      //   }
      //   return true;
      // },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 130,
      maxWidth: 150,
      flex: 1,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        const baseTotal = parseFloat(
          (
            (Number(data?.unit_cost || 0) * Number(data?.quantity || 0)) /
            100
          ).toString()
        );
        const value = formatter(
          formatAmount(baseTotal || 0)
        )?.value_with_symbol;
        return (
          <Tooltip title={value}>
            <Typography className="table-tooltip-text">{value}</Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Tax"),
      field: "tax",
      maxWidth: 50,
      minWidth: 50,
      hide: !isTaxEnabled,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <CustomCheckBox
              checked={
                typeof data?.apply_global_tax === "boolean"
                  ? data?.apply_global_tax
                  : data?.apply_global_tax?.toString() === "1"
              }
              disabled={
                isReadOnly ||
                Object.values(isLoadingCheckBox).some((isLoading) => isLoading)
              }
              loadingProps={{
                isLoading:
                  (data?.item_id && isLoadingCheckBox?.[data?.item_id]) ||
                  false,
                className: "bg-[#ffffff]",
              }}
              onChange={() => {
                if (!data?.item_id) {
                  return;
                }
                // handleChange(params, "is_billable");
                updateItemField({
                  itemId: data?.item_id,
                  itemData: data as IPOItemData,
                  updatedItem: {
                    apply_global_tax: data?.apply_global_tax ? 0 : 1,
                  },
                });
              }}
              className="gap-0"
              name="apply_global_tax"
            />
          </div>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: ({ data }: IPOItemTableCellRenderer) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                //edit & view item ======
                handleViewItem({
                  data: data as IPOItemData,
                  allDetails: rowData,
                  sectionName,
                  ViewItemsKey,
                });
                // setItemsData(data);
                // setPurchaseOrderItem(true);
              }}
            />
            {!isReadOnly && (
              <ButtonWithTooltip
                tooltipTitle={_t("Delete")}
                tooltipPlacement="top"
                icon="fa-regular fa-trash-can"
                onClick={() => {
                  // updateItemField({
                  //   itemId: data?.item_id,
                  //   itemData: data as IPOItemData,
                  //   updatedItem: {
                  //     apply_global_tax: data?.apply_global_tax ? 0 : 1,
                  //   },
                  // });
                  handleDeleteItem(data as IPOItemData);
                  // setSelectedItemId(Number(data?.item_id) || 0);
                  // handleDeleteItemOpen(data as IPOItemData,);
                }}
              />
            )}
          </div>
        );
      },
    },
  ];
};

export const suppressKeyboardEvent: ColDef<
  CostItemTableCellRenderer["data"]
>["suppressKeyboardEvent"] = (params) => {
  const { event, api } = params;

  if (event?.key === "ArrowUp" || event?.key === "ArrowDown") {
    event?.preventDefault();
    event?.stopPropagation();
    return true;
  }

  if (event?.key === "Enter") {
    event?.preventDefault();
    event?.stopPropagation();

    // Call valueSetter or custom logic for saving the value
    api?.stopEditing(); // Stop editing to trigger valueSetter
    return true;
  }

  return true;
};

export const DeliveredHeader = ({
  _t,
  handleAllClick,
  rowData = [],
  isReadOnly,
  isParentReadOnly,
}: {
  _t: (key: string) => string;
  handleAllClick: () => void;
  rowData?: IPOItemData[];
  isReadOnly?: boolean;
  isParentReadOnly?: boolean;
}) => {
  return (
    <div className="text-right w-[120px]">
      <Typography className="text-[#181d1f] font-semibold dark:text-white/90">
        {_t("Delivered")} <br />
        <div className="flex cursor-pointer relative z-10 justify-end">
          <Typography
            className={`text-[#181d1f] text-xs font-semibold dark:text-white/90 ${
              !isParentReadOnly ? "cursor-pointer" : ""
            }`}
            onClick={() => {
              if (isParentReadOnly) return;
              handleAllClick();
            }}
          >
            {_t("(ALL)")}
          </Typography>
          {/* <div className="flex cursor-pointer relative z-10"> */}
          <Tooltip
            title={_t(
              "Click on (All) to set delivered quantity from QTY field for all item"
            )}
          >
            <FontAwesomeIcon
              className="w-[13px] h-[15px] ml-1 text-[#21252966]"
              icon="fa-solid fa-circle-info"
            />
          </Tooltip>
        </div>
        {/* </div> */}
      </Typography>
    </div>
  );
};

export const getAllItems = (state: IPOItemSectionData | undefined) => {
  // Get change_order items
  const changeOrderItems =
    state?.change_order?.flatMap((section) => section.items) || [];

  // Get work_order items
  const workOrderItems =
    state?.work_order?.flatMap((section) => section.items) || [];

  // Get estimate items
  const estimateItems =
    state?.estimate
      ?.flatMap((section) => section.sections)
      ?.flatMap((section) => section.items) || [];

  // Get other items
  const otherItems = state?.others || [];
  const lumpsumItems = state?.lumpsum || [];

  // Combine all items into one array
  const allItems = [
    ...changeOrderItems,
    ...workOrderItems,
    ...estimateItems,
    ...otherItems,
    ...lumpsumItems,
  ].filter((item) => item !== undefined);

  // Sort by purchase_order_item_no if needed
  return allItems?.sort(
    (a, b) =>
      (a?.purchase_order_item_no || 0) - (b?.purchase_order_item_no || 0)
  );
};

// export const EmptyStaticTable = () => {
//   return (
//     <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
//       <div className="ag-theme-alpine double-line-header">
//         <StaticTable
//           className="static-table"
//           columnDefs={getCommonColumns({
//             filteredCodeCostData: [],
//             handleAllClick: () => {},
//             sectionName: "work_order",
//             rowData: [],
//             isReadOnly: true,
//             updateItemField: () => Promise.resolve(),
//             formatter: () => ({ value_with_symbol: "", value: "" }),
//             inputFormatter: () => ({ value: "", value_with_symbol: "" }),
//             isLoadingCheckBox: {},
//             handleDeleteItemOpen: () => {},
//             handleDeleteItemClose: () => {},
//             handleViewItem: () => {},
//             _t: () => "",
//           })}
//           rowDragManaged={false}
//           rowData={[]}
//           animateRows={true}
//           noRowsOverlayComponent={() => (
//             <NoRecords
//               image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
//             />
//           )}
//         />
//       </div>
//     </div>
//   );
// };
