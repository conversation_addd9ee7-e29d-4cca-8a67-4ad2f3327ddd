import { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import delay from "lodash/delay";

// Hookes And redux
import { useTranslation } from "~/hook";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { addCustomDataAct } from "~/redux/slices/customDataSlice";
import { addCustomData } from "~/redux/action/customDataAction";

// Redux
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { getModuleAutoNumber } from "~/redux/action/commonAction";
import { setActiveField } from "~/redux/slices/sendEmailSlice";
import { addIncidentAPI } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Organisms
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";

// Other
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import {
  backendDateFormat,
  backendTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { customDataTypesByKey } from "~/utils/constasnts";
import { defaultConfig } from "~/data";
import {
  escapeHtmlEntities,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";

const IncidentsAdd = ({
  isOpen,
  onAddedRec,
  onClose,
  isViewOnly = false,
}: IIncidentsProps) => {
  const { _t } = useTranslation();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { is_custom_incident_id } = appSettings || {};

  const { getGlobalModuleByKey } = useGlobalModule();

  const module = useMemo(
    () => getGlobalModuleByKey(CFConfig.incident_module),
    []
  );

  const dispatch = useAppDLDispatch();

  const { customDataList }: ICustomDataInitialState = useAppDLSelector(
    (state) => state.customData
  );
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [selectCustomerOpen, setSelectCustomerOpen] = useState<string>("");
  const [numberLoading, setNumberLoading] = useState<boolean>(false);
  const [additionalContact, setAdditionContact] = useState<number>(0);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();

  const [selectedInvolved, setSelectedInvolved] = useState<
    Partial<IDirectoryData[]>
  >([]);
  const [selectedWitness, setSelectedWitness] = useState<
    Partial<IDirectoryData[]>
  >([]);

  const categCusDataItems: ICategCusDataItems = useMemo(() => {
    const accumulatedData = customDataList.reduce<ICategCusDataItems>(
      (acc, item) => {
        if (item.item_type === customDataTypesByKey.incidentTypeId.toString()) {
          acc.incidentTypeList?.push({
            label: replaceDOMParams(sanitizeString(item.name)),
            value: item.item_id,
            disabled:
              item.item_id === "self_reported_from_timecard" ? true : false,
          });
        }
        return acc;
      },
      { incidentTypeList: [] }
    );
    return accumulatedData;
  }, [customDataList]);

  const validationSchema = Yup.object().shape({
    customIncidentId: Yup.string().trim().required("This field is required."),
    incidentType: Yup.string().trim().required("This field is required."),
    description: Yup.string().trim().required("This field is required."),
  });
  const initialValues: IAddDLIncidentsReq = {
    customIncidentId: "",
    incidentType: "",
    description: "",
    projectId: 0,
    incidentDate: "",
    incidentTime: "",
  };

  const [initialValuesState, setInitialValuesState] = useState(initialValues);

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      values.projectId = details?.projectId || 0;
      const formData = getValuableObj({ ...values });
      formData.description = escapeHtmlEntities(formData.description);
      formData.customIncidentId = escapeHtmlEntities(
        formData?.customIncidentId?.toString() || ""
      );

      if (is_custom_incident_id == 0) {
        delete formData.customIncidentId;
      }

      if (details.arrivalDate) {
        formData.incidentDate = backendDateFormat(
          details.arrivalDate,
          CFConfig.day_js_date_format
        );
      }
      if (details.arrivalTime) {
        formData.incidentTime = backendTimeFormat(details.arrivalTime);
      }

      const empInvolved = selectedInvolved
        .map((item) => item?.module_display_name || item?.display_name)
        .join(", ");
      const empInvolvedImg = selectedInvolved
        .map((item) => item?.image || "")
        .join(", ");

      const incidentType = categCusDataItems?.incidentTypeList?.find(
        (item) => item.value == formData?.incidentType
      );

      try {
        const responseApi = (await addIncidentAPI(
          formData
        )) as IAddDLIncidentRes;
        if (responseApi?.success) {
          dispatch(resetDash());
          onAddedRec({
            incident_id: responseApi?.data?.incident_id || undefined,
            txn_type: "incident",
            incident_type_name: incidentType?.label,
            emp_involved_image: empInvolvedImg || "",
            emp_involved: empInvolved || "",
            description: formData.description,
          });
        } else {
          notification.error({
            description: responseApi?.message || "",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "",
        });
      } finally {
        setSubmitting(false);
      }
    },
  });
  const {
    handleSubmit,
    handleChange,
    setFieldValue,
    isSubmitting,
    values,
    touched,
    errors,
  } = formik;

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!event.currentTarget.value?.trim()) {
      return;
    }
    if (event.key === "Enter") {
      event.preventDefault();
      if (
        categCusDataItems?.incidentTypeList?.some((type: Option) =>
          type?.label
            .toLowerCase()
            .includes(event?.currentTarget?.value.toLowerCase())
        )
      ) {
        return;
      }
      setCustomDataAdd({
        itemType: customDataTypesByKey?.incidentTypeId,
        name: escapeHtmlEntities(event?.currentTarget?.value),
      });
      setIsConfirmDialogOpen(true);
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addCustomDataAct(cDataRes?.data));
        delay(() => {
          setFieldValue("incidentType", cDataRes?.data?.item_id);
        }, 500);
        setIsConfirmDialogOpen(false);
      }
      setIsAddingCustomData(false);
    }
  };

  const handleSelectCustomer = (data: Partial<IDirectoryData[]>) => {
    const userIds = data.map((item) => item?.user_id);
    if (selectCustomerOpen === "empInvolved") {
      setSelectedInvolved(data);
      setFieldValue("empInvolved", userIds?.length ? userIds : []);
      setInitialValuesState((prevState: IAddDLIncidentsReq) => ({
        ...prevState,
        empInvolved: [],
      }));
    } else {
      setSelectedWitness(data);
      setFieldValue("empWitness", userIds?.length ? userIds : []);
      setInitialValuesState((prevState: IAddDLIncidentsReq) => ({
        ...prevState,
        empWitness: [],
      }));
    }
    setSelectCustomerOpen("");
  };

  const getAutoNumber = async () => {
    setNumberLoading(true);
    const autoNumberRes = (await getModuleAutoNumber({
      // Utsav say, set static id and key
      module_id: 37,
      module_key: "incidents",
    })) as GetModuleAutoNumberApiResponse;
    if (autoNumberRes.success) {
      const newId = autoNumberRes?.data?.last_primary_id
        ? Number(autoNumberRes.data.last_primary_id) +
          autoNumberRes.data?.need_to_increment
        : "";
      setFieldValue("customIncidentId", newId);
      setInitialValuesState((prevState) => ({
        ...prevState,
        customIncidentId: newId,
      }));
    }
    if (!autoNumberRes?.success) {
      notification.error({
        description: autoNumberRes.message || "Something went wrong",
      });
    }
    setNumberLoading(false);
  };
  useEffect(() => {
    if (is_custom_incident_id == 2) {
      getAutoNumber();
    }
    if (is_custom_incident_id == 0) {
      setFieldValue("customIncidentId", "Save To View");
      setInitialValuesState((prevState: IAddDLIncidentsReq) => ({
        ...prevState,
        customIncidentId: "Save To View",
      }));
    }
  }, [is_custom_incident_id]);

  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-triangle-exclamation"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {module ? _t(`Add ${module.module_name}`) : _t("Add Incident")}
            </Header>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose(false)} />}
      >
        {!numberLoading ? (
          <form
            noValidate
            method="post"
            className="py-4"
            onSubmit={handleSubmit}
          >
            <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
              <div className="grid gap-4">
                <SidebarCardBorder addGap={true}>
                  <div className="w-full">
                    <InputField
                      label={_t("Incident") + " #"}
                      labelPlacement="top"
                      name="customIncidentId"
                      isRequired={true}
                      value={values?.customIncidentId}
                      onChange={handleChange}
                      disabled={isViewOnly || is_custom_incident_id == 0}
                      errorMessage={
                        touched?.customIncidentId ? errors.customIncidentId : ""
                      }
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={_t("Classification")}
                      isRequired={true}
                      value={values?.incidentType}
                      labelPlacement="top"
                      allowClear={true}
                      disabled={isViewOnly}
                      showSearch
                      options={categCusDataItems?.incidentTypeList}
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      onChange={(val) => {
                        if (val) {
                          setFieldValue("incidentType", val);
                        } else {
                          setFieldValue("incidentType", "");
                        }
                      }}
                      addItem={{
                        text: _t("Press Enter to add new Classification"),
                        icon: "fa-regular fa-plus",
                      }}
                      onInputKeyDown={(e) => handlekeyDown(e)}
                      errorMessage={
                        touched?.incidentType ? errors?.incidentType : ""
                      }
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("People Involved")}
                      name="empInvolved"
                      labelPlacement="top"
                      value={
                        selectedInvolved?.length == 1
                          ? selectedInvolved
                              .map(
                                (item) =>
                                  item?.module_display_name ||
                                  item?.display_name
                              )
                              .join(", ")
                          : selectedInvolved?.length > 1
                          ? `${selectedInvolved?.length} ${_t("Selected")}`
                          : ""
                      }
                      isDisabled={isViewOnly}
                      onClick={() => {
                        setSelectCustomerOpen("empInvolved");
                      }}
                      avatarProps={
                        selectedInvolved?.length == 1
                          ? {
                              user: {
                                name: HTMLEntities.decode(
                                  sanitizeString(
                                    selectedInvolved[0]?.display_name
                                  )
                                ),
                                image: selectedInvolved[0]?.image,
                              },
                            }
                          : undefined
                      }
                      addonBefore={
                        <div className="flex items-center gap-1">
                          {selectedInvolved.length === 1 && (
                            <>
                              <ContactDetailsButton
                                onClick={(event) => {
                                  event.stopPropagation();
                                  setIsOpenContactDetails(true);

                                  setAdditionContact(
                                    Number(selectedInvolved?.[0]?.contact_id)
                                  );
                                  setcontactId(
                                    selectedInvolved?.[0]?.user_id as number
                                  );
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                className="!w-5 !h-5"
                                directoryId={
                                  selectedInvolved[0]?.user_id.toString() || ""
                                }
                                directoryTypeKey={
                                  selectedInvolved?.[0]?.type_key || ""
                                }
                              />
                            </>
                          )}
                          {selectedInvolved?.length > 1 && (
                            <AvatarIconPopover
                              redirectionIcon={true}
                              assignedTo={
                                selectedInvolved as IAssignedToUsers[]
                              }
                              setSelectedUserId={(data) => {
                                setcontactId(data.id);
                                setAdditionContact(data.contactId || 0);
                              }}
                              setIsOpenContactDetails={() => {
                                setIsOpenContactDetails(true);
                              }}
                            />
                          )}
                        </div>
                      }
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Witness")}
                      name="empWitness"
                      isDisabled={isViewOnly}
                      labelPlacement="top"
                      value={
                        selectedWitness?.length == 1
                          ? selectedWitness
                              .map(
                                (item) =>
                                  item?.module_display_name ||
                                  item?.display_name
                              )
                              .join(", ")
                          : selectedWitness?.length > 1
                          ? `${selectedWitness?.length} ${_t("Selected")}`
                          : ""
                      }
                      onClick={() => {
                        setSelectCustomerOpen("empWitness");
                      }}
                      avatarProps={
                        selectedWitness?.length == 1
                          ? {
                              user: {
                                name: HTMLEntities.decode(
                                  sanitizeString(
                                    selectedWitness[0]?.display_name
                                  )
                                ),
                                image: selectedWitness[0]?.image,
                              },
                            }
                          : undefined
                      }
                      addonBefore={
                        <div className="flex items-center gap-1">
                          {selectedWitness.length === 1 && (
                            <>
                              <ContactDetailsButton
                                onClick={(event) => {
                                  event.stopPropagation();
                                  setAdditionContact(
                                    Number(selectedWitness?.[0]?.contact_id)
                                  );
                                  setIsOpenContactDetails(true);
                                  setcontactId(
                                    selectedWitness?.[0]?.user_id as number
                                  );
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                className="!w-5 !h-5"
                                directoryId={
                                  selectedWitness[0]?.user_id.toString() || ""
                                }
                                directoryTypeKey={
                                  selectedWitness?.[0]?.type_key || ""
                                }
                              />
                            </>
                          )}
                          {selectedWitness?.length > 1 && (
                            <AvatarIconPopover
                              redirectionIcon={true}
                              assignedTo={selectedWitness as IAssignedToUsers[]}
                              setSelectedUserId={(data) => {
                                setcontactId(data.id);
                                setAdditionContact(data.contactId || 0);
                              }}
                              setIsOpenContactDetails={setIsOpenContactDetails}
                            />
                          )}
                        </div>
                      }
                    />
                  </div>
                  <div className="w-full">
                    <TextAreaField
                      label={_t("Description")}
                      name="description"
                      labelPlacement="top"
                      required={true}
                      disabled={isViewOnly}
                      value={values?.description}
                      onChange={handleChange}
                      errorMessage={
                        touched?.description ? errors.description : ""
                      }
                    />
                  </div>
                </SidebarCardBorder>
              </div>
            </div>
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              {!isViewOnly && (
                <PrimaryButton
                  htmlType="submit"
                  disabled={isSubmitting}
                  isLoading={isSubmitting}
                >
                  {_t("Save")}
                </PrimaryButton>
              )}
            </div>
          </form>
        ) : (
          <Spin className="w-full h-[calc(100dvh-55px)] flex items-center justify-center" />
        )}

        {isConfirmDialogOpen && (
          <ConfirmModal
            isOpen={isConfirmDialogOpen}
            modalIcon="fa-regular fa-clipboard-list-check"
            modaltitle={_t("Add Option To List")}
            description={_t(
              `This will add "${replaceDOMParams(
                sanitizeString(customDataAdd?.name || "")
              )}" to the list. Do you want to add it?`
            )}
            isLoading={isAddingCustomData}
            onCloseModal={() => {
              setIsConfirmDialogOpen(false);
            }}
            onAccept={() => {
              handleAddCustomData();
            }}
            onDecline={() => {
              setIsConfirmDialogOpen(false);
            }}
          />
        )}
      </Drawer>
      {isOpenContactDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetails}
          contactId={contactId}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          additional_contact_id={additionalContact}
        />
      )}
      {selectCustomerOpen !== "" && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={selectCustomerOpen !== ""}
          closeDrawer={() => {
            dispatch(setActiveField(defaultConfig.employee_key));
            setSelectCustomerOpen("");
          }}
          projectId={details?.projectId}
          singleSelecte={false}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.contractor_key,
            "by_service",
            "my_project",
          ]}
          setCustomer={(data) => {
            handleSelectCustomer(data as unknown as Partial<IDirectoryData[]>);
          }}
          selectedCustomer={
            selectCustomerOpen === "empInvolved"
              ? (selectedInvolved as unknown as TselectedContactSendMail[])
              : (selectedWitness as unknown as TselectedContactSendMail[])
          }
          groupCheckBox={true}
          additionalContactDetails={0} // additional contact will select from here as per PHP
        />
      )}
    </>
  );
};

export default IncidentsAdd;
