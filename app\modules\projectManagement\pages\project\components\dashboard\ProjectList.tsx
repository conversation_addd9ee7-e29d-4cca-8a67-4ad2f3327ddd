import debounce from "lodash/debounce";

// Fontawesome
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// hooks
import { useDateFormatter, useTranslation } from "~/hook";
// Atoms
import { Progress } from "~/shared/components/atoms/progress";
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import {
  getGModuleDashboard,
  getGModuleFilters,
  getGSettings,
} from "~/zustand";
import { memo, useCallback, useEffect, useMemo, useRef } from "react";
import {
  CellClickedEvent,
  GridReadyEvent,
  SortChangedEvent,
} from "ag-grid-community";
import { STATUS_CODE } from "~/shared/constants";
import {
  escapeHtmlEntities,
  Number,
  sanitizeString,
  getFormat,
} from "~/helpers/helper";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import { getProjectListApi } from "../../redux/action/proDashAction";
import { useNavigate } from "@remix-run/react";
import { ProjectListTableDropdownItems } from "./ProjectListTableDropdownItems";
import { routes } from "~/route-services/routes";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { useModuleAccess } from "../../hook/useModuleAcces";
import { fnumProjectList } from "../utils/common-util";

interface ProjectListProps {
  setAddProjectOpen: (value: boolean) => void;
  search: string;
}

const ProjectList = ({ setAddProjectOpen, search }: ProjectListProps) => {
  const { _t } = useTranslation();

  const { date_format }: GSettings = getGSettings();

  const filterSrv: Partial<ProjectFilter> | undefined = getGModuleFilters() as
    | Partial<ProjectFilter>
    | undefined;
  const { formatter } = useCurrencyFormatter();

  const dateFormat = useDateFormatter();

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();

  const { module_access, hasEnoughAccessofProjectFinanceTabModule } =
    useModuleAccess();

  const { datasource, gridRowParams } = useTableGridData();

  const navigate = useNavigate();

  const MemoTooltip = memo(Tooltip);
  const MemoTypography = memo(Typography);
  const MemoAvatarProfile = memo(AvatarProfile);
  const MemoTag = memo(Tag);

  const getSanitizedText = useCallback(
    (text: string) => HTMLEntities.decode(sanitizeString(text)),
    []
  );

  const defaulProjectStatus = useMemo(() => {
    if (filterSrv?.tab === "open") {
      return "bidding,project_submittal,started,unscheduled,pending ";
    } else if (filterSrv?.tab === "completed") {
      return "completed";
    } else {
      return "";
    }
  }, [filterSrv?.tab]);

  const filter = useMemo(() => {
    return {
      start_date: filterSrv?.start_date || "",
      end_date: filterSrv?.end_date || "",
      status: filterSrv?.status ?? "0",
      customer: filterSrv?.customer || "",
      project_contacts: filterSrv?.project_contacts || "",
      project_manager: filterSrv?.project_manager || "",
      project_status: filterSrv?.project_status || defaulProjectStatus,
      project_type: filterSrv?.project_type || "",
      sales_rep: filterSrv?.sales_rep || "",
      tab: filterSrv?.tab || "all",
    };
  }, [filterSrv]);

  const fetchProjectList = useCallback(
    async (gridRowParams: IGridParamsCus) => {
      let gridData: { rowCount?: number; rowData: IProjectData[] } = {
        rowData: [],
      };
      const { changeGridParams, gridParams } = gridRowParams ?? {};
      const length = changeGridParams?.length ?? 0;

      const filter = previousValues.current?.filter
        ? JSON.parse(previousValues.current?.filter)
        : {};

      const search = previousValues.current.search;

      const tempFil: IProjectTempFil = {
        status: STATUS_CODE.ACTIVE,
        tab: "all",
      };
      if (filter?.tab) {
        tempFil.tab = filter.tab.toString() || "";
      }
      if (filter?.status !== undefined) {
        tempFil.status = filter.status;
      }
      if (filter?.project_contacts) {
        tempFil.project_contacts = filter.project_contacts;
      }
      if (filter?.sales_rep) {
        tempFil.sales_rep = filter.sales_rep;
      }
      if (filter?.project_manager) {
        tempFil.project_manager = filter.project_manager;
      }
      if (filter?.start_date) {
        tempFil.start_date = filter.start_date || "";
      }
      if (filter?.project_type) {
        tempFil.project_type = filter.project_type;
      }
      if (filter?.end_date) {
        tempFil.end_date = filter.end_date || "";
      }
      if (filter?.customer) {
        tempFil.customer = filter.customer;
      }
      if (filter?.project_status) {
        tempFil.project_status = filter.project_status;
      }
      if (filter?.status === STATUS_CODE.ALL) {
        delete tempFil.status;
      }

      let dataParams: IProjectListParmas = {
        search: escapeHtmlEntities(search),
        filter: tempFil,
        limit: length,
        page: changeGridParams?.start
          ? Math.floor(changeGridParams?.start / length)
          : 0,
        is_kanban: false,
      };
      if (search === "") {
        delete dataParams.search;
      }

      if (isEmpty(tempFil)) {
        delete dataParams.filter;
      }

      if (changeGridParams?.order_by_name) {
        dataParams.order_by_name = changeGridParams.order_by_name;
      }
      if (changeGridParams?.order_by_dir) {
        dataParams.order_by_dir = changeGridParams.order_by_dir;
      }

      try {
        gridParams?.api.hideOverlay();
        const resData = (await getProjectListApi(
          dataParams
        )) as IProjectListApiRes;

        const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
        if (resData?.data?.data?.length < length) {
          gridData = {
            ...gridData,
            rowCount: rowCount + (resData?.data?.data?.length ?? 0) - 1,
          };
        }

        gridData = { ...gridData, rowData: resData?.data?.data ?? [] };
        gridParams?.success(gridData);
        if (
          (!resData?.success || gridData.rowData.length <= 0) &&
          dataParams?.page === 0
        ) {
          gridParams?.api.showNoRowsOverlay();
        } else if (resData?.success && gridData.rowData.length > 0) {
          gridParams?.api.hideOverlay();
        }
      } catch (err) {
        gridParams?.success({ rowCount: 0, rowData: [] });
        gridParams?.api.showNoRowsOverlay();
        gridParams?.fail();
      }
    },
    []
  );

  const onGridReady = useCallback(
    (gridParams: GridReadyEvent) => {
      gridParams?.api?.setServerSideDatasource(datasource);
    },
    [datasource]
  );

  const onSortChanged = useCallback(
    (params: SortChangedEvent) => {
      params.api.setServerSideDatasource({ getRows: () => {} });
      params.api.setServerSideDatasource(datasource);
    },
    [datasource]
  );

  const previousValues = useRef({
    filter: JSON.stringify(filter),
    search,
  });

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      fetchProjectList(gridRowParams);
    }
  }, [gridRowParams]);

  // React to filter changes immediately
  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search,
    };

    if (!isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search, JSON.stringify(filter)]);

  const numFormatterWithoutCurrencyValues = useCallback(
    (value: number) => {
      const rounded = Math.round(Number(value ?? 0) / 100);

      return {
        originalVal: formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0"
        ).value_with_symbol,
        roundedVal: formatter(rounded !== 0 ? rounded?.toString() : "0").value,
      };
    },
    [formatter]
  );

  const columnDefs = useMemo(
    () => [
      {
        headerName: _t("Project"),
        field: "project_name",
        minWidth: 130,
        flex: 2,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const title = getSanitizedText(data?.project_name ?? "");

          return (
            <div className="flex items-center gap-2">
              <MemoTooltip title={_t("Project Color")} placement="top">
                <div className="!min-w-[10px] !max-w-[10px]">
                  <MemoTypography
                    className="h-2.5 w-2.5 !min-w-[10px] block rounded-full"
                    style={{
                      backgroundColor: data.project_color || "#000000",
                    }}
                  ></MemoTypography>
                </div>
              </MemoTooltip>
              <MemoTooltip title={title}>
                <MemoTypography className="table-tooltip-text text-center">
                  {title}
                </MemoTypography>
              </MemoTooltip>
            </div>
          );
        },
      },
      {
        headerName: "Project #",
        field: "project_id",
        minWidth: 120,
        maxWidth: 180,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const project_id = getSanitizedText(data.project_id ?? "");

          return data.project_id ? (
            <MemoTooltip title={project_id}>
              <MemoTypography className="table-tooltip-text">
                {project_id}
              </MemoTypography>
            </MemoTooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Complete (%)"),
        field: "progress",
        maxWidth: 135,
        minWidth: 135,
        headerClass: "ag-header-center",
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;

          return (
            <div className="text-center project-percent">
              <Progress
                size={27}
                type="circle"
                trailColor={"#00800080"}
                strokeWidth={9}
                strokeColor={"#008000"}
                percent={Math.floor(Number(data.progress) * 100)}
                format={() =>
                  `${fnumProjectList(Math.floor(Number(data.progress) * 100))}%`
                }
              />
            </div>
          );
        },
      },
      {
        headerName: _t("Project Amt."),
        field: "no_tax_contract_amount",
        maxWidth: 130,
        minWidth: 130,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        hide: !hasEnoughAccessofProjectFinanceTabModule,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const { originalVal: tooltipVal, roundedVal } =
            numFormatterWithoutCurrencyValues(
              Number(data.no_tax_contract_amount ?? 0)
            );

          return (
            <MemoTooltip title={`${tooltipVal}`}>
              <MemoTypography className="table-tooltip-text">
                {roundedVal}
              </MemoTypography>
            </MemoTooltip>
          );
        },
      },
      {
        headerName: _t("Est. Cost"),
        field: "budgeted",
        maxWidth: 120,
        minWidth: 100,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        hide: !hasEnoughAccessofProjectFinanceTabModule,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const { originalVal: tooltipVal, roundedVal } =
            numFormatterWithoutCurrencyValues(Number(data.budgeted ?? 0));

          return (
            <MemoTooltip title={`${tooltipVal}`}>
              <MemoTypography className="table-tooltip-text">
                {roundedVal}
              </MemoTypography>
            </MemoTooltip>
          );
        },
      },
      {
        headerName: _t("Committed"),
        field: "commited",
        maxWidth: 120,
        minWidth: 120,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        hide: !hasEnoughAccessofProjectFinanceTabModule,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const { originalVal: tooltipVal, roundedVal } =
            numFormatterWithoutCurrencyValues(Number(data.commited ?? 0));

          return (
            <MemoTooltip title={`${tooltipVal}`}>
              <MemoTypography className="table-tooltip-text">
                {roundedVal}
              </MemoTypography>
            </MemoTooltip>
          );
        },
      },
      {
        headerName: _t("Actual Cost"),
        field: "actual",
        maxWidth: 120,
        minWidth: 120,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        hide: !hasEnoughAccessofProjectFinanceTabModule,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const { originalVal: tooltipVal, roundedVal } =
            numFormatterWithoutCurrencyValues(Number(data.actual ?? 0));

          return (
            <MemoTooltip title={`${tooltipVal}`}>
              <MemoTypography className="table-tooltip-text">
                {roundedVal}
              </MemoTypography>
            </MemoTooltip>
          );
        },
      },
      {
        headerName: _t("Invoiced"),
        field: "invoiced",
        maxWidth: 120,
        minWidth: 120,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        hide: !hasEnoughAccessofProjectFinanceTabModule,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const { originalVal: tooltipVal, roundedVal } =
            numFormatterWithoutCurrencyValues(Number(data.invoiced ?? 0));

          return (
            <MemoTooltip title={`${tooltipVal}`}>
              <MemoTypography className="table-tooltip-text">
                {roundedVal}
              </MemoTypography>
            </MemoTooltip>
          );
        },
      },
      {
        headerName: _t("Paid"),
        field: "paid_amount",
        maxWidth: 120,
        minWidth: 120,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        hide: !hasEnoughAccessofProjectFinanceTabModule,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const { originalVal: tooltipVal, roundedVal } =
            numFormatterWithoutCurrencyValues(Number(data.paid_amount ?? 0));

          return (
            <MemoTooltip title={`${tooltipVal}`}>
              <MemoTypography className="table-tooltip-text">
                {roundedVal}
              </MemoTypography>
            </MemoTooltip>
          );
        },
      },
      {
        headerName: _t("PM"),
        field: "project_manager_name",
        maxWidth: 60,
        minWidth: 60,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;

          const managerName = getSanitizedText(
            data?.project_manager_name ?? ""
          );

          return (
            <>
              {data?.project_manager_name !== "" ? (
                <MemoTooltip title={managerName}>
                  <div>
                    <MemoAvatarProfile
                      user={{
                        name: managerName,
                        image: data?.profile_image,
                      }}
                      className="m-auto"
                      iconClassName="text-[11px] font-semibold"
                    />
                  </div>
                </MemoTooltip>
              ) : (
                <div>-</div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("Customer"),
        field: "customer_name",
        maxWidth: 90,
        minWidth: 90,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          const customerName = getSanitizedText(data?.customer_name ?? "");

          return (
            <>
              {!!customerName && customerName !== "" ? (
                <MemoTooltip title={customerName}>
                  <div>
                    <MemoAvatarProfile
                      user={{
                        name: customerName,
                        image: "",
                      }}
                      className="m-auto"
                      iconClassName="text-[11px] font-semibold"
                    />
                  </div>
                </MemoTooltip>
              ) : (
                <div>-</div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("Type"),
        field: "project_type_name",
        maxWidth: 120,
        minWidth: 120,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          let moduleStatus: ModuleStatus | undefined =
            gModuleDashboard?.module_setting?.module_status?.find(
              (moduleStatus: ModuleStatus) => {
                return moduleStatus?.name === data?.project_type_name;
              }
            );

          const statusName = getSanitizedText(moduleStatus?.name ?? "");
          const proTypeName = getSanitizedText(data?.project_type_name ?? "");

          return (
            <>
              {moduleStatus ? (
                <MemoTag
                  color={
                    moduleStatus?.status_color
                      ? `${moduleStatus?.status_color}24`
                      : "#fff"
                  }
                  className="mx-auto text-13 type-badge common-tag w-[100px]"
                  style={{
                    color: moduleStatus?.status_color,
                  }}
                >
                  {statusName}
                </MemoTag>
              ) : data?.project_type_name ? (
                <MemoTooltip title={proTypeName}>
                  <div className="text-center overflow-hidden">
                    <MemoTag
                      color="#EBF1F9"
                      className="!text-primary-900 mx-auto text-13 type-badge common-tag"
                    >
                      {proTypeName}
                    </MemoTag>
                  </div>
                </MemoTooltip>
              ) : (
                <div>-</div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("Status"),
        field: "project_status_name",
        maxWidth: 120,
        minWidth: 120,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          let moduleStatus: ModuleStatus | undefined =
            gModuleDashboard?.module_setting?.module_status?.find(
              (moduleStatus: ModuleStatus) => {
                return moduleStatus?.name === data?.project_status_name;
              }
            );

          const statusName = getSanitizedText(moduleStatus?.name ?? "");

          return (
            <>
              {moduleStatus ? (
                <MemoTag
                  color={
                    moduleStatus?.status_color
                      ? `${moduleStatus?.status_color}24`
                      : "#fff"
                  }
                  className="mx-auto text-13 type-badge common-tag w-[100px]"
                  style={{
                    color: moduleStatus?.status_color,
                  }}
                >
                  {statusName}
                </MemoTag>
              ) : (
                "-"
              )}
            </>
          );
        },
      },
      {
        headerName: "",
        field: "",
        maxWidth: 80,
        minWidth: 80,
        suppressMenu: true,
        cellClass: "!cursor-auto",
        cellRenderer: (params: IProjectTableCellRenderer) => {
          const { data } = params;
          return (
            <div className="flex items-center gap-3 justify-center">
              <MemoTooltip
                className={`${
                  data.start_date || data.end_date ? "visible" : "invisible"
                }`}
                title={
                  <>
                    {data.start_date && (
                      <div>
                        {_t("Start Date")}:{" "}
                        {dateFormat({
                          date: data?.start_date,
                          format: getFormat(date_format),
                          dateFormat: "dd/MMM/yyyy",
                        })}
                      </div>
                    )}
                    {data.end_date && (
                      <div>
                        {_t("End Date")}:{" "}
                        {dateFormat({
                          date: data?.end_date,
                          format: getFormat(date_format),
                          dateFormat: "dd/MMM/yyyy",
                        })}
                      </div>
                    )}
                  </>
                }
              >
                <FontAwesomeIcon
                  className="w-4 h-4 text-primary-900"
                  icon="fa-regular fa-calendar-days"
                />
              </MemoTooltip>
              <ProjectListTableDropdownItems
                data={data}
                className="m-0 hover:!bg-[#0000000f]"
                iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                contentClassName="menu-h-scroll"
                refreshTable={refreshAgGrid}
              />
            </div>
          );
        },
      },
    ],
    [
      numFormatterWithoutCurrencyValues,
      gModuleDashboard?.module_setting?.module_status,
      refreshAgGrid,
    ]
  );

  return (
    <div
      className={`ag-grid-cell-pointer ag-theme-alpine list-view-table ${
        module_access === "read_only"
          ? "md:h-[calc(100dvh-308px)] h-[calc(100dvh-326px)]"
          : "md:h-[calc(100dvh-270px)] h-[calc(100dvh-304px)]"
      }`}
    >
      <DynamicTable
        columnDefs={columnDefs}
        onGridReady={onGridReady}
        onSortChanged={onSortChanged}
        suppressAggFuncInHeader
        onCellClicked={(params: CellClickedEvent) => {
          const column = params.column;
          if (
            column.getColDef().field !== "" &&
            column.getColDef().field !== "email"
          ) {
            navigate(
              `${routes.MANAGE_PROJECT.url}/${params?.data?.id}${
                !hasEnoughAccessofProjectFinanceTabModule ? "/details" : ""
              }`
            );
          }
        }}
        noRowsOverlayComponent={() => (
          <NoRecords
            rootClassName="w-full max-w-[280px]"
            image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
            text={
              module_access === "full_access" ||
              module_access === "own_data_access" ? (
                <div>
                  <MemoTypography
                    onClick={() => setAddProjectOpen(true)}
                    className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                  >
                    {_t("Click here")}
                  </MemoTypography>
                  <MemoTypography className="sm:text-base text-xs text-black font-semibold">
                    {_t(" to Create a New Record")}
                  </MemoTypography>
                </div>
              ) : (
                <MemoTypography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </MemoTypography>
              )
            }
          />
        )}
      />
    </div>
  );
};

export default memo(ProjectList);
