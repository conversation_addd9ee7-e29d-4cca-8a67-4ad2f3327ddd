interface IProjectsbyStatusProgressProps {
  name: string;
  record: string;
  amount: string;
  backgroundColor: string;
  statusValue: string;
}

interface IIconProps {
  icon?: IconName | [IconPrefix, IconName] | IconLookup;
  containerClassName?: string;
  id: string;
  colors?: Array<string>;
}

interface ISummaryInfoCardListProps {
  title?: string;
  children?: React.ReactNode | React.ReactNode[];
  iconProps?: IIconProps;
  contantWidth?: string;
  iconClassName?: string;
  containerClassName?: string;
}

interface IProjectsbyStatusProgressProps {
  name: string;
  record: string;
  amount: string;
  backgroundColor: string;
  statusValue: string;
}
interface IProjectFilesDefaultViewProps {
  title?: string;
  loadMore?: () => void;
  hasMore?: boolean;
  isfileLoading?: boolean;
  mobileMenu?: IFilePhotoLeftSideProps["mobileMenu"];
  infiniteScrollHideLoadingComponent?: boolean;
  isInfiniteScrollLoading?: boolean;
  fileList?: IFileListProps;
  changeAddOrEditFileOpen?: (
    { addOrEditFileOpen, editView }: IActionPayloadSetAddOrEditFileOpenRedux,
    imageData: FilePhotoRightDetail
  ) => void;
  changeMarkupModelOpen?: (
    value: boolean | "previewUrl",
    imageData: FilePhotoRightDetail
  ) => void;
  changeConfirmModelOpen?: (
    value: boolean,
    imageData: FilePhotoRightDetail
  ) => void;
  changeSendEmailDrawerOpen?: {
    (value: boolean, imageData: FilePhotoRightDetail): void;
  };
  handleClose?: IFilePhotoLeftSideProps["handleClose"];
  markupedFileData?: Partial<IFile>;
  selectedFiles: FilePhotoRightDetail[];
}
interface IFinancialRecordsOptionsProps {
  financialTotal?: number;
  title?: string;
  children?: React.ReactNode | React.ReactNode[];
  iconProps: IIconProps;
  checked: boolean;
  onChange: () => void;
  scrollToTable: () => void;
}

interface FinancialRecordsOptionsListProps {
  visibleTables: VisibleTables;
  onToggle: (key: keyof VisibleTables) => void;
  scrollToTable: (key: keyof VisibleTables) => void;
}

interface FinancialData {
  [key: string]: {
    length: number;
  };
}

interface VisibleTables {
  bills: boolean;
  change_orders: boolean;
  estimate: boolean;
  expenses: boolean;
  invoices: boolean;
  payments: boolean;
  purchase_order: boolean;
  sub_contarct: boolean;
  work_orders: boolean;
}

interface IProjectStatusbarProps {
  inputValues: Partial<IProjectDetails>;
  setInputValues: React.Dispatch<
    React.SetStateAction<Partial<IProjectDetails>>
  >;
  setProjectStatus?: React.Dispatch<React.SetStateAction<string | undefined>>;
  handleUpdateField: (data: Partial<IProjectDetails>) => Promise<void>;
  handleChangeFieldStatus: (data: IFieldStatus) => void;
  projectStatusList: IProjectStatusList[];
  isReadOnly: boolean;
  loadingStatus?: Array<IFieldStatus>;
}

interface IProjectDetailsTopBarProps {
  inputValues: Partial<IProjectDetails>;
  setInputValues: React.Dispatch<
    React.SetStateAction<Partial<IProjectDetails>>
  >;
  sidebarCollapse?: boolean;
  onReloadDetails: () => void;
  handleChangeFieldStatus: (data: IFieldStatus) => void;
  loadingStatus: Array<IFieldStatus>;
}

interface BillData {
  bill_id: number;
  total: string;
  supplier_company_name: string;
  order_date: string;
  due_date: string;
  company_bill_id: string;
  payments: string;
  notes: string;
  number_of_bill: string;
}

interface ChangeOrder {
  company_order_id: string;
  change_order_id: number;
  total: string;
  billing_status_name: string;
  order_date: string;
  subject: string;
  billing_status: number;
  count_in_contract_amount: number;
  status_color: string;
}

interface Estimate {
  approval_type_key: string;
  approval_type_name: string;
  company_estimate_id: string;
  customer_name: string;
  description: string;
  estimate_date: string;
  estimate_id: number;
  status_color: string;
  title: string;
  total: string;
}

interface Expense {
  expense_id: number;
  expense_name: string;
  amount: string;
  total: string;
  expense_date: string;
  vendor_name: string;
}

interface WorkOrder {
  company_order_id: number;
  no_markup_total: string;
  order_date: string;
  status_color: string;
  status_name: string;
  total: string;
  work_order: string;
  work_order_id: string;
  work_order_status: string;
}

interface SubCon {
  prefix_sub_contract_id: string;
  date: string;
  subject: string;
  sub_contractor_name: string;
  total: string;
  sub_contract_id: string;
}

interface PurchaseOrders {
  prefix_purchase_order_id: string;
  po_date: string;
  subject: string;
  supplier_name: string;
  approval_status_name: string;
  status_color: string;
  total: string;
  purchase_order_id: string;
}

interface Payments {
  prefix_company_invoice_id: string;
  payment_date: string;
  payment_type_name: string;
  approval_status_name: string;
  status_color: string;
  amount: string;
  payment_id: number;
}

interface PurchaseOrder {
  company_purchase_order_id: number;
  order_date: string;
  subject: string;
  supplier_company_name: string;
  status_color: string;
  billing_status_name: string;
  total: string;
  purchase_order_id: number;
}

interface SubContract {
  prefix_sub_contract_id: number;
  date: string;
  subject: string;
  sub_contractor_name: string;
  total: string;
  sub_contract_id: number;
}

interface WorkOrders {
  work_order_id: number;
  order_date: string;
  work_order: string;
  status_name: string;
  total: string;
  status_color: string;
}

interface Invoices {
  prefix_company_invoice_id: string;
  invoice_date: string;
  due_date: string;
  status_color: string;
  approval_type_name: string;
  total: string;
  paymentTotal: string;
  invoice_id: number;
}

interface TableProps {
  fetchAllProjectFinancialModules: (
    showMore: boolean,
    module_key: string[]
  ) => void;
  dataLimit: number;
  openTable?: string;
  refresh?: boolean;
  formattedTotalAmount: (amount: number) => string;
}

interface DocumentTabProps {
  isInitialLoad: boolean;
}

interface DocumentTableProps {
  fetchAllProjectDocumentsModules: (
    showMore: boolean,
    module_key: array[]
  ) => void;
  dataLimit: number;
}

interface TimeCardTableProps {
  dataLimit: number;
  fetchData: () => void;
}

interface TimeCardStatsProps {
  selectedPeriod: string;
  setSelectedPeriod: React.Dispatch<React.SetStateAction<string>>;
}

interface LaborCostHrsCodeProps {
  laborCostHrsTab: string;
  setLaborCostHrsTab: React.Dispatch<React.SetStateAction<string>>;
}

interface HoursMoneyProps {
  selectedHrsMoneyTab: string;
  setSelectedHrsMoneyTab: React.Dispatch<React.SetStateAction<string>>;
}

interface TimeTabProps {
  selectedPeriod: string;
  setSelectedPeriod: React.Dispatch<React.SetStateAction<string>>;
  selectedHrsMoneyTab: string;
  setSelectedHrsMoneyTab: React.Dispatch<React.SetStateAction<string>>;
  laborCostHrsTab: string;
  setLaborCostHrsTab: React.Dispatch<React.SetStateAction<string>>;
  fetchData: () => void;
}

interface ReportTabProps {
  laborRate: string;
  setLaborRate: React.Dispatch<React.SetStateAction<string>>;
  markup: string;
  setMarkup: React.Dispatch<React.SetStateAction<string>>;
  generateFrom: string;
  setGenerateFrom: React.Dispatch<React.SetStateAction<string>>;
  selectedReport: string;
  setSelectedReport: React.Dispatch<React.SetStateAction<string>>;
}

interface ReportAgGridProps {
  laborRate: string;
  setLaborRate: React.Dispatch<React.SetStateAction<string>>;
  markup: string;
  setMarkup: React.Dispatch<React.SetStateAction<string>>;
  generateFrom: string;
  setGenerateFrom: React.Dispatch<React.SetStateAction<string>>;
  selectedReport: string;
}

interface JobCostActualReportProps {
  selectedReport: string;
  AGformatNumber: (
    total: number | string | null | undefined,
    is_currency: number
  ) => void;
  currencyFormatter: (params: ValueFormatterParams) => void;
  setIsCommonModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedSubject: React.Dispatch<
    React.SetStateAction<IProjectJobCostActualData | null>
  >;
}

interface BudgetActualLaborReportProps {
  selectedReport: string;
  AGformatNumber: (
    total: number | string | null | undefined,
    is_currency: number
  ) => void;
  currencyFormatter: (params: ValueFormatterParams) => void;
  setIsCommonModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedSubject: React.Dispatch<
    React.SetStateAction<IProjectLaborBudgetData | null>
  >;
}

interface JobCostCommittedReportProps {
  selectedReport: string;
  AGformatNumber: (
    total: number | string | null | undefined,
    is_currency: number
  ) => void;
  currencyFormatter: (params: ValueFormatterParams) => void;
  markup: string;
  setIsCommonModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedSubject: React.Dispatch<
    React.SetStateAction<IProjectJobCostDetailsData | null>
  >;
}

interface ReportTopBarProps {
  selectedReport: string;
  setSelectedReport: React.Dispatch<React.SetStateAction<string>>;
}
interface ProcurementTopBarProps {
  procurementData?: IProcurement[];
  costTypeData: costTypeDataObj;
}

interface ProcurementTableProps {
  procurementData: IProcurement[];
  procurementGenerateData: IProcurementGenerate | {};
  costTypeData: costTypeDataObj;
}

interface CostProcurementSourceProps {
  procurementData?: IProcurement[];
}

interface costTypeDataObj {
  assigned: number;
  unassigned: number;
  grandTotal: number;
}

interface CostsBasedTypeProps {
  procurementData: IProcurement[];
}

interface TasksAssignmentsProps {
  procurementData: IProcurement[];
  mappedCostTypeData: Record<string, number>;
}

interface TimeCardStatsChartTooltipProps {
  w: { globals: { initialSeries: any[]; seriesTotals: number[] } };
  dataPointIndex: number;
}

interface TimeCardData {
  cost_code_id: string;
  code_is_deleted: number;
  cost_code_name: string;
  detail_id: number;
  employee: string;
  entry_type: string;
  has_estimate_item: string;
  modified_detail_id: string | number;
  ot_hours: string;
  overtime_code: string;
  overtime_cost: string;
  record_date: string;
  regular_cost: string;
  regular_hours: string;
  tc_date: string;
  time_card_type: string;
  timecard_id: string;
  total_cost: string;
  unit_wage: string;
  wage: string;
  work_mins: string;
}

interface EstimatedCardData {
  cost_code_id: string;
  cost_code_name: string;
  quantity: string;
  total: string;
  item_total: string;
}

interface DailyLogData {
  arrival_date: string;
  arrival_time: string;
  generated_by: string;
  log_id: string;
  project_id: string;
  task_performed: string;
  user_avtar: string;
  weather_notes: string;
}

interface DocumentsData {
  attached_files: string;
  document_html: string;
  authorized_by: string;
  company_document_id: string;
  company_id: string;
  contact_name: string;
  date_added: string;
  document_id: string;
  document_name: string;
  document_status_key: string;
  document_type: string;
  module_id: string;
  module_name: string;
  signature: string;
  status: string;
  status_name: string;
  time_added: string;
  user_id: string;
  default_status_color: string;
}

interface ChecklistData {
  assignee_name: string;
  checklist_form_name: string;
  checklist_id: string;
  company_checklist_id: string;
  date_added: string;
  emp_init_name: string;
  form_name: string;
  form_status_key: string;
  locked_username: string;
  status_name: string;
  default_status_color: string;
}

interface IncidentsData {
  company_incident_id: string;
  description: string;
  incident_date: string;
  incident_id: string;
  incident_type_name: string;
  notified_date: string;
  occurrence_type: string;
  primary_id: string;
  txn_type: string;
  type: string;
  type_name: string;
  any_injuries: string;
  osha_violence: string;
  osha_violence: string;
}

interface InspectionData {
  inspection_date: string;
  inspection_id: string;
  inspection_key: string;
  inspection_status_name: string;
  inspection_time: string;
  inspection_type: string;
  status_color: string;
}

interface RfiNoticesData {
  created_by: string;
  date_added: string;
  note_id: string;
  note_status: string;
  status_name: string;
  subject: string;
  user_avtar: string;
  default_status_color: string;
}

interface NotesData {
  created_by: string;
  date_added: string;
  note_id: string;
  note_status: string;
  status_name: string;
  subject: string;
  user_avtar: string;
  default_status_color: string;
}

interface PermitsData {
  agency: string;
  agency_contact_id: string;
  agency_name: string;
  approval_date: string;
  company_id: string;
  company_inspection_id: string;
  date_added: string;
  date_modified: string;
  demo_data: string;
  expire_date: string;
  inspection_id: string;
  is_deleted: string;
  is_notes_convert: string;
  is_project_template: string;
  notes: string;
  parent_permit_id: string;
  permission: string;
  permit_date: string;
  permit_fees: string;
  permit_id: string;
  permit_type: string;
  permit_type_key: string;
  permit_type_name: string;
  project_id: string;
  project_template_id: string;
  pulled_by_date: string;
  user_id: string;
}

interface ServiceTicketData {
  count_in_contract_amount: string;
  job_status: string;
  service_date: string;
  service_ticket_id: string;
  service_ticket_status_type: string;
  service_time: string;
  st_status: string;
  status_color: string;
  title: string;
  total: string;
}

interface SubmittalsData {
  company_submittal_id: string;
  date_added: string;
  date_received: string;
  due_date: string;
  prefix_company_submittal_id: string;
  status_color: string;
  status_name: string;
  submittal_id: string;
  submittal_status_key: string;
  title: string;
}

interface TodosData {
  due_date: string;
  priority_name: string;
  status_name: string;
  task_name: string;
  todo_date: string;
  todo_id: string;
  todo_status_key: string;
  default_status_color: string;
}
