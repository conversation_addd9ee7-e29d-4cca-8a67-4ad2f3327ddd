// hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { type GridReadyEvent, type SortChangedEvent } from "ag-grid-community";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { useEffect, useMemo, useRef } from "react";
import { getWorkOrderListApi } from "../../redux/action/workorderDashAction";
import { routes } from "~/route-services/routes";
import { getGConfig, getGModuleDashboard, getGModuleFilters } from "~/zustand";
import { WorkOrderTableDropdownItems } from "./WorkOrderTableDropdownItems";
import isEqual from "lodash/isEqual";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { defaultConfig } from "~/data";
import {
  escapeHtmlEntities,
  formatAmount,
  sanitizeString,
} from "~/helpers/helper";
import Status from "~/components/common/status";

interface WorkOrderListProps {
  setDrawerOpen: (value: boolean) => void;
  fetchKanbanWorkOrderList?: Function;
}

let timeout: NodeJS.Timeout | null;
const WorkOrderList = ({
  setDrawerOpen,
  fetchKanbanWorkOrderList,
}: WorkOrderListProps) => {
  const { _t } = useTranslation();
  const { datasource, gridRowParams } = useTableGridData();
  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const { formatter } = useCurrencyFormatter();

  const agGridRef = useRef<ExtendedAgGridReact | null>(null);
  const filterSrv: Partial<WorkOrdersFilter> | undefined =
    getGModuleFilters() as Partial<WorkOrdersFilter> | undefined;
  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    defaultConfig.work_order_module
  );

  const { module_access }: GConfig = getGConfig();

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const filter = useMemo(() => {
    return {
      start_date: filterSrv?.start_date || "",
      end_date: filterSrv?.end_date || "",
      status: filterSrv?.status || "",
      project: filterSrv?.project || "",
      module_status: filterSrv?.module_status ?? "0",
      is_my_wo: filterSrv?.is_my_wo || "0",
    };
  }, [filterSrv]);

  const previousValues = useRef({
    filter: JSON.stringify(filter),
    search,
  });

  const getWorkOrderList = async () => {
    let gridData: { rowCount?: number; rowData: IWorkOrderData[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const limit = changeGridParams?.length ?? 0;
    const { start, order_by_name, order_by_dir } = changeGridParams || {};

    const tempFil: IWorkOrderTempFil = {
      is_my_wo: filter.is_my_wo,
      module_status: filter.module_status,
    };
    if (filter?.start_date) {
      tempFil.start_date = filter.start_date || "";
    }
    if (filter?.end_date) {
      tempFil.end_date = filter.end_date || "";
    }
    if (filter?.status && filter?.status !== "" && filter?.status != "0") {
      tempFil.status = filter.status;
    }
    if (filter?.module_status) {
      tempFil.module_status = filter.module_status;
    }
    if (filter?.project) {
      tempFil.project = filter.project;
    }
    // if (filter?.module_status === STATUS_CODE.ALL) {
    //   delete tempFil.module_status;
    // }

    let dataParams: IWorkOrderListParmas = {
      limit,
      filter: tempFil,
      ignore_filter: 1,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
      search: escapeHtmlEntities(search),
      start: start ? Math.floor(start / limit) : 0,
    };
    if (search === "") {
      delete dataParams.search;
    }

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_name;
    }

    try {
      gridParams?.api.hideOverlay();
      const resData = (await getWorkOrderListApi(
        dataParams
      )) as IWorkOrderListApiRes;
      try {
        const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
        if (resData?.data?.workOrders?.length < length) {
          gridData = {
            ...gridData,
            rowCount: rowCount + (resData?.data?.workOrders?.length ?? 0) - 1,
          };
        }
        gridData = { ...gridData, rowData: resData?.data?.workOrders ?? [] };
        gridParams?.success(gridData);

        if (
          (!resData?.success || gridData.rowData.length <= 0) &&
          dataParams?.start === 0
        ) {
          gridParams?.api.showNoRowsOverlay();
        } else if (resData?.success && gridData.rowData.length > 0) {
          gridParams?.api.hideOverlay();
        }
      } catch (err) {
        notification.error({
          description: (err as Error)?.message,
        });
      }
    } catch (error) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        getWorkOrderList();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search,
    };

    if (filterSrv && !isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [JSON.stringify(filter), search]);

  const columnDefs = [
    {
      headerName: _t("WO") + " #",
      field: "work_order_id",
      minWidth: 120,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        const id = data.prefix_company_work_order_id || data.company_order_id;
        return (
          <Tooltip title={HTMLEntities.decode(sanitizeString(id))}>
            <Typography className="table-tooltip-text text-center">
              {id ? (
                HTMLEntities.decode(sanitizeString(id ?? "-"))
              ) : (
                <div className="text-center">-</div>
              )}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Subject"),
      field: "subject",
      minWidth: 140,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        const subject = HTMLEntities.decode(sanitizeString(data.subject));
        return (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text text-center">
              {subject ? subject : <div className="text-center">-</div>}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      sortable: true,
      minWidth: 140,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        const projectName = HTMLEntities.decode(
          sanitizeString(data.project_name)
        );
        return (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text text-center">
              {projectName ? projectName : <div className="text-center">-</div>}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Customer"),
      field: "customer_name",
      sortable: true,
      minWidth: 140,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        const customer = HTMLEntities.decode(
          sanitizeString(data?.customer_name)
        );

        return (
          <Tooltip title={customer}>
            <Typography className="table-tooltip-text text-center">
              {customer ? customer : <div className="text-center">-</div>}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Service Date"),
      field: "order_date",
      sortable: true,
      maxWidth: 135,
      minWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        return (
          <>
            {data.order_date ? (
              <DateTimeCard format="date" date={data.order_date} />
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Assigned To"),
      field: "assigned_to",
      sortable: false,
      maxWidth: 120,
      minWidth: 120,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        const assignees = data?.assignees_to || [];
        return (
          <>
            {assignees.length > 0 ? (
              <AvatarGroup
                max={{
                  count: 1,
                  style: {
                    color: "#223558",
                    backgroundColor: "#ECF1F9",
                  },
                }}
                size={24}
                className="flex justify-center"
                prefixCls="multi-avatar-scroll"
              >
                {assignees.map((items: IAssigneesTo, index: number) => {
                  const dName = HTMLEntities.decode(
                    sanitizeString(items.assignee_name)
                  );
                  const assignedToNameOnlyName = HTMLEntities.decode(
                    sanitizeString(items.assigned_to_name_only)
                  );

                  return (
                    <div
                      key={index}
                      className={`flex items-center ${
                        index === 0 ? "" : "gap-2 py-0.5 px-1"
                      }`}
                    >
                      {index === 0 ? (
                        <Tooltip title={dName} placement="top">
                          <div>
                            <AvatarProfile
                              user={{
                                name: dName,
                                image: items.image,
                              }}
                              iconClassName="text-[11px] font-semibold"
                            />
                          </div>
                        </Tooltip>
                      ) : (
                        <Tooltip title={dName} placement="top">
                          <div className="p-1 flex items-center gap-1">
                            <AvatarProfile
                              user={{
                                name: dName,
                                image: items.image,
                              }}
                              iconClassName="text-[11px] font-semibold"
                            />
                            <Typography className="max-w-[160px] truncate block">
                              {items.assigned_to_name_only?.trim() !== ""
                                ? assignedToNameOnlyName
                                : dName}
                            </Typography>
                          </div>
                        </Tooltip>
                      )}
                    </div>
                  );
                })}
              </AvatarGroup>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Response"),
      field: "response",
      sortable: true,
      minWidth: 110,
      maxWidth: 110,
      suppressMovable: false,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        const { data } = params;
        return (
          <div>
            {data.response !== null ? (
              <Tooltip
                title={data.response === 1 ? _t("Accepted") : _t("Rejected")}
              >
                <FontAwesomeIcon
                  className={`h-4 w-4 ${
                    data.response === 1 ? "text-[#4FB91D]" : "text-[#E25A32]"
                  }`}
                  icon={
                    data.response === 1
                      ? "fa-solid fa-circle-check"
                      : "fa-solid fa-circle-xmark"
                  }
                />
              </Tooltip>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </div>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellStyle: { textAlign: "right" },
      headerClass: "ag-header-right",
      valueGetter: (data: IWorkOrderTableCellRenderer) => {
        let value = Number((data.data.total || 0) / 100);

        return formatter(formatAmount(value, { isDashboard: true }))
          .value_with_symbol;
      },
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Status"),
      field: "work_order_status_name",
      sortable: true,
      maxWidth: 120,
      minWidth: 120,
      suppressMovable: false,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: (params: IWorkOrderTableCellRenderer) => {
        let moduleStatus: ModuleStatus | undefined =
          gModuleDashboard?.module_setting?.module_status?.find(
            (moduleStatus: ModuleStatus) => {
              return (
                moduleStatus?.item_id ===
                params?.data?.work_order_status?.toString()
              );
            }
          );
        return (
          <div className="text-center mx-auto max-w-[120px]">
            {moduleStatus ? (
              <Status
                status={moduleStatus?.name ?? ""}
                statusBg={
                  moduleStatus?.status_color
                    ? `${moduleStatus?.status_color}24`
                    : "#fff"
                }
                color={moduleStatus?.status_color}
              />
            ) : (
              <div className="table-tooltip-text text-center">-</div>
            )}
          </div>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellStyle: { textAlign: "center" },
      cellClass: "!cursor-auto",
      cellRenderer: ({ data }: IWorkOrderTableCellRenderer) =>
        (module_access === "full_access" ||
          module_access === "own_data_access") &&
        data ? (
          <WorkOrderTableDropdownItems
            data={data}
            refreshTable={() => {
              refreshAgGrid();
              fetchKanbanWorkOrderList?.();
            }}
            isCallApi={true}
          />
        ) : null,
    },
  ];

  return (
    <div
      className={`list-view-table ag-grid-cell-pointer ag-theme-alpine ${
        module_access === "read_only"
          ? "md:h-[calc(100dvh-304px)] h-[calc(100dvh-334px)]"
          : "md:h-[calc(100dvh-270px)] h-[calc(100dvh-294px)]"
      }`}
    >
      <DynamicTable
        columnDefs={columnDefs}
        onGridReady={onGridReady}
        onSortChanged={onSortChanged}
        ref={agGridRef}
        className="static-table"
        noRowsOverlayComponent={() => (
          <NoRecords
            rootClassName="w-full max-w-[280px]"
            image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
            imageWSize="280"
            imageHSize="227"
            text={
              module_access === "full_access" ||
              module_access === "own_data_access" ? (
                <div>
                  <Typography
                    onClick={() => setDrawerOpen(true)}
                    className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                  >
                    {_t("Click here")}
                  </Typography>
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t(" to Create a New Record")}
                  </Typography>
                </div>
              ) : (
                <Typography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </Typography>
              )
            }
          />
        )}
        enableOpenInNewTab={true}
        generateOpenInNewTabUrl={(data: { work_order_id?: number }) =>
          `${routes.MANAGE_WORKORDER.url}/${data?.work_order_id}`
        }
        restrictOpenInNewTabFields={["email"]}
      />
    </div>
  );
};

export default WorkOrderList;
