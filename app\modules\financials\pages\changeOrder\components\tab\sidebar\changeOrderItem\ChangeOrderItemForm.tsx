// Hook
import { useTranslation } from "~/hook";
import { type RadioChangeEvent } from "antd";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import SelectCostCode from "~/shared/components/molecules/selectCostCode/SelectCostCode";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// Organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import {
  FormEvent,
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from "react";
import { IChangeOrderItemProps } from "./type";
import { getGConfig, getGSettings, getGTypes, useGModules } from "~/zustand";
import { defaultConfig } from "~/data";
import { useFormik } from "formik";
import * as Yup from "yup";
import { IChangeOrderDetailState } from "../../../../redux/types";
import { useAppSelector } from "~/modules/financials/pages/changeOrder/redux/store";
import {
  floatNumberRegex,
  getCOModuleName,
  wholeNumberRegex,
} from "../../../../utils/helpers";
import isEqual from "lodash/isEqual";
import {
  filterOptionBySubstring,
  getItemTypeIcon,
  onKeyDownCurrency,
  roundToAmount,
} from "~/shared/utils/helper/common";
import isEmpty from "lodash/isEmpty";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import debounce from "lodash/debounce";
import { getItemVariants } from "../../../../redux/action/changeOrderItemsActions";
import { isValidId } from "~/modules/financials/pages/estimates/utils/common";

const validationSchema = Yup.object().shape({
  subject: Yup.string().required("This field is required."),
  item_type: Yup.number().required("This field is required."),
  item_id: Yup.number().optional(),
  quantity: Yup.number().optional(),
  unit_cost: Yup.number().required("This field is required."),
  description: Yup.string().optional().default(""),
  internal_notes: Yup.string().optional().default(""),
  is_markup_percentage: Yup.number().optional(),
});

type FormikSubmitEvent = FormEvent<HTMLFormElement> & {
  nativeEvent: { submitter?: HTMLButtonElement };
};

const calculateMarkupAmount = (
  unitCost: number,
  quantity: number,
  markup: number,
  isPercentage: number
) => {
  return isPercentage === 1
    ? undefined
    : (markup - unitCost * quantity).toString();
};

const ChangeOrderItemForm = ({
  changeOrderItemShow,
  setChangeOrderItemShow,
  setChangeOrderItemToView,
  isViewOnly = false,
  formData: _formData,
  onSubmit: onSubmit,
  currentType,
  itemTypes: markupItemTypes,
  setIsExistingLoading = () => {},
  setConfirmSaveData = () => {},
  confirmSaveData = { rid: 0 },
  isExistingLoading = false,
  dollarValues,
  setDollarValues = () => {},
  selectedSection,
  filteredItems,
  isNewItem,
}: IChangeOrderItemProps) => {
  const { _t } = useTranslation();
  const gSettings: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const currentCurrency = gSettings?.currency_symbol ?? "$";

  const [formEvent, setFormEvent] = useState<FormikSubmitEvent | null>(null);
  const [itemType, setItemType] = useState<boolean>(false);
  const [variantOptions, setVariantOptions] = useState<VariaonsofItem[]>([]);
  const [variantloading, setVariantloading] = useState(false);
  const items = selectedSection?.items;
  const { details }: IChangeOrderDetailState = useAppSelector((state) => {
    return state.changeOrderDetails;
  });
  const [submitting, setSubmitting] = useState<
    "saving" | "savingWithoutClose" | null
  >();
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const skipClose = useRef(false);

  const initialDollarValues = useRef(
    _formData
      ? {
          unit_cost: (Number(_formData.unit_cost) || 0) / 100,
          markup: _formData.is_markup_percentage
            ? Number(_formData.markup)
            : (_formData.markup || 0) / 100,
        }
      : {}
  );

  const isNavigationRef = useRef(false);

  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [totalToDisplay, setTotalToDisplay] = useState<number>(0);
  const { is_cidb_auto_save } = gSettings;
  const initialValues = useMemo(() => {
    const defaultMarkupItem =
      markupItemTypes?.find((item) => item.name === "undefined")?.mark_up || 0;

    const defaultMarkup = isNaN(Number(defaultMarkupItem))
      ? ""
      : Number(defaultMarkupItem);
    setDollarValues({
      markup: _formData
        ? _formData?.is_markup_percentage !== 1
          ? _formData?.markup === ""
            ? ""
            : String(Number(_formData?.markup) / 100)
          : String(_formData?.markup)
        : defaultMarkup,
      unit_cost: _formData ? String(_formData?.unit_cost) : 0,
    });
    return {
      quantity: "",
      subject: HTMLEntities.decode(_formData?.subject || ""),
      add_item_to_database: _formData?.item_id
        ? _formData?.item_on_database
        : is_cidb_auto_save || 0,
      is_markup_percentage: _formData ? _formData?.is_markup_percentage : 1,
      markup:
        _formData?.is_markup_percentage === 0
          ? _formData?.markup != null
            ? (Number(_formData?.markup) / 100)?.toString()
            : null
          : _formData?.markup,
      unit_cost: _formData ? String(_formData?.unit_cost) : 0,
      ..._formData,
      internal_notes: _formData?.internal_notes || "",
      description: _formData?.description || "",
    };
  }, [_formData, is_cidb_auto_save]);

  const {
    values: formData,
    errors,
    setFieldValue,
    setFieldError,
    handleSubmit,
    isSubmitting,
    resetForm,
    handleReset,
    setValues,
    submitForm,
  } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: (values) => {
      if (!values.subject?.trim()) {
        setFieldError("subject", _t("This field is required."));
        return;
      }
      const isSkipClose =
        formEvent?.nativeEvent.submitter?.getAttribute("data-skip-close") ===
          "true" || skipClose.current;
      setSubmitting(isSkipClose ? "savingWithoutClose" : "saving");
      onSubmit?.(
        [
          {
            ...values,
            markup: values?.markup?.toString()
              ? values?.is_markup_percentage === 0
                ? Number(values.markup)?.toString()
                : values.markup
              : "",
            total: values.quantity == "" ? 0 : totalToDisplay * 100,
            markup_amount: calculateMarkupAmount(
              Number(values.unit_cost) || 0,
              Number(values.quantity) || 0,
              Number(values.markup) || 0,
              Number(values.is_markup_percentage) || 0
            ),
            internal_notes: values.internal_notes || "",
            description: values.description || "",
            reference_item_id: values?.reference_item_id
              ? values?.reference_item_id
              : confirmSaveData && confirmSaveData?.rid > 0
              ? confirmSaveData.rid
              : undefined,
            ...(selectedSection?.section_id
              ? { section_id: selectedSection?.section_id }
              : {}),
          },
        ] as IChangeOrderItem[],
        {
          skipClose: isSkipClose,
          addFromResponse: !Boolean(_formData?.change_order_id),
          isNavigatingItems: isNavigationRef.current,
        }
      ).then((response) => {
        setSubmitting(null);
        if (
          isSkipClose &&
          Number(response?.data?.reference_item_id || "") <= 0
        ) {
          handleReset(1);
        }
        setAssignedTo({});
      });
    },
    enableReinitialize: true,
    validateOnMount: false,
    validateOnChange: false,
    validateOnBlur: false,
  });
  const gConfig = getGConfig();
  const { module_singular_name, module_key } = gConfig;
  // this is will NF once testing done it will be merge on dev
  // const [initialValuesState, setInitialValuesState] = useState<any>(formData);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);
  const [isOpenSelectCustomer, setOpenSelectCustomer] =
    useState<boolean>(false);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const [assignedTo, setAssignedTo] = useState<Partial<CustomerEmail>>(
    _formData
      ? {
          module_display_name: formData.assigned_to_company_name,
          display_name: formData.assignee_name,
          user_id: Number(formData.assigned_to),
          type_key: formData.assignee_type || "",
          contact_id: formData.assigned_to_contact_id,
          image:
            formData?.assigned_to_contact_id === 0 ? formData?.user_image : "",
        }
      : {}
  );

  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  useEffect(() => {
    getUnit();
  }, []);
  const itemTypes: GType[] = getGTypes();
  const filteredItemsTypes = useMemo(
    () =>
      itemTypes?.filter(
        (item: Partial<GType>) => item?.type === "company_items"
      ),
    [itemTypes]
  );
  const reorderedItemsTypes = (() => {
    if (!Array.isArray(filteredItemsTypes)) return [];
    const items = [...filteredItemsTypes];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();

  const selectedItemType = useMemo(() => {
    const itemType = reorderedItemsTypes.find(
      (item: GType) => item.type_id === formData.item_type?.toString()
    );
    const name = itemType?.name || "Material";
    return _t(`Save this item into my ${name} Items list?`);
  }, [formData.item_type, reorderedItemsTypes]);

  const total = useMemo(
    () =>
      (Number(formData?.quantity) || 0) * (Number(formData?.unit_cost) || 0),
    [formData?.quantity, formData?.unit_cost, formData.unit]
  );

  const markUpAmount = useMemo(
    () =>
      formData?.is_markup_percentage?.toString() == "1"
        ? (total * Number(formData?.markup)) / 100
        : Number(formData.markup),
    [formData.markup, formData?.is_markup_percentage, total]
  );

  const possibleMarkupPercentage = useMemo(() => {
    const total = Number(formData?.quantity) * Number(formData?.unit_cost);
    const _markup =
      formData?.markup == "-" || !formData?.markup ? 0 : formData.markup;

    if (formData.is_markup_percentage !== 1 && total) {
      const markup = Number(_markup);
      const markupPercentage =
        Number(markup) === 0
          ? 0
          : (Number(markup) * 100) / (Number(total) || 1) - 100;

      return markupPercentage;
    }

    return "0.00";
  }, [
    markUpAmount,
    total,
    formData.is_markup_percentage,
    formData.quantity,
    formData.unit_cost,
    formData.markup,
  ]);

  const grossTotal = useMemo(() => total + markUpAmount, [total, markUpAmount]);

  const parseNumber = (_value: string | string[]) => {
    const value = Array.isArray(_value) ? _value[0] : _value;
    let finalValue = 0;
    const parsedNumber = parseFloat(value);
    if (isNaN(parsedNumber) || !value) {
      finalValue = 0;
    } else {
      finalValue = parsedNumber;
    }

    return finalValue;
  };

  const currentItemIndex = useMemo(() => {
    const curItemIndex = filteredItems?.findIndex(
      (i: IChangeOrderItem) => i.item_id === formData?.item_id
    );

    return curItemIndex;
  }, [filteredItems, formData]);

  const title = useMemo(() => {
    if (formData.item_id) {
      return _t(
        getCOModuleName({ gConfig, isRequest: currentType === "cor" }) + " Item"
      );
    } else {
      return _t(
        "Add " +
          getCOModuleName({ gConfig, isRequest: currentType === "cor" }) +
          " Item"
      );
    }
  }, [formData.item_id, currentType]);

  const handleFormReset = () => {
    setFormEvent(null);
    resetForm();
    setAssignedTo({});
    setDollarValues({
      unit_cost: 0,
      markup: "",
    });
  };

  useEffect(() => {
    if (_formData?.item_id && _formData?.item_id !== formData?.item_id) {
      handleFormReset();
      setDollarValues({
        //  unit_cost:(Number(_formData.unit_cost) || 0) / 100  //commented for CU https://app.clickup.com/t/86cyyb9pb
        unit_cost: Number(_formData.unit_cost) || 0,
        markup: _formData.is_markup_percentage
          ? _formData.markup == ""
            ? _formData.markup
            : Number(_formData.markup)
          : _formData.markup / 100,
      });
      setAssignedTo({
        module_display_name: _formData.assigned_to_company_name,
        display_name: _formData.assignee_name,
        user_id: Number(_formData.assigned_to),
        type_key: _formData.assignee_type || "",
        image: _formData.user_image || "",
      });
      setValues({
        ..._formData,
        add_item_to_database: _formData?.item_id
          ? _formData?.item_on_database
          : is_cidb_auto_save || 0,
        quantity: _formData?.quantity || 0,
        unit_cost: _formData?.unit_cost || 0,
        markup: _formData?.markup,
        is_markup_percentage: _formData?.is_markup_percentage || 0,
        internal_notes: _formData?.internal_notes || "",
        description: _formData?.description || "",
        user_image: _formData.user_image || "",
      });
    }
  }, [_formData?.item_id, is_cidb_auto_save]);

  useMemo(() => {
    if (
      !!formData?.unit_cost &&
      !!formData?.unit &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [formData]);

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (!!formData?.unit_cost && !!formData.unit) {
        setShowUnitInputs(false);
      }
    }
  };

  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
    }
  };
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  const renderUnitForm = () => {
    if (isViewOnly) {
      return (
        <Typography className="text-[#008000] font-medium text-13">
          {formData?.unit?.toString() !== "" ? (
            <>
              {
                formatter(
                  formatAmount((Number(formData?.unit_cost) / 100).toFixed(2))
                ).value
              }
              /{formData?.unit}
            </>
          ) : (
            <>
              {
                formatter(
                  formatAmount(
                    (Number(formData?.unit_cost || 0) / 100).toFixed(2)
                  )
                ).value
              }
            </>
          )}
        </Typography>
      );
    }

    // 🛑 FIXED: Removed `return;` so that the function continues execution.
    if (showUnitInputs) {
      return (
        <div className="flex gap-2">
          {/* Unit Cost Input */}
          <div className="w-[calc(100%-52px)]">
            <InputNumberField
              name="unit_cost"
              id="unit_cost"
              rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
              placeholder={_t("Item Unit Cost")}
              disabled={isViewOnly}
              labelPlacement="left"
              errorMessage={errors.unit_cost}
              autoFocus={!!formData?.unit_cost && !!formData.unit}
              defaultValue={
                Number(dollarValues?.unit_cost) !== 0
                  ? Number(dollarValues?.unit_cost) / 100
                  : ""
              }
              value={
                Number(dollarValues?.unit_cost) !== 0
                  ? Number(dollarValues?.unit_cost) / 100
                  : ""
              }
              onPaste={handlePaste}
              onChange={(value) => {
                setFieldValue("unit_cost", Number(value) * 100);
                setDollarValues((prev) => ({
                  ...prev,
                  unit_cost: Number(value) * 100 || "",
                }));
              }}
              formatter={(value) => inputFormatter(value?.toString()).value}
              parser={(value) => (value ? unformatted(value.toString()) : "")}
              onKeyDown={(event) =>
                onKeyDownCurrency(event, {
                  integerDigits: 10,
                  decimalDigits: 2,
                  unformatted,
                  allowNegative: formData?.item_type === 165,
                  decimalSeparator: inputFormatter().decimal_separator,
                })
              }
              onBlur={handleFocusOut}
            />
          </div>

          {/* Unit Select Field */}
          <div className="w-[62px]">
            <SelectField
              className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
              placeholder="Unit"
              name="unit"
              disabled={isViewOnly}
              labelPlacement="left"
              maxLength={15}
              value={formData?.unit}
              iconView={true}
              popupClassName="!w-[260px]"
              showSearch
              options={
                unitData.map((type) => ({
                  label: type.name.toString(),
                  value: type.name.toString(),
                })) ?? []
              }
              allowClear
              filterOption={(input, option) =>
                filterOptionBySubstring(input, option?.label as string)
              }
              onChange={(value) => setFieldValue("unit", value)}
              addItem={{
                text: "Add Unit: Type Unit & Press Enter",
                icon: "fa-regular fa-plus",
              }}
              onInputKeyDown={(e) => {
                if (e.key === "Enter") {
                  const value = e?.currentTarget?.value?.trim();
                  const newType = onEnterSelectSearchValue(
                    e,
                    unitData?.map((unit) => ({
                      label: unit?.name,
                      value: "",
                    })) || []
                  );
                  if (newType) {
                    setNewTypeName(newType);
                  } else if (value) {
                    notification.error({
                      description:
                        "Records already exist, no new records were added.",
                    });
                  }
                }
              }}
              onClear={() => setFieldValue("unit", "")}
              errorMessage={errors.unit}
              onBlur={handleFocusOut}
            />
          </div>
        </div>
      );
    }

    // Show formatted unit cost when inputs are hidden
    return (
      <Typography
        className="text-[#008000] cursor-pointer text-13 font-medium"
        onClick={handleParagraphClick}
        disabled={isViewOnly}
      >
        {
          formatter(
            formatAmount((Number(formData?.unit_cost) / 100).toFixed(2))
          ).value_with_symbol
        }
        /{formData?.unit}
      </Typography>
    );
  };

  const handleItemNav = async (increment: number) => {
    const formModified = isEqual(formData, initialValues);
    if (!formModified) {
      isNavigationRef.current = true;
      skipClose.current = true;
      await submitForm();
      isNavigationRef.current = false;
      skipClose.current = false;
    }
    if (isEmpty(errors)) {
      setChangeOrderItemToView?.(filteredItems[currentItemIndex + increment]);
    }
  };

  useEffect(() => {
    if (formData.is_markup_percentage === 1) {
      setTotalToDisplay(grossTotal == 0 ? 0 : grossTotal / 100);
    } else {
      setTotalToDisplay(
        !formData.quantity || formData.quantity == 0 || !formData?.unit_cost
          ? 0
          : (Number(formData.markup) || total) / 100
      );
    }
  }, [
    grossTotal,
    formData.is_markup_percentage,
    total,
    formData.quantity,
    possibleMarkupPercentage,
  ]);

  const CO_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {currentCurrency}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];
  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(formData) !== JSON.stringify(initialValuesState);
  // }, [formData, initialValuesState]);

  // const closeConfirmationModal = () => {
  //   setChangeOrderItemShow(changeOrderItemShow);
  //   setIsConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDialogOpen(false);
  //   setChangeOrderItemShow(!changeOrderItemShow);
  // };

  // // Reset form on drawer close
  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     setChangeOrderItemShow(false);
  //   } else {
  //     setIsConfirmDialogOpen(true);
  //   }
  // };

  useEffect(() => {
    const selectedItem = markupItemTypes?.find(
      (item) => String(item.type_id) === String(formData.item_type)
    );
    if (selectedItem && !!selectedItem.mark_up && itemType) {
      const markUp = Number(selectedItem.mark_up);
      if (!isNaN(markUp)) {
        setDollarValues((prev) => ({
          ...prev,
          markup: Number(selectedItem.mark_up),
        }));
        setFieldValue("markup", Number(selectedItem.mark_up));
      }
    }
  }, [formData.item_type, markupItemTypes]);

  const debouncedSetAndGetVariants = useCallback(
    debounce(async (values) => {
      const respose = (await getItemVariants({
        item_id: values?.reference_item_id,
        item_type: values?.item_type,
      })) as IEstimateItemVariantApiRes;
      if (respose?.data?.length) {
        setVariantOptions(
          respose?.data?.map((el) => ({
            ...el,
            label: el?.name,
            value: el?.variation_id,
          }))
        );
      } else {
        setVariantOptions([]);
      }
      setVariantloading(false);
    }, 300),
    []
  );

  useEffect(() => {
    if (!isNewItem) {
      if (formData?.variation_id) {
        setVariantOptions([
          {
            label: formData?.variation_name,
            value: formData?.variation_name,
          },
        ]);
      } else {
        setVariantOptions([]);
      }
      setVariantloading(true);
      debouncedSetAndGetVariants(formData);
    }
  }, [isNewItem]);
  return (
    <>
      <Drawer
        open={changeOrderItemShow}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
                <FontAwesomeIcon
                  className="w-4 h-4"
                  icon="fa-regular fa-dolly"
                />
              </div>
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {title}
              </Header>
            </div>
            {!!formData.item_id && (
              <div className="flex items-center sm:gap-2 gap-0 pr-2">
                <ButtonWithTooltip
                  tooltipTitle={_t("Previous")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-chevron-left"
                  className="item-pre-next-button disabled:bg-transparent"
                  onClick={() => handleItemNav(-1)}
                  disabled={currentItemIndex === 0}
                />
                <ButtonWithTooltip
                  tooltipTitle={_t("Next")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-chevron-right"
                  className="item-pre-next-button disabled:bg-transparent"
                  onClick={() => handleItemNav(1)}
                  disabled={currentItemIndex === filteredItems?.length - 1}
                />
              </div>
            )}
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        // closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
        closeIcon={
          <CloseButton onClick={() => setChangeOrderItemShow(false)} />
        }
      >
        <form
          className="py-4"
          onSubmit={(e) => {
            e?.preventDefault();
            setFormEvent(e);
            handleSubmit();
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    isRequired={true}
                    value={HTMLEntities.decode(formData?.subject || "")}
                    disabled={isViewOnly}
                    errorMessage={errors.subject}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value);
                      if (e.target.value?.trim()) {
                        delete errors.subject;
                      } else {
                        setFieldError("subject", _t("This field is required."));
                      }
                    }}
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-4 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      disabled={
                        isViewOnly ||
                        (!!_formData?.item_id &&
                          !!_formData.reference_item_id) ||
                        (!!_formData?.item_id &&
                          !!_formData.item_on_database) ||
                        !!_formData?.is_discount_item
                      }
                      value={
                        formData?.item_type
                          ? formData.item_type?.toString()
                          : ""
                      }
                      options={reorderedItemsTypes?.map((item: GType) => ({
                        label: (
                          <div className="flex items-center gap-1.5">
                            <FontAwesomeIcon
                              icon={getItemTypeIcon({
                                type: item?.type_id?.toString(),
                              })}
                            />
                            {item.name}
                          </div>
                        ),
                        // name: item.name,
                        value: item.type_id,
                        ...item,
                      }))}
                      errorMessage={errors.item_type}
                      onChange={(value, option) => {
                        setFieldValue("item_type", parseNumber(value));
                        setFieldValue("item_type_name", option?.name);
                        setItemType(true);
                        if (_formData?.is_markup_percentage) {
                          const selectedItem = markupItemTypes?.find(
                            (item) =>
                              String(item.type_id) ===
                              String(formData.item_type)
                          );
                          if (selectedItem && !!selectedItem.mark_up) {
                            const markUp = Number(selectedItem.mark_up);
                            if (!isNaN(markUp)) {
                              setDollarValues((prev) => ({
                                ...prev,
                                markup: Number(selectedItem.mark_up),
                              }));
                              setFieldValue(
                                "markup",
                                Number(selectedItem.mark_up)
                              );
                            }
                          }
                        }
                        if (option?.name?.trim()) {
                          delete errors.item_type;
                        } else {
                          setFieldError(
                            "item_type",
                            _t("This field is required.")
                          );
                        }
                      }}
                    />
                  </div>
                  <div className="w-full relative">
                    <ButtonField
                      value={assignedTo.display_name}
                      label={_t("Assigned To")}
                      labelPlacement="top"
                      onClick={() => setOpenSelectCustomer(true)}
                      onChange={() => {}}
                      isDisabled={isReadOnly}
                      errorMessage={errors.assigned_to}
                      addonBefore={
                        assignedTo.user_id && assignedTo.display_name ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setContactDetailDialogOpen(true);
                              }}
                            />
                          </div>
                        ) : (
                          <></>
                        )
                      }
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(assignedTo?.display_name)
                          ),
                          image:
                            formData?.assigned_to_contact_id === 0
                              ? assignedTo?.image
                              : "",
                        },
                      }}
                    />
                  </div>
                </div>

                <div
                  className={`grid ${
                    !isNewItem ? "md:grid-cols-2 md:gap-4 gap-5" : ""
                  }  `}
                >
                  <div className="w-full">
                    <SelectCostCode
                      label={_t("Cost Code")}
                      labelPlacement="top"
                      projectId={details?.project_id}
                      costCodeId={formData.cost_code_id}
                      onChange={(value, option) => {
                        const selectedCostCode = option as ICostCode;
                        if (selectedCostCode) {
                          setFieldValue("cost_code_id", parseNumber(value));
                          setFieldValue(
                            "cost_code_name",
                            selectedCostCode?.cost_code_name.trim()
                          );
                          setFieldValue("csi_code", selectedCostCode?.csi_code);
                          setFieldValue(
                            "cost_code",
                            selectedCostCode?.csi_code
                          );
                        } else {
                          setFieldValue("cost_code_id", null);
                          setFieldValue("cost_code_name", null);
                          setFieldValue("csi_code", null);
                          setFieldValue("cost_code", null);
                        }
                      }}
                      loadingStatus={[]}
                      disabled={isReadOnly}
                    />
                  </div>
                  {!isNewItem && (
                    <div className="w-full">
                      <SelectField
                        label={_t("Variation")}
                        labelPlacement="top"
                        value={
                          formData?.variation_id && formData?.variation_name
                            ? variantOptions?.filter((item) => {
                                return (
                                  formData?.variation_id?.toString() ===
                                  item?.value?.toString()
                                );
                              })
                            : []
                        }
                        loading={variantloading}
                        onChange={(value) => {
                          setFieldValue("variation_id", value);
                          const selectedOption = variantOptions.find(
                            (item) => item.value === value
                          );
                          setFieldValue(
                            "variation_name",
                            selectedOption ? selectedOption.label : ""
                          );
                        }}
                        showSearch
                        filterOption={(input, option) =>
                          filterOptionBySubstring(
                            input,
                            option?.label as string
                          )
                        }
                        options={variantOptions as IOption[]}
                        // disabled={isItemDetailDrawer || ParentIsReadOnly}
                        allowClear={true}
                        onClear={() => {
                          setFieldValue("variation_id", null);
                          setFieldValue("variation_name", "");
                        }}
                      />
                    </div>
                  )}
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("QTY")}
                    </Typography>

                    {isViewOnly ? (
                      <Typography
                        className={`text-[#008000] font-medium !text-right text-13 ${
                          isViewOnly ? "cursor-no-drop" : ""
                        }`}
                      >
                        <>{Number(formData?.quantity) || 0}</>
                      </Typography>
                    ) : (
                      <div className="sm:w-40 w-28">
                        <InputNumberField
                          name="quantity"
                          id="quantity"
                          rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                          placeholder={_t("Item Quantity")}
                          disabled={isViewOnly}
                          errorMessage={errors.quantity}
                          labelPlacement="left"
                          formInputClassName={
                            isViewOnly ? "flex items-center justify-end" : ""
                          }
                          onPaste={handlePaste}
                          defaultValue={
                            Number(formData.quantity) !== 0
                              ? formData.quantity
                              : ""
                          }
                          value={
                            Number(formData.quantity) !== 0
                              ? formData.quantity?.toString()
                              : ""
                          }
                          formatter={(value) => {
                            return inputFormatter(value?.toString()).value;
                          }}
                          onChange={(value) =>
                            setFieldValue("quantity", value?.toString())
                          }
                          parser={(value) => {
                            const inputValue = value
                              ? unformatted(value.toString())
                              : "";
                            return inputValue;
                          }}
                          onKeyDown={(event) =>
                            onKeyDownCurrency(event, {
                              integerDigits: 6,
                              decimalDigits: 2,
                              unformatted,
                              allowNegative: true,
                              decimalSeparator:
                                inputFormatter().decimal_separator,
                            })
                          }
                        />
                      </div>
                    )}
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div className="sm:w-[260px] w-28 h-[22px]">
                      <div
                        className={`text-right !text-[#008000] leading-[22px] font-semibold text-13 ${
                          isViewOnly ? "cursor-no-drop" : ""
                        }`}
                        ref={unitCostContainerRef}
                      >
                        {renderUnitForm()}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {total?.toString() === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  (Number(total || 0) / 100).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
                <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          value={
                            Number(formData?.is_markup_percentage) === 1
                              ? "markup_percent"
                              : "markup_dolar"
                          }
                          options={CO_ITEMS_LIST_TAB} // MU_TAB: change this in future ( temporary resolve type issue )
                          className={`min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5 ${
                            isViewOnly ? "cursor-no-drop" : ""
                          }`}
                          activeclassName="active:bg-[#ffffff]"
                          onChange={(e: RadioChangeEvent) => {
                            if (!isViewOnly) {
                              setDollarValues((prev) => ({
                                ...prev,
                                markup: "",
                              }));
                              setFieldValue("markup", "");
                              setFieldValue(
                                "is_markup_percentage",
                                e.target.value === "markup_percent" ? 1 : 0
                              );
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${currentCurrency} -- Add the ${currentCurrency} amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    {isViewOnly ? (
                      <Typography
                        className={`text-[#008000] font-medium text-13 ${
                          isViewOnly ? "cursor-no-drop" : ""
                        }`}
                      >
                        {formData?.is_markup_percentage === 1 ? (
                          <>{Number(formData?.markup)}%</>
                        ) : (
                          <>
                            {
                              formatter(
                                formatAmount(
                                  (Number(formData?.markup) / 100).toFixed(2)
                                )
                              ).value
                            }
                          </>
                        )}
                      </Typography>
                    ) : (
                      <div className="sm:w-40 w-28">
                        <InputField
                          className="!p-0 !pl-1.5 field-text-13 text-success !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                          placeholder={
                            formData?.is_markup_percentage === 1
                              ? _t("Item Markup") + " %"
                              : _t("Total Sales Price")
                          }
                          disabled={isViewOnly}
                          labelPlacement="left"
                          value={
                            formData.is_markup_percentage === 1
                              ? dollarValues?.markup ?? ""
                              : dollarValues?.markup !== null &&
                                dollarValues?.markup !== undefined
                              ? dollarValues.markup
                              : ""
                          }
                          onPaste={handlePaste}
                          type="text"
                          onChange={(e) => {
                            if (!floatNumberRegex.test(e.target.value)) {
                              return;
                            }
                            if (formData?.is_markup_percentage) {
                              if (e.target.value?.length > 3) {
                                return;
                              }
                              if (!wholeNumberRegex.test(e.target.value)) {
                                return;
                              }
                            }
                            const cleanedInput = e.target.value
                              .split(".")[0]
                              .replace("-", "");
                            if (cleanedInput?.length > 8) {
                              return;
                            }
                            if (formData?.is_markup_percentage === 1) {
                              setFieldValue("markup", e.target.value);
                            } else {
                              if (e.target.value === "") {
                                setFieldValue("markup", "");
                              } else {
                                setFieldValue(
                                  "markup",
                                  Number(e.target.value) * 100
                                );
                              }
                            }
                            setDollarValues((prev) => ({
                              ...prev,
                              markup: e.target.value,
                            }));
                          }}
                          onPressEnter={handleEnterKeyPress}
                        />
                      </div>
                    )}
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("Markup")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={true}
                      >
                        {formData.is_markup_percentage === 1
                          ? `${
                              formatter(
                                formatAmount(
                                  roundToAmount(
                                    Number(markUpAmount || 0) / 100,
                                    2
                                  ).toFixed(2)
                                )
                              ).value_with_symbol
                            }`
                          : `${formatAmount(
                              Number(possibleMarkupPercentage || 0)?.toFixed(2)
                            )}%`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-equals"
                    />
                  </li>
                </ul>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Revenue")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {totalToDisplay.toString() === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  roundToAmount(
                                    Number(totalToDisplay || 0),
                                    2
                                  ).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    value={formData?.description || ""}
                    errorMessage={errors.description}
                    disabled={isViewOnly}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Note")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={isViewOnly}
                    errorMessage={errors.internal_notes}
                    value={formData?.internal_notes || ""}
                    onChange={(e) => {
                      setFieldValue("internal_notes", e.target.value);
                    }}
                  />
                </div>
                <div className="grid gap-2">
                  {formData.item_type && (
                    <CheckBox
                      className="gap-1.5 w-fit"
                      checked={
                        !!formData.add_item_to_database ||
                        isValidId(formData?.reference_item_id)
                      }
                      onChange={(e) => {
                        setFieldValue(
                          "add_item_to_database",
                          e.target.checked ? 1 : 0
                        );
                        setFieldValue(
                          "item_on_database",
                          e.target.checked ? 1 : 0
                        );
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                        }
                      }}
                      disabled={
                        isViewOnly ||
                        (!!_formData?.item_id &&
                          !!_formData.reference_item_id) ||
                        (!!_formData?.item_id &&
                          !!_formData.item_on_database) ||
                        !!_formData?.is_discount_item
                      }
                    >
                      {selectedItemType}
                    </CheckBox>
                  )}
                  {/* commented due to this PM Reply https://prnt.sc/Q5FI90g8FFCX (https://app.clickup.com/t/86cz47nuw) */}
                  {/* <Tooltip
                    title={
                      "This feature is currently unavailable as the Project module is under revision. It will be re-enabled once the updates are complete."
                    }
                  >
                    <CheckBox
                      className="gap-1.5 text-primary-900 w-fit"
                      checked={!!formData?.is_optional_item}
                      disabled={isViewOnly}
                      onChange={(event) => {
                        const valueToSet: number = event.target.checked ? 1 : 0;
                        setFieldValue("is_optional_item", valueToSet);
                      }}
                    >
                      {_t("Make Item Optional")}
                    </CheckBox>
                  </Tooltip> */}
                  <CheckBox
                    className="gap-1.5 w-fit"
                    disabled={isViewOnly}
                    checked={!!formData.apply_global_tax}
                    onChange={(e) => {
                      setFieldValue(
                        "apply_global_tax",
                        e.target.checked ? 1 : 0
                      );
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                      }
                    }}
                  >
                    {_t("Collect Tax on this Item?")}
                  </CheckBox>
                </div>
              </SidebarCardBorder>
            </div>
          </div>

          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4 gap-4">
            <PrimaryButton
              htmlType="submit"
              buttonText={_t("Save & Close")}
              disabled={(isSubmitting && submitting === "saving") || isReadOnly}
              isLoading={submitting === "saving"}
            />

            <PrimaryButton
              htmlType="submit"
              buttonText={_t("Save & Add Another Item")}
              disabled={
                (isSubmitting && submitting === "savingWithoutClose") ||
                isReadOnly
              }
              isLoading={submitting === "savingWithoutClose"}
              data-skip-close="true"
            />
          </div>
        </form>
      </Drawer>
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            setOpenSelectCustomer(false);
          }}
          projectId={details?.project_id}
          singleSelecte={true}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "my_project",
            "by_service",
          ]}
          setCustomer={(data) => {
            const firstUser = data?.[0] as Partial<CustomerEmail>;
            const selectedUser = {
              ...firstUser,
            };
            setAssignedTo(selectedUser || {});
            if (selectedUser) {
              setValues({
                ...formData,
                assignee_name: selectedUser.display_name || "",
                assigned_to: Number(selectedUser.user_id) || 0,
                assigned_to_contact_id: selectedUser.contact_id || 0,
                assigned_to_company_name: selectedUser.company_name || "",
                assignee_type: selectedUser?.type_key || "",
                user_image: selectedUser?.image || "",
              });
            } else {
              setAssignedTo({});
              setValues({
                ...formData,
                assignee_name: "",
                assigned_to: 0,
                assigned_to_contact_id: 0,
                assigned_to_company_name: "",
                assignee_type: "",
              });
            }
          }}
          selectedCustomer={
            (assignedTo as CustomerEmail).user_id && assignedTo.display_name
              ? [assignedTo as CustomerEmail]
              : []
          }
          groupCheckBox={true}
          activeTab={defaultConfig.contractor_key}
        />
      )}

      {contactDetailDialogOpen && assignedTo.user_id ? (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => setContactDetailDialogOpen(false)}
          contactId={Number(assignedTo.user_id)}
          additional_contact_id={formData?.assigned_to_contact_id}
          sendEmailDrawer={{
            projectId: details?.project_id,
          }}
        />
      ) : null}
      {confirmSaveData.rid > 0 && (
        <ConfirmModal
          isOpen={confirmSaveData?.rid > 0}
          modaltitle={_t("This Item Already Exists")}
          description={confirmSaveData?.message || ""}
          modalIcon="fa-regular fa-triangle-exclamation"
          yesButtonLabel={_t("Use Existing")}
          noButtonLabel={_t("Rename")}
          onAccept={() => {
            setIsExistingLoading(true);
            handleSubmit();
          }}
          isLoading={isExistingLoading && isExistingLoading}
          onDecline={() => {
            setConfirmSaveData({ rid: 0 });
          }}
          onCloseModal={() => {
            setConfirmSaveData({ rid: 0 });
          }}
        />
      )}
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeConfirmationModal}
        />
      )} */}
    </>
  );
};

export default ChangeOrderItemForm;
