import { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Header } from "~/shared/components/atoms/header";
import { Drawer } from "~/shared/components/atoms/drawer";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";

// Redux slice, action and store
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { addJobsiteInspection } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { addJobsiteInspectionAct } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLNotesSlice";
import { getModuleAutoNumber } from "~/redux/action/commonAction";

// Hooks and Helper
import { useTranslation } from "~/hook";
import {
  escapeHtmlEntities,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";

const Inspection = ({
  isOpen,
  onAddedRec,
  onClose,
  isViewOnly = false,
}: IInspectionProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { is_custom_inspection_id } = appSettings || {};

  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );

  const [numberLoading, setNumberLoading] = useState<boolean>(false);

  const { inspectionStatus, inspectionAgency }: IStatusListDataInitialState =
    useAppDLSelector((state) => state.statusListData);

  const inspectionStatusList = useMemo(
    () =>
      inspectionStatus.map((item: IInspectionStatusSL) => ({
        label: replaceDOMParams(sanitizeString(item.display_name || item.name)),
        value: item.type_id.toString(),
      })),
    [inspectionStatus]
  );

  const inspectionAgencyList = useMemo(
    () =>
      inspectionAgency.map((item: IInspectionAgencySL) => ({
        label: replaceDOMParams(sanitizeString(item.display_name || item.name)),
        value: item.type_id.toString(),
      })),
    [inspectionAgency]
  );

  const initialValues: IJobsiteInspectionsAdd = {
    customInspectionId: "",
    projectId: 0,
    inspectionType: "",
    inspectionAgency: 0,
    inspectionDate: details?.arrivalDate,
    inspectedBy: "",
    inspectionStatus: 439,
    isShared: 0,
    forceLogin: 0,
    referenceCompanyId: 0,
  };
  const [initialValuesState, setInitialValuesState] =
    useState<IJobsiteInspectionsAdd>(initialValues);

  const validationSchema = Yup.object().shape({
    inspectionType: Yup.string().trim().required("This field is required."),
    customInspectionId: Yup.string().trim().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      values.projectId = details.projectId || 0;
      const formData = { ...values };
      formData.inspectionType = escapeHtmlEntities(formData.inspectionType);
      formData.customInspectionId = escapeHtmlEntities(
        formData?.customInspectionId?.toString() || ""
      );
      if (is_custom_inspection_id == 0) {
        delete formData.customInspectionId;
      }
      try {
        const responseApi = (await addJobsiteInspection(
          formData
        )) as IAddJobsiteInspectionRes;
        if (responseApi?.success) {
          dispatch(addJobsiteInspectionAct(responseApi.data));
          onAddedRec(responseApi);
          dispatch(resetDash());
        } else {
          notification.error({
            description: responseApi?.message || "",
          });
        }
      } catch (e) {
        notification.error({
          description: "Something went wrong!",
        });
      } finally {
        setSubmitting(false);
      }
    },
  });

  const {
    handleSubmit,
    handleChange,
    setFieldValue,
    values,
    isSubmitting,
    touched,
    errors,
  } = formik;

  const getAutoNumber = async () => {
    setNumberLoading(true);
    const autoNumberRes = (await getModuleAutoNumber({
      // Utsav say, set static id and key
      module_id: 17,
      module_key: "inspections",
    })) as GetModuleAutoNumberApiResponse;
    if (autoNumberRes.success) {
      const newId = autoNumberRes?.data?.last_primary_id
        ? Number(autoNumberRes.data.last_primary_id) +
          autoNumberRes.data?.need_to_increment
        : "";
      setFieldValue("customInspectionId", newId);
      setInitialValuesState((prevState: IJobsiteInspectionsAdd) => ({
        ...prevState,
        customInspectionId: newId,
      }));
    }
    if (!autoNumberRes?.success) {
      notification.error({
        description: autoNumberRes.message || "Something went wrong",
      });
    }
    setNumberLoading(false);
  };
  useEffect(() => {
    if (is_custom_inspection_id == 2) {
      getAutoNumber();
    }
    if (is_custom_inspection_id == 0) {
      setFieldValue("customInspectionId", "Save To View");
      setInitialValuesState((prevState: IJobsiteInspectionsAdd) => ({
        ...prevState,
        customInspectionId: "Save To View",
      }));
    }
  }, [is_custom_inspection_id]);

  return (
    <Drawer
      open={isOpen}
      rootClassName="drawer-open"
      width={718}
      push={false}
      maskClosable={false}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-user-tie"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t("Add Inspection")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => onClose(false)} />}
    >
      {!numberLoading ? (
        <form
          method="post"
          noValidate
          className="py-4"
          onSubmit={handleSubmit}
          onKeyDown={(e) => {
            if (
              e.key === "Enter" &&
              (e.target as HTMLElement).tagName !== "TEXTAREA"
            ) {
              e.preventDefault();
            }
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Inspection") + " #"}
                    labelPlacement="top"
                    isRequired={true}
                    disabled={isViewOnly || is_custom_inspection_id == 0}
                    name="customInspectionId"
                    value={values?.customInspectionId}
                    onChange={handleChange}
                    onBlur={(e) => {
                      setFieldValue(
                        "customInspectionId",
                        e.target.value.trim()
                      );
                    }}
                    errorMessage={
                      touched.customInspectionId
                        ? errors.customInspectionId
                        : ""
                    }
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                  <div className="w-full">
                    <InputField
                      label={_t("Type")}
                      labelPlacement="top"
                      disabled={isViewOnly}
                      name="inspectionType"
                      isRequired={true}
                      value={values?.inspectionType}
                      onChange={handleChange}
                      errorMessage={
                        touched.inspectionType ? errors.inspectionType : ""
                      }
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={_t("Status")}
                      labelPlacement="top"
                      disabled={isViewOnly}
                      allowClear={true}
                      placeholder=""
                      value={
                        values?.inspectionStatus != 0
                          ? values?.inspectionStatus?.toString()
                          : undefined
                      }
                      options={inspectionStatusList}
                      onChange={(value) => {
                        setFieldValue("inspectionStatus", value);
                      }}
                    />
                  </div>
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Agency")}
                    labelPlacement="top"
                    placeholder=""
                    allowClear={true}
                    disabled={isViewOnly}
                    value={
                      values?.inspectionAgency != 0
                        ? values?.inspectionAgency?.toString()
                        : undefined
                    }
                    options={inspectionAgencyList}
                    onChange={(value) => {
                      setFieldValue("inspectionAgency", value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Inspected By")}
                    labelPlacement="top"
                    placeholder=""
                    name={"inspectedBy"}
                    disabled={isViewOnly}
                    value={values?.inspectedBy}
                    onChange={handleChange}
                  />
                </div>
                {details?.showClientAccess == "1" &&
                  details?.isInspectionAccess == "1" && (
                    <CheckBox
                      className="gap-1.5 w-fit"
                      children={_t("Share with Client")}
                      disabled={isViewOnly}
                      name="isShared"
                      onChange={(e) => {
                        setFieldValue("isShared", e.target.checked ? 1 : 0);
                      }}
                    />
                  )}
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            {!isViewOnly && (
              <PrimaryButton
                htmlType="submit"
                disabled={isSubmitting}
                isLoading={isSubmitting}
              />
            )}
          </div>
        </form>
      ) : (
        <Spin className="w-full h-[calc(100dvh-55px)] flex items-center justify-center" />
      )}
    </Drawer>
  );
};

export default Inspection;
