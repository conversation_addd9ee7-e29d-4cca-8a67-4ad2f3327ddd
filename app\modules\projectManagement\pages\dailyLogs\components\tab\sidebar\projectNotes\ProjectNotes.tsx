import { useMemo } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
import { useTranslation } from "~/hook";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { NOTE_PROJECT_STATUS } from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";
import { addProjectNote } from "~/modules/projectManagement/pages/dailyLogs/redux/action";

import {
  escapeHtmlEntities,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";

// atoms
import { Header } from "~/shared/components/atoms/header";
import { Drawer } from "~/shared/components/atoms/drawer";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";

// Other
import { addNoteProjectAct } from "../../../../redux/slices/dLNotesSlice";

const ProjectNotes = ({
  isOpen,
  onAddedRec,
  onClose,
  isViewOnly = false,
}: IProjectNotesProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const { tagCategoryList }: ITagCatInitialState = useAppDLSelector(
    (state) => state.tagCategories
  );
  const { notesPriority }: IStatusListDataInitialState = useAppDLSelector(
    (state) => state.statusListData
  );

  const tagCateList = useMemo(
    () =>
      tagCategoryList.map((item: ITag) => ({
        label: replaceDOMParams(sanitizeString(item.name)),
        value: item.original_tag_id.toString(),
      })),
    [tagCategoryList]
  );

  const notesPriorityList = useMemo(
    () =>
      notesPriority.map((item: INotesPrioritySL) => ({
        label: replaceDOMParams(sanitizeString(item.display_name || item.name)),
        value: item.type_id.toString(),
      })),
    [notesPriority]
  );

  const initialValues: IProjectNotesAdd = {
    projectId: 0,
    title: "",
    status: NOTE_PROJECT_STATUS[0].value,
    dateAdded: backendDateFormat(
      details?.arrivalDate || "",
      CFConfig.day_js_date_format
    ),
    priority: notesPriorityList[1].value,
    categories: "",
    isPrivateToUser: 0,
    isShared: 0,
    dailyLogProjectNote: 1,
  };

  const validationSchema = Yup.object().shape({
    title: Yup.string().trim().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      values.projectId = details.projectId || 0;
      const formData = getValuableObj(values);
      formData.title = escapeHtmlEntities(formData.title);

      try {
        const responseApi = (await addProjectNote(
          formData
        )) as IAddProjectNoteRes;
        if (responseApi?.success) {
          dispatch(addNoteProjectAct(responseApi.data));
          onAddedRec(responseApi);
        } else {
          notification.error({
            description: responseApi?.message || "",
          });
        }
      } catch (e) {
        notification.error({
          description: "Something went wrong!",
        });
      } finally {
        setSubmitting(false);
      }
    },
  });
  const {
    handleSubmit,
    handleChange,
    setFieldValue,
    values,
    isSubmitting,
    touched,
    errors,
  } = formik;

  return (
    <Drawer
      open={isOpen}
      rootClassName="drawer-open"
      width={718}
      push={false}
      maskClosable={false}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-file-invoice"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t("Add Note")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => onClose(false)} />}
    >
      <form
        method="post"
        noValidate
        className="py-4"
        onSubmit={handleSubmit}
        onKeyDown={(e) => {
          if (
            e.key === "Enter" &&
            (e.target as HTMLElement).tagName !== "TEXTAREA"
          ) {
            e.preventDefault();
          }
        }}
      >
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="w-full">
                <InputField
                  label={_t("Title")}
                  labelPlacement="top"
                  placeholder=""
                  disabled={isViewOnly}
                  name="title"
                  isRequired={true}
                  value={values?.title}
                  onChange={handleChange}
                  errorMessage={touched.title ? errors.title : ""}
                />
              </div>
              <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                <div className="w-full">
                  <SelectField
                    label={_t("Status")}
                    labelPlacement="top"
                    disabled={isViewOnly}
                    placeholder=""
                    options={NOTE_PROJECT_STATUS}
                    value={values?.status}
                    onChange={(value) => {
                      setFieldValue("status", value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Priority")}
                    labelPlacement="top"
                    disabled={isViewOnly}
                    placeholder=""
                    options={notesPriorityList}
                    value={values?.priority}
                    onChange={(value) => {
                      setFieldValue("priority", value);
                    }}
                  />
                </div>
              </div>
              <div className="w-full">
                <SelectField
                  label={_t("Tags")}
                  placeholder=""
                  value={
                    typeof values?.categories === "string" && values.categories
                      ? tagCateList.filter((item) =>
                          (values.categories ?? "")
                            .split(",")
                            .includes(item.value || "")
                        )
                      : []
                  }
                  labelPlacement="top"
                  mode="multiple"
                  allowClear={true}
                  disabled={isViewOnly}
                  showSearch
                  options={tagCateList}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(val) => {
                    if (typeof val === "object") {
                      setFieldValue("categories", val.join(","));
                    }
                  }}
                  onCloseTag={(val) => {
                    const valArr = values.categories.split(",");
                    const filtVal = valArr.filter((item) => item != val);
                    setFieldValue("categories", filtVal.join(","));
                  }}
                />
              </div>
              <div className="grid gap-3">
                <CheckBox
                  className="gap-1.5 w-fit text-primary-900 font-medium dark:text-white/90"
                  children={_t("Make Private: Visible Only to Me")}
                  disabled={isViewOnly}
                  name="isPrivateToUser"
                  checked={values.isPrivateToUser == 1}
                  onChange={(e) => {
                    setFieldValue("isPrivateToUser", e.target.checked ? 1 : 0);
                    if (e.target.checked) {
                      setFieldValue("isShared", 0);
                    }
                  }}
                />
                {details.showClientAccess == "1" &&
                  details?.isProjectNotesAccess == "1" && (
                    <CheckBox
                      className="gap-1.5 w-fit text-primary-900 font-medium dark:text-white/90 mb-1.5"
                      children={_t("Share with Client")}
                      disabled={isViewOnly}
                      name="isShared"
                      checked={values.isShared == 1}
                      onChange={(e) => {
                        setFieldValue("isShared", e.target.checked ? 1 : 0);
                        if (e.target.checked) {
                          setFieldValue("isPrivateToUser", 0);
                        }
                      }}
                    />
                  )}
              </div>
            </SidebarCardBorder>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          {!isViewOnly && (
            <PrimaryButton
              htmlType="submit"
              disabled={isSubmitting}
              isLoading={isSubmitting}
            />
          )}
        </div>
      </form>
    </Drawer>
  );
};

export default ProjectNotes;
