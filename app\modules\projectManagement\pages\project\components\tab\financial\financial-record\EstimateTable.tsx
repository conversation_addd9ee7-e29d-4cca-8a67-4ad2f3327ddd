import { useState, useEffect } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { EstimatesFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/estimatesFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import {
  getDefaultStatuscolor,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { useParams } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { getGConfig } from "~/zustand";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { sendMessageKeys } from "~/components/page/$url/data";

const EstimateTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const estimateModule = getGlobalModuleByKey(CFConfig.estimate_module);
  const [isShowingMore, setIsShowingMore] = useState(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const estimates = financialData?.estimates ?? [];
  const { formatter } = useCurrencyFormatter();
  const { id } = useParams();
  const [allEstimates, setAllEstimates] = useState<IProjectEstimatesData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const displayedEstimates = isShowingMore
    ? allEstimates
    : allEstimates?.slice(0, dataLimit);
  const totalCount = Number(
    financialData?.estimates_count?.[0]?.number_of_estimate ?? 0
  );
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const totalAmount = Number(financialData?.estimates_count?.[0]?.total ?? 0);
  const [collapse, setCollapse] = useState<string[]>([]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "estimate") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  useEffect(() => {
    if (isInitialLoad) {
      setAllEstimates(estimates);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(estimates?.map((e) => [e?.estimate_id, e]));

    const mergedEstimates = allEstimates?.map((existing) => {
      const updated = updatedMap?.get(existing?.estimate_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(allEstimates?.map((e) => e?.estimate_id));
    const newEstimates = estimates?.filter(
      (e) => !existingIds?.has(e?.estimate_id)
    );

    const nextAll = [...mergedEstimates, ...newEstimates];

    const hasChanged =
      nextAll?.length !== allEstimates?.length ||
      nextAll?.some(
        (e, i) => JSON.stringify(e) !== JSON.stringify(allEstimates[i])
      );

    if (hasChanged) {
      setAllEstimates(nextAll);
    }
  }, [estimates, isInitialLoad]);

  const handleShowMore = () => {
    if (allEstimates.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["estimates"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "company_estimate_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const estId = `Est. #${HTMLEntities.decode(sanitizeString(value))}`;
        return value ? (
          <Tooltip title={estId}>
            <Typography className="table-tooltip-text">{estId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "estimate_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: _t("Title"),
      field: "title",
      minWidth: 320,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const title = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={title}>
            <Typography className="table-tooltip-text">{title}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "approval_type_name",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: Estimate }) => {
        const status = data.approval_type_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.status_color || ""
        );
        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formattedValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "estimate_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: Estimate }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_ESTIMATE.url +
                    "/" +
                    (data.estimate_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <EstimatesFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              estimatesId={data.estimate_id}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(estimateModule?.plural_name ?? "Estimates")}
        onChange={setCollapse}
        activeKey={collapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(estimateModule?.module_name ?? "Estimate")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_ESTIMATE.url}?action=new&project=${id}`,
            "_self"
          );
          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_ESTIMATE.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={displayedEstimates}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-estimates.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["estimates", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, ["estimates", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default EstimateTable;
