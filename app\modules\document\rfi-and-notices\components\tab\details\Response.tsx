import { useTranslation } from "~/hook";
// molecules
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { useCallback, useEffect, useRef, useState } from "react";
import { RFIDetailsField } from "../../../utils/constasnts";
import { useAppRFIDispatch, useAppRFISelector } from "../../../redux/store";
import { getGSettings } from "~/zustand";
import delay from "lodash/delay";
import dayjs from "dayjs";
import { RFIFieldStatus } from "~/constants/rfi-and-notices";
import { getStatusActionForField } from "~/shared/utils/helper/common";
import { updateRFIDetails } from "../../../redux/slices/rfiDetailSlice";
import { useParams } from "@remix-run/react";
import { updateRfiDetailsApi } from "../../../redux/action/rfiDetailAction";
import debounce from "lodash/debounce";

export const getStatusForField = (
  loadingStatus: IFieldStatus[],
  fieldName: string
): IStatus => {
  const itemField = loadingStatus.find(
    (item: IFieldStatus) => item && item.field === fieldName
  );
  if (itemField && itemField.status) {
    return itemField.status;
  }
  return "button";
};

const Response = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { tab, id: RFI_Id }: RouteParams = useParams();
  const descriptionFieldRef = useRef<HTMLInputElement>(null);
  const { date_format }: GSettings = getGSettings();
  const [inputValues, setInputValues] =
    useState<Partial<IRFIDetails>>(RFIDetailsField);
  const loadingStatusRef = useRef(RFIFieldStatus);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(RFIFieldStatus);
  const dispatch = useAppRFIDispatch();
  const { rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const submitUpdate = useCallback(
    async (data: { [key: string]: string | number | boolean | null }) => {
      const field = Object.keys(data);
      const response = (await updateRfiDetailsApi({
        correspondence_id: RFI_Id,
        ...data,
      })) as ApiCallResponse;
      if (response.success) {
        if (field[0] === "response_needed_by") {
          const values = Object.values(data)[0] as string;
          dispatch(
            updateRFIDetails({
              ...data,
              response_needed_by: !!values
                ? dayjs(values).format(date_format)
                : "",
            })
          );
        } else if (field[0] === "response_received") {
          const values = Object.values(data)[0] as string;
          dispatch(
            updateRFIDetails({
              ...data,
              response_received: !!values
                ? dayjs(values).format(date_format)
                : "",
            })
          );
        } else {
          dispatch(updateRFIDetails(data));
        }
      } else {
        notification.error({
          message: "Error",
          description: response.message,
        });
      }
      return response;
    },
    []
  );

  const handleUpdate = useCallback(
    async (key: string, value: string | number | null) => {
      if (!rfiDetail) return;

      let data = {
        [key]: value,
      };

      if (
        (key === "response_needed_by" || key === "response_received") &&
        value
      ) {
        let parsedDate;
        if (date_format === "DD/MM/YYYY") {
          parsedDate = dayjs(value, "DD/MM/YYYY", true);
        } else if (date_format === "MM/DD/YYYY") {
          parsedDate = dayjs(value, "MM/DD/YYYY", true);
        } else {
          parsedDate = dayjs(
            value,
            ["YYYY-MM-DD", "DD.MM.YYYY", "DD/MMM/YYYY"],
            true
          );
        }

        data = {
          [key]: parsedDate.isValid() ? parsedDate.format("YYYY-MM-DD") : null,
        };
      }

      handleChangeFieldStatus({
        field: key,
        status: "loading",
        action: "API",
      });

      const { success } = await submitUpdate(data);

      if (success) {
        handleChangeFieldStatus({
          field: key,
          status: "success",
          action: "API",
        });
      }

      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatusRef.current,
          key
        );
        handleChangeFieldStatus({
          field: key,
          status: fieldAction === "FOCUS" ? "save" : "button",
          action: fieldAction || "API",
        });
      }, 1000);
    },
    [rfiDetail]
  );

  const handleUpdateField = async (data: Partial<IRFIDetails>) => {
    const field = Object.keys(data)[0] as keyof IRFIDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateRfiDetailsApi({
      correspondence_id: RFI_Id,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateRFIDetails(data));
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 1000);
  };

  useEffect(() => {
    if (rfiDetail?.correspondence_id) {
      setInputValues(rfiDetail);
    }
  }, [rfiDetail]);

  const handleResponseNeededDateChange = debounce((dateString: string) => {
    if (dateString === "") {
      setInputValues({
        ...inputValues,
        response_needed_by: dateString,
      });
      handleUpdateField({
        response_needed_by: null,
      });
      return false;
    } else {
      const isValidFormat = dayjs(
        dateString,
        CFConfig.day_js_date_format,
        true
      ).isValid();

      if (isValidFormat) {
        const selectedDate = dayjs(
          dateString,
          CFConfig.day_js_date_format,
          true
        );
        const formattedDate = selectedDate.format(CFConfig.day_js_date_format);
        const ResolutionDate = dayjs(dateString, CFConfig.day_js_date_format);
        const correspondencedate = dayjs(
          rfiDetail?.correspondence_date,
          CFConfig.day_js_date_format
        );

        if (ResolutionDate.isBefore(correspondencedate)) {
          notification.error({
            description:"Response Needed by must be greater than or equal to Date value."
          });
          setInputValues((prev) => ({
            ...prev,
            response_needed_by: rfiDetail?.response_needed_by,
          }));

          return false;
        }
        handleUpdate("response_needed_by", formattedDate);
        setInputValues({
          ...inputValues,
          response_needed_by: formattedDate,
        });
        // handleUpdateField({
        //   response_needed_by: formattedDate,
        // });
      }
    }
  }, 100);

  const handleResponseReceivedDateChange = debounce((dateString: string) => {
    if (dateString === "") {
      setInputValues((prev) => ({
        ...prev,
        response_received: "",
      }));
      handleUpdateField({
        response_received: null,
      });
      return false;
    }

    const isValidFormat = dayjs(
      dateString,
      CFConfig.day_js_date_format,
      true
    ).isValid();
    if (!isValidFormat) return false;

    const selectedDate = dayjs(dateString, CFConfig.day_js_date_format, true);
    const today = dayjs().startOf("day");
    const ResolutionReceived = dayjs(dateString, CFConfig.day_js_date_format);
    const correspondencedate = dayjs(
      rfiDetail?.correspondence_date,
      CFConfig.day_js_date_format
    );

    const formattedDate = selectedDate.format(CFConfig.day_js_date_format);

    if (ResolutionReceived.isBefore(correspondencedate)) {
      notification.error({
        description:
          "Response Received must be greater than or equal to Date value.",
      });
      setInputValues((prev) => ({
        ...prev,
        response_received: rfiDetail?.response_received,
      }));

      return false;
    }

    if (selectedDate.isAfter(today)) {
      notification.error({
        description: "Response Received cannot be after current Date",
      });
      setInputValues((prev) => ({
        ...prev,
        response_received: rfiDetail?.response_received,
      }));

      return false;
    }

    handleUpdate("response_received", formattedDate);
    setInputValues((prev) => ({
      ...prev,
      response_received: formattedDate,
    }));
    if (rfiDetail.rfi_status?.toString() === "323") {
      handleUpdateField({
        rfi_status: rfiDetail?.rfi_status,
        response_received: formattedDate,
      });
    } else {
      handleUpdateField({
        rfi_status: 326,
        response_received: formattedDate,
      });
    }
  }, 100);

  const handleInpOnChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Response")}
        iconProps={{
          icon: "fa-solid fa-hourglass-half",
          containerClassName:
            "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
          id: "rfi_response_icon",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li>
                <DatePickerField
                  label={_t("Response Needed By")}
                  placeholder={_t("Select Date")}
                  labelPlacement="left"
                  labelClass="sm:w-[180px] sm:max-w-[180px]"
                  name="response_needed_by"
                  disabled={isReadOnly}
                  readOnly={isReadOnly}
                  inputReadOnly={true}
                  editInline={true}
                  iconView={true}
                  allowClear={true}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "response_needed_by"
                  )}
                  value={
                    inputValues.response_needed_by
                      ? dayjs(
                          inputValues.response_needed_by,
                          CFConfig.day_js_date_format
                        )
                      : null
                  }
                  onChange={(_, dateString) =>
                    handleResponseNeededDateChange(dateString as string)
                  }
                  format={date_format}
                />
              </li>
              <li>
                <DatePickerField
                  label={_t("Response Received")}
                  placeholder={_t("Select Date")}
                  labelPlacement="left"
                  labelClass="sm:w-[180px] sm:max-w-[180px]"
                  name="response_received"
                  disabled={isReadOnly}
                  readOnly={isReadOnly}
                  inputReadOnly={true}
                  editInline={true}
                  iconView={true}
                  allowClear={
                    rfiDetail.rfi_status?.toString() === "323" ? true : false
                  }
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "response_received"
                  )}
                  value={
                    inputValues.response_received
                      ? dayjs(
                          inputValues.response_received,
                          CFConfig.day_js_date_format
                        )
                      : null
                  }
                  onChange={(_, dateString) =>
                    handleResponseReceivedDateChange(dateString as string)
                  }
                  format={date_format}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Response")}
                  placeholder={_t("Response")}
                  labelPlacement="left"
                  labelClass="sm:w-[180px] sm:max-w-[180px]"
                  name="resolution_notes"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  ref={descriptionFieldRef}
                  value={HTMLEntities.decode(
                    inputValues.resolution_notes ?? ""
                  )}
                  onClickStsIcon={() => {
                    if (
                      getStatusForField(loadingStatus, "resolution_notes") ===
                      "edit"
                    ) {
                      descriptionFieldRef?.current?.focus();
                    }
                  }}
                  onChange={handleInpOnChange}
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== rfiDetail.resolution_notes) {
                      handleUpdateField({ resolution_notes: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "resolution_notes",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        resolution_notes: rfiDetail.resolution_notes,
                      });
                    }
                  }}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "resolution_notes"
                  )}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "resolution_notes",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "resolution_notes",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "resolution_notes",
                      status: "button",
                      action: "ML",
                    });
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />
    </>
  );
};

export default Response;
