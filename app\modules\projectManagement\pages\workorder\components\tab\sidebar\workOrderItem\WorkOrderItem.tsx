import isEmpty from "lodash/isEmpty";
import { useTranslation } from "~/hook";
import { type RadioChangeEvent } from "antd";
import * as Yup from "yup";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useEffect, useMemo, useRef, useState } from "react";
import { workOrderItemsField } from "../../../../utils/constasnts";
import { defaultConfig } from "~/data";
import { useWoAppDispatch, useWoAppSelector } from "../../../../redux/store";
import { useFormik } from "formik";
import {
  filterOptionBySubstring,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { Form, useParams } from "@remix-run/react";
import {
  addWorkorderItem,
  updateWorkorderItem,
} from "../../../../redux/action/workorderDetailsAction";
import { updateWorkorderItems } from "../../../../redux/slices/workorderDetailsSlice";
import { onKeyDownNumber } from "~/helpers/key-down.helper";
import type { InputRef } from "antd";
import { getGConfig, getGSettings } from "~/zustand";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { floatNumberRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { isValidId } from "~/modules/financials/pages/estimates/utils/common";

const WorkOrderItem = ({
  isOpenViewWorkOrderItem,
  setIsOpenViewWorkOrderItem,
  isViewOnly,
  formData,
  isWrokOrderItemAdd,
  setWorkorderItemToView,
  isReadOnly,
  setIsWrokOrderItemAdd = () => {},
  filteredItems,
}: IWorkOrderItemProps) => {
  const { _t } = useTranslation();
  const params = useParams();
  const gConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const { is_cidb_auto_save } = gSettings;
  const [markup, setMarkup] = useState<string>("markup_percent");
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [inputValues, setInputValues] =
    useState<IWorkorderDetailsItem>(workOrderItemsField);
  const [submitAction, setSubmitAction] = useState<string>("");
  const [isMuPercentFieldChanged, setIsMuPercentFieldChanged] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});

  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const unitCostRef = useRef<InputRef>(null);
  const [isPrevNext, setIsPrevNext] = useState<boolean>(false);
  const { items, details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const { itemTypes }: IWorkorderItemTypes = useWoAppSelector(
    (state) => state.workorderItemTypes
  );
  const { codeCostData }: IGetCostCodeList = useWoAppSelector(
    (state) => state.costCode
  );
  const dispatch = useWoAppDispatch();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const currentCurrency = gSettings?.currency_symbol ?? "$";
  const [cidbItemExistPopupData, setCidbItemExistPopupData] =
    useState<ICidbItemExistPopupData | null>();
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  useEffect(() => {
    getUnit();
  }, []);

  const costCodeOptions = useMemo(() => {
    let costCodeOpts = codeCostData.map((item: ICostCode) => {
      return {
        label: `${item.cost_code_name}${
          item.csi_code ? ` (${item.csi_code})` : ""
        }${item.is_deleted === 1 ? ` (Archived)` : ""}`,
        value: item.code_id,
      };
    });

    if (!isWrokOrderItemAdd && formData?.is_deleted === 1) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          label: `${formData?.cost_code_name}${
            formData.csi_code ? ` (${formData.csi_code})` : ""
          } (Archived)`,
          value: formData.cost_code_id as string,
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData]);

  const itemTypeOptions = useMemo(
    () =>
      itemTypes.length
        ? itemTypes
            .filter((item: IWorkOrderType) => item.type_id)
            .map((item: IWorkOrderType) => {
              return {
                // label: item?.name,
                label: (
                  <div className="flex items-center gap-1.5">
                    <FontAwesomeIcon
                      icon={getItemTypeIcon({
                        type: item?.type_id?.toString(),
                      })}
                    />
                    {item?.name}
                  </div>
                ),
                value: item.type_id?.toString(),
              };
            })
        : [],
    [itemTypes]
  );

  useEffect(() => {
    if (isWrokOrderItemAdd === false && formData) {
      setInputValues(formData);
    }
  }, [formData]);

  useEffect(() => {
    if (isWrokOrderItemAdd === false) {
      if (formData?.is_markup_percentage === 1) {
        setMarkup("markup_percent");
      } else {
        setMarkup("markup_dolar");
      }
    }
  }, [formData?.is_markup_percentage]);

  // Keep it for future use
  const intialMarkupForAdd = useMemo(() => {
    return itemTypes?.find((i) => !i.type_id)?.mark_up || "";
  }, [itemTypes]);

  const initialValues: IWorkorderDetailsItem = useMemo(() => {
    return isWrokOrderItemAdd === false
      ? {
          ...formData,
          unit_cost: (Number(formData?.unit_cost || 0) / 100)
            ?.toFixed(2)
            .toString(),
          markup:
            formData?.is_markup_percentage === 0
              ? formData?.markup != null
                ? (Number(formData?.markup) / 100)?.toString()
                : null
              : formData?.markup,
          add_item_to_database: formData?.item_on_database,
        }
      : {
          ...inputValues,
          is_markup_percentage: 1,
          add_item_to_database: is_cidb_auto_save,
          apply_global_tax: 0,
          markup: intialMarkupForAdd,
        };
  }, [isWrokOrderItemAdd, formData, inputValues, is_cidb_auto_save]);

  const [initialValuesState, setInitialValuesState] =
    useState<IWorkorderDetailsItem>(initialValues);
  const validationSchema = Yup.object().shape({
    subject: Yup.string()
      .required("This field is required.")
      .test("not-blank", "This field is required.", (value) => {
        return !isEmpty(value) && !!value?.trim().length;
      }),
    item_type: Yup.string().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values, { setSubmitting }) => {
      if (!values.subject?.trim()) {
        formik.setFieldError("subject", _t("This field is required."));
        return;
      }
      let response: IWorkorderDetailsAddItemApiRes | null = null;
      if (isWrokOrderItemAdd) {
        const workorderItem = {
          work_order_id: Number(params?.id),
          items: [
            {
              subject: values?.subject,
              quantity: values?.quantity,
              unit: values?.unit,
              unit_cost: Number(values?.unit_cost) * 100,
              cost_code_id: values?.cost_code_id,
              tax_id: values?.tax_id,
              markup: values?.markup?.toString()
                ? values?.is_markup_percentage === 0
                  ? (Number(values?.markup) * 100)?.toString()
                  : values?.markup?.toString()
                : null,
              is_markup_percentage: values?.is_markup_percentage,
              total: Number(mainTotal) * 100,
              item_type: values?.item_type,
              assigned_to: values?.assigned_to,
              assigned_to_contact_id: values?.assigned_to_contact_id,
              contractor_id: "",
              contractor_contact_id: 0,
              add_item_to_database: cidbItemExistPopupData?.data
                ?.reference_item_id
                ? 0
                : values?.add_item_to_database,
              description: values?.description,
              internal_notes: values?.internal_notes,
              apply_global_tax: values.apply_global_tax,
              reference_item_id: cidbItemExistPopupData?.data?.reference_item_id
                ? cidbItemExistPopupData?.data?.reference_item_id
                : "",
            },
          ],
        };
        const formData = getValuableObj(workorderItem);
        response = (await addWorkorderItem(
          formData as IWorkOrderAddItemParmas
        )) as IWorkorderDetailsAddItemApiRes;

        if (response?.success) {
          const newItem = response.data[0];
          const newItems = [...items, newItem];
          dispatch(updateWorkorderItems({ items: newItems }));
        } else {
          if (isValidId(response?.data?.reference_item_id)) {
            setCidbItemExistPopupData(response);
          } else {
            notification.error({
              description: response?.message,
            });
          }
          setSubmitting(false);
        }
      } else {
        const workorderItem = {
          work_order_id: Number(params?.id),
          is_single_item: 1,
          items: [
            {
              item_id: values.item_id,
              subject: values?.subject,
              quantity: values?.quantity ?? "",
              unit: values?.unit,
              unit_cost: Number(values?.unit_cost) * 100,
              cost_code_id: values?.cost_code_id ?? "",
              tax_id: values?.tax_id,
              markup: values?.markup?.toString()
                ? values?.is_markup_percentage === 0
                  ? (Number(values?.markup) * 100)?.toString()
                  : values?.markup?.toString()
                : null,
              is_markup_percentage: values?.is_markup_percentage,
              total: Number(mainTotal) * 100,
              item_type: values?.item_type,
              assigned_to: values?.assigned_to,
              assigned_to_contact_id: values?.assigned_to_contact_id,
              contractor_id: "",
              contractor_contact_id: 0,
              // add_item_to_database: !!values?.add_item_to_database
              //   ? values?.add_item_to_database
              //   : 0,
              description: values?.description,
              internal_notes: values?.internal_notes,
              apply_global_tax: values.apply_global_tax,
              reference_item_id: cidbItemExistPopupData?.data?.reference_item_id
                ? cidbItemExistPopupData?.data?.reference_item_id
                : "",
            },
          ],
        };
        const formData = getValuableObj(workorderItem);
        response = (await updateWorkorderItem(
          formData as IWorkOrderUpdateItemParmas
        )) as IWorkorderDetailsAddItemApiRes;
        if (response?.success === true) {
          const newItem = response.data[0];
          const newItems = items.map((item) => {
            if (item.item_id === response.data[0].item_id) {
              return newItem;
            } else {
              return item;
            }
          });
          if (isPrevNext) {
            dispatch(updateWorkorderItems({ items: newItems }));
            setIsPrevNext(false);
          }
          if (
            submitAction === "save_n_close" ||
            submitAction === "save_n_add_another"
          ) {
            dispatch(updateWorkorderItems({ items: newItems }));
          }
        } else {
          if (isValidId(response?.data?.reference_item_id)) {
            setCidbItemExistPopupData(response);
          } else {
            notification.error({
              description: response?.message,
            });
          }
        }
      }

      if (
        submitAction === "save_n_close" &&
        (!response?.data?.reference_item_id ||
          !isValidId(response?.data?.reference_item_id))
      ) {
        setIsOpenViewWorkOrderItem(false);
      } else if (submitAction === "save_n_add_another") {
        formik.handleReset(1);
        setInputValues(workOrderItemsField);
        setIsWrokOrderItemAdd(true);
      }
      setSubmitting(false);
    },
  });

  const { handleSubmit, setFieldValue, values, errors, isSubmitting } = formik;

  useEffect(() => {
    formik.setValues(initialValues);
  }, [formData, isOpenViewWorkOrderItem]);

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (
      !unitCostContainerRef.current?.contains(e.relatedTarget as Node) &&
      !isWrokOrderItemAdd
    ) {
      if (Number(formData?.unit_cost) === 0) {
        setInputValues({
          ...formData,
          unit_cost: "",
          total: "0",
        });
      }
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values.unit &&
        !isEmpty(values.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  useEffect(() => {
    if (
      values?.quantity !== "" &&
      values?.unit_cost !== "" &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
      setInitialValuesState((prevState: IWorkorderDetailsItem) => ({
        ...prevState,
        total: total.toString(),
      }));
      setMainTotal(Number(values?.markup_amount) + Number(values?.total));
    } else {
      setFieldValue("total", "");
      setInitialValuesState((prevState: IWorkorderDetailsItem) => ({
        ...prevState,
        total: "",
      }));
      setMainTotal("");
    }
    if (
      values?.total !== "" &&
      values?.total &&
      values?.markup !== "" &&
      values?.markup
    ) {
      if (markup === "markup_percent") {
        const markup = (Number(values?.total) * Number(values?.markup)) / 100;
        setFieldValue("markup_amount", markup);
        setInitialValuesState((prevState: IWorkorderDetailsItem) => ({
          ...prevState,
          markup_amount: markup,
        }));
        setMainTotal(Number(markup) + Number(values?.total));
      } else {
        const markup = Number(values?.markup);
        if (
          markup !== 0 &&
          values?.unit_cost != "0.00" &&
          values?.unit_cost != "0"
        ) {
          const markupPercentage =
            (Number(markup) * 100) / (Number(values?.total) || 1) - 100;
          setFieldValue("markup_amount", markupPercentage.toFixed(2));
          setInitialValuesState((prevState: IWorkorderDetailsItem) => ({
            ...prevState,
            markup_amount: markupPercentage.toFixed(2),
          }));
          setMainTotal(markup);
        } else {
          setFieldValue("markup_amount", "0");
          setMainTotal(Number(values?.total));
          setInitialValuesState((prevState: IWorkorderDetailsItem) => ({
            ...prevState,
            markup_amount: "0",
          }));
        }
      }
    } else {
      setFieldValue("markup_amount", "");
      setInitialValuesState((prevState: IWorkorderDetailsItem) => ({
        ...prevState,
        markup_amount: "",
      }));
      setMainTotal(Number(values?.total));
    }
  }, [
    values?.quantity,
    values?.unit_cost,
    values?.total,
    values?.markup,
    values?.is_markup_percentage,
    markup,
  ]);

  const assignedTo = useMemo(() => {
    if (
      values?.assigned_to !== 0 &&
      values?.assigned_to !== "" &&
      values?.assigned_to
    ) {
      const assigned_to = [
        {
          display_name: values?.assignee_name,
          user_id: Number(values?.assigned_to),
          contact_id: Number(values?.assigned_to_contact_id) ?? 0,
          type: values?.assignee_type,
          type_key: getDirectaryKeyById(Number(values?.assignee_type), gConfig),
          type_name: values?.type_name,
          image:
            values?.assigned_to_contact_id === 0 ||
            values?.assigned_to_contact_id === null
              ? values?.user_image
              : "",
        },
      ];
      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [
    values?.assigned_to,
    values.assignee_name,
    values.assignee_type,
    values.assigned_to_contact_id,
    values.type_name,
  ]);

  useMemo(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [isWrokOrderItemAdd, formData, values]);

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show =
      isWrokOrderItemAdd ||
      (values.reference_item_id === 0 && values.reference_module_item_id === 0);

    const disable = !isWrokOrderItemAdd && !!values.item_on_database;

    return { show, disable };
  }, [isWrokOrderItemAdd, values]);

  const handleSaveItem = (key: string) => {
    setSubmitAction(key);
  };

  const currentItemIndex = useMemo(() => {
    const curItemIndex = filteredItems?.findIndex(
      (i) => i.item_id === formData?.item_id
    );

    return curItemIndex;
  }, [filteredItems, formData]);

  const handlePrevNextItem = async (increment: number) => {
    const hasChanges = formik.dirty;

    if (hasChanges && isEmpty(formik.errors)) {
      setIsPrevNext(true);
      await formik.submitForm();
      setWorkorderItemToView?.(filteredItems[currentItemIndex + increment]);
    }
  };

  const WORK_ORDER_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {currentCurrency}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];

  const saveItemKey = useMemo(() => {
    const itemType = itemTypes?.find(
      (i: IWorkOrderType) =>
        i.type_id?.toString() === values?.item_type?.toString()
    );

    return itemType?.name;
  }, [itemTypes, values?.item_type]);

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };

  return (
    <>
      <Drawer
        open={isOpenViewWorkOrderItem}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-clipboard-list"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {!isWrokOrderItemAdd
                  ? _t(`${gConfig.module_singular_name} Item`)
                  : _t(`Add ${gConfig.module_singular_name} Item`)}
              </Header>
              {!isWrokOrderItemAdd && (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Previous")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-left"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={() => handlePrevNextItem(-1)}
                    disabled={currentItemIndex === 0}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={_t("Next")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-right"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={() => handlePrevNextItem(1)}
                    disabled={currentItemIndex === filteredItems.length - 1}
                  />
                </div>
              )}
            </div>
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        // closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
        closeIcon={
          <CloseButton onClick={() => setIsOpenViewWorkOrderItem(false)} />
        }
      >
        <Form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    id="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    errorMessage={
                      formik.touched?.subject ||
                      /^\s+$/.test(values?.subject as string)
                        ? errors.subject
                        : ""
                    }
                    disabled={isViewOnly}
                    isRequired={true}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value);
                    }}
                    onBlur={formik.handleBlur}
                    autoComplete="off"
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      name="item_type"
                      errorMessage={
                        formik.touched?.item_type && !values.item_type
                          ? errors.item_type
                          : ""
                      }
                      disabled={
                        isViewOnly ||
                        !itemTypeAndSaveItemToListField.show ||
                        itemTypeAndSaveItemToListField.disable
                      }
                      value={
                        values?.item_type !== 0
                          ? HTMLEntities.decode(
                              sanitizeString(values?.item_type?.toString())
                            )
                          : "" // Show nothing (blank) if item_type is "0"
                      }
                      onChange={(value) => {
                        setFieldValue("item_type", value);

                        if (!isMuPercentFieldChanged) {
                          const itemType = itemTypes?.find(
                            (i: IWorkOrderType) =>
                              i.type_id?.toString() === value?.toString()
                          );

                          setFieldValue("markup", itemType?.mark_up || "");
                        }
                      }}
                      onBlur={formik.handleBlur}
                      options={itemTypeOptions}
                    />
                  </div>
                  <div className="w-full relative">
                    <ButtonField
                      label={_t("Assigned To")}
                      name="assigned_to"
                      labelPlacement="top"
                      disabled={isReadOnly}
                      value={HTMLEntities.decode(
                        sanitizeString(
                          values?.assigned_to !== 0 ? values?.assignee_name : ""
                        )
                      )}
                      onClick={() => {
                        setIsOpenSelectAssignedTo(true);
                      }}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values?.assignee_name)
                          ),
                          image:
                            values?.assigned_to_contact_id === 0 ||
                            values?.assigned_to_contact_id === null
                              ? values?.user_image
                              : "",
                        },
                      }}
                      addonBefore={
                        values?.assigned_to ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setContactDetailDialogOpen(true);
                              }}
                            />
                          </div>
                        ) : (
                          <></>
                        )
                      }
                    />
                  </div>
                </div>

                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    value={
                      values?.cost_code_id
                        ? costCodeOptions.filter((item) => {
                            return (
                              values?.cost_code_id?.toString() ===
                              item?.value?.toString()
                            );
                          })
                        : []
                    }
                    onChange={(value) => {
                      setFieldValue("cost_code_id", value);
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    options={costCodeOptions}
                    disabled={isReadOnly}
                    allowClear={true}
                    onClear={() => {
                      setFieldValue("cost_code_id", "");
                    }}
                  />
                </div>
              </SidebarCardBorder>

              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("QTY")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                        placeholder={_t("Item Quantity")}
                        disabled={isViewOnly}
                        errorMessage={errors.quantity}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ? "flex items-center justify-end" : ""
                        }
                        onPaste={handlePaste}
                        defaultValue={
                          Number(values.quantity) !== 0 ? values.quantity : ""
                        }
                        value={
                          Number(values.quantity) !== 0
                            ? values.quantity?.toString()
                            : ""
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits: 6,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                        onPressEnter={handleEnterKeyPress}
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                      >
                        {!isViewOnly && (
                          <>
                            {showUnitInputs ? (
                              <div className="flex gap-2">
                                <div className="w-[calc(100%-52px)]">
                                  <InputNumberField
                                    name="unit_cost"
                                    id="unit_cost"
                                    rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                    placeholder={_t("Item Unit Cost")}
                                    disabled={isViewOnly}
                                    labelPlacement="left"
                                    errorMessage={errors.unit_cost}
                                    onPaste={handlePaste}
                                    autoFocus={Boolean(
                                      values?.unit_cost &&
                                        !isEmpty(values?.unit_cost) &&
                                        values.unit &&
                                        !isEmpty(values.unit)
                                    )}
                                    defaultValue={
                                      Number(values.unit_cost) !== 0
                                        ? values.unit_cost
                                        : ""
                                    }
                                    value={
                                      Number(values.unit_cost) !== 0
                                        ? values.unit_cost
                                        : ""
                                    }
                                    onChange={(value) => {
                                      let formattedValue = value
                                        ?.toString()
                                        .trim();
                                      if (formattedValue?.startsWith("-")) {
                                        formattedValue =
                                          formattedValue?.replace("-", "");
                                      }
                                      setFieldValue(
                                        "unit_cost",
                                        formattedValue
                                      );
                                    }}
                                    formatter={(value, info) => {
                                      return inputFormatter(value?.toString())
                                        .value;
                                    }}
                                    parser={(value) => {
                                      const inputValue = value
                                        ? unformatted(value.toString())
                                        : "";
                                      return inputValue;
                                    }}
                                    onKeyDown={(event) => {
                                      onKeyDownCurrency(event, {
                                        integerDigits: 10,
                                        decimalDigits: 2,
                                        unformatted,
                                        allowNegative: false,
                                        decimalSeparator:
                                          inputFormatter().decimal_separator,
                                      });
                                    }}
                                    onBlur={handleFocusOut}
                                  />
                                </div>
                                <div className="w-[62px]">
                                  {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      disabled={isViewOnly}
                                      labelPlacement="left"
                                      maxLength={15}
                                      value={values?.unit || null}
                                      iconView={true}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitData.map((type) => ({
                                          label: type.name.toString(),
                                          value: type.name.toString(),
                                        })) ?? []
                                      }
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      onChange={(value) => {
                                        setFieldValue("unit", value);
                                      }}
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onInputKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          const value =
                                            e?.currentTarget?.value?.trim();
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitData?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewTypeName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        }
                                      }}
                                      onClear={() => {
                                        setFieldValue("unit", "");
                                      }}
                                      errorMessage={errors.unit}
                                      onBlur={handleFocusOut}
                                    />
                                  ) : (
                                    <InputField
                                      className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                        !showUnitInputs && "!hidden"
                                      }`}
                                      placeholder={_t("Unit")}
                                      labelPlacement="left"
                                      errorMessage={errors.unit}
                                      maxLength={15}
                                      onBlur={handleFocusOut}
                                      value={values?.unit}
                                      disabled={isViewOnly}
                                      type="text"
                                      onChange={(e) => {
                                        setFieldValue("unit", e.target.value);
                                      }}
                                      onPressEnter={handleEnterKeyPress}
                                    />
                                  )}
                                </div>
                              </div>
                            ) : (
                              <Typography
                                className="text-[#008000] cursor-pointer text-13 font-medium"
                                onClick={handleParagraphClick}
                                disabled={isViewOnly}
                              >
                                {
                                  formatter(
                                    formatAmount(
                                      Number(values?.unit_cost).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                                /{values?.unit}
                              </Typography>
                            )}
                          </>
                        )}

                        {isViewOnly &&
                          (!isEmpty(values?.unit_cost) &&
                          values?.unit_cost !== 0.0 &&
                          values?.unit_cost !== "0.00" &&
                          !isEmpty(values?.unit) &&
                          !!values?.unit ? (
                            <Typography
                              className={`text-[#008000] font-medium text-13 ${
                                isViewOnly ? "cursor-no-drop" : ""
                              }`}
                            >
                              {values?.unit_cost}/{values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <div className="w-[calc(100%-52px)]">
                                <InputField
                                  ref={unitCostRef}
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Item Unit Cost")}
                                  type="number"
                                  name="unit_cost"
                                  id="unit_cost"
                                  maxLength={10}
                                  disabled={isViewOnly}
                                  value={values?.unit_cost}
                                  onPaste={handlePaste}
                                  onChange={() => {}}
                                  onPressEnter={handleEnterKeyPress}
                                />
                              </div>
                              <div className="w-11">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  name="unit"
                                  id="unit"
                                  disabled={isViewOnly}
                                  value={values?.unit}
                                  onPaste={handlePaste}
                                  type="text"
                                  onChange={() => {}}
                                  onPressEnter={handleEnterKeyPress}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {values?.total === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  Number(values?.total || 0).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>

                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
                <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          value={markup ? markup : ""}
                          options={WORK_ORDER_ITEMS_LIST_TAB}
                          disabled={isViewOnly}
                          className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                          activeclassName="active:bg-[#ffffff]"
                          onChange={(e: RadioChangeEvent) => {
                            setMarkup(e.target.value);
                            setFieldValue("markup", "");
                            setIsMuPercentFieldChanged(true);
                            if (e.target.value === "markup_percent") {
                              setFieldValue("is_markup_percentage", 1);
                            } else {
                              setFieldValue("is_markup_percentage", 0);
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${currentCurrency} -- Add the ${currentCurrency} amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <div className="sm:w-40 w-28">
                      <InputField
                        className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                        placeholder={
                          markup === "markup_percent"
                            ? _t("Item Markup") + " %"
                            : _t("Total Sales Price")
                        }
                        onPaste={handlePaste}
                        onChange={(e) => {
                          const input = e.target.value;

                          if (input === "") {
                            setFieldValue("markup", null);
                            return;
                          }

                          if (!floatNumberRegex.test(input)) {
                            return;
                          }
                          const parsed = Number(input);

                          setFieldValue(
                            "markup",
                            markup === "markup_percent"
                              ? parsed < 0 || isNaN(parsed)
                                ? 0
                                : parsed
                              : input
                          );
                        }}
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          return onKeyDownNumber(event, {
                            decimalDigits: 2,
                            integerDigits: markup === "markup_percent" ? 3 : 8,
                            allowNegative: false,
                          });
                        }}
                        value={values?.markup ?? ""}
                        labelPlacement="left"
                        type="text"
                        disabled={
                          details?.work_order_status_name === "Approved" ||
                          details?.work_order_status_name === "Completed" ||
                          isViewOnly
                        }
                      />
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("Markup")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={
                          details?.work_order_status_name !== "Approved"
                        }
                      >
                        {markup === "markup_percent"
                          ? values?.markup_amount === ""
                            ? `${
                                formatter(formatAmount("0.00"))
                                  .value_with_symbol
                              }`
                            : `${
                                formatter(
                                  formatAmount(
                                    Number(values?.markup_amount || 0).toFixed(
                                      2
                                    )
                                  )
                                ).value_with_symbol
                              }`
                          : values?.markup_amount === ""
                          ? "0.00%"
                          : `${Number(values?.markup_amount || 0)?.toFixed(
                              2
                            )}%`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-equals"
                    />
                  </li>
                </ul>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Revenue")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {mainTotal === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(Number(mainTotal || 0).toFixed(2))
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>

              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    required={false}
                    label={_t("Description")}
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    labelPlacement="top"
                    disabled={isReadOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={isReadOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    onChange={(e) => {
                      setFieldValue("internal_notes", e.target.value);
                    }}
                  />
                </div>
                {values.item_type ? (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={
                      !!values?.add_item_to_database ||
                      isValidId(values?.reference_item_id)
                    }
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("add_item_to_database", valueToSet);
                    }}
                    disabled={
                      isViewOnly ||
                      !itemTypeAndSaveItemToListField.show ||
                      itemTypeAndSaveItemToListField.disable
                    }
                  >
                    {_t(`Save this item into my ${saveItemKey} Items list?`)}
                  </CheckBox>
                ) : (
                  ""
                )}
                <CheckBox
                  className="gap-1.5 w-fit"
                  disabled={isViewOnly}
                  checked={!!values.apply_global_tax}
                  onChange={(e) => {
                    setFieldValue("apply_global_tax", e.target.checked ? 1 : 0);
                  }}
                >
                  {_t("Collect Tax on this Item?")}
                </CheckBox>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
            <Button
              type="primary"
              className="w-full primary-btn justify-center"
              htmlType="submit"
              disabled={
                // !values.subject || //commented for 86cy910d7
                // !values.item_type || //commented for 86cy910d7
                isSubmitting || isReadOnly
              }
              onClick={() => handleSaveItem("save_n_close")}
              name="save_n_close"
              loading={isSubmitting && submitAction === "save_n_close"}
            >
              {_t("Save & Close")}
            </Button>
            {isViewOnly ? (
              ""
            ) : (
              <Button
                type="primary"
                className="w-full primary-btn justify-center"
                htmlType="submit"
                disabled={
                  // !values.subject || //commented for 86cy910d7
                  // !values.item_type || //commented for 86cy910d7
                  isSubmitting || isReadOnly
                }
                loading={isSubmitting && submitAction === "save_n_add_another"}
                onClick={() => handleSaveItem("save_n_add_another")}
                name="save_n_add_another"
              >
                {_t("Save & Add Another Item")}
              </Button>
            )}
          </div>
        </Form>
      </Drawer>

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            if (data.length) {
              // const assigneeName = `${data[0].display_name} ${
              //   (data as CustomerEmail[])[0]?.company_name
              //     ? `(${(data as CustomerEmail[])[0]?.company_name})`
              //     : ""
              // }`;
              if (data[0].contact_id == "0") {
                setFieldValue("assigned_to_contact_id", data[0].contact_id);
              } else {
                setFieldValue("assigned_to_contact_id", data[0].contact_id);
              }
              setFieldValue("assigned_to", data[0].user_id);
              setFieldValue("assignee_name", data[0].display_name);
              setFieldValue(
                "assignee_type",
                data[0].type ||
                  getDirectaryIdByKey(data[0].type_key as CustomerTabs, gConfig)
              );
              setFieldValue("type_key", data[0].type_key);
              setFieldValue("user_image", data[0]?.image);
              setFieldValue("type_name", data[0].type_name);
            } else {
              setFieldValue("assigned_to", 0);
              setFieldValue("assignee_name", "");
            }
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={assignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={details?.project_id as number}
          activeTab={defaultConfig.contractor_key}
        />
      )}
      {contactDetailDialogOpen && (
        <ContactDetails
          isOpenContact={contactDetailDialogOpen}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isReadOnly}
          additional_contact_id={values?.assigned_to_contact_id}
        />
      )}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />

      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}

      {isValidId(cidbItemExistPopupData?.data?.reference_item_id) && (
        <ConfirmModal
          isOpen={isValidId(cidbItemExistPopupData?.data?.reference_item_id)}
          modaltitle={_t("This item already exists")}
          description={
            cidbItemExistPopupData?.message ??
            `There is already an item associated with this name in your CIDB. Would you like to rename the current item or import the existing item from your CIDB?`
          }
          onAccept={() => {
            setFieldValue(
              "reference_item_id",
              cidbItemExistPopupData?.data?.reference_item_id
            );
            setFieldValue("add_item_to_database", 0);
            setCidbItemExistPopupData(null);
            handleSubmit();
            setIsOpenViewWorkOrderItem(false);
          }}
          yesButtonLabel="Use Existing"
          noButtonLabel="Rename"
          onDecline={() => setCidbItemExistPopupData(null)}
          onCloseModal={() => setCidbItemExistPopupData(null)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-trash-can"
        />
      )}
    </>
  );
};

export default WorkOrderItem;
