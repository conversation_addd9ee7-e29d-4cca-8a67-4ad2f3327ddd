// Atom
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useEffect, useMemo, useReducer, useRef, useState } from "react";
import cloneDeep from "lodash/cloneDeep";
import isEmpty from "lodash/isEmpty";
import { getGModules, getGProject, useGModules } from "~/zustand";

import SidebarSelectHeader from "./component/SidebarSelectHeader";
import SelectOptions from "./component/SelectOption";
import CustomerSelectBody from "./body";
// third-party
import { CFCloseButton } from "~/components/third-party/ant-design/cf-close-button";
import { CFDrawer } from "~/components/third-party/ant-design/cf-drawer";
import { CFIconButton } from "~/components/third-party/ant-design/cf-iconbutton";
import { customerSelectOptions } from "~/components/sidebars/multi-select/customer";

import { defaultConfig } from "~/data";
import { getCustomerDetails } from "~/redux/action/selectCustomerAction";
import { customersDataLimit } from "~/components/sidebars/multi-select/customer/zustand/utils";
import {
  OPTIONS_MODULE_IDS,
  OPTIONS_MODULE_GMODULE_ID_MAP,
} from "../sendEmailDrawer/contstant";
import { useTranslation } from "~/hook";
import AddMultiSelectDir from "../addMultiselectDir/AddMultiselectDir";
import {
  dirTypeKeys,
  dirTypesList,
} from "~/modules/people/directory/utils/constasnts";
import { getUniqueIdDirData } from "../sendEmailDrawer/utils";
import { ConfirmModal } from "../confirmModal";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";

const initialState: TInitialStateSelectCustomers = {
  activeField: defaultConfig.employee_key,
  openSelectCustomerSidebar: false,
  customer: {
    all: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    employee: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    my_crew: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    customer: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    contractor: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    vendor: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    misc_contact: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    lead: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    by_service: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
    my_project: {
      list: [],
      pageNumber: 0,
      search: "",
      mainSearch: "",
      infiniteScrollhasMore: true,
      error: false,
      isFavorite: false,
      isDataFetching: true,
    },
  },
  selectedContact: [],
  receivedContact: [],
  callAgain: 0,
};
function reducer(
  state: TInitialStateSelectCustomers,
  action: ISelectCustomerAction
) {
  switch (action.type) {
    case "SET_ACTIVE_FIELD":
      const state_active = cloneDeep(state);
      if (state_active.activeField !== action.payload) {
        state_active.activeField = action.payload;
        state_active.customer[action.payload].list = [];
        state_active.customer[action.payload].pageNumber = 0;
        state_active.customer[action.payload].infiniteScrollhasMore = true;
        state_active.customer[action.payload].error = false;
        state_active.customer[action.payload].isDataFetching = true;
      }

      return state_active;
    case "SET_SELECTED_CONTACT":
      const contactData = action.payload;
      if (Array.isArray(contactData)) {
        return { ...state, selectedContact: contactData };
      } else if (contactData !== "reset") {
        const isSelected = state.selectedContact.some(
          (contact) =>
            getUniqueIdDirData(contact) === getUniqueIdDirData(contactData)
        );
        const updatedSelectedContact = isSelected
          ? state.selectedContact.filter(
              (contact) =>
                getUniqueIdDirData(contact) !== getUniqueIdDirData(contactData)
            )
          : [...state.selectedContact, contactData];
        return { ...state, selectedContact: updatedSelectedContact };
      } else {
        return { ...state, selectedContact: [] };
      }
    case "SET_RECEIVED_SELECTED_CONTATACT":
      return { ...state, receivedContact: action.payload };
    case "SET_OPEN_SEND_EMAIL_SIDEBAR":
      return { ...state, openSendEmailSidebar: action.payload };
    case "SET_INCREMENT_PAGE_NUMBER":
      const customerField = state.customer[action.payload];
      return {
        ...state,
        customer: {
          ...state.customer,
          [action.payload]: {
            ...customerField,
            pageNumber: customerField.pageNumber + 1,
          },
        },
      };
    case "SET_SEARCH":
      const { activeField, searchText } = action.payload;
      const searchField = state.customer[activeField];
      const trimmedSearchText = searchText.trimStart(); // Cu : https://app.clickup.com/t/86czacfx4
      return {
        ...state,
        customer: {
          ...state.customer,
          [activeField]: {
            ...searchField,
            pageNumber: 0,
            search: trimmedSearchText,
          },
        },
      };
    case "GET_EMAIL_DETAILS_PENDING":
      if (!action.payload.formState) {
        const pendingField = state.customer[action.payload.tab];
        return {
          ...state,
          isDataFetching: true,
          customer: {
            ...state.customer,
            [action.payload.tab]:
              action.payload.pageNumber === 0
                ? {
                    ...pendingField,
                    list: [],
                    infiniteScrollhasMore: true,
                    isDataFetching: true,
                  }
                : {
                    ...pendingField,
                    isDataFetching: true,
                  },
          },
        };
      }
      return state;
    case "SET_ADD_DIRECOTRY_DATA": {
      const { response, tab } = action.payload;
      let { data, success } = response;
      if (success && data) {
        let state_ = cloneDeep(state);

        state_.selectedContact.push(...data);

        const shouldShowFavorites = state_.customer[tab]?.isFavorite;

        // Filter data to include only favorite items if the checkbox is checked
        const filteredData = shouldShowFavorites
          ? data.filter((item) => item.is_favorite === 1)
          : data;
        state_.customer[tab].list.unshift(...filteredData);
        return state_;
      }

      return state;
    }
    case "GET_EMAIL_DETAILS_FULFILLED":
      const { response, tab, formState, app_access, pageNumber, options } =
        action.payload;
      let { data, success } = response;
      let state_ = cloneDeep(state);

      // set loading to true of customer of active field
      state_ = {
        ...state,
        customer: {
          ...state.customer,
          [tab]:
            pageNumber === 0
              ? {
                  ...state.customer[tab],
                  list: [],
                  isDataFetching: false,
                }
              : {
                  ...state.customer[tab],
                  isDataFetching: false,
                },
        },
      };

      const limit =
        tab === "by_service" || tab === "my_crew"
          ? 500
          : app_access
          ? 200
          : customersDataLimit;

      // When app_access is true infinite scroll is not working properly that's why i modified this condition
      if (
        (response.group_details?.length >= 0 &&
          response.group_details?.length < (response.limit || limit)) ||
        response.services?.length >= 0 ||
        // Backend side in service data pagination not implemented yet because of dependecy issue, that why i commont this line
        // && response.services?.length < limit
        (response.data?.length >= 0 &&
          response.data?.length < (response.limit || limit) &&
          !formState)
      ) {
        state_.customer[tab].infiniteScrollhasMore = false;
      }
      if (success) {
        if (formState) {
          state_.selectedContact.push(...data);
          (state_.customer[tab] as CustomersEmailState).list.unshift(...data);
        } else {
          if (app_access) {
            data = data.filter(
              (item) => item.app_access === 1 && item.contact_id?.toString() === "0"
            );
          }
          if (
            tab !== "my_crew" &&
            tab !== "by_service" &&
            tab !== "my_project"
          ) {
            state_.customer[tab].list.push(...data);
          } else if (tab === "my_project") {
            const newData: any = data.filter((item) =>
              options?.includes(item?.type_key as CustomerEmailTab)
            );
            const additioalData = data
              .filter(
                (item) =>
                  item?.type_key === "contact" &&
                  item?.parent_type_key &&
                  item?.parent_type_key !== "" &&
                  options?.includes(item.parent_type_key as CustomerEmailTab)
              )
              .map((objects) => objects);

            if (additioalData?.length !== 0) {
              state_.customer[tab].list = [
                ...state_.customer[tab].list,
                ...newData?.concat(additioalData),
              ];
            } else {
              state_.customer[tab].list = [
                ...state_.customer[tab].list,
                ...newData,
              ];
            }
          } else if (tab === "my_crew") {
            let newGroups: GroupEmailDetail[] = [];
            if (response && Array.isArray(response.group_details)) {
              newGroups = cloneDeep(response.group_details);
              newGroups = newGroups.sort((a, b) =>
                a.group_name.localeCompare(b.group_name)
              );
              newGroups.forEach((group, i) => {
                newGroups[i].employees = group.employees.filter((employee) =>
                  options?.includes(employee?.type_key as string)
                );
              });
            }

            state_.customer.my_crew.list.push(...newGroups);
          } else if (tab === "by_service") {
            const directories = response?.directories.filter((directory) =>
              options?.includes(directory?.type_key as CustomerTabs)
            );
            const services = response?.services;
            const currentData = services?.map(
              (service: DirectoryEmailService) => {
                const serviceData = service.directories
                  .map((directory: number) => {
                    const directoryData = directories.find(
                      (d: DirectoryEmail) => d.user_id === directory
                    );
                    return directoryData;
                  })
                  .filter(Boolean) as DirectoryEmail[];

                let servicesData = JSON.parse(
                  JSON.stringify(service)
                ) as DirectoryEmailServiceList;
                servicesData.directories = serviceData;
                return servicesData;
              }
            );
            state_.customer.by_service.list.push(...currentData);
          }
        }
      }
      return state_;

    case "CHANGE_FAV_ACTION_FULFILLED":
      const { response: res_fav, unique_id, is_favorite } = action.payload;

      const { success: success_fav } = res_fav;
      const state_fav = cloneDeep(state);
      if (success_fav) {
        const index = (
          state_fav.customer[state_fav.activeField] as CustomersEmailState
        ).list.findIndex((item) => {
          return getUniqueIdDirData(item) === unique_id;
        });

        if (index !== -1) {
          (
            state_fav.customer[state_fav.activeField] as CustomersEmailState
          ).list[index].is_favorite = is_favorite;
        }

        // favData.is_favorite remove then if only fav then remove from list
        if (
          state_fav.customer[state_fav.activeField].isFavorite &&
          String(is_favorite) === "0"
        ) {
          state_fav.customer[state_fav.activeField].list.splice(index, 1);
        }

        // in received contact
        const receivedIndex = state_fav.receivedContact.findIndex((item) => {
          return getUniqueIdDirData(item) === unique_id;
        });

        if (receivedIndex !== -1) {
          state_fav.receivedContact[receivedIndex].is_favorite = is_favorite;
        }
      }
      return state_fav;
    case "CHANGE_IS_FAVORITE_MAIN":
      // below code need to check on dev if break anything then revert
      const { is_favorite: isFavorite } = action.payload;
      return {
        ...state,
        customer: {
          ...state.customer,
          [action.payload?.is_all_tab
            ? action.payload?.tab
            : state.activeField]: {
            ...state.customer[
              action.payload?.is_all_tab
                ? action.payload?.tab
                : state.activeField
            ],
            isFavorite: isFavorite,
            list: [],
            pageNumber: 0,
            error: false,
            infiniteScrollhasMore: true,
            isDataFetching: true,
            is_all_tab: true,
          },
        },
        callAgain: 1 - state.callAgain,
      };
    case "SET_MAIN_SEARCH":
      const { activeField: activeFieldSearch, searchText: mainSearchText } =
        action.payload;
      const searchMainField = state.customer[activeFieldSearch];
      const trimmedMainSearchText = mainSearchText.trimStart(); // Cu : https://app.clickup.com/t/86czacfx4
      return {
        ...state,
        customer: {
          ...state.customer,
          [activeFieldSearch]: {
            ...searchMainField,
            pageNumber: 0,
            mainSearch: trimmedMainSearchText,
          },
        },
      };
    case "RESET_ALL_DATA":
      return cloneDeep(initialState);
    default:
      return state;
  }
}

const SelectCustomerDrawer = ({
  projectId,
  options,
  singleSelecte = false,
  closeDrawer = () => {},
  groupCheckBox,
  isViewAttachment = true,
  saveFileModuleName = "",
  isViewSaveThisToFileCheckbox = false,
  openSelectCustomerSidebar,
  setCustomer,
  selectedCustomer,
  showSaveBtn = true,
  app_access = false,
  canWrite = true,
  additionalContactDetails = 1,
  activeTab,
  hideAddBtnForFilter = false,
  showCustomField = true,
  is_all_tab = true,
  modulesShouldNotHaveAddButton = [],
  buttontext = "Save",
  isLoading = false,
  loadingOnSave = false,
}: ISelectCustomerDrawerProps) => {
  const [sendEmail, setSendEmail] = useState<boolean>(false);
  const [sideMenuOpen, setSideMenuOpen] = useState<boolean>(false);
  const [addMultiselectDirectoryOpen, setAddMultiselectDirectoryOpen] =
    useState<boolean>(false);
  const [defaultSelectedContact, setDefaultSelectedContact] =
    useState<TselectedContactSendMail[]>();
  const { project_id }: GProject = getGProject();
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);

  const { getGModuleByKey, checkModuleAccess } = useGModules();

  let { _t } = useTranslation();

  const [selectCustomerData, dispatch] = useReducer(reducer, initialState);

  const {
    activeField,
    customer,
    selectedContact,
    callAgain,
    receivedContact,
  }: TInitialStateSelectCustomers = selectCustomerData;

  const isDataFetching: boolean = customer[activeField].isDataFetching;
  const rightScrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (rightScrollRef.current) {
      rightScrollRef.current.scrollTop = rightScrollRef.current.scrollHeight;
    }
  }, [selectedContact]);

  useEffect(() => {
    if (
      selectedCustomer &&
      selectedCustomer.length &&
      openSelectCustomerSidebar
    ) {
      dispatch({ type: "SET_SELECTED_CONTACT", payload: selectedCustomer });
      dispatch({
        type: "SET_RECEIVED_SELECTED_CONTATACT",
        payload: selectedCustomer,
      });
    }
  }, [openSelectCustomerSidebar]);

  const currentSelected = useRef<Partial<IDrtContactDataItf> | null>(null);

  const scrollRef = useRef<HTMLDivElement>(null);

  const module: GModule[] = getGModules();

  // loadMore -- function to increment the page number
  const loadMore = () => {
    dispatch({ type: "SET_INCREMENT_PAGE_NUMBER", payload: activeField });
  };

  const [optionList, setOptionList] = useState<
    CustomerSelectOption<CustomerEmailTab>[]
  >([]);

  const [emptyOptionList, setEmptyOptionList] = useState<boolean>(false);

  useEffect(() => {
    // // if current active field is not options then set active field to first option
    if (
      !optionList.some((option) => option.value === activeField) &&
      optionList.length > 0 &&
      activeTab === undefined
    ) {
      dispatch({ type: "SET_ACTIVE_FIELD", payload: optionList[0].value });
    } else if (activeTab !== undefined && activeTab !== "") {
      dispatch({ type: "SET_ACTIVE_FIELD", payload: activeTab });
    }
  }, [JSON.stringify(optionList), openSelectCustomerSidebar]);

  useEffect(() => {
    const globalData = module?.filter((data) =>
      [23, 22, 71, 24, 25, 38].includes(data?.module_id)
    );

    const defaultOptions = customerSelectOptions.map(
      (dataItem: CustomerSelectOption<CustomerTabs>) => {
        if (
          !["my_project", "by_service", "my_crew", "all"].includes(
            dataItem?.value
          )
        ) {
          const matchingCustomerOption = globalData.find((option) => {
            return (
              option.module_key === `${dataItem.value}` ||
              option.module_key === `${dataItem.value}s`
            );
          });

          if (matchingCustomerOption) {
            return {
              ...dataItem,
              addButton: Boolean(Number(matchingCustomerOption.can_write)),
            };
          }
        }
        return dataItem;
      }
    );

    const customerOptions = defaultOptions
      .map((option: CustomerSelectOption<CustomerEmailTab>) => {
        // filter out my_project
        if (
          option.value === "my_project" &&
          !projectId
          // (!project_id || project_id === "0")
        ) {
          return null;
        }

        if (options?.includes(option.value)) {
          if (
            OPTIONS_MODULE_GMODULE_ID_MAP[
              option.value as keyof typeof OPTIONS_MODULE_GMODULE_ID_MAP
            ]
          ) {
            const moduleKey =
              OPTIONS_MODULE_GMODULE_ID_MAP[
                option.value as keyof typeof OPTIONS_MODULE_GMODULE_ID_MAP
              ];
            const module = getGModuleByKey(moduleKey);

            if (module && Number(module.hide_tab_in_directory_popup)) {
              return null;
            }

            if (
              option.value !== "my_project" &&
              option.value !== "by_service" &&
              option.value !== "my_crew" &&
              option.value !== "all"
            ) {
              // Check module access
              const moduleId =
                OPTIONS_MODULE_IDS[
                  option.value as keyof typeof OPTIONS_MODULE_IDS
                ];

              if (moduleId) {
                const moduleAccess: TModuleAccessStatus =
                  checkModuleAccess(moduleId);
                if (moduleAccess !== "no_access") {
                  option.addButton =
                    moduleAccess !== "read_only" &&
                    canWrite &&
                    !modulesShouldNotHaveAddButton.includes(option.value);
                }
              }
            }
          }

          return option as CustomerSelectOption<CustomerEmailTab>;
        }
      })
      .filter(Boolean) as CustomerSelectOption<CustomerEmailTab>[];

    if (customerOptions.length === 0) {
      setEmptyOptionList(true);
    }

    setOptionList(customerOptions);
  }, [options, openSelectCustomerSidebar]);

  useEffect(() => {
    dirTypesList?.map((selectOption: IDirTypeOption) => {
      if (
        activeField &&
        OPTIONS_MODULE_GMODULE_ID_MAP[
          activeField as keyof typeof OPTIONS_MODULE_GMODULE_ID_MAP
        ] === selectOption?.key
      ) {
        currentSelected.current = selectOption;
      }
    });

    if (!openSelectCustomerSidebar) {
      return;
    }

    const getData = async () => {
      const my_project_key: string[] = [
        "employee_id",
        "customer_id",
        "contractor_id",
        "vendor_id",
        "misc_contact_id",
        "lead_id",
      ];
      const my_project_ids = my_project_key.map((id: string) => {
        return defaultConfig[id as keyof IDefaultConfig] as number;
      });
      const userID = (selectedContact || [])
        .filter(
          (customerData) =>
            customerData?.contact_id == "0" || !customerData?.contact_id
        )
        .map((customerData) => customerData?.user_id?.toString())
        .filter((userId): userId is string => Boolean(userId));
      const contactID = (selectedContact || [])
        .filter(
          (customerData) =>
            customerData?.contact_id && customerData.contact_id != "0"
        )
        .map((customerData) => customerData?.contact_id?.toString());
      const getEmailDataParams: ISendEmailActionProps = {
        tab: activeField,
        search: customer[activeField].mainSearch,
        page: customer[activeField].pageNumber,
        additionalContactDetails: additionalContactDetails,
        projectId:
          project_id?.toString() && project_id.toString() !== "0"
            ? Number(project_id)
            : projectId,
        selected_users: selectedContact?.length
          ? {
              user_id: userID.length ? userID.join(",") : undefined, // Join all user IDs as a comma-separated string
              contact_id: contactID.length ? contactID.join(",") : undefined,
            }
          : {},
        directoryId:
          (defaultConfig[`${activeField}_id` as keyof IDefaultConfig] !==
            undefined && [
            defaultConfig[
              `${activeField}_id` as keyof IDefaultConfig
            ] as number,
          ]) ||
          (activeField === "my_project" && my_project_ids) ||
          [],
        app_access: app_access,
        globalProject: project_id !== "0" ? project_id : "",
      };

      if (customer[activeField].isFavorite) {
        getEmailDataParams.is_favorite = customer[activeField].isFavorite
          ? 1
          : undefined;
        getEmailDataParams.additionalContactDetails = 0;
      }

      dispatch({
        type: "GET_EMAIL_DETAILS_PENDING",
        payload: {
          tab: activeField,
          pageNumber: customer[activeField].pageNumber,
        },
      });
      const response = (await getCustomerDetails(
        getEmailDataParams
      )) as IGetGlobalDirectoryApiResponse;
      dispatch({
        type: "GET_EMAIL_DETAILS_FULFILLED",
        payload: {
          response,
          tab: activeField,
          app_access,
          pageNumber: customer[activeField].pageNumber,
          options,
        },
      });
    };
    if (optionList.some((option) => option.value === activeField)) {
      getData();
    }
  }, [
    activeField,
    customer[activeField].pageNumber,
    customer[activeField].mainSearch,
    callAgain,
    openSelectCustomerSidebar,
    JSON.stringify(optionList),
  ]);

  useEffect(() => {
    setDefaultSelectedContact(receivedContact);
  }, [receivedContact]);

  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return (
  //     JSON.stringify(selectedContact) !== JSON.stringify(defaultSelectedContact)
  //   );
  // }, [selectedContact, defaultSelectedContact]);

  // const closeConfirmationModal = () => {
  //   sideMenuOpen;
  //   setIsConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDialogOpen(false);
  //   closeDrawer();
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     closeDrawer();
  //     dispatch({
  //       type: "RESET_ALL_DATA",
  //     });
  //     setOptionList([]);
  //   } else {
  //     setIsConfirmDialogOpen(true);
  //   }
  // };

  const handleCloseDrawer = () => {
    closeDrawer();
    dispatch({
      type: "RESET_ALL_DATA",
    });
    setOptionList([]);
  };

  // ------------------------------------------------------------------------------------------------------------------------------------
  return (
    <>
      <CFDrawer
        // in open if openSendEmailSidebar pass through props then give priority to it so check it not null
        open={activeField && openSelectCustomerSidebar}
        size={1210}
        drawerBody="!h-screen !overflow-hidden"
      >
        <div className="sidebar-body">
          <div className="flex">
            <div
              className={`w-60 md:min-w-[240px] md:max-w-[240px] md:relative absolute flex-[1_0_0%] z-20 ${
                sideMenuOpen
                  ? "h-screen w-full"
                  : "md:h-screen md:w-full w-0 h-0"
              }`}
            >
              <div
                className={`md:hidden block absolute bg-black/20 ${
                  sideMenuOpen ? "h-full w-full" : "h-0 w-0"
                }`}
                onClick={() => setSideMenuOpen(false)}
              ></div>
              <div
                className={`overflow-y-auto h-screen relative w-[240px] md:bg-gray-200/50 bg-gray-200 dark:bg-dark-800 transition-all ease-in-out duration-300 ${
                  sideMenuOpen ? "left-0" : "md:left-0 -left-[100%]"
                }`}
              >
                {!isEmpty(activeField) && options && (
                  <SelectOptions<CustomerEmailTab>
                    selectedOption={activeField}
                    optionList={optionList}
                    addFunction={() => {
                      setAddMultiselectDirectoryOpen(true);
                    }}
                    onClick={(value: CustomerEmailTab) => {
                      dispatch({ type: "SET_ACTIVE_FIELD", payload: value });
                      if (scrollRef.current) {
                        scrollRef.current.scrollTop = 0;
                      }
                    }}
                    canWrite={canWrite}
                    hideAddBtnForFilter={hideAddBtnForFilter}
                  />
                )}
              </div>
            </div>
            <div className="flex flex-[1_0_0%] overflow-hidden flex-col">
              <div className="md:px-4 pl-10 pr-5 py-2.5 border-b border-gray-300 dark:border-white/10 flex items-center relative justify-start">
                <CFIconButton
                  htmlType="button"
                  className={`md:hidden flex w-6 max-w-[24px] max-h-[24px] !absolute left-2.5`}
                  variant="text"
                  onClick={() => setSideMenuOpen((prev) => !prev)}
                >
                  <FontAwesomeIcon
                    className="text-base w-[18px] h-[18px] text-primary-gray-80 dark:text-white/90"
                    icon="fa-regular fa-bars"
                  />
                </CFIconButton>
                {
                  <SidebarSelectHeader
                    headerTitle={
                      singleSelecte
                        ? _t("Select Contact")
                        : _t("Select Contacts")
                    }
                    headerTitleIcon="fa-regular fa-id-badge"
                  />
                }
                <div className="md:relative md:right-[18px] !absolute right-2.5">
                  <CFCloseButton
                    onClick={() => {
                      handleCloseDrawer();
                    }}
                    iconClassName="!w-[18px] !h-[18px]"
                  />
                </div>
              </div>
              {!emptyOptionList ? (
                <CustomerSelectBody
                  rightScrollRef={rightScrollRef}
                  dispatch={dispatch}
                  customer={customer}
                  activeField={activeField}
                  selectedContact={selectedContact}
                  receivedContact={receivedContact}
                  loadMore={loadMore}
                  hasMore={customer[activeField].infiniteScrollhasMore}
                  isInfiniteScrollLoading={isDataFetching}
                  infiniteScrollHideLoadingComponent={
                    customer[activeField].error
                  }
                  options={options}
                  singleSelecte={singleSelecte}
                  sendEmail={sendEmail}
                  setSendEmail={setSendEmail}
                  groupCheckBox={groupCheckBox}
                  closeDrawer={closeDrawer}
                  isViewAttachment={isViewAttachment}
                  isViewSaveThisToFileCheckbox={isViewSaveThisToFileCheckbox}
                  saveFileModuleName={saveFileModuleName}
                  setCustomer={setCustomer}
                  showSaveBtn={showSaveBtn}
                  scrollRef={scrollRef}
                  is_all_tab={true}
                  buttontext={buttontext}
                  isLoading={isLoading}
                  loadingOnSave={loadingOnSave}
                />
              ) : (
                // show messsage of Your permissions prevent
                <div className="flex items-center justify-center h-full">
                  <p className="text-center text-base text-red-600">
                    {_t(
                      "Your permissions prevent you from accessing this data. Contact your account admin if this is an error."
                    )}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </CFDrawer>
      {addMultiselectDirectoryOpen && currentSelected.current && (
        <AddMultiSelectDir
          addDirectory={addMultiselectDirectoryOpen}
          setAddDirectory={setAddMultiselectDirectoryOpen}
          contactData={currentSelected.current}
          callOnAddComplete={(
            res: Partial<IResponse<IDirectoryAddResponse>>
          ) => {
            if (res && res.data) {
              if (res?.success) {
                let eventName = "";
                switch (currentSelected.current?.key) {
                  case dirTypeKeys.contractor:
                    eventName = EVENT_LOGGER_NAME.contractors;
                    break;
                  case dirTypeKeys.customer:
                    eventName = EVENT_LOGGER_NAME.customers;
                    break;
                  case dirTypeKeys.lead:
                    eventName = EVENT_LOGGER_NAME.leads;
                    break;
                  case dirTypeKeys.misc_contact:
                    eventName = EVENT_LOGGER_NAME.misc_contacts;
                    break;
                  case dirTypeKeys.vendor:
                    eventName = EVENT_LOGGER_NAME.vendors;
                    break;
                  case dirTypeKeys.employee:
                    eventName = EVENT_LOGGER_NAME.employees;
                    break;
                }
                if (eventName) {
                  EventLogger.log(eventName + EVENT_LOGGER_ACTION.added, 1);
                }
              }

              if (singleSelecte) {
                let { data, success } = res;
                if (success) {
                  setCustomer &&
                    setCustomer([
                      {
                        ...data,
                        is_favorite: 0,
                      },
                    ]);
                  dispatch({
                    type: "RESET_ALL_DATA",
                  });
                  closeDrawer();
                }
              } else {
                let response: Partial<IGetGlobalDirectoryApiResponseCustomer> =
                  {
                    ...res,
                    data: [
                      {
                        ...res.data,
                        is_favorite: 0,
                      },
                    ],
                  };
                dispatch({
                  type: "SET_ADD_DIRECOTRY_DATA",
                  payload: {
                    response: response,
                    tab: activeField,
                    formState: true,
                  },
                });
                dispatch({
                  type: "SET_SEARCH",
                  payload: {
                    activeField,
                    searchText: "",
                  },
                });
                dispatch({
                  type: "SET_MAIN_SEARCH",
                  payload: {
                    activeField,
                    searchText: "",
                  },
                });
              }
              setAddMultiselectDirectoryOpen(false);
            }
          }}
          showCustomField={showCustomField}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeConfirmationModal}
        />
      )} */}
    </>
  );
};
export default SelectCustomerDrawer;
