import React, { useMemo, useState, useEffect } from "react";

// Atoms
import { ApexChart } from "~/shared/components/atoms/chart";
import { Spin } from "~/shared/components/atoms/spin";
// Molecules
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import MultipleChartslabels from "~/shared/components/molecules/multipleChartslabels/MultipleChartslabels";
// Hook
import { useTranslation } from "~/hook";
import { useApexCharts } from "~/shared/hooks/useApexCharts";
// Other
import { type RadioChangeEvent } from "antd";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { useAppProSelector } from "../../../redux/store";
import { calculateTimecardsHours } from "../../utils/common-util";
import { Number } from "~/helpers/helper";

const HoursMoney = (props: HoursMoneyProps) => {
  const { selectedHrsMoneyTab, setSelectedHrsMoneyTab } = props;
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { details }: IProjectDetailsInitialState = useAppProSelector(
    (state) => state.proDetails
  );
  const timecardAry = details?.timecardAry ?? [];
  const estimatedAry = details?.estimatedAry ?? [];
  const estimatedLine = useApexCharts({ type: "donut" });

  // Loading state for chart updates
  const [isChartLoading, setIsChartLoading] = useState(false);

  // Handle loading animation when tab changes
  useEffect(() => {
    setIsChartLoading(true);
    const timer = setTimeout(() => {
      setIsChartLoading(false);
    }, 500); // 500ms loading animation

    return () => clearTimeout(timer);
  }, [selectedHrsMoneyTab]);

  const CURRENCY_SYMBOL = formatter().currency_symbol;

  let estimated_chart_total = 0;
  let committed_chart_total = 0;

  if (selectedHrsMoneyTab == "hours") {
    estimated_chart_total =
      estimatedAry.reduce(
        (acc, curr, i) =>
          (acc =
            acc + parseInt(curr["quantity"] == "" ? "0" : curr["quantity"])),
        0
      ) * 60;
  } else {
    estimated_chart_total = estimatedAry.reduce(
      (acc, curr, i) => (acc = acc + parseFloat(curr["item_total"])),
      0
    );
  }
  estimated_chart_total = isNaN(estimated_chart_total)
    ? 0
    : estimated_chart_total;

  let estimated_data = [estimated_chart_total];
  let committed_data = [0, 0];
  timecardAry?.forEach(function (itemRow) {
    let regular_total =
      selectedHrsMoneyTab == "hours"
        ? itemRow["regular_hours"]
        : itemRow["regular_cost"];
    let overtime_total =
      selectedHrsMoneyTab == "hours"
        ? itemRow["ot_hours"]
        : itemRow["overtime_cost"];

    committed_data[0] += parseFloat(regular_total);
    committed_data[1] += parseFloat(overtime_total);
  });

  committed_chart_total += committed_data[0] + committed_data[1];

  let estimated_chart_data = [0];
  let committed_chart_data = [0, 0];

  estimated_chart_data = estimated_data;
  committed_chart_data = committed_data;
  estimated_chart_total = estimated_chart_data.reduce(
    (a, b) => parseFloat(a?.toString()) + parseFloat(b?.toString()),
    0
  );
  committed_chart_total = committed_chart_data.reduce(
    (a, b) => parseFloat(a?.toString()) + parseFloat(b?.toString()),
    0
  );

  let tooltipLabel = selectedHrsMoneyTab === "hours" ? "Hours" : "Amount";

  const estimatedOptions = useMemo(() => {
    return {
      ...estimatedLine,
      chart: {
        offsetY: 15,
      },
      title: {
        text: undefined,
      },
      labels: [
        `Est. Regular ${tooltipLabel}`,
        `Overtime ${tooltipLabel}`,
        `Cmt. Regular ${tooltipLabel}`,
      ],
      legend: {
        show: false,
        position: "right",
        horizontalAlign: "center",
        floating: !1,
        offsetX: 30,
        offsetY: 38,
        onItemClick: {
          toggleDataSeries: false,
        },
      },
      colors: ["#223558", "#fa6d3d", "#45897e"],
      dataLabels: {
        enabled: false,
      },
      tooltip: {
        enabled: true,
        y: {
          formatter: function (val: string) {
            let status_per = (Number(val) * 100) / estimated_chart_total;
            const formateValue = formatter(
              Number(val) !== 0 ? Number(val)?.toFixed(2) : "0"
            ).value_with_symbol;

            if (selectedHrsMoneyTab === "hours") {
              const hours = calculateTimecardsHours(Number((+val)?.toFixed(2)));

              return status_per.toFixed(0) + "%" + " " + "(" + hours + ")";
            } else {
              return (
                status_per.toFixed(0) + "%" + " " + "(" + formateValue + ")"
              );
            }
          },
        },
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: "12px",
                color: "#000",
                offsetY: -7,
                formatter: function () {
                  return "Estimated";
                },
              },
              value: {
                show: true,
                fontSize: "12px",
                color: "#000",
                opacity: 1,
                offsetY: -2,
              },
              total: {
                show: true,
                label: "Total",
                color: "#000",
                fontSize: "12px",
                fontWeight: "600",
                showAlways: true,
                formatter: function (w: TimeCardStatsChartTooltipProps["w"]) {
                  let val = w.globals.seriesTotals.reduce(
                    (a: number, b: number) => {
                      return a + b;
                    },
                    0
                  );
                  return selectedHrsMoneyTab === "hours"
                    ? calculateTimecardsHours(+val?.toFixed(2))
                    : Number(val) === 0
                    ? formatter("0.00").currency_symbol + "0"
                    : formatter(val.toFixed(2)).value_with_symbol_and_c_type;
                },
              },
            },
          },
        },
      },
      states: {
        normal: {
          filter: {
            type: "none",
          },
        },
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
            value: 0.35,
          },
        },
      },
    };
  }, [selectedHrsMoneyTab]);

  const committedestimatedLine = useApexCharts({ type: "donut" });
  const committedOptions = useMemo(() => {
    return {
      ...committedestimatedLine,
      chart: {
        offsetY: 15,
      },
      title: {
        text: undefined,
      },
      labels: [`Cmt. Regular ${tooltipLabel}`, `Overtime ${tooltipLabel}`],
      legend: {
        show: false,
        position: "right",
        horizontalAlign: "center",
        floating: !1,
        offsetX: 30,
        offsetY: 38,
        onItemClick: {
          toggleDataSeries: false,
        },
      },
      colors: ["#45897e", "#FA6D3D"],
      dataLabels: {
        enabled: false,
      },
      tooltip: {
        enabled: true,
        y: {
          formatter: function (val: string) {
            let status_per = (Number(val) * 100) / committed_chart_total;
            const formateValue = formatter(
              Number(val) !== 0 ? Number(val)?.toFixed(2) : "0"
            ).value_with_symbol;

            if (selectedHrsMoneyTab === "hours") {
              const hours = calculateTimecardsHours(Number(val));
              return status_per.toFixed(0) + "%" + " " + "(" + hours + ")";
            } else {
              return (
                status_per.toFixed(0) + "%" + " " + "(" + formateValue + ")"
              );
            }
          },
        },
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: "12px",
                color: "#000",
                offsetY: -7,
                formatter: function () {
                  return "Committed";
                },
              },
              value: {
                show: true,
                fontSize: "12px",
                color: "#000",
                opacity: 1,
                offsetY: -2,
              },
              total: {
                show: true,
                label: "Total",
                color: "#000",
                fontSize: "12px",
                fontWeight: "600",
                showAlways: true,
                formatter: function (w: TimeCardStatsChartTooltipProps["w"]) {
                  let val = w.globals.seriesTotals.reduce(
                    (a: number, b: number) => {
                      return a + b;
                    },
                    0
                  );
                  return selectedHrsMoneyTab === "hours"
                    ? calculateTimecardsHours(+val?.toFixed(2))
                    : Number(val) === 0
                    ? formatter("0.00").currency_symbol + "0"
                    : formatter(val.toFixed(2)).value_with_symbol_and_c_type;
                },
              },
            },
          },
        },
      },
      states: {
        normal: {
          filter: {
            type: "none",
          },
        },
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
            value: 0.35,
          },
        },
      },
    };
  }, [selectedHrsMoneyTab]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Hours & Money")}
        iconProps={{
          icon: "fa-solid fa-sack-dollar",
          containerClassName:
            "bg-[linear-gradient(180deg,#96DCC51a_0%,#40D2A21a_100%)]",
          id: "hrs_icon",
          colors: ["#96DCC5", "#40D2A2"],
        }}
        headerRightButton={
          <ListTabButton
            value={selectedHrsMoneyTab}
            options={[
              {
                label: `Amount (${CURRENCY_SYMBOL})`,
                value: "amount",
              },
              {
                label: "Hours",
                value: "hours",
              },
            ]}
            className="sm:min-w-[90px] min-w-fit sm:px-1.5 px-2"
            onChange={(e: RadioChangeEvent) => {
              setSelectedHrsMoneyTab(e.target.value);
            }}
            activeclassName="!bg-[#F1F4F9] !text-primary-900"
          />
        }
        children={
          <>
            <MultipleChartslabels
              className="flex flex-col-reverse gap-3.5"
              legendClassName="rounded-none h-3 w-3"
              chartProps={{
                colors: ["#223558", "#fa6d3d", "#45897e"],
              }}
              seriesKeys={[
                {
                  title:
                    selectedHrsMoneyTab == "hours"
                      ? "Est. Regular Hours"
                      : "Est. Regular Amount",
                  className: "est-reg-amount",
                },
                {
                  title:
                    selectedHrsMoneyTab == "hours"
                      ? "Overtime Hours"
                      : "Overtime Amount",
                  className: "overtime-amount",
                },
                {
                  title:
                    selectedHrsMoneyTab == "hours"
                      ? "Cmt. Regular Hours"
                      : "Cmt. Regular Amount",
                  className: "cmt-reg-amount",
                },
              ]}
            >
              {isChartLoading ? (
                <div className="flex items-center justify-center h-[160px] relative">
                  <Spin className="w-8 h-8" />
                </div>
              ) : (
                <ApexChart
                  className="donut-chart flex items-center justify-center"
                  series={estimated_chart_data}
                  options={estimatedOptions}
                  type={"donut"}
                  height={160}
                />
              )}
              {isChartLoading ? (
                <div className="flex items-center justify-center h-[160px] relative">
                  <Spin className="w-8 h-8" />
                </div>
              ) : (
                <ApexChart
                  className="donut-chart flex items-center justify-center"
                  series={committed_chart_data}
                  options={committedOptions}
                  type={"donut"}
                  height={160}
                />
              )}
            </MultipleChartslabels>
          </>
        }
      />
    </>
  );
};

export default React.memo(HoursMoney);
