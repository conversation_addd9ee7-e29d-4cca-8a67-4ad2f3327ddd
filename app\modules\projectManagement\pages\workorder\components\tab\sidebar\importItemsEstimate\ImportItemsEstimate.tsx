// Hook
import { useC<PERSON><PERSON>cy<PERSON>ormatter, useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";
// Other
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { fetchEstItemsListApi } from "../../../../redux/action/workorderDetailsAction";
import { getGConfig, useGModules } from "~/zustand";
import { useWoAppDispatch, useWoAppSelector } from "../../../../redux/store";
import { Number } from "~/helpers/helper";
import {
  GridApi,
  IRowNode,
  SelectionChangedEvent,
  GridReadyEvent,
} from "ag-grid-community";
import { defaultConfig } from "~/data";

const ImportItemsEstimate = ({
  importItemsEstimate,
  setImportItemsEstimate,
  estimateItemHandler,
  isDataUpdating,
}: IWorkOrderImportItemsEstimateProps) => {
  const { _t } = useTranslation();
  const { module_id } = getGConfig();
  const { getGModuleByKey } = useGModules();
  const dispatch = useWoAppDispatch();
  const { estimateItems, isEstitemsLoading } = useWoAppSelector(
    (state) => state.workorderEstItemList
  );
  const { currentCurrency } = useCurrencyFormatter();

  const { details, items }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const gridApiRef = useRef<GridApi | null>(null);
  const [defaultselectedData, setDefaultselectedData] = useState<
    IWorkOrderEstimateItem[]
  >([]);
  const [selectedItems, setSelectedItems] = useState<IWorkOrderEstimateItem[]>(
    []
  );
  const [isProgrammaticSelection, setIsProgrammaticSelection] =
    useState<boolean>(true);
  const [selectedItemsByTable, setSelectedItemsByTable] = useState({});
  const [tableKey, setTableKey] = useState<number>(0);
  const [open, setOpen] = useState<boolean>(false);
  const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);

  const estimatelistFilter = useMemo(() => {
    if (filter.length === 0) {
      return estimateItems;
    }

    return estimateItems?.filter(
      (item) =>
        item?.item_type_key !== undefined &&
        filter.includes(item?.item_type_key)
    );
  }, [JSON.stringify(estimateItems), JSON.stringify(filter)]);

  useEffect(() => {
    setTableKey((prevKey) => prevKey + 1);
  }, [JSON.stringify(filter)]);

  const separatedBySection = useMemo(() => {
    const grouped: Record<string, typeof estimatelistFilter> = {};

    estimatelistFilter?.forEach((item) => {
      const key = item.section_id ?? "unknown";

      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(item);
    });

    return Object.entries(grouped).map(([section_id, items]) => ({
      section_id,
      items,
    }));
  }, [estimatelistFilter]);

  useEffect(() => {
    if (estimatelistFilter && !!estimatelistFilter.length) {
      const defaultselectedData = estimatelistFilter
        .filter((jobItem) =>
          items.some(
            (item) =>
              item?.reference_module_item_id?.toString() ===
              jobItem?.item_id?.trimStart()
          )
        )
        .map((item) => {
          return item;
        });
      setDefaultselectedData(defaultselectedData);
    }
  }, [items, estimatelistFilter]);

  const onGridReady = (params: GridReadyEvent) => {
    const gridApi = params.api;
    gridApi.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        [...defaultselectedData, ...selectedItems].some(
          (item) => item.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
  };

  useEffect(() => {
    if (
      isProgrammaticSelection &&
      estimateItems &&
      estimateItems.length > 0 &&
      defaultselectedData.length > 0
    ) {
      const selectedNodes: IRowNode[] = [];
      gridApiRef.current?.forEachNode((node) => {
        if (
          defaultselectedData.some((item) => item.item_id === node.data.item_id)
        ) {
          node.setSelected(true);
          selectedNodes.push(node);
        }
      });

      if (selectedNodes.length > 0) {
        gridApiRef.current?.ensureNodeVisible(selectedNodes[0]);
      }
    }
  }, [estimateItems, defaultselectedData, isProgrammaticSelection]);

  const handleSelectionChanged = (
    event: SelectionChangedEvent,
    sectionId: string
  ): void => {
    setIsProgrammaticSelection(false);

    const selectedNodes = event.api.getSelectedNodes();
    const selectedInTable = selectedNodes.map((node: IRowNode) => node.data);

    setSelectedItemsByTable((prev) => {
      const newSelection = {
        ...prev,
        [sectionId]: selectedInTable,
      };

      const combined = Object.values(
        newSelection
      )?.flat() as IWorkOrderEstimateItem[];
      setSelectedItems(combined);
      return newSelection;
    });
  };

  useEffect(() => {
    const params: IGetEstimateItemsParams = {
      module_id: module_id,
      project_id: Number(details.project_id),
    };
    dispatch(fetchEstItemsListApi(params));
  }, [importItemsEstimate]);

  const handleSaveItems = useCallback(async () => {
    const itemsToAdd: IWorkOrderEstimateItem[] = [];
    let itemToBeDelete: IWorkOrderEstimateItem[] = [];
    let itemToBeDeleteId: number[];
    selectedItems.forEach((item) => {
      if (!defaultselectedData.some((i) => i.item_id === item.item_id)) {
        itemsToAdd.push(item);
      }
    });

    defaultselectedData.forEach((item) => {
      if (!selectedItems.some((i) => i.item_id === item.item_id)) {
        itemToBeDelete.push(item);
      }
    });

    itemToBeDeleteId =
      itemToBeDelete?.map((item: IWorkOrderEstimateItem) => {
        const mainItemId = items.find(
          (i) => i.reference_module_item_id?.toString() === item.item_id
        )?.item_id as number;

        return mainItemId;
      }) ?? [];

    estimateItemHandler({ itemsToAdd, itemToBeDeleteId });
  }, [selectedItems, defaultselectedData]);

  const columnDefs = [
    {
      headerName: _t("Section"),
      field: "section_name",
      minWidth: 170,
      maxWidth: 170,
      headerCheckboxSelection: true,
      checkboxSelection: true,
      suppressMenu: true,
      flex: 2,
    },
    {
      headerName: _t("Item Name"),
      field: "item_name",
      minWidth: 150,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IEstimateItemsCellRenderer) => {
        const { data } = params;

        return data.subject ? (
          <Tooltip title={data.subject}>
            <Typography className="table-tooltip-text">
              {data.subject}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IEstimateItemsCellRenderer) => {
        const { data } = params;
        return data.quantity ? (
          <Tooltip title={data.quantity}>
            <Typography className="table-tooltip-text">
              {data.quantity}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Cost/Unit"),
      field: "cost_unit",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IEstimateItemsCellRenderer) => {
        const { data } = params;
        const costUnit = `${
          currentCurrency() + (Number(data.unit_cost || 0) / 100).toFixed(2)
        }${data.unit ? `/${data?.unit}` : ""}`;
        return data.unit_cost || costUnit ? (
          <Tooltip title={costUnit}>
            <Typography className="table-tooltip-text">
              {currentCurrency() +
                (Number(data.unit_cost || 0) / 100).toFixed(2)}
              {data.unit ? `/${data?.unit}` : ""}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: IEstimateItemsCellRenderer) => {
        const { data } = params;
        return data.total ? (
          <Tooltip title={`$${(Number(data.total || 0) / 100).toFixed(2)}`}>
            <Typography className="table-tooltip-text">
              {currentCurrency() + (Number(data.total || 0) / 100).toFixed(2)}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noDataTable = useMemo(() => {
    return (
      <StaticTable
        className="static-table"
        rowSelection="multiple"
        columnDefs={columnDefs}
        rowMultiSelectWithClick={true}
        suppressRowClickSelection={true}
        rowData={[]}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
          />
        )}
      />
    );
  }, [JSON.stringify(separatedBySection)]);

  return (
    <Drawer
      open={importItemsEstimate}
      rootClassName="drawer-open"
      width={750}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-calculator"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(
              `Import Items from ${
                getGModuleByKey(defaultConfig.estimate_module)?.module_name
              }`
            )}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setImportItemsEstimate(false)} />}
    >
      <div className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100vh-132px)] px-4">
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Header level={5} className="!text-sm !mb-0 text-[#4B4B4B]">
                {_t(
                  `${
                    getGModuleByKey(defaultConfig.estimate_module)
                      ?.module_name || "Estimate"
                  } Items`
                )}
              </Header>
              <ModuleItemsFilter
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                filter={filter}
                onChangeFilter={setFilter}
                openFilter={open}
              />
            </div>
            <div className="grid gap-4">
              {isEstitemsLoading ? (
                <SidebarCardBorder addGap={true}>
                  <Spin className="w-full h-20 flex items-center justify-center" />
                </SidebarCardBorder>
              ) : separatedBySection?.length > 0 ? (
                separatedBySection.map((item) => (
                  <SidebarCardBorder addGap={true} key={item.section_id}>
                    <div className="p-2 common-card">
                      <div className="ag-theme-alpine">
                        <StaticTable
                          key={`${tableKey}-${item.section_id}`}
                          className="static-table"
                          rowSelection="multiple"
                          columnDefs={columnDefs}
                          rowData={item.items}
                          rowMultiSelectWithClick={true}
                          suppressRowClickSelection={true}
                          onSelectionChanged={(event) =>
                            handleSelectionChanged(event, item.section_id)
                          }
                          onGridReady={onGridReady}
                          noRowsOverlayComponent={() => (
                            <NoRecords
                              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </SidebarCardBorder>
                ))
              ) : (
                <SidebarCardBorder addGap={true}>
                  <div className="p-2 common-card">
                    <div className="ag-theme-alpine">{noDataTable}</div>
                  </div>
                </SidebarCardBorder>
              )}
            </div>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            className="w-full"
            htmlType="button"
            buttonText={_t("Save & Close")}
            isLoading={isDataUpdating}
            disabled={isDataUpdating || selectedItems.length === 0}
            onClick={() => {
              handleSaveItems();
            }}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default ImportItemsEstimate;
