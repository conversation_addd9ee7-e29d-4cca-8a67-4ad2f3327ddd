import { defaultConfig } from "~/data";

import { useNavigate } from "@remix-run/react";
import { useMemo, useState } from "react";
import { useTranslation } from "~/hook";

//Zustand
import { getGConfig, getGSettings, useGModules } from "~/zustand";

//shared
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";

//Other
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

import { apiRoutes, routes } from "~/route-services/routes";
import { getApiData } from "~/helpers/axios-api-helper";
import dayjs from "dayjs";
import { getFileUrl, handleEmailApiCall } from "../../utils/common";
import { setSendEmailOpenStatus } from "~/components/sidebars/multi-select/customer/zustand/action";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import {
  copyBillApi,
  downloadBillPDF,
} from "../../redux/action/billDetailAction";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { useAppBillSelector } from "../../redux/store";

export default function BillTableDropDownItems({
  confirmArchiveDialogOpen,
  isCopyBillConfirmOpen,
  isDeleted,
  callApiAgain,
  setBillData,
  confirmDialogOpen,
  onClose,
  isViewEmailModalOpen,
  data,
  billId,
  setIsViewEmailModalOpen,
  isShareOpen,
  setShareLink,
  setIsShareOpen,
  shareLink,
}: IBillTableDropDownItemsProps) {
  const { billDetail }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { user_id = 0, company_id = 0, timezone_utc_tz_id = "" } = user || {};
  const { module_singular_name }: GConfig = getGConfig();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const [pdfTempId, setPdfTempId] = useState<string>("");

  const {
    module_key = "default_module_key",
    module_id = 0,
    module_access = "full-access",
    name,
  } = currentModule || {};
  const [isCopyBillLoading, setIsCopyBillLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const { _t } = useTranslation();
  const [deleteRecordLoading, setDeleteRecordLoading] =
    useState<boolean>(false);
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const [isArchiveLoading, setIsArchiveLoading] = useState<boolean>(false);
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);
  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs(new Date()).tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);
  const { checkModuleAccessByKey } = useGModules();

  //Delete function
  const handleDelete = async () => {
    setDeleteRecordLoading(true);
    try {
      const data = await getWebWorkerApiParams({
        otherParams: {
          company_id: 1,
          module_key: "bills",
          primary_key: Number(billId),
          remove_associated_data: 0,
          user_id: user_id,
          force_login: 0,
          op: "delete_module_record",
        },
      });
      const response = (await webWorkerApi({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: data,
      })) as { message: string; success: string };
      if (response?.success == "1") {
        if (callApiAgain) {
          callApiAgain();
          // navigate(routes.BILLS.url); once new module move into dev will chnage
          navigate(`/manage-bills`);
        }
        if (setBillData) {
          setBillData({} as IBillListApiResponse["data"][0]);
        }
        setDeleteRecordLoading(false);
      } else {
        notification.error({ description: response.message });
        setDeleteRecordLoading(false);
      }
    } catch (error) {
      setDeleteRecordLoading(false);
      notification.error({ description: (error as Error).message });
    }
  };

  //handle Archive function

  const handleArchive = () => {
    setIsArchiveLoading(true);
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: Number(billId),
            module_key: module_key,
            status: isDeleted === "1" ? 0 : 1,
          },
        }),

        success: (response: { success: string; message: string }) => {
          if (response.success == "1") {
            if (callApiAgain) {
              callApiAgain();
            } else {
              navigate(routes.BILLS.url);
            }
            setIsArchiveLoading(false);
          } else {
            notification.error({ description: response.message });
            setIsArchiveLoading(false);
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
          setIsArchiveLoading(false);
        },
        callComplete: () => {},
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const formData: IBillDownloadDocumentWithAction = {
    action: "download",
    op: "pdf_bill",
    bill_id: Number(billId) || "",
    tz: dayjs().format("ZZ"),
    tzid: Intl.DateTimeFormat().resolvedOptions().timeZone,
    curr_time: dayjs(currentDateTime).format("YYYY-MM-DD HH:mm:ss"),
  };

  const pdfOptions: CustomerEmailTab[] = [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    // defaultConfig.lead_key, // https://app.clickup.com/t/86cwyy94q
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const downloadPdf = async (tId: string) => {
    const res = (await downloadBillPDF({
      bill_id: Number(billId),
      action: "download",
      t_id: tId,
    })) as IDownloadBillRes;

    if (res) {
      if (res.success) {
        const fileName = res?.data?.pdf_name;
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download = fileName
          ? fileName.toString()
          : res.base64_encode_pdfUrl ?? "";

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IBillSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id: Number(billId),
      module_id: module_id,
      module_key: module_key,
      t_id: pdfTempId,
      bill_id: Number(billId),
      action: "send",
      op: "pdf_bill",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
          setIsViewEmailModalOpen(false);
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleCopyBill = async () => {
    setIsCopyBillLoading(true);
    try {
      const response = await copyBillApi({
        id: billDetail?.data?.bill_id,
      });

      if (response) {
        const billId = response?.data?.billId as string;
        if (billId) {
          const url = `/manage-bills/${billId}`;
          if (window?.ENV?.PAGE_IS_IFRAME) {
            window.parent.location.href = url;
          } else {
            navigate(url);
          }
          setIsCopyBillLoading(false);
          onClose("Copy_bill");
        } else {
          notification.error({
            description:
              response?.message || "An error occurred while copying the bill.",
          });
        }
      }
    } catch (error) {
      console.error("Error copying bill:", error);
      notification.error({
        description: "Failed to copy the bill. Please try again.",
      });
    }
  };

  return (
    <>
      {isViewEmailModalOpen && billId && (
        <PDFFilePreview
          isOpen={isViewEmailModalOpen}
          onCloseModal={() => setIsViewEmailModalOpen(false)}
          moduleId={module_id}
          op="pdf_bill"
          idName="bill_id"
          isLoading={false}
          id={billId.toString()}
          options={pdfOptions}
          emailSubject={data?.email_subject}
          handleEmailApiCall={emailApiCall}
          handleDownload={downloadPdf}
          isViewAttachment={false}
          moduleName={HTMLEntities.decode(sanitizeString(module_singular_name))}
          setPdfTempId={setPdfTempId}
        />
      )}

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            user_id: user_id,
            record_id: Number(billId),
            module_key: module_key,
            module_page: "manage-bills",
          }}
          onEmailLinkClick={(data) => {
            setSendEmailOpenStatus(true); // Open the email modal
            setSendEmailOpen(true);
            setShareLink(data);
            setIsShareOpen(false);
          }}
          onCloseModal={() => {
            // setSelectedData({});
            setIsShareOpen(false);
            setShareLink("");
          }}
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
            setSendEmailOpenStatus(false);
          }}
          canWrite={false}
          appUsers={true}
          isViewAttachment={false}
          openSendEmailSidebar={sendEmailOpen}
          options={[defaultConfig.employee_key, defaultConfig.contractor_key]}
          singleSelecte={false}
          emailApiCall={async (...params) => {
            await handleEmailApiCall(...params, Number(billId) as number);
          }}
          customEmailData={{
            body: `A link to a record within Contractor Foreman has been shared with you. <a href = ${shareLink}>View Details</a>.`,
            subject: "Shared Link",
          }}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              defaultConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
            save_a_copy_of_sent_pdf,
          }}
          contactId={0}
        />
      )}

      {confirmArchiveDialogOpen && (
        <ConfirmModal
          isOpen={confirmArchiveDialogOpen}
          modaltitle={_t(
            `${isDeleted?.toString() === "1" ? "Active" : "Archive"}`
          )}
          description={_t(
            `Are you sure you want to ${
              isDeleted?.toString() === "1" ? "Activate" : "Archive"
            } this ${isDeleted?.toString() === "1" ? "data?" : "item?"} ${
              isDeleted?.toString() === "0"
                ? "To view it or Activate it later, set the filter to show Archived items."
                : ""
            }`
          )}
          modalIcon={
            isDeleted?.toString() === "1"
              ? "fa-regular fa-regular-active"
              : "fa-regular fa-box-archive"
          }
          isLoading={isArchiveLoading}
          onAccept={() => {
            handleArchive();
          }}
          onDecline={() => {
            onClose("change_status");
          }}
          onCloseModal={() => {
            onClose("change_status");
          }}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          isLoading={deleteRecordLoading}
          withConfirmText={true}
          onAccept={() => {
            handleDelete();
          }}
          onDecline={() => onClose("delete")}
          onCloseModal={() => onClose("delete")}
        />
      )}

      {isCopyBillConfirmOpen && (
        <ConfirmModal
          isOpen={isCopyBillConfirmOpen}
          modaltitle={_t("Copy")}
          description={_t(
            `Are you sure you want to generate a copy of this ${module_singular_name}?`
          )}
          modalIcon="fa-regular fa-clone"
          isLoading={isCopyBillLoading}
          onAccept={() => {
            handleCopyBill();
          }}
          onDecline={() => onClose("Copy_bill")}
          onCloseModal={() => onClose("Copy_bill")}
        />
      )}
    </>
  );
}
