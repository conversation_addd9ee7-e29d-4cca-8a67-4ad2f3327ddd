// atoms
import { Too<PERSON><PERSON> } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useTranslation } from "~/hook";
import { useCOContext } from "~/context/COContext";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { COSectionOptions } from "../../../utils/constants";
import { useDrag, useDrop } from "react-dnd";
import {
  useAppSelector,
  useAppDispatch,
} from "~/modules/financials/pages/changeOrder/redux/store";
import {
  updateCOSectionOrder,
  updateItemOrder,
} from "../../../redux/action/changeOrderItemsActions";
import SectionOpen from "../sidebar/section/Section";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  setSection,
  moveSectionState,
  selectItem,
  selectAll,
  unselectAll,
} from "../../../redux/slices/changeOrderItemsSlice";
import {
  ICellRendererParams,
  RowDragEvent,
  ValueFormatterParams,
  ValueParserParams,
  ValueSetterParams,
} from "ag-grid-community";
import { ICON_MAP } from "../../sidebar/utils";
import { ValueGetterParams } from "ag-grid-community";
import {
  IChangeOrderDetailState,
  IChangeOrderItem,
  ISection,
} from "../../../redux/types";
import { canModifyItem, floatWithNegativeRegex } from "../../../utils/helpers";
import { qtyNumberCheck } from "~/shared/utils/helper/common";
import { useParams } from "@remix-run/react";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import ChangeOrderDeleteItemAction from "./item/ChangeOrderDeleteItemAction";
import { itemTotalCalculator } from "./ChangeOrderCalculation";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { ColDef } from "ag-grid-community";
import { getGConfig } from "~/zustand";

interface ICellParams extends ICellRendererParams<IChangeOrderItem> {}
interface sectionRedux {
  sections: ISection[] | undefined;
}

type DragItem = {
  index: number;
};

const COAddModifyItems = ({
  singleSection,
  index,
  onGridReady,
  showCOItemDatabase,
  setCostItemDatabaseOpen,
  setConfirmCopyDialogOpen,
  setCopySectionData,
  setSelectDeletedItems,
  setConfirmDialogOpen,
  setShowDiscountModal,
  setSelectedSection,
  setShowNewChangeOrderItem,
  setSelectedChangeOrderItem = () => {},
  setShowChangeOrderItem,
  handleUpdate,
  setChangeOrderBulkMarkup,
  selectedSection,
}: IChangeOrderCreateUpdateItemProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppDispatch();
  const { id }: RouteParams = useParams();
  const { isReadOnly } = useCOContext();
  const gConfig = getGConfig();
  const { module_singular_name } = gConfig;
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();

  const { default_item_view } = appSettings || {};
  const {
    sections,
    selectedItems,
    isMultipleSelected,
  }: IChangeOrderItemsState = useAppSelector((state) => {
    return state.changeOrderItems;
  });
  const { formatter } = useCurrencyFormatter();

  const [sectionOpen, setSectionOpen] = useState<boolean>(false);
  const [sectionData, setSectionData] = useState<Partial<IChangeOrderSection>>(
    {}
  );
  const gridRef = useRef<ExtendedAgGridReact<IChangeOrderSectionItem> | null>(
    null
  );
  const [selectedData, setSelectedData] = useState<IChangeOrderSectionItem>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  useEffect(() => {
    (async function () {
      try {
        const response = await getUnitList();
        if (!response.success) {
          setIsUnitsGetting(false);
          return;
        }
        const units = response.data?.units || [];
        setUnits(units);
        const api = gridRef.current?.api;
        if (!api) return;

        const existingColDefs = api.getColumnDefs();
        if (!existingColDefs) return;

        const updatedColDefs = existingColDefs.map((col) =>
          "field" in col && col.field === "unit"
            ? {
                ...col,
                filterParams: {
                  values:
                    units.map((unit) => ({
                      label: unit.name?.toString(),
                      value: unit.name?.toString(),
                    })) ?? [],
                },
              }
            : col
        );

        api.setColumnDefs(updatedColDefs);

        // Ensure the grid header re-renders
        api.refreshHeader();
      } catch (error) {}
      setIsUnitsGetting(false);
    })();
  }, []);

  const { codeCostData = [] }: IGetCostCodeList = useAppSelector(
    (state) =>
      state.selectCostCodeData || {
        codeCostData: [],
      }
  );
  const { details }: IChangeOrderDetailState = useAppSelector(
    (state) => state.changeOrderDetails
  );
  const disableModification = useMemo(
    () =>
      ((details ? !canModifyItem(details) : true) || isReadOnly) &&
      !isUnitsGetting,
    [details, isReadOnly, isUnitsGetting]
  );
  const codeCostList: (ICostCode & { label: string })[] = useMemo(
    () =>
      codeCostData?.map((item: ICostCode) => ({
        ...item,
        label: `${item.cost_code_name} ${
          !!item?.csi_code ? "(" + item?.csi_code + ")" : ""
        }`,
      })),
    [codeCostData]
  );
  const getTotalAmount = useCallback(
    (data: IWorkorderDetailsItem, qty: string, unit_cost: string | number) => {
      const markup_amount = Number(data.markup || 0);
      const is_markup_percentage = Number(data.is_markup_percentage);

      const total: number = Number(qty) * Number(unit_cost);
      let mainTotal: number = 0;

      if (is_markup_percentage === 1) {
        const markup = (total * markup_amount) / 100;
        mainTotal = Number(markup) + Number(total);
      } else {
        mainTotal = markup_amount;
      }
      return mainTotal;
    },
    []
  );
  const handleSelection = useCallback(
    (selected: boolean, params: { data: ESEstimateItem }) => {
      dispatch(selectItem({ selected, data: params?.data }));
    },
    [dispatch]
  );
  const SelectedMul = useMemo(() => {
    return Object?.keys(selectedItems ?? {})?.length >= 1;
  }, [selectedItems]);

  const ClickableToolTipCell = (params: ICellRendererParams) => {
    const { data, value } = params;

    return (
      <div
        className="w-full h-full cursor-pointer"
        onClick={() => {
          setSelectedSection(singleSection);
        }}
      >
        <ToolTipCell {...params} />
      </div>
    );
  };

  const uniCostDropdownHight =
    selectedSection?.items?.length === 1
      ? 60
      : selectedSection?.items?.length === 2
      ? 90
      : selectedSection?.items?.length === 3
      ? 120
      : selectedSection?.items?.length === 4
      ? 150
      : 180;

  const columnDefs: ColDef[] = [
    {
      headerName: "",
      field: "move",
      minWidth: isReadOnly ? 0 : 30,
      maxWidth: isReadOnly ? 0 : 30,
      rowDrag: !isReadOnly,
      suppressMenu: true,
      cellClass: () =>
        `ag-cell-center ag-move-cell custom-move-icon-set cell-invinsible ${
          Object.keys(selectedItems)?.length ? "" : " hover-visibility"
        }`,
      cellRenderer: () => {
        return (
          <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
            <FontAwesomeIcon
              className="w-4 h-4 text-[#4b5a76]"
              icon="fa-solid fa-grip-dots"
            />
          </div>
        );
      },
    },
    {
      headerName: "",
      headerComponent: () => {
        const allSelected =
          singleSection?.items?.every(
            (item) => selectedItems?.[item?.item_id]?.selected
          ) && singleSection?.items?.length > 0;
        console.log("allSelected", allSelected);

        return (singleSection?.items?.length || 0) > 0
          ? (singleSection?.items?.length || 0) > 0 && (
              <CheckBox
                checked={allSelected}
                onChange={(e) => {
                  console.log("e.target.checked", e.target.checked);

                  if (e.target.checked)
                    dispatch(
                      selectAll({
                        sectionId: singleSection.section_id as number,
                      })
                    );
                  else
                    dispatch(
                      unselectAll({
                        sectionId: singleSection.section_id as number,
                      })
                    );
                }}
              />
            )
          : null;
      },
      field: "checkbox2",
      minWidth: isReadOnly ? 0 : 40,
      maxWidth: isReadOnly ? 0 : 40,
      hide: Boolean(isReadOnly),
      checkboxSelection: false,
      headerCheckboxSelection: false,
      suppressMenu: true,
      suppressMovable: true,
      cellClass: () =>
        `ag-cell-center ad-call-pr-0 ad-call-pl-0 hover-visibility ${
          Object.keys(selectedItems)?.length ? "visible" : "cell-invinsible"
        }`,
      cellRenderer: (params: any) => {
        if (!params.data) {
          return;
        }
        const data = params?.data as ESEstimateItem;
        const is_checked: boolean = Boolean(
          data?.selected || selectedItems?.[data?.item_id]
        );

        return (
          <CheckBox
            checked={is_checked}
            onChange={(e) => {
              if (params && params.node) {
                const selected = Boolean(e.target.checked);
                const updatedData = {
                  ...params.data,
                  selected,
                };
                handleSelection(e.target.checked, {
                  data: updatedData,
                });
                params.node.setData(updatedData);
                params.api.refreshCells({
                  rowNodes: [params.node],
                  force: true,
                });
              }
            }}
          />
        );
      },
    },
    {
      headerName: _t("Type"),
      minWidth: 50,
      maxWidth: 50,
      field: "item_type_name",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ value }: ICellParams) => {
        return (
          <Tooltip title={value}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              icon={
                ICON_MAP[value as keyof typeof ICON_MAP] || ICON_MAP["default"]
              }
            />
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: 150,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,

      editable: !disableModification,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: ESEstimateItem }) => {
        const itemName =
          HTMLEntities.decode(sanitizeString(data?.subject)) ?? "-";
        return (
          <Tooltip title={itemName}>
            <Typography className="table-tooltip-text">
              {itemName}
              <span className="text-gray-500 ml-1">
                {data?.is_optional_item ? "(Optional)" : ""}
              </span>
            </Typography>
          </Tooltip>
        );
      },
      valueGetter: (params: ValueGetterParams) => {
        return HTMLEntities.decode(sanitizeString(params.data.subject));
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node && params.newValue) {
          const updatedData = {
            ...params.data,
            subject: HTMLEntities.decode(
              sanitizeString(params.newValue.trim())
            ),
          };
          params.node.setData(updatedData);
          handleUpdate([updatedData], {});
        }
        return true;
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code_name",
      minWidth: 180,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ClickableToolTipCell,

      editable: !isReadOnly,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellEditor: "agRichSelectCellEditor",
      keyCreator: (params: ValueFormatterParams) =>
        params.value?.cost_code_name?.trim(),
      valueFormatter: (params: ValueFormatterParams) =>
        params.value?.cost_code_name,
      valueParser: (params: ValueParserParams) =>
        codeCostList.find(
          (costCode) => costCode.cost_code_name === params.newValue
        ),
      cellEditorParams: {
        values: codeCostList,
        searchType: "matchAny",
        allowTyping: true,
        filterList: true,
        valueListMaxHeight: uniCostDropdownHight,
        formatValue: (value: ICostCode & { label: string }) => {
          return value?.label?.trim();
        },
      },
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? `${params.data?.cost_code_name}${
              params.data?.cost_code ? ` (${params.data?.cost_code})` : ""
            }`
          : !params.data?.cost_code_name && params.data?.cost_code
          ? ` (${params.data?.cost_code})`
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const costCode = params.newValue;
          if (costCode) {
            const updatedData = {
              ...params.data,
              cost_code_name: costCode?.cost_code_name,
              cost_code_id: costCode?.code_id,
              cost_code: costCode?.csi_code,
            };
            params.node.setData(updatedData);
            handleUpdate([updatedData], {});
          }
        }
        return true;
      },
    },
    {
      headerName: _t("QTY"),
      field: "quantity",
      minWidth: 80,
      maxWidth: 80,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      editable: !disableModification,
      cellEditorParams: {
        maxLength: 20,
      },
      cellRenderer: (params: ICellRendererParams) => {
        const { data } = params;
        const quantity =
          formatter(
            formatAmount(Number(data.quantity || 0), {
              isQuantity: true,
            })
          ).value || 0;
        return (
          <Tooltip title={quantity}>
            <Typography className="table-tooltip-text">{quantity}</Typography>
          </Tooltip>
        );
      },

      cellEditor: "agNumberCellEditor",
      // suppressKeyboardEvent,
      valueGetter: (params: ValueGetterParams) => {
        return params.data.quantity && params.data.quantity !== "0"
          ? Number(params.data.quantity)
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              quantity: "",
            };
            params.node.setData(updatedData);
            handleUpdate([updatedData], {});
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);
          const parts = nVal.toString().split(".");
          const digitsBeforeDecimal = parts[0] || "";
          if (digitsBeforeDecimal.length > 6 || !checkNum) {
            notification.error({
              description:
                "Quantity should be less than or equal to 6 digits before the decimal.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = Number(nVal).toFixed(2);
          }
          const quantity = Number(nVal);
          const unitCost = (Number(params.data?.unit_cost) * 100) / 100 || 0;

          const updatedTotal = quantity * unitCost;

          const updatedData = {
            ...params.data,
            quantity: nVal,
            total: updatedTotal,
          };

          params.node.setData(updatedData);
          handleUpdate([updatedData], {});
        }
        return true;
      },
    },
    {
      headerName: _t("Unit Cost"),
      field: "unit_cost",
      minWidth: 130,
      maxWidth: 130,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellEditor: "agNumberCellEditor",
      // suppressKeyboardEvent,
      cellRenderer: ({ data: { unit_cost } }: ICellRendererParams) => {
        // Convert unit_cost to a number and handle NaN case
        const numericValue = isNaN(Number(unit_cost)) ? 0 : Number(unit_cost);
        const formattedValue = formatter(
          formatAmount(Number(numericValue / 100))
        ).value_with_symbol;
        return (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        );
      },
      editable: !disableModification,
      cellEditorParams: {
        maxLength: 20,
      },
      valueGetter: (params: ValueSetterParams) => params.data.unit_cost / 100,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              unit_cost: "",
            };
            params.node.setData(updatedData);
            handleUpdate([updatedData], {});
            return false;
          }

          if (isNaN(nVal)) {
            nVal = 0;
          }

          const checkNum = qtyNumberCheck(nVal);
          const parts = nVal.toString().split(".");
          const digitsBeforeDecimal = parts[0] || "";
          const fullStr = BigInt(Math.floor(Number(nVal))).toString();

          if (fullStr.length > 10) {
            notification.error({
              description:
                "Unit cost should be less than or equal to 10 digits.",
            });
            return false;
          }

          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = Number(nVal).toFixed(2);
          }

          const updatedTotal: number = getTotalAmount(
            params.data,
            nVal && Number(nVal) * 100,
            params.data?.quantity as string | number
          );

          const updatedData = {
            ...params.data,
            unit_cost: Number(nVal) * 100,
            total: updatedTotal,
          };

          params.node.setData(updatedData);
          handleUpdate([updatedData], {});
        }
        return true;
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      minWidth: 100,
      maxWidth: 100,
      suppressMovable: false,
      suppressMenu: true,
      suppressKeyboardEvent: (params) => {
        if (params.event.key === "Enter") {
          params.event.preventDefault();
          return true; // Block Ag-Grid's default behavior
        }
        return false;
      },
      cellEditorParams: {
        values: units,
        onKeyDown: (
          e: React.KeyboardEvent<HTMLInputElement>,
          data: IChangeOrderSectionItem
        ) => {
          if (e.key === "Enter") {
            const value = e?.currentTarget?.value?.trim();
            const newType = onEnterSelectSearchValue(
              e,
              units?.map((unit) => ({
                label: unit?.name,
                value: "",
              })) || []
            );
            if (newType) {
              setNewUnitName(newType);
              setSelectedData(data);
            } else if (value) {
              notification.error({
                description:
                  "Records already exist, no new records were added.",
              });
            }
          }
        },
      },
      cellEditor: UnitCellEditor<IChangeOrderSectionItem>,
      editable: !disableModification,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: ({ data }: ICellRendererParams) => {
        const unit = data?.unit;
        return unit ? (
          <Tooltip title={unit}>
            <Typography className="table-tooltip-text">{unit}</Typography>
          </Tooltip>
        ) : (
          <div>-</div>
        );
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const cleanedValue = (
            params?.newValue?.name?.trim() || ""
          )?.toString();
          if (cleanedValue?.length > 15) {
            notification.error({
              description: _t(
                "Unit should be less than or equal to 15 characters"
              ),
            });
            return false;
          }
          const updatedData = {
            ...params.data,
            unit: cleanedValue,
          };
          params.node.setData(updatedData);
          handleUpdate([updatedData], {});
        }
        return true;
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 130,
      maxWidth: 130,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: ICellRendererParams) => {
        // const totalFloat = itemTotalCalculator(data);
        // const value = formatter((totalFloat || 0) / 100);
        const value = formatter(
          formatAmount(Number(data?.total || 0) / 100)
        ).value_with_symbol;
        return (
          <Tooltip title={value}>
            <Typography className="table-tooltip-text">{value}</Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Tax"),
      field: "apply_global_tax",
      minWidth: 50,
      maxWidth: 50,
      suppressMovable: false,
      suppressMenu: true,

      headerClass: "ag-header-center",
      cellClass: "ag-cell-center flex justify-center",
      editable: !disableModification,
      cellEditorParams: {
        maxLength: 20,
      },
      cellRenderer: "agCheckboxCellRenderer",
      cellEditor: "agCheckboxCellEditor",
      valueGetter: (params: ValueSetterParams) => {
        return !!params.data.apply_global_tax;
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const updatedData = {
            ...params.data,
            apply_global_tax: Boolean(params.newValue),
          };
          params.node.setData(updatedData);
          handleUpdate([updatedData], {});
        }
        return true;
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 80,
      maxWidth: 80,
      cellRenderer: ({ data }: ICellRendererParams) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                const selectedItemData = {
                  ...data,
                  quantity: data.quantity || "",
                };
                setSelectedChangeOrderItem(selectedItemData);
                setShowNewChangeOrderItem(false);
                setShowChangeOrderItem(true);
                setSelectedSection(singleSection);
              }}
            />
            {!disableModification && details?.change_order_id && (
              <ChangeOrderDeleteItemAction
                itemId={data.item_id}
                changeOrderId={details?.change_order_id}
                // {selectedSection?.section_id}
              />
            )}
          </div>
        );
      },
    },
  ];

  const dynamicCOCommonListOptions = COSectionOptions.map((option) => {
    if (option.value === "title") {
      return {
        ...option,
        label:
          _t("Add Item to ") +
          _t(
            details?.type === "cor"
              ? `${module_singular_name} Request`
              : module_singular_name
          ),
      };
    }

    if (option.value === "add_manual_item") {
      return {
        ...option,
        label: `Add Manual ${
          details?.type === "cor"
            ? `${module_singular_name} Request`
            : module_singular_name
        } Item`,
      };
    }

    return option;
  });

  const taxDetails = useMemo(() => {
    if (!details?.taxes) return [];

    return details?.taxes?.map((tax) => {
      const isReversible =
        typeof tax.is_reversible === "boolean"
          ? tax.is_reversible
          : tax.is_reversible?.toString() === "1";

      return {
        taxIsReversible: isReversible,
        taxRate: parseFloat(tax?.tax_rate ?? 0),
      };
    });
  }, [details?.taxes]);
  const { taxIsReversible = false, taxRate = 0 } = taxDetails?.[0] || {};

  const SingleSectionMetrics = useMemo(() => {
    const items = singleSection?.items;

    const filteredItems =
      (items as IChangeOrderItem[])
        ?.filter((el: IChangeOrderItem) => {
          return !el?.is_optional_item;
        })
        ?.map((el: IChangeOrderItem) => ({
          ...el,
          unit_cost: `${Number(el.unit_cost) / 100}`,
          markup: el?.is_markup_percentage
            ? Number(el?.markup)
            : Number(el?.markup) / 100,
        })) || [];

    const calculateSum = (
      array: IChangeOrderItem[],
      callback: (item: IChangeOrderItem) => number
    ): number =>
      array?.map(callback)?.reduce((sum, value) => sum + value, 0) || 0;

    const estimatedCost = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, false)
    );
    const subtotal = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, true)
    );

    const subtotalTaxeble = calculateSum(
      filteredItems?.filter((ite: IChangeOrderItem) =>
        typeof ite?.apply_global_tax === "boolean"
          ? ite?.apply_global_tax
          : ite?.apply_global_tax?.toString() === "1"
      ),
      (ite: IChangeOrderItem) => itemTotalCalculator(ite, false)
    );

    const markup = calculateSum(filteredItems, (item) =>
      item?.is_markup_percentage
        ? Number(item?.unit_cost) *
          (Number(item?.markup) / 100) *
          Number(item?.quantity)
        : Number(item?.markup) &&
          Number(item?.unit_cost) * Number(item?.quantity)
        ? Number(item?.markup) -
          Number(item?.unit_cost) * Number(item?.quantity)
        : 0
    );

    const profitMargin = estimatedCost
      ? ((markup / estimatedCost) * 100).toFixed(2) + "%"
      : "0%";

    const tax = taxIsReversible ? 0 : subtotalTaxeble * (taxRate / 100);
    const grandTotal = subtotal + tax;

    return {
      // sectionName: section?.section_name,
      estimatedCost,
      subtotalTaxeble,
      subtotal,
      markup,
      profitMargin,
      tax,
      grandTotal,
    };
  }, [sections, taxRate, taxIsReversible, singleSection?.items]);

  const [{ isDragging }, dragRef] = useDrag({
    type: "SECTION",
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: async (draggedItem, monitor) => {
      // if (draggedItem.index === index) return;
      let payloadSec: Array<Partial<IChangeOrderSection>> = [];
      const updatedSections = sections?.map((section, idx) => {
        payloadSec?.push({
          section_id: section?.section_id,
          custom_section_id: idx + 1,
        });

        return {
          ...section,
          custom_section_id: idx + 1,
          // items: section?.items?.map((item) => {
          //   return {
          //     ...item,
          //     custom_section_id: idx + 1,
          //   };
          // }),
        };
      });
      try {
        const update_Section = await dispatch(
          updateCOSectionOrder({
            change_order_id: id,
            sectionItems: payloadSec,
          })
        );
        const response = update_Section?.payload as IEstimatesItemsApiRes;

        if (response?.success) {
          dispatch(
            moveSectionState({ fromIndex: index, toIndex: draggedItem?.index })
          );
          dispatch(setSection(updatedSections));
        } else {
          notification.error({
            description: response?.message,
          });
          dispatch(setSection(updatedSections));
        }
      } catch (error) {
        notification.error({
          description: error?.toString(),
        });
        // await dispatch(getEstimateItems({ estimate_id }));
        dispatch(setSection(sections));
      }
    },
  });

  const [{ isOver }, dropRef] = useDrop<DragItem, void, { isOver: boolean }>({
    accept: "SECTION", // Define the type of draggable item this drop target accepts
    hover: (item) => {
      if (item.index === index) return; // Prevent unnecessary updates if already in the correct position

      // Perform the drag-and-drop action
      dispatch(moveSectionState({ fromIndex: item.index, toIndex: index }));

      // Update the dragged item's index to prevent further redundant hover actions
      item.index = index;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(), // Tracks whether the drop target is being hovered
    }),
  });

  const dragAndDropRef = (node: HTMLElement) => {
    dragRef(node);
    dropRef(node);
  };

  const handleDragEnd = useCallback(
    async (
      event: RowDragEvent<RowDragEvent, RowDragEvent>,
      sectionDet: Partial<IChangeOrderSection>
    ) => {
      if (isReadOnly) return;
      const prevSection = event?.node?.data?.section_id;
      const prevRow = event.node.rowIndex;
      const nextSection = sectionDet?.section_id || null;

      let nextRow = event.overIndex === -1 ? 0 : event.overIndex;

      const newItems: ESEstimateItem[] = [];

      let newPayload: {
        item_id: number;
        order_number: number;
        section_id: number;
      }[] = [];

      event?.api?.forEachNode((e, index) => {
        newItems?.push({
          ...e?.data,
          changeOrder_item_no: index + 1,
          order_number: index + 1,
        });
      });

      const Newsections = JSON.parse(JSON.stringify(sections)) as ESSection[];

      if (nextSection !== prevSection) {
        // Moving between sections
        const PreviousSection = Newsections?.find(
          (sec) => sec.section_id === prevSection
        );
        const NextSection = Newsections?.find(
          (sec) => sec.section_id === nextSection
        );

        if (NextSection && PreviousSection) {
          if (prevRow !== null) {
            NextSection?.items?.splice(nextRow, 0, {
              ...PreviousSection?.items?.[prevRow],
              section_id: nextSection ?? 0,
            });
            PreviousSection?.items?.splice(prevRow, 1);
          }
          newPayload = [
            ...(PreviousSection?.items?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? []),
            ...(NextSection?.items?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? []),
          ];
        }
        dispatch(setSection(Newsections));

        const update_Item = await dispatch(
          updateItemOrder({
            change_order_item: id,
            items: newPayload,
          })
        );
        const response = update_Item?.payload as IEstimatesItemsApiRes;
        if (response?.success) {
          dispatch(setSection(Newsections));
        } else {
          notification.error({
            description: _t("Item Order Not updated in between Sections."),
          });
          dispatch(setSection(sections));
        }
      }
      if (nextSection === prevSection) {
        // Rearranging within the same section
        const UpdateSameSection = Newsections?.find(
          (sec) => sec.section_id === prevSection
        );
        if (UpdateSameSection) {
          UpdateSameSection.items = newItems;
          newPayload =
            newItems?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? [];
        }

        const update_Item = await dispatch(
          updateItemOrder({
            change_order_id: id,
            items: newPayload,
          })
        );
        const response = update_Item?.payload as IEstimatesItemsApiRes;
        if (response?.success) {
          dispatch(setSection(Newsections));
        } else {
          notification.error({
            description: _t("Item Order Not updated in Section."),
          });
          dispatch(setSection(sections));
        }
      }
    },
    [dispatch, isReadOnly, sections]
  );

  const handleOptionClick = (option: IDropdownMenuOption) => {
    setSelectedSection(singleSection);
    switch (option.value) {
      case "cost_item_database":
        setCostItemDatabaseOpen(singleSection);
        break;
      case "automatic_bulk_markup":
        setChangeOrderBulkMarkup(singleSection);
        break;
      case "add_discount":
        setShowDiscountModal(true);
        setSelectedSection(singleSection);
        break;
      case "copy_section":
        setConfirmCopyDialogOpen(true);
        setCopySectionData({
          section_id: singleSection?.section_id,
          change_order_id: id,
        });
        break;
      case "delete_section":
        if (sections?.length > 1) {
          setConfirmDialogOpen(true);
          setSelectDeletedItems(singleSection);
        } else {
          notification.error({
            description: _t("At least one section is needed"),
          });
        }
        break;
      case "add_manual_item":
        setShowNewChangeOrderItem(true);
        setShowChangeOrderItem(false);

      default:
        break;
    }
  };
  const CollapseSingleTableMemo = useMemo(() => {
    return (
      <div
        key={`${singleSection?.section_id}-${singleSection?.code_id}`}
        style={{ opacity: isDragging ? 0.5 : 1 }}
      >
        <CollapseSingleTable
          key={`${singleSection?.section_id}-${singleSection?.code_id}`}
          style={{
            opacity: isDragging ? 0.5 : 1,
            transform: isDragging ? "scale(1.05)" : "scale(1)",
            transition: "transform 0.2s ease, opacity 0.2s ease", // Smooth animation
          }}
          defaultActiveKey={default_item_view ? [] : [1]}
          title={_t(
            `${HTMLEntities.decode(
              sanitizeString(singleSection?.section_name ?? "")
            )}`
          )}
          className={`${
            isReadOnly ? "" : "move-collapse-table"
          } est-item-table`}
          leftsideContant={
            !isReadOnly && (
              <div ref={dragAndDropRef as any}>
                <ButtonWithTooltip
                  icon="fa-solid fa-grip-dots"
                  tooltipTitle={_t("Move")}
                  tooltipPlacement="top"
                  iconClassName="w-3.5 h-3.5"
                  className="hover:!bg-primary-8 active:!bg-primary-8 cursor-move absolute top-3.5 left-[5px]"
                  onClick={() => {}}
                />
              </div>
            )
          }
          rightsideContant={
            <div className="flex flex-wrap items-center gap-1">
              <div className="flex items-center gap-1.5 mr-2 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
                <FontAwesomeIcon
                  className="w-4 h-4"
                  icon="fa-duotone fa-solid fa-money-check-dollar"
                />
                <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
                  {
                    formatter(SingleSectionMetrics?.grandTotal?.toFixed(2))
                      ?.value_with_symbol
                  }
                </Typography>
              </div>
              {!isReadOnly && !disableModification && (
                <DropdownMenu
                  options={dynamicCOCommonListOptions.map((option) => {
                    return {
                      ...option,
                      onClick: () => handleOptionClick(option),
                    };
                  })}
                  buttonClass="w-fit h-auto m-0"
                  contentClassName="min-w-[280px] add-items-drop-down"
                  placement="bottomRight"
                >
                  <div className="py-1 px-2.5 bg-primary-8 rounded flex items-center gap-[5px]">
                    <Typography className="text-primary-900 text-sm">
                      {_t("Add Items / Modify Section")}
                    </Typography>
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900"
                      icon="fa-regular fa-chevron-down"
                    />
                  </div>
                </DropdownMenu>
              )}
              <ButtonWithTooltip
                icon="fa-solid fa-eye"
                tooltipTitle={_t("View")}
                tooltipPlacement="top"
                iconClassName="w-3.5 h-3.5"
                className="hover:!bg-primary-8 active:!bg-primary-8 ml-1"
                onClick={() => {
                  setSectionOpen(true);
                  setSectionData(singleSection);
                }}
              />
            </div>
          }
          children={
            <div className="p-2 common-card">
              <div className="ag-theme-alpine">
                <StaticTable
                  ref={gridRef}
                  suppressContextMenu={true}
                  suppressDragLeaveHidesColumns={true}
                  suppressMoveWhenRowDragging={true}
                  className="static-table"
                  suppressRowClickSelection={true}
                  rowDragManaged={!isReadOnly}
                  animateRows={true}
                  onRowDragEnd={(e) => handleDragEnd(e, singleSection)}
                  onGridReady={(params) =>
                    onGridReady(index, params, singleSection as ESSection)
                  }
                  stopEditingWhenCellsLoseFocus={true}
                  columnDefs={columnDefs}
                  getRowClass={(params) => {
                    const sectionId = params.node.data.section_id;
                    const rowIndex = params.node.rowIndex;
                    return `section-${sectionId} row-${rowIndex} hoverablerow`; // Add custom classes to rows
                  }}
                  rowData={singleSection?.items}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                    />
                  )}
                />
              </div>
            </div>
          }
        />
      </div>
    );
  }, [
    singleSection,
    // flags,
    default_item_view,
    isReadOnly,
    SelectedMul,
    // isLoadingCheckBox,
    sections?.length,
    selectedItems,
    SingleSectionMetrics,
    columnDefs,
  ]);
  return (
    <>
      {CollapseSingleTableMemo}
      <SectionOpen
        sectionOpen={sectionOpen}
        setSectionOpen={setSectionOpen}
        isViewOnly={isReadOnly}
        isSectionAdd={false}
        formData={sectionData ?? {}}
        onClose={() => {
          setSectionData({});
        }}
      />
      {newUnitName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.item_id === selectedData?.item_id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode?.setData({
                      ...currentRowNode.data,
                      unit: newUnitName,
                    });
                    const updatedData = {
                      ...selectedData,
                      unit: newUnitName,
                    };
                    const response = await handleUpdate([updatedData], {});
                    if (!response.success) {
                      currentRowNode?.setData(oldData);
                      notification.error({ description: response.message });
                    }
                  }
                }
                const existingColDefs = api.getColumnDefs();
                if (!existingColDefs) return;

                const updatedColDefs = existingColDefs.map((col) =>
                  "field" in col && col.field === "unit"
                    ? {
                        ...col,
                        filterParams: {
                          values:
                            newUnits.map((unit) => ({
                              label: unit.name?.toString(),
                              value: unit.name?.toString(),
                            })) ?? [],
                        },
                        cellEditorParams: {
                          ...col.cellEditorParams,
                          values: newUnits,
                        },
                      }
                    : col
                );

                api.setColumnDefs(updatedColDefs);

                // Ensure the grid header re-renders
                api.refreshHeader();
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
};

export default COAddModifyItems;
