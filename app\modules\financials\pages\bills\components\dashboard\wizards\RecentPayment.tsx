// Fortawesome
// Hook
import { useTranslation } from "~/hook";
// Atomd
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { SkeletonInput } from "~/shared/components/atoms/skeleton";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { formatAmount, Number } from "~/helpers/helper";
import { useState } from "react";
import { fetchSingleDashData } from "../../../redux/action/billDashAction";
import { setRecentPayments } from "../../../redux/slices/dashboardSlice";
import { useAppBillDispatch, useAppBillSelector } from "../../../redux/store";

const RecentPayment = () => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppBillDispatch();
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const {
    countBillPaymentValue,
    countBillPaymentValueLastRefreshTime,
    isDashLoading,
  }: IBillDashState = useAppBillSelector((state) => state.dashboardData);
  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    const response = await fetchSingleDashData({
      refresh_type: "count_bill_payment",
    } as IBillDashApiRes);
    dispatch(setRecentPayments(response));
    setIsCashLoading(false);
  };
  const recentFormattedAmount = formatter(
    formatAmount(Number(countBillPaymentValue?.amount).toFixed(2))
  );

  const recentAmount =
    Number(countBillPaymentValue?.amount) > 1000
      ? recentFormattedAmount.value_with_symbol_and_c_type
      : recentFormattedAmount.value_with_symbol;

  return (
    <>
      <DashboardCardHeader
        title={_t("Recent Payments")}
        isRefreshing={isCashLoading}
        showRefreshIcon={true}
        refreshIconTooltip={countBillPaymentValueLastRefreshTime}
        onClickRefresh={handleRefreshClick}
      />
      <div className="p-[15px] flex flex-col justify-between min-h-[141px]">
        <Typography className="sm:text-lg text-sm text-primary-gray-700 font-semibold inline-block dark:text-white/90">
          {_t("Last 14 Days")}
        </Typography>
        <div className="flex items-center justify-between">
          <FontAwesomeIcon
            className="h-[58px] w-[70px] text-primary-900 dark:text-white"
            icon="fa-duotone fa-solid fa-money-check-dollar"
          />
          <div className="flex items-center h-[60px]">
            {isDashLoading || isCashLoading ? (
              <SkeletonInput className="sm:!w-[200px] !w-10 !h-[50px]" />
            ) : (
              <Tooltip title={recentAmount}>
                <Typography className="md:text-[32px] sm:text-2xl text-lg ml-auto text-primary-900 font-semibold dark:text-white/90">
                  {recentAmount}
                </Typography>
              </Tooltip>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default RecentPayment;
