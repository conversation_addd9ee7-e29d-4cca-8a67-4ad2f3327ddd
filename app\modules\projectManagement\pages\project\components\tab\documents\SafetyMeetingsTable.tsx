import { useEffect, useMemo, useState } from "react";

// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { SafetyMeetingsFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/safetyMeetingsFieldRedirectionIcon";
// Other
import { useParams } from "@remix-run/react";
import { useAppProSelector } from "../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig, useGModules } from "~/zustand";
import { sendMessageKeys } from "~/components/page/$url/data";
import { sanitizeString } from "~/helpers/helper";
import { getModuleAccess } from "~/shared/utils/helper/module";
import { useAppSMDispatch } from "~/modules/people/safetymeetings/redux/store";
import { fetchDashData } from "~/modules/people/safetymeetings/redux/action";
import { defaultConfig } from "~/data";
import { ADD_SAFETY_OPTION } from "~/modules/people/safetymeetings/utils/constants";

const SafetyMeetingsTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const safetyMeetingModule = getGlobalModuleByKey(
    CFConfig.safety_meeting_module
  );
  const dispatch = useAppSMDispatch();
  const { safetyMeetingOption }: ISafetyMeetingsIntlState = useAppProSelector(
    (state) => state.smDashboard
  );

  const { checkModuleAccessByKey, getGModuleByKey } = useGModules();
  const { module_name, module_singular_name, module_access }: GConfig =
    getGConfig();

  const isNoAccessGroupAndIndividual = useMemo(() => {
    const groupAccess = checkModuleAccessByKey(
      defaultConfig.safety_meeting_module_group
    );
    const individualAccess = checkModuleAccessByKey(
      defaultConfig.safety_meeting_module_individual
    );

    return {
      group: groupAccess === "no_access",
      individual: individualAccess === "no_access",
    };
  }, [
    defaultConfig.safety_meeting_module_group,
    defaultConfig.safety_meeting_module_individual,
  ]);

  const availableMeetingOptions = useMemo(() => {
    if (
      isNoAccessGroupAndIndividual.individual &&
      isNoAccessGroupAndIndividual.group
    )
      return [];

    return ADD_SAFETY_OPTION.filter((option) => {
      if (option.value === "group") {
        if (
          isNoAccessGroupAndIndividual.group ||
          safetyMeetingOption.group_safety_meeting !== 1
        ) {
          return false;
        }
      }

      if (option.value === "individual") {
        if (
          isNoAccessGroupAndIndividual.individual ||
          safetyMeetingOption.individual_safety_meeting !== 1
        ) {
          return false;
        }
      }

      if (option.value === "schedule") {
        if (safetyMeetingOption.scheduled_safety_meeting !== 1) {
          return false;
        }
      }

      return true;
    }).map((option) => ({
      ...option,
      label: option.label.replace("Safety Meeting", module_singular_name),
    }));
  }, [isNoAccessGroupAndIndividual, safetyMeetingOption, module_singular_name]);

  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const safetyMeetings = documentsData?.safety_meeting ?? [];
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [allSafetyMeetings, setAllSafetyMeetings] = useState(safetyMeetings);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const displayedSafetyMeetings = isShowingMore
    ? allSafetyMeetings
    : allSafetyMeetings.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.safety_meetings_count?.[0]?.number_of_safety_meeting ?? 0
  );
  const { authorization }: GConfig = getGConfig();
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllSafetyMeetings(safetyMeetings);
      setIsInitialLoad(false);
    } else {
      const newSafetyMeetings = safetyMeetings.filter(
        (item) =>
          !allSafetyMeetings.some((p) => p.meeting_id === item.meeting_id)
      );
      if (newSafetyMeetings.length > 0) {
        setAllSafetyMeetings((prev) => [...prev, ...newSafetyMeetings]);
      } else {
        setAllSafetyMeetings(safetyMeetings);
      }
    }
  }, [safetyMeetings, isInitialLoad]);

  useEffect(() => {
    if (safetyMeetings.length) {
      setCollapse(["1"]);
    }
  }, [safetyMeetings]);

  useEffect(() => {
    dispatch(fetchDashData());
  }, []);

  const handleShowMore = () => {
    if (allSafetyMeetings.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["safety_meetings", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: _t("Type"),
      field: "type_name",
      minWidth: 135,
      maxWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const type_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={type_name}>
            <Typography className="table-tooltip-text">{type_name}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Topic Name"),
      field: "subject",
      flex: 2,
      minWidth: 130,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const topic_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={topic_name}>
            <Typography className="table-tooltip-text">{topic_name}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "item_date",
      maxWidth: 390,
      minWidth: 390,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: string }) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },
    {
      headerName: "",
      field: "meeting_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: number }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const pathname = "manage_safety_meetings.php";
                const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);
                newURL.searchParams.set("id", value?.toString());
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <div className="w-6">
              <SafetyMeetingsFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                safetyMeetingsId={value}
              />
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(safetyMeetingModule?.plural_name || "Safety Meetings")}
        addButton={safetyMeetingModule?.module_name || "Safety Meeting"}
        addButtonDisabled={
          (availableMeetingOptions?.length > 0 ? false : true) ||
          safetyMeetingModule?.can_write !== "1"
        }
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }
          const pathname = "manage_safety_meetings.php";
          const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

          newURL.searchParams.set("authorize_token", tempAuthorization);
          newURL.searchParams.set("iframecall", "1");
          newURL.searchParams.set("from_remix", "1");
          newURL.searchParams.set("action", "new");
          newURL.searchParams.set("project", id?.toString());
          setIframeData({
            addUrl: newURL.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedSafetyMeetings}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-safety-meetings.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectDocumentsModules(false, [
              "safety_meetings",
              "counts",
            ]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            console.log(key, sendMessageKeys, sendMessageKeys.iframe_change);
            if (key === sendMessageKeys.iframe_change) {
              // reload data
              fetchAllProjectDocumentsModules(false, [
                "safety_meetings",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default SafetyMeetingsTable;
