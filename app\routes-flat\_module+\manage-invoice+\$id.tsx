import { useEffect, useState } from "react";
import delay from "lodash/delay";
import { Outlet, useNavigate, useParams } from "@remix-run/react";
// Hooks, redux
import {
  getCommonSidebarCollapse,
  getGConfig,
  getGModuleByKey,
  getGSettings,
  setCommonSidebarCollapse,
} from "~/zustand";
import { useIframe, useTranslation } from "~/hook";
import { sendMessageKeys } from "~/components/page/$url/data";
import { routes } from "~/route-services/routes";
import { fetchIVDetails } from "~/modules/financials/pages/invoice/redux/action/iVDetailsAction";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import { getInvoiceTerms } from "~/redux/action/invoiceTermsAction";
import { getTaxDetails } from "~/redux/action/getTaxDetailsAction";
import { getCostCode } from "~/redux/action/getCostCodeAction";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
// ModuleSidebar
import { ModuleSidebar } from "~/shared/components/moduleSidebar";
// Other
import { SELECT_TAB_OPTIONS } from "~/modules/financials/pages/invoice/utils/constants";
import ManageInvoiceTab from "./$id+/$tab";
import IVStoreProvider from "~/modules/financials/pages/invoice/redux/IVStoreProvider";
import { fetchMarkupWithItemTypes } from "~/redux/action/commonMarkupWithItemTypesAction";
import { fetchIVStatusListApi } from "~/modules/financials/pages/invoice/redux/action";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import { getCustomStatusList } from "~/redux/action/getCustomStatusAction";
import { defaultConfig } from "~/data";
// FontAwesome File
import { IVDetailRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/invoice/detail/regular";
import { IVDetailSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/invoice/detail/solid";
import { IVDetailLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/invoice/detail/light";
import InvoiceFilePreview from "~/modules/financials/pages/invoice/components/InvoiceFilePreview";
import { Tooltip } from "~/shared/components/atoms/tooltip";

// Fort Awesome Library Add icons
IVDetailRegularIconAdd();
IVDetailSolidIconAdd();
IVDetailLightIconAdd();

const ManageInvoiceCom = () => {
  const params: RouteParams = useParams();
  const { page_is_iframe, module_access }: GConfig = getGConfig();
  const ivModule: GModule | undefined = getGModuleByKey(
    defaultConfig.invoice_merge_module_key
  );
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const gSetting: GSettings = getGSettings();
  const navigate = useNavigate();
  const { parentPostMessage } = useIframe();
  const dispatch = useAppIVDispatch();
  const { _t } = useTranslation();
  let { getExistingUsersWithApi } = useExistingCustomers();
  const { sLFetchedModuleId }: ICustomStatusListInitialState = useAppIVSelector(
    (state) => state.customStatusListData
  );
  const { details, isDetailLoading }: IIVDetailsInitialState = useAppIVSelector(
    (state) => state.invoiceDetails
  );

  const [viewEmailPdf, setViewEmailPdf] = useState<Partial<IInvoiceData>>({});
  const [billedToData, setBilledToData] = useState<TselectedContactSendMail[]>(
    []
  );

  const { isStatusListDataFetched } = useAppIVSelector(
    (state) => state.statusList
  );

  useEffect(() => {
    if (!isStatusListDataFetched) {
      dispatch(
        fetchIVStatusListApi({
          quickbook_sync: gSetting?.quickbook_sync?.toString() || "0",
          quickbook_desktop_sync:
            gSetting?.quickbook_desktop_sync?.toString() || "0",
        })
      );
    }
  }, [isStatusListDataFetched]);

  useEffect(() => {
    if (params?.id) {
      (async () => {
        const resData = (await dispatch(
          fetchIVDetails({ id: params.id || "", add_event: true })
        ).unwrap()) as IIVDetailsApiRes;

        if (!resData?.success) {
          notification.error({
            description: resData?.message || "No data found.",
          });
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            delay(() => {
              parentPostMessage(sendMessageKeys?.modal_change, {
                open: false,
              });
            }, 1000);
          } else {
            navigate(`${routes.MANAGE_INVOICE.url}`);
          }
        }
      })();
    }
  }, [params?.id]);

  useEffect(() => {
    if (
      ivModule?.module_id &&
      ivModule?.module_id?.toString() != sLFetchedModuleId
    ) {
      dispatch(
        getCustomStatusList({
          is_deleted: 0,
          module_id: [ivModule?.module_id],
        })
      );
    }
  }, [ivModule?.module_id, sLFetchedModuleId]);

  useEffect(() => {
    dispatch(getInvoiceTerms({ is_deleted: 0 }));
    // dispatch(fetchMarkupWithItemTypes());
    // dispatch(getTaxDetails({ status: 0 }));
    dispatch(getTaxDetails({ status: 0, start: 0, limit: -1 }));
  }, []);

  useEffect(() => {
    if (details?.invoice_id) {
      dispatch(
        fetchMarkupWithItemTypes({
          project_id: details?.project_id || undefined,
        })
      );
      dispatch(getCostCode({ project_id: details?.project_id || undefined }));
    }
  }, [details?.invoice_id, details?.project_id]);

  const handleSubmitClient = () => {
    const viewEmailPdf = {
      invoice_id: details?.invoice_id,
      email_subject: details?.email_subject,
      project_id: details?.project_id,
      due_date: details?.due_date,
      billed_to: details?.billed_to,
      customer_id: details?.customer_id,
      prefix_company_invoice_id: details?.prefix_company_invoice_id,
    };
    setViewEmailPdf(viewEmailPdf);
    if (
      (details?.billed_to && details?.billed_to != 0) ||
      (details?.customer_id && details?.customer_id != 0)
    ) {
      // const userIds =
      //   details?.customer_id && details?.billed_to
      //     ? [details?.customer_id.toString(), details?.billed_to.toString()]
      //         .filter(Boolean)
      //         .join(",")
      //     : details?.customer_id && !details?.billed_to
      //     ? details.customer_id?.toString()
      //     : !details?.customer_id && details?.billed_to
      //     ? details.billed_to.toString()
      //     : "";

      const userIdsArr = [
        !details?.customer_contact_id || details.customer_contact_id == 0
          ? details?.customer_id
          : "0",
        !details?.billed_to_contact || details.billed_to_contact == 0
          ? details?.billed_to
          : "0",
      ].filter(Boolean);

      getExistingUsersWithApi({
        usersIds: userIdsArr.map(String).join(","),
        contactIDs: [
          details?.customer_contact_id?.toString() || "0",
          details?.billed_to_contact?.toString() || "0",
        ].join(","),
        apiDataReturn: (
          invoicedToCustomer: Partial<CustomerSelectedData>[]
        ) => {
          if (invoicedToCustomer.length > 0) {
            setBilledToData(invoicedToCustomer as TselectedContactSendMail[]);
          }
        },
      });
    }
  };

  return (
    <div
      className={`flex overflow-hidden ${
        !page_is_iframe
          ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
          : "h-screen"
      }`}
    >
      {!sidebarCollapse && (
        <div
          className={`xl:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
            sidebarCollapse
              ? "w-0 h-0"
              : window.ENV.PAGE_IS_IFRAME
              ? "w-full h-full"
              : "w-full h-[calc(100dvh-112px)]"
          }`}
          onClick={() => setCommonSidebarCollapse(true)}
        ></div>
      )}
      <ModuleSidebar
        sidebarCollapse={sidebarCollapse}
        onSidebarCollapse={setCommonSidebarCollapse}
        selectOptions={SELECT_TAB_OPTIONS}
        onSelectedOption={(value: string) => {
          navigate(value);
        }}
        sidebarClassName={
          window.ENV.PAGE_IS_IFRAME
            ? "max-h-[calc(100vh-123px)]"
            : "max-h-[calc(100vh-265px)]"
        }
        selectedOption={params?.tab ?? "details"}
        SelecteMenuBottom={
          !(module_access === "read_only") &&
          details?.is_deleted != 1 &&
          !isDetailLoading && (
            <div
              className={`fixed bg-primary-900 ${
                window.ENV.PAGE_IS_IFRAME ? "bottom-0" : "md:bottom-8 bottom-28"
              } ${
                sidebarCollapse
                  ? "md:w-[75px] w-0 md:p-2.5 md:flex hidden"
                  : "w-[225px] p-2.5"
              }`}
            >
              {sidebarCollapse ? (
                <Tooltip
                  title={_t("Submit to Client")}
                  placement="right"
                  overlayClassName="menu-tooltip"
                >
                  <Button
                    type="primary"
                    htmlType="button"
                    className={`h-[38px] w-[calc(100%-12px)] mx-auto gap-0 px-3 bg-[#FF5400] hover:!bg-[#FF5400] active:!bg-[#FF5400] !shadow-none !duration-1000 ease-in-out ${
                      sidebarCollapse ? "md:flex hidden" : ""
                    }`}
                    onClick={handleSubmitClient}
                  >
                    <div className="w-[25px] flex justify-center">
                      <FontAwesomeIcon
                        className="w-5 h-5 text-white"
                        icon="fa-regular fa-paper-plane"
                      />
                    </div>
                  </Button>
                </Tooltip>
              ) : (
                <Button
                  type="primary"
                  htmlType="button"
                  className={`w-full h-[38px] gap-0 px-3 bg-[#FF5400] hover:!bg-[#FF5400] active:!bg-[#FF5400] !shadow-none !duration-1000 ease-in-out ${
                    sidebarCollapse ? "md:flex hidden" : ""
                  }`}
                  onClick={handleSubmitClient}
                >
                  <div className="w-[25px] flex justify-center">
                    <FontAwesomeIcon
                      className="w-5 h-5 text-white"
                      icon="fa-regular fa-paper-plane"
                    />
                  </div>
                  <Typography className="ease-in-out whitespace-nowrap duration-75 text-white normal-case font-normal pl-2">
                    {_t("Submit to Client")}
                  </Typography>
                </Button>
              )}
            </div>
          )
        }
      />

      {params?.tab ? <Outlet /> : <ManageInvoiceTab />}

      <InvoiceFilePreview
        viewEmailPdf={viewEmailPdf}
        setViewEmailPdf={setViewEmailPdf}
        billedToData={billedToData}
        setBilledToData={setBilledToData}
        isDetailDropDown={true}
      />
    </div>
  );
};

const ManageInvoice = () => {
  return (
    <IVStoreProvider>
      <ManageInvoiceCom />
    </IVStoreProvider>
  );
};
export default ManageInvoice;

export { ErrorBoundary };
