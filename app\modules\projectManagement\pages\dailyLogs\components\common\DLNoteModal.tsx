// Hook
import { useTranslation } from "~/hook";

// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { InfoTitle } from "~/shared/components/molecules/infoTitle";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";

// Organisms
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";

// Other
import { sanitizeString } from "~/helpers/helper";

const DLNoteModal = ({
  isOpen,
  notesData,
  onCloseModal,
}: {
  isOpen: boolean;
  notesData: ICommonNote[];
  onCloseModal: () => void;
}) => {
  const { _t } = useTranslation();

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="800px"
      onCloseModal={onCloseModal}
      maskClosable={true}
      modalBodyClass="p-0"
      header={{
        title: _t("Notes"),
        icon: (
          <FontAwesomeIcon className="w-3.5 h-3.5" icon="fa-regular fa-memo" />
        ),
        closeIcon: true,
      }}
    >
      <div className="grid gap-2.5 p-4 max-h-[calc(100dvh-150px)] overflow-y-auto">
        {notesData?.map((item) => {
          return (
            <div
              key={item?.note_id}
              className="common-card notes-card p-[15px] group/notes dark:!bg-dark-900"
            >
              <Typography className="text-13 text-primary-900 font-medium block">
                {HTMLEntities.decode(sanitizeString(item?.title))}
              </Typography>
              <Typography className="text-13 text-primary-900 block">
                {HTMLEntities.decode(sanitizeString(item?.description))}
              </Typography>

              {item?.aws_files?.length > 0 && (
                <div className="flex items-start flex-wrap gap-3.5 my-1.5 min-h-[126px] pt-2">
                  <AttachmentCard
                    isAddAllow={false}
                    isReadOnly={true}
                    files={item?.aws_files}
                    onDeleteFile={() => {}}
                    isShowAddIcon={false}
                    validationParams={{
                      date_format: "",
                      file_support_module_access: "no_access",
                      image_resolution: "",
                      module_access: "no_access",
                      module_id: 0,
                      module_key: "",
                    }}
                  />
                </div>
              )}

              <div className="flex md:flex-row flex-col md:items-center items-start gap-1 mt-2.5 pr-2.5 whitespace-nowrap overflow-x-auto">
                <div className="flex items-center gap-1">
                  <Typography className="text-gray-700 dark:text-white/90 flex items-center gap-1">
                    <FontAwesomeIcon
                      className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
                      icon="fa-regular fa-clock"
                    />
                    {_t("Posted")}:
                  </Typography>
                  <DateTimeCard
                    format="datetime"
                    date={item?.date_added}
                    time={item?.time_added}
                  />
                </div>
                <div className="flex items-center gap-1">
                  <Typography className="text-gray-700 dark:text-white/90">
                    {_t("by")}:
                  </Typography>
                  <InfoTitle
                    icon="fa-regular fa-user"
                    title={item?.added_by}
                    iconClassName="w-3 h-3"
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </CommonModal>
  );
};

export default DLNoteModal;
