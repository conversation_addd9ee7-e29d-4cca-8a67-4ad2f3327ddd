import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "@remix-run/react";
import delay from "lodash/delay";
// Hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
// Other
import { getStatusForField } from "~/shared/utils/helper/common";
import {
  workorderfieldStatus,
  workOrderStatusIconMap,
} from "../../utils/constasnts";
//Store
import {
  getGConfig,
  getGModuleDashboard,
  getGSettings,
  setCommonSidebarCollapse,
  useGModules,
} from "~/zustand"; // In future this code move in redux, developer change this code
import { useWoAppSelector, useWoAppDispatch } from "../../redux/store";
import {
  fetchWorkorderDetails,
  updateWorkOrderDetailApi,
} from "../../redux/action/workorderDetailsAction";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { MenuProps } from "antd";
import { WorkOrderTableDropdownItems } from "../dashboard/WorkOrderTableDropdownItems";
import WorkOrderStatusbar from "./WorkOrderStatusbar";
import { updateWorkorderDetail } from "../../redux/slices/workorderDetailsSlice";

const WorkOrderTopBar = ({
  sidebarCollapse,
  onReloadDirDetails,
  inputValues,
  setInputValues,
}: WorkOrderTopBarProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const { module_key, module_read_only }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const params = useParams();
  const { details, isDetailLoading }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const { is_custom_work_orders_id }: GSettings = getGSettings();

  const [workOrderStatus, setWorkOrderStatus] = useState(
    details.work_order_status
  );

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const dispatch = useWoAppDispatch();

  const StatusbarOption: Array<ModuleStatus> | undefined =
    gModuleDashboard?.module_setting?.module_status;

  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    setWorkOrderStatus(inputValues.work_order_status);
  }, [details, inputValues]);

  const workOrderStatusList: WorkStatusList[] = useMemo(
    () =>
      (
        StatusbarOption?.filter((itm) => itm?.show_in_progress_bar === "1") ||
        []
      )
        .map((item, index): WorkStatusList | null => {
          if (item && item.name) {
            const itemId = item.item_id;
            return {
              label: replaceDOMParams(sanitizeString(item.name)) || "",
              value: item.item_id?.toString() || "",
              default_color: item.status_color || "",
              icon: itemId
                ? workOrderStatusIconMap[itemId]
                : "fa-regular fa-lock-keyhole-open",
              index,
            };
          }
          return null;
        })
        .filter((item): item is WorkStatusList => item !== null),
    [StatusbarOption]
  );

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(workorderfieldStatus);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  useEffect(() => {
    if (
      details.project_id &&
      details.project_id !== "0" &&
      details.project_name &&
      details.project_name !== ""
    ) {
      setSelectedProject([
        {
          id: Number(details.project_id),
          project_name: details.project_name,
        },
      ]);
    }
  }, [details]);

  const handleUpdateField = async (data: IWorkorderDetails) => {
    const field = Object.keys(data)[0] as keyof IWorkorderDetails;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateWorkOrderDetailApi({
      id: params.id,
      ...data,
    })) as ApiCallResponse;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateWorkorderDetail(data));

      if ("project_id" in data) {
        dispatch(
          fetchWorkorderDetails({ id: params.id, shouldLoading: false })
        );
      }
    } else {
      notification.error({
        description: updateRes.message,
      });
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const handleStatus: MenuProps["onClick"] = (e) => {
    // Set the selected work order status from the clicked item
    setWorkOrderStatus(e.key);

    // Ensure the key is different from the current work order status
    if (e.key !== inputValues?.work_order_status) {
      // Find the selected option in the status list
      const selectedOption = status?.statusList?.find(
        (option) => option.key === e.key?.toString()
      );

      if (selectedOption) {
        // Update the work order status in the backend or store
        handleUpdateField({
          work_order_status: Number(e.key),
          work_order_status_name: selectedOption.label,
        });

        // Update inputValues with the new status and label
        setInputValues({
          ...inputValues,
          work_order_status: Number(e.key),
          work_order_status_name: selectedOption.label, // Set the label from selectedOption
        });
      } else {
        const description = "Selected option not found in statusList";
        notification.error({
          description,
        });
      }
    }
  };

  const onChangeProject = (projects: IProject[]) => {
    setSelectedProject(projects);
    if (projects.length) {
      handleUpdateField({
        project_id: projects[0].id,
        project_name: projects[0].project_name,
      });
      setSelectedProject([
        {
          id: Number(projects[0].id),
          project_name: projects[0].project_name,
        },
      ]);
      setInputValues({
        ...inputValues,
        project_id: projects[0].id,
        project_name: projects[0].project_name,
      });

      // dispatch(fetchWorkorderDetails({ id: params.id }));
    } else {
      setSelectedProject([
        {
          id: Number(details.project_id),
          project_name: details.project_name as string,
        },
      ]);
      notification.error({
        description: "Project field is required.",
      });
    }
  };

  const status = useMemo(() => {
    const statusList = StatusbarOption?.map((item: ModuleStatus) => ({
      label: replaceDOMParams(sanitizeString(item.name)),
      key: item?.item_id?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.status_color,
          }}
        />
      ),
    }));
    const getSelectStatus = StatusbarOption?.find(
      (item) => item.item_id?.toString() === workOrderStatus?.toString()
    );

    const selectStatus = (
      <Tooltip
        title={replaceDOMParams(sanitizeString(getSelectStatus?.name)) || ""}
      >
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.status_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.status_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {replaceDOMParams(sanitizeString(getSelectStatus?.name)) || ""}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.status_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [
    StatusbarOption,
    workOrderStatusList,
    details,
    workOrderStatus,
    inputValues,
  ]);

  const isStatusCancelled = useMemo(() => {
    return workOrderStatus === 182;
  }, [workOrderStatus]);

  const currentOptionIcon = useMemo(() => {
    let curOptIcon =
      workOrderStatusList.find(
        (option) => option.label === inputValues?.work_order_status_name
      )?.icon ?? null;

    if (isStatusCancelled) {
      curOptIcon = workOrderStatusIconMap["182"] ?? null;
    }
    return curOptIcon;
  }, [workOrderStatusList, StatusbarOption, isStatusCancelled, inputValues]);

  const handleInpOnChange = ({
    target: { name, value },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isDetailLoading ? (
              <TopBarSkeleton statusList={true} num={5} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:max-w-[calc(100%-125px)] w-full">
                  <div
                    className={`w-11 h-11 flex items-center justify-center  rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white ${
                      `bg-[${status?.getSelectStatus?.status_color}]` ||
                      "bg-[#C7A27C]"
                    }`}
                    style={{
                      backgroundColor: status?.getSelectStatus?.status_color,
                    }}
                  >
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon={
                        currentOptionIcon || "fa-regular fa-lock-keyhole-open"
                      }
                    />
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] 2xl:pr-3.5 ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <ButtonField
                      label=""
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      isDisabled={isReadOnly}
                      readOnly={isReadOnly}
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      onClick={() => setIsSelectProjectOpen(true)}
                      placeholder={_t("Select Project")}
                      required={true}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-base h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-base font-medium"
                      statusProps={{
                        status: getStatusForField(loadingStatus, "project_id"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "project_id",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      value={HTMLEntities.decode(
                        sanitizeString(inputValues?.project_name || "")
                      )}
                      headerTooltip={`Project: ${HTMLEntities.decode(
                        sanitizeString(inputValues?.project_name || "")
                      )}`}
                      rightIcon={
                        details?.project_name && (
                          <ProjectFieldRedirectionIcon
                            projectId={
                              inputValues?.project_id?.toString() || ""
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          />
                        )
                      }
                    />
                    <Tooltip
                      title={`Subject: ${HTMLEntities.decode(
                        sanitizeString(inputValues?.subject || "")
                      )}`}
                      placement="topLeft"
                    >
                      <div
                        className={`max-w-full  ${
                          module_read_only
                            ? "w-fit"
                            : "xl:w-full sm:w-1/2 w-full"
                        }`}
                      >
                        <InputField
                          placeholder={_t("Subject")}
                          labelPlacement="left"
                          name="subject"
                          id="subject"
                          formInputClassName="ellipsis-input-field"
                          className="h-[22px] py-0 text-sm font-medium"
                          readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate block"
                          labelClass="hidden"
                          inputStatusClassName="!w-[15px] !h-[15px]"
                          iconClassName="!w-3 !h-3"
                          value={HTMLEntities.decode(
                            sanitizeString(inputValues?.subject || "")
                          )}
                          maxLength={200}
                          disabled={isReadOnly}
                          readOnly={isReadOnly}
                          editInline={true}
                          iconView={true}
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "subject"
                          )}
                          onFocus={() => {
                            handleChangeFieldStatus({
                              field: "subject",
                              status: "save",
                              action: "FOCUS",
                            });
                          }}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "subject",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "subject",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onBlur={(e) => {
                            setIsFocused(false);
                            const value = e?.target?.value
                              ?.replace(/\s+/g, " ")
                              ?.trim();
                            if (value === "") {
                              notification.error({
                                description: _t("Subject field is required."),
                              });
                            }
                            if (value !== details?.subject && value !== "") {
                              handleUpdateField({ subject: value });
                            } else {
                              handleChangeFieldStatus({
                                field: "subject",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                subject: details.subject
                                  ?.replace(/\s+/g, " ")
                                  ?.trim(),
                              });
                            }
                          }}
                          onChange={handleInpOnChange}
                        />
                      </div>
                    </Tooltip>

                    <div
                      className={`flex items-center gap-2 flex-row ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex gap-2 items-center">
                        <Dropdown
                          menu={{
                            items: status.statusList,
                            selectable: true,
                            defaultSelectedKeys: [workOrderStatus as string],
                            selectedKeys: workOrderStatus
                              ? [workOrderStatus?.toString()]
                              : [],
                            onClick: handleStatus,
                          }}
                          disabled={isReadOnly}
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "work_order_status")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(
                              loadingStatus,
                              "work_order_status"
                            )}
                          />
                        )}
                      </div>
                      <Tooltip
                        title={
                          HTMLEntities.decode(
                            sanitizeString(
                              isFocused
                                ? inputValues?.custom_work_order_id?.trim() ||
                                    inputValues?.company_order_id?.trim() // On focus, show only custom_work_order_id
                                : inputValues?.need_prefix_project === 1
                                ? `WO1 #${inputValues?.projectPrefix}${
                                    inputValues?.custom_work_order_id ||
                                    inputValues?.company_order_id
                                  }` // When not focused, show projectPrefix + custom_work_order_id
                                : `WO #${
                                    inputValues?.custom_work_order_id ||
                                    inputValues?.company_order_id
                                  }`
                            )
                          ) ??
                          HTMLEntities.decode(
                            sanitizeString(
                              isFocused
                                ? inputValues?.custom_work_order_id?.trim() ||
                                    inputValues?.company_order_id?.trim() // On focus, show only custom_work_order_id
                                : inputValues?.need_prefix_project === 1
                                ? `WO1 #${inputValues?.projectPrefix}${
                                    inputValues?.custom_work_order_id ||
                                    inputValues?.company_order_id
                                  }` // When not focused, show projectPrefix + custom_work_order_id
                                : `WO #${
                                    inputValues?.custom_work_order_id ||
                                    inputValues?.company_order_id
                                  }`
                            )
                          )
                        }
                        placement="topLeft"
                      >
                        <div
                          className={`overflow-hidden ${
                            isReadOnly || is_custom_work_orders_id !== 0
                              ? "w-full"
                              : "w-fit"
                          }
                          `}
                        >
                          <InputField
                            placeholder={_t("WO") + " #"}
                            labelPlacement="left"
                            name="custom_work_order_id"
                            id="custom_work_order_id"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] text-sm font-medium py-0"
                            readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate sm:block flex"
                            maxLength={21}
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            disabled={
                              isReadOnly || is_custom_work_orders_id !== 0
                                ? false
                                : true
                            }
                            readOnly={isReadOnly}
                            value={
                              isFocused
                                ? HTMLEntities.decode(
                                    inputValues?.custom_work_order_id
                                  ) || ""
                                : `WO #${HTMLEntities.decode(
                                    sanitizeString(
                                      inputValues?.custom_work_order_id ||
                                        inputValues?.company_order_id ||
                                        ""
                                    )
                                  )}`
                            }
                            editInline={true}
                            iconView={true}
                            onChange={handleInpOnChange} // Temporary resolve type issue
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "custom_work_order_id"
                            )}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "custom_work_order_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "custom_work_order_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onFocus={() => {
                              setIsFocused(true);
                              handleChangeFieldStatus({
                                field: "custom_work_order_id",
                                status: "save",
                                action: "FOCUS",
                              });

                              // If projectPrefix is part of custom_work_order_id, strip it
                              if (inputValues.projectPrefix) {
                                if (
                                  inputValues?.need_prefix_project === 1 &&
                                  inputValues?.custom_work_order_id
                                    ?.toString()
                                    ?.startsWith(
                                      inputValues?.projectPrefix?.toString()
                                    )
                                ) {
                                  setInputValues((prev) => {
                                    return {
                                      ...prev, // Spread the previous state to preserve other properties
                                      custom_work_order_id:
                                        prev.custom_work_order_id
                                          ? prev.custom_work_order_id.replace(
                                              prev?.projectPrefix
                                                ? prev.projectPrefix
                                                : "",
                                              "" // Remove projectPrefix from custom_work_order_id
                                            )
                                          : prev.custom_work_order_id, // If no custom_work_order_id, return the same value
                                    };
                                  });
                                }
                              }
                            }}
                            onBlur={(e) => {
                              setIsFocused(false);
                              const value = e?.target?.value
                                ?.replace(/\s+/g, " ")
                                ?.trim();

                              // Handle saving the updated value
                              if (value === "") {
                                notification.error({
                                  description: _t("WO # field is required."),
                                });
                              }
                              if (
                                value !== details?.custom_work_order_id &&
                                value !== ""
                              ) {
                                setInputValues({
                                  ...inputValues,
                                  custom_work_order_id: value,
                                });
                                handleUpdateField({
                                  custom_work_order_id: value,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "custom_work_order_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_work_order_id:
                                    details.custom_work_order_id
                                      ?.replace(/\s+/g, " ")
                                      ?.trim(),
                                });
                              }
                            }}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                <div className="flex-[0_0_auto] w-auto">
                  <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                    {!isDetailLoading && !isStatusCancelled && (
                      <WorkOrderStatusbar
                        inputValues={inputValues}
                        setInputValues={setInputValues}
                        setWorkOrderStatus={setWorkOrderStatus}
                        handleUpdateField={handleUpdateField}
                        handleChangeFieldStatus={handleChangeFieldStatus}
                        workOrderStatusList={workOrderStatusList}
                        isReadOnly={isReadOnly}
                      />
                    )}
                  </ul>
                </div>
                <div className="xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:w-fit w-full">
                  <div className="flex justify-between">
                    <div className="flex gap-2.5">
                      {!window.ENV.PAGE_IS_IFRAME && (
                        <div
                          className="flex items-center cursor-pointer md:!hidden"
                          onClick={() => {
                            const params: Partial<IframeRouteParams> =
                              parseParamsFromURL(window?.location?.pathname);
                            if (params?.page && params?.id) {
                              navigate("/" + params?.page);
                            }
                          }}
                        >
                          <IconButton
                            htmlType="button"
                            variant="default"
                            className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                            icon="fa-regular fa-chevron-left"
                          />
                        </div>
                      )}
                      <div>
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-bars"
                          onClick={() =>
                            setCommonSidebarCollapse(!sidebarCollapse)
                          }
                        />
                      </div>
                    </div>

                    <ul className="flex items-center justify-end gap-2.5">
                      <li>
                        <ButtonWithTooltip
                          tooltipTitle={_t("Refresh")}
                          tooltipPlacement="top"
                          icon="fa-regular fa-arrow-rotate-right"
                          disabled={isDetailLoading}
                          iconClassName={`!text-primary-900
                        ${
                          isDetailLoading
                            ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                            : "group-hover/buttonHover:!text-deep-orange-500"
                        }`}
                          className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                            isDetailLoading
                              ? "hover:bg-transparent"
                              : "hover:!bg-deep-orange-500/5"
                          }`}
                          onClick={onReloadDirDetails}
                        />
                      </li>
                      {!isReadOnly && !isDetailLoading && (
                        <li>
                          <WorkOrderTableDropdownItems
                            data={details}
                            refreshTable={onReloadDirDetails}
                            tooltipcontent={_t("More")}
                            className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                            isDetailView={true}
                            isCallApi={true}
                          />
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={false}
          module_key={module_key}
        />
      )}
    </>
  );
};

export default WorkOrderTopBar;
