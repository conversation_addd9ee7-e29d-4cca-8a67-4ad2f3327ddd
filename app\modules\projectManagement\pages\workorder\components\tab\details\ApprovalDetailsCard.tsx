// Atoms
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { FilePreview } from "~/shared/components/molecules/filePreview";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ThumbnailView } from "~/shared/components/molecules/thumbnailView";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Other
import { useWoAppDispatch, useWoAppSelector } from "../../../redux/store";
import {
  capitalizeFirstLetter,
  getStatusForField,
} from "~/shared/utils/helper/common";
// Hook
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
import { useEffect, useMemo, useState } from "react";
import {
  workorderDetailsField,
  workorderfieldStatus,
} from "../../../utils/constasnts";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import {
  fetchWorkorderDetails,
  updateWorkOrderDetailApi,
} from "../../../redux/action/workorderDetailsAction";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { useParams } from "@remix-run/react";
import { updateWorkorderDetail } from "../../../redux/slices/workorderDetailsSlice";
import delay from "lodash/delay";

const ApprovalDetailsCard = () => {
  const { _t } = useTranslation();
  const { module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const { details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const [isOpenSelectIssuedBy, setIsOpenSelectIssuedBy] =
    useState<boolean>(false);
  const [isOpenSelectApprovedBy, setIsOpenSelectApprovedBy] =
    useState<boolean>(false);
  const [isOpenSelectBillTo, setIsOpenSelectBillTo] = useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();
  const [additionalContact, setAdditionContact] = useState<number>(0);
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const [inputValues, setInputValues] = useState<IWorkorderDetails>(
    workorderDetailsField
  );
  const params = useParams();
  const dispatch = useWoAppDispatch();
  const gConfig: GConfig = getGConfig();

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(workorderfieldStatus);
  const { date_format }: GSettings = getGSettings();

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleUpdateField = async (data: IWorkorderDetails) => {
    const field = Object.keys(data)[0] as keyof IWorkorderDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateWorkOrderDetailApi({
      id: params.id,
      ...data,
      ...(field === "order_date" && data.order_date
        ? {
            order_date: backendDateFormat(data.order_date, date_format),
          }
        : {}),
      ...(field === "end_date" && data.end_date
        ? {
            end_date: backendDateFormat(data.end_date, date_format),
          }
        : {}),
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateWorkorderDetail(data));
      dispatch(fetchWorkorderDetails({ id: params.id, shouldLoading: false }));
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 1000);
  };

  const handleIssuedBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (data?.user_id != inputValues.issued_by) {
        setInputValues({
          ...inputValues,
          issued_by: data?.user_id,
          issued_by_name: data?.display_name,
          issued_by_type:
            data?.orig_type?.toString() ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          issued_by: data.user_id,
          issued_by_contact: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "issued_by",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          issued_by: details.issued_by,
          issued_by_name: details.issued_by_name,
        });
      }
    } else {
      handleUpdateField({ issued_by: "" });
      setInputValues({
        ...inputValues,
        issued_by: "",
        issued_by_name: "",
      });
    }
  };

  const handleApprovedBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (
        data?.user_id?.toString() != details.approved_by ||
        data?.contact_id?.toString() !== details.approved_by_contact_id
      ) {
        setInputValues({
          ...inputValues,
          approved_by: data?.user_id,
          approved_by_name:
            !!data.contact_id &&
            data.company_name &&
            !data.display_name?.includes(data.company_name)
              ? `${data.display_name} (${data.company_name})`
              : data?.display_name,
          approved_by_contact_id: data?.contact_id,
          approved_by_image: data?.image,
          approved_by_dir_type:
            data?.orig_type ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          approved_by: data?.user_id?.toString(),
          approved_by_contact_id: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "approved_by",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          approved_by: details.approved_by,
          approved_by_name: details.approved_by_name,
          approved_by_contact_id: details.approved_by_contact_id,
        });
      }
    } else {
      handleUpdateField({
        approved_by: "",
        approved_by_contact_id: 0,
      });
      setInputValues({
        ...inputValues,
        approved_by: "",
        approved_by_name: "",
      });
    }
  };

  useEffect(() => {
    const woDetails = {
      ...details,
      response_name:
        details.response_name === "Accept"
          ? "Accepted"
          : details.response_name === "Reject"
          ? "Rejected"
          : details.response_name,
    };
    setInputValues(woDetails);
  }, [details.billed_to_name, details.work_order_id]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Approval Details")}
        iconProps={{
          icon: "fa-solid fa-memo-circle-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
          id: "approval_details_icon",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Approved By")}
                  name="approved_by"
                  placeholder={_t("Select Contact")}
                  labelPlacement="left"
                  className="justify-between"
                  readOnlyClassName="sm:block flex"
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  editInline={true}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.approved_by_name)
                  )}
                  avatarProps={
                    inputValues?.approved_by_name
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(inputValues?.approved_by_name)
                            ),
                            image:
                              inputValues?.approved_by_contact_id === 0 ||
                              inputValues?.approved_by_contact_id === null ||
                              !inputValues?.approved_by_contact_id
                                ? inputValues?.approved_by_image
                                : "",
                          },
                        }
                      : undefined
                  }
                  iconView={true}
                  onClick={() => {
                    setIsOpenSelectIssuedBy(false);
                    setIsOpenSelectBillTo(false);
                    setIsOpenSelectAssignedTo(false);
                    setIsOpenSelectApprovedBy(true);
                  }}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "approved_by"),
                  }}
                  rightIcon={
                    <>
                      {inputValues.approved_by &&
                      inputValues.approved_by !== "" ? (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={async () => {
                              await setAdditionContact(
                                Number(details?.approved_by_contact_id)
                              );
                              setIsContactDetails(true);
                              setcontactId(inputValues?.approved_by as number);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={inputValues.approved_by.toString()}
                            directoryTypeKey={
                              inputValues.approved_by !== "" &&
                              inputValues.approved_by
                                ? getDirectaryKeyById(
                                    inputValues.approved_by_dir_type === 1
                                      ? 2
                                      : Number(
                                          inputValues.approved_by_dir_type
                                        ),
                                    gConfig
                                  )
                                : ""
                            }
                          />
                        </div>
                      ) : (
                        <></>
                      )}
                    </>
                  }
                />
              </li>
              {details?.billed_to_status && (
                <>
                  <li>
                    <InputField
                      label={_t("Response")}
                      labelPlacement="left"
                      value={
                        details?.billed_to_status
                          ? capitalizeFirstLetter(
                              (details?.billed_to_status === "accept"
                                ? "Accepted"
                                : "") ||
                                (details?.billed_to_status === "decline"
                                  ? "Declined"
                                  : "")
                            )
                          : "-"
                      }
                      editInline={false}
                      readOnly={true}
                      iconView={true}
                      onChange={() => {}}
                    />
                  </li>
                  <li>
                    <InputField
                      label={_t("Name")}
                      labelPlacement="left"
                      value={
                        details?.approved_by_name
                          ? capitalizeFirstLetter(details?.approved_by_name)
                          : "-"
                      }
                      editInline={false}
                      readOnly={true}
                      iconView={true}
                      onChange={() => {}}
                    />
                  </li>
                  <li>
                    <InputField
                      label={_t("Date")}
                      labelPlacement="left"
                      value={
                        details?.date_billed_to_status ||
                        details?.time_billed_to_status
                          ? details?.date_billed_to_status +
                            " - " +
                            details?.time_billed_to_status
                          : "-"
                      }
                      editInline={false}
                      readOnly={true}
                      iconView={true}
                      onChange={() => {}}
                    />
                  </li>
                  <li>
                    <InputField
                      label={_t("IP")}
                      labelPlacement="left"
                      value={details?.ip_address || "-"}
                      editInline={false}
                      readOnly={true}
                      iconView={true}
                      onChange={() => {}}
                    />
                  </li>
                  <li>
                    <InlineField
                      label={_t("Signature")}
                      labelPlacement="left"
                      labelClass="flex items-center"
                      field={
                        <div className="px-1.5">
                          {details.signature && (
                            <LightGalleryModel
                              zoom={true}
                              thumbnail={true}
                              backdropDuration={150}
                              showZoomInOutIcons={true}
                              actualSize={false}
                              mode="lg-slide"
                              alignThumbnails="left"
                              className="grid max-w-[130px] md:gap-3 gap-5"
                              mousewheel={true}
                              addClass="signature-bg"
                            >
                              <div className="w-auto group/upload-file App">
                                <div className="w-full h-12 p-2 overflow-hidden relative !rounded-xl flex items-center justify-center dark:bg-dark-400">
                                  <ThumbnailView
                                    file_ext={""}
                                    file_path={details.signature ?? ""}
                                    image_id={""}
                                    markupedFileData={{}}
                                    id={0}
                                    isLoading={false}
                                    setIsLoading={() => {}}
                                  />
                                  <div
                                    className={`absolute top-0 w-full h-full ease-in opacity-0 before:scale-0 before:rounded-xl group-hover/upload-file:opacity-100 group-hover/upload-file:!visible group-hover/upload-file:before:scale-100 delay-100 before:absolute before:w-full before:h-full before:top-0 before:left-0 before:bg-black/50 before:ease-in before:duration-300 `}
                                  >
                                    <div className="absolute top-2/4 left-2/4 -translate-x-1/2 -translate-y-1/2 group-hover/upload-file:block hidden">
                                      <FilePreview
                                        imageData={{
                                          file_path: details.signature ?? "",
                                          file_ext: "",
                                          file_name: "Signature",
                                          image_id: "",
                                          id: 0,
                                        }}
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </LightGalleryModel>
                          )}
                        </div>
                      }
                    />
                  </li>
                </>
              )}
            </ul>
          </div>
        }
      />

      {isOpenSelectIssuedBy && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectIssuedBy}
          closeDrawer={() => {
            setIsOpenSelectIssuedBy(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleIssuedBy(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[CFConfig.employee_key, "my_crew", "my_project"]}
          selectedCustomer={
            inputValues.issued_by && inputValues?.issued_by !== ""
              ? ([
                  {
                    display_name: inputValues?.issued_by_name,
                    user_id: inputValues?.issued_by,
                    type: inputValues.issued_by_type,
                    type_key: getDirectaryKeyById(
                      Number(inputValues.issued_by_type) === 1
                        ? 2
                        : Number(inputValues.issued_by_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
          additionalContactDetails={0}
        />
      )}

      {isOpenSelectApprovedBy && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectApprovedBy}
          closeDrawer={() => {
            setIsOpenSelectApprovedBy(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleApprovedBy(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.approved_by && inputValues?.approved_by !== ""
              ? ([
                  {
                    display_name: inputValues?.approved_by_name,
                    user_id: inputValues?.approved_by,
                    image: inputValues?.approved_by_image,
                    contact_id: inputValues?.approved_by_contact_id,
                    type: inputValues.approved_by_dir_type,
                    type_key: getDirectaryKeyById(
                      Number(inputValues.approved_by_dir_type) === 1
                        ? 2
                        : Number(inputValues.approved_by_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
        />
      )}

      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => {
            setcontactId(0);
            setIsContactDetails(false);
          }}
          contactId={contactId}
          readOnly={isReadOnly}
          additional_contact_id={additionalContact}
        />
      )}
    </>
  );
};

export default ApprovalDetailsCard;
