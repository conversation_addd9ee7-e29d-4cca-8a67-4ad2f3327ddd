// React + ag-grid
import { useEffect, useMemo, useRef } from "react";
import { useNavigate } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import {
  CellClickedEvent,
  type GridReadyEvent,
  type SortChangedEvent,
} from "ag-grid-community";
// Hooks
import { useTranslation } from "~/hook";
import useTableGridData from "~/shared/hooks/useTableGridData";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
// Constants, Shared & Common
import {
  sanitizeString,
  getDefaultStatuscolor,
  escapeHtmlEntities,
} from "~/helpers/helper";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
// Redux & Zustand
import { getGConfig, getGModuleFilters } from "~/zustand";
import {
  useAppPLDispatch,
  useAppPLSelector,
} from "~/modules/projectManagement/punchlists/redux/store";
import { fetchDashData } from "~/modules/projectManagement/punchlists/redux/action";
// Other
import { routes } from "~/route-services/routes";
import { getPLListApi } from "../../redux/action";
import PunchListAction from "./PunchListAction";

let timeout: NodeJS.Timeout;
const PunchList = ({ search, setAddPunchlist }: IPLListProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppPLDispatch();
  const { datasource, gridRowParams } = useTableGridData();
  const { module_access, module_can_read, module_can_write }: GConfig =
    getGConfig();
  const filterSrv = getGModuleFilters() as
    | Partial<IPunchlistModuleFilter>
    | undefined;
  const limit = 30;

  const { isOwnData }: IPunchlistIntlState = useAppPLSelector(
    (state) => state.dashboard
  );

  const filter = useMemo(() => {
    return {
      project: filterSrv?.project || "",
      status: filterSrv?.status !== "2" ? filterSrv?.status : "",
      completed_status:
        filterSrv?.completed_status == ""
          ? undefined
          : filterSrv?.completed_status,
    };
  }, [JSON.stringify(filterSrv)]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };
  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };
  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const fetchPunchList = async () => {
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const { start, order_by_name, order_by_dir } = changeGridParams || {};
    const filterObj: Partial<IPunchlistModuleFilter> | undefined = !isEmpty(
      filter
    )
      ? getValuableObj(filter)
      : undefined;
    const length = changeGridParams?.length ?? limit;
    const startPagination = !!start ? Math.floor(start) : 0;
    let dataParams: IPLListParmas = {
      limit: length,
      start: startPagination,
      ignore_filter: 1,
      filter: filterObj,
      is_own_data: isOwnData,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
      search: !!search ? escapeHtmlEntities(search || "") : undefined,
    };

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_name;
    }

    try {
      gridParams?.api.hideOverlay();
      const resData = (await getPLListApi(dataParams)) as IPLApiRes;
      const punchlistArr = resData?.data?.punchlist || [];

      // Check if we got less data than requested - indicates last page
      const isLastPage = punchlistArr.length < length;

      // Calculate total based on current page and data length
      const currentTotal = isLastPage
        ? (start ? start : 0) + punchlistArr.length
        : (start ? start : 0) + length + 1; // +1 indicates there might be more

      // Send response to grid
      gridParams?.success({
        rowData: punchlistArr,
        rowCount: currentTotal,
      });

      // Handle no records case
      if (
        (!resData?.success || punchlistArr.length === 0) &&
        startPagination === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  // use ref
  const previousValues = useRef({
    filter: JSON.stringify(filterSrv),
    isOwnData,
    search,
  });

  // use effects
  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        if (!isEmpty(filterSrv)) {
          fetchPunchList();
        }
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filterSrv),
      isOwnData,
      search,
    };

    if (
      !isEqual(previousValues.current, currentValues) &&
      !isEmpty(filterSrv)
    ) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [JSON.stringify(filterSrv), isOwnData, search]);

  const columnDefs = [
    {
      headerName: "#",
      sortable: false,
      field: "company_punchlist_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMovable: false,
      suppressMenu: true,
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(
          sanitizeString(data?.company_punchlist_id?.toString())
        ),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Title"),
      field: "punchlist_name",
      minWidth: 130,
      flex: 2,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.punchlist_name)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Created Date"),
      field: "date_added",
      sortable: true,
      maxWidth: 135,
      minWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IPLTableCellRenderer) => {
        return data?.date_added ? (
          <DateTimeCard format="date" date={data?.date_added} />
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 120,
      flex: 2,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.project_name)),
      cellRenderer: ToolTipCell,
    },
    // {
    //   headerName: _t("Project Type"),
    //   field: "project_type_name",
    //   sortable: true,
    //   minWidth: 150,
    //   flex: 2,
    //   resizable: true,
    //   suppressMovable: false,
    //   suppressMenu: true,
    //   valueGetter: ({ data }: IPLTableCellRenderer) =>
    //     HTMLEntities.decode(sanitizeString(data?.project_type_name)),
    //   cellRenderer: ToolTipCell,
    // },

    {
      headerName: _t("Customer"),
      field: "customer_name_only",
      minWidth: 120,
      flex: 2,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.customer_name_only)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Assigned To"),
      field: "assignes_to",
      sortable: false,
      maxWidth: 130,
      minWidth: 130,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IPLTableCellRenderer) => {
        const { data } = params;
        const assignees = data?.assignes_to || [];
        return (
          <>
            {assignees.length > 0 ? (
              <AvatarGroup
                max={{
                  count: 1,
                  style: {
                    color: "#223558",
                    backgroundColor: "#ECF1F9",
                  },
                }}
                size={24}
                className="flex justify-center"
                prefixCls="multi-avatar-scroll"
              >
                {assignees.map((items: IAssigneesTo, index: number) => {
                  const dName = HTMLEntities.decode(items.assignee_name);
                  const assignedToNameOnlyName = HTMLEntities.decode(
                    sanitizeString(items.assigned_to_name_only)
                  );

                  return (
                    <div
                      key={index}
                      className={`flex items-center ${
                        index === 0 ? "" : "gap-2 py-0.5 px-1"
                      }`}
                    >
                      {index === 0 ? (
                        <Tooltip title={dName} placement="top">
                          <div>
                            <AvatarProfile
                              user={{
                                name: dName,
                                image: items.image,
                              }}
                              iconClassName="text-[11px] font-semibold"
                            />
                          </div>
                        </Tooltip>
                      ) : (
                        <Tooltip title={dName} placement="top">
                          <div className="p-1 flex items-center gap-1">
                            <AvatarProfile
                              user={{
                                name: dName,
                                image: items.image,
                              }}
                              iconClassName="text-[11px] font-semibold"
                            />
                            <Typography className="max-w-[160px] truncate block">
                              {items.assigned_to_name_only?.trim() !== ""
                                ? assignedToNameOnlyName
                                : dName}
                            </Typography>
                          </div>
                        </Tooltip>
                      )}
                    </div>
                  );
                })}
              </AvatarGroup>
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: "# " + _t("Incomplete"),
      field: "incomplete_items",
      sortable: true,
      minWidth: 132,
      maxWidth: 132,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.incomplete_items)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: "# " + _t("Complete"),
      field: "completed_items",
      sortable: true,
      minWidth: 120,
      maxWidth: 120,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.completed_items)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Status"),
      field: "punchlist_status",
      sortable: true,
      maxWidth: 100,
      minWidth: 100,
      suppressMovable: false,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: ({ data }: IPLTableCellRenderer) => {
        const { color, textColor } = getDefaultStatuscolor(
          data?.punchlist_status === "Open" ? "#008000" : "#d2322d"
        );
        const status = HTMLEntities.decode(
          sanitizeString(data?.punchlist_status || "")
        );
        return !!status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-20`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <div className="table-tooltip-text text-center">-</div>
        );
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 50,
      maxWidth: 50,
      hide: Boolean(module_can_read == 1 && module_can_write == 0),
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellClass: "!cursor-auto",
      cellRenderer: ({ data }: IPLTableCellRenderer) => {
        const isReadOnly = module_can_read == 1 && module_can_write == 0;
        return (
          <div className="flex items-center gap-1.5 justify-center">
            {!isReadOnly && (
              <PunchListAction
                paramsData={data}
                onActionComplete={() => {
                  refreshAgGrid();
                  dispatch(fetchDashData());
                }}
              />
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div
      className={`list-view-table ag-grid-cell-pointer ag-theme-alpine ${
        module_access === "read_only"
          ? "md:h-[calc(100dvh-304px)] h-[calc(100dvh-380px)]"
          : "md:h-[calc(100dvh-270px)] h-[calc(100dvh-304px)]"
      }`}
    >
      <DynamicTable
        columnDefs={columnDefs}
        onGridReady={onGridReady}
        onSortChanged={onSortChanged}
        onCellClicked={(params: CellClickedEvent) => {
          const column = params.column;
          const field = column.getColDef().field;

          if (field !== "" && !!params?.data?.punchlist_id)
            navigate(
              `${routes.MANAGE_PUNCHLISTS.url}/${params.data.punchlist_id}`
            );
        }}
        noRowsOverlayComponent={() => (
          <NoRecords
            rootClassName="w-full max-w-[280px]"
            image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
            imageWSize="280"
            imageHSize="227"
            text={
              module_access === "full_access" ||
              module_access === "own_data_access" ? (
                <div>
                  <Typography
                    className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    onClick={() => setAddPunchlist(true)}
                  >
                    {_t("Click here")}
                  </Typography>
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t(" to Create a New Record")}
                  </Typography>
                </div>
              ) : (
                <Typography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </Typography>
              )
            }
          />
        )}
      />
    </div>
  );
};

export default PunchList;
