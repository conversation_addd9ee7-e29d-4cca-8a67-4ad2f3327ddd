import { useMemo, useState } from "react";
// Hook + redux
import { useTranslation } from "~/hook";

import {
  useAppSMDispatch,
  useAppSMSelector,
} from "~/modules/people/safetymeetings/redux/store";
import { updateSMDetailApi } from "~/modules/people/safetymeetings/redux/action/sMDetailsAction";
import { updateSMDetail } from "~/modules/people/safetymeetings/redux/slices/sMDetailsSlice";
// Components
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import SignatureItems from "./SignatureItems";
import { Button } from "~/shared/components/atoms/button";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { NoRecords } from "~/shared/components/molecules/noRecords";
const Signature = ({
  isReadOnly,
  onSignature,
}: {
  isReadOnly: boolean;
  onSignature: (data: boolean) => void;
}) => {
  const { _t } = useTranslation();
  const dispatch = useAppSMDispatch();
  const { details, isDetailLoading }: ISMDetailsInitialState = useAppSMSelector(
    (state) => state.safetyMeetingDetails
  );
  const [isRemoveConfirmOpen, setIsRemoveConfirmOpen] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const signatureData: ISMDetailsAttendees[] = useMemo(() => {
    if (details?.group_safety_meeting == 1) {
      return details?.attendees_details && details.attendees_details.length > 0
        ? details.attendees_details?.filter((i) => !!i.display_name)
        : ([] as ISMDetailsAttendees[]);
    }

    return !!details?.signature && !!details?.leader_name
      ? [
          {
            user_id: details?.leader_id || 0,
            signature: details?.signature || "",
            display_name: details?.leader_name || "",
            image: details?.leader_image || "",
            dir_type: Number(details?.leader_type || ""),
            type_key: details?.leader_type_key || "",
            type_name: details?.leader_type_name || "",
          },
        ]
      : [];
  }, [
    details.attendees_details,
    details?.signature,
    details?.group_safety_meeting,
  ]);

  const handleRemoveSig = async () => {
    setIsLoading(true);

    const updateRes = (await updateSMDetailApi({
      id: details?.meeting_id || "",
      ...{ signatures: [{ signature: "", user_id: details?.leader_id || 0 }] },
    })) as ISMDetailsUpdateApiRes;

    if (updateRes?.success) {
      dispatch(updateSMDetail({ signature: "" }));
      setIsRemoveConfirmOpen(false);
    } else {
      notification.error({
        description: updateRes?.message,
      });
    }
    setIsLoading(false);
  };

  const onCloseDelModal = () => {
    setIsRemoveConfirmOpen(false);
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t(
          details?.group_safety_meeting == 1 ? "Signatures" : "Signature"
        )}
        iconProps={{
          icon: "fa-solid fa-signature",
          containerClassName:
            "bg-[linear-gradient(180deg,#63759A1a_0%,#63759A1a_100%)]",
          id: "signature_icon",
          colors: ["#63759A", "#63759A"],
        }}
        headerRightButton={
          details?.group_safety_meeting == 0 &&
          details?.meeting_status != 2 && (
            <Button
              type="primary"
              className="px-2 h-[30px] justify-center font-medium !bg-[#EBF1F9] !text-primary-900 !border-0 dark:text-white/90 dark:!bg-dark-400"
              htmlType="button"
              disabled={isReadOnly}
              onClick={() => onSignature(true)}
            >
              {!!details?.signature
                ? _t("Update Signature")
                : _t("Add Signature")}
            </Button>
          )
        }
        children={
          <div className="pt-2 grid gap-4">
            <div className="flex gap-[15px] flex-wrap">
              {signatureData?.map((item) => {
                return (
                  <SignatureItems
                    isReadOnly={isReadOnly}
                    meetingType={details?.group_safety_meeting || 0}
                    item={item}
                    onRemoveSig={() => setIsRemoveConfirmOpen(true)}
                  />
                );
              })}

              {!isDetailLoading && signatureData?.length < 1 && (
                <NoRecords
                  className="w-full"
                  image={`${window.ENV.CDN_URL}assets/images/no-records-sm-signature.svg`}
                />
              )}
            </div>
          </div>
        }
      />

      {isRemoveConfirmOpen && (
        <ConfirmModal
          isOpen={isRemoveConfirmOpen}
          modaltitle={_t("Confirmation")}
          description={_t("Are you sure you want to remove this signature?")}
          modalIcon="fa-regular fa-file-check"
          isLoading={isLoading}
          onAccept={() => {
            handleRemoveSig();
          }}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}
    </>
  );
};

export default Signature;
