interface IGetGanttInstance {
  getGanttInstance: () => TGanttInstance;
}

interface GanttTooltipProps extends IGetGanttInstance {
  task: TGanttTaskTypes;
  start: Date | string;
  end: Date | string;
  isLightboxOpen?: boolean;
  dragMode: string;
  company_date_format: string;
}

interface RightSideTextProps extends IGetGanttInstance {
  task: TGanttTaskTypes;
  end: Date;
  showBaseline: boolean;
  checkWidth: (task: TGanttTaskTypes) => boolean;
}

type TGanttInstance = TGanttStaticTypes | null | undefined;

interface TaskTextProps {
  task: TGanttTaskTypes;
  start: Date;
  end: Date;
  gantt: TGanttInstance;
}

type ScaleConfig = {
  unit: string;
  step: number;
  scale_unit: string;
  date_scale?: string;
  template?: (date: Date) => string;
  subscales?: any[];
};

type CachedGanttConfig = {
  scale_unit: string;
  step: string | number;
  subscales: Array<{
    unit: string;
    step: number;
    date: string;
  }>;
  date_scale: string;
  template: ((date: Date) => string) | null;
  start_date: Date | undefined;
  end_date: Date | undefined;
  scale_height: number;
};

interface CustomLightboxProps<T> {
  data: T;
  onSave: (task: Partial<TGanttTaskTypes>) => void;
  onCancel?: () => void;
  onDelete?: () => void;
  isNewTask?: boolean;
}

interface ILightboxForm {
  data: IGanttTask;
  onCancel?: () => void;
  onSave: (task: IGanttTask) => void;
}

type TDateFieldWithIncreamentDaysOptProps = {
  date: Date | string;
  field: string;
  duration: string | number;
  onChangeDate: (date: string) => void;
  onChangeDays: (newDays: string | number) => void;
  isDateDisabled?: boolean;
  isTask?: boolean;
};

interface IDragLinkProps {
  fromTask: TGanttTaskTypes;
  toTask: TGanttTaskTypes | null;
  from_start: boolean;
  to_start: boolean;
}

interface LinkEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  onDelete: () => void;
  linkTitle: string;
  lag: string | number;
  setModalData: React.Dispatch<React.SetStateAction<TLinkModalState>>;
}

type TLinkModalState = {
  open: boolean;
  linkId: string | number | null;
  lag: string | number;
  title: string;
};

interface ILinkEditModalRef {
  open: (id: string) => void;
  close: () => void;
}

interface IFullScreenGanttWrapperRef {
  handleExpand: () => void;
  handleCollapse: () => void;
}

type TImportMicroProFileProps = {
  isOpen: boolean;
  onClose: () => void;
  gantt: TGanttInstance;
};
