import dayjs from "dayjs";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import { Form, useNavigate, useSearchParams } from "@remix-run/react";
import * as Yup from "yup";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
//hooks
import { useIframe, useTranslation } from "~/hook";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addComplianceNotice } from "../../../redux/action/dashboardAction";
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import { defaultConfig } from "~/data";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
import {
  backendDateFormat,
  displayDateFormat,
  displayTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { getGlobalTypes } from "~/zustand/global/types/slice";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { RFIDetailsField } from "../../../utils/constasnts";
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import { getModuleAutoNumber } from "~/redux/action/commonAction";
import { useAppRFIDispatch, useAppRFISelector } from "../../../redux/store";
import { getCustomData } from "~/redux/action/customDataAction";
import { MultiSelectButtonField } from "~/shared/components/molecules/multiSelectButtonField";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import { sendMessageKeys } from "~/components/page/$url/data";

const AddComplianceNotice = ({
  addComplianceNoticeOpen,
  setAddComplianceNoticeOpen,
}: TAddComplianceNoticeProps) => {
  const navigate = useNavigate();
  const { _t } = useTranslation();
  const typesData = getGlobalTypes();
  const [searchParams] = useSearchParams();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id } = user || {};
  const { project_id, project_name }: GProject = getGProject();
  const { module_id, module_key }: GConfig = getGConfig();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const { date_format, is_custom_compliance_notice_id }: GSettings =
    getGSettings();
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
    } as IRequestCustomFieldForSidebar
  );

  // state
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [isIframe, setIsIframe] = useState<boolean>(true);
  const { parentPostMessage } = useIframe();
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [selectedContactId, setSelectedContactId] = useState<string>("");
  const [selectedAdditionalContactId, setSelectedAdditionalContactId] =
    useState<string>("");
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [projectAutoNumber, setProjectAutoNumber] = useState<string>("");
  const [projectDefId, setProjectDefId] = useState<string>("");
  const dispatch = useAppRFIDispatch();
  const [inputValues, setInputValues] =
    useState<Partial<IRFIDetails>>(RFIDetailsField);
  const iFrame = searchParams?.get("iframecall")?.trim();
  const pageOpenForm = searchParams?.get("pageOpenfrom")?.trim();
  const authToken = searchParams?.get("authorize_token")?.trim();
  const referralSourceData =
    typesData
      ?.filter((item) => item.type === "correspondence_compliance_notice")
      ?.map((item) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.type_id.toString(),
      })) || [];

  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  useEffect(() => {
    if (window && window?.location?.href) {
      const currentUrl = window.location.href;
      setIsIframe(currentUrl.includes("iframecall=1"));
    }
  }, []);

  const initValues = useMemo(
    (): TAddComplianceNotice => ({
      project_id: null,
      title: "",
      custom_correspondence_id: projectAutoNumber,
      correspondence_date: currentDateTime.format(date_format),
      correspondence_time: currentDateTime.format("HH:mm A"),
      user_to: "",
      notice_type: "",
      custom_fields: {},
    }),
    [currentDateTime, date_format, projectAutoNumber]
  );

  useEffect(() => {
    const action = searchParams.get("action")?.trim();
    const projectParam = searchParams?.get("project") ?? "";

    const fetchProjectDetails = async (project: string) => {
      try {
        const proResApi = (await getProjectDetails({
          start: 0,
          limit: 1,
          projects: project,
          need_all_projects: 0,
          global_call: true,
          is_completed: true,
          filter: { status: "0" },
        })) as IProjectDetailsRes;

        const queryPro = proResApi?.data?.projects[0];
        if (queryPro) {
          setFieldValue("project_id", queryPro.id);
          setProjectDefId(queryPro.id as string);
          setSelectedProject([
            {
              id: Number(queryPro.id),
              project_name: queryPro.project_name,
              prj_record_type:
                queryPro.prj_record_type === "opportunity"
                  ? "opportunity"
                  : "project",
            },
          ]);
        }
      } catch (error) {}
    };

    if (action === "new" && !!projectParam && projectParam !== "0") {
      setFieldValue("project_id", project_id);
      fetchProjectDetails(projectParam);
    } else if (
      project_id &&
      project_id !== "0" &&
      (projectParam == "" || projectParam != "0")
    ) {
      setFieldValue("project_id", project_id);
      setProjectDefId(project_id);
      setSelectedProject([
        {
          id: Number(project_id),
          project_name,
          prj_record_type: "project",
        },
      ]);
    }
  }, [
    project_id,
    searchParams,
    project_name,
    componentList,
    isNoAccessCustomField,
  ]);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const baseValidationSchema = {
    project_id: Yup.number().required("This field is required."),
    custom_correspondence_id:
      is_custom_compliance_notice_id === 0
        ? Yup.string()
        : Yup.string().required("This field is required."),
    title: Yup.string().required("This field is required."),
    correspondence_date: Yup.string().required("This field is required."),
    correspondence_time: Yup.string().required("This field is required."),
    notice_type: Yup.string().required("This field is required."),
    user_to: Yup.string().required("This field is required."),
  };

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...baseValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...baseValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    validateOnChange: false,
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      const formData = {
        type: 123,
        project_id: values.project_id || projectDefId,
        title: HTMLEntities.encode(values.title ?? "").trim(),
        custom_correspondence_id: HTMLEntities.encode(
          values.custom_correspondence_id ?? ""
        ).trim(),
        user_to: values.user_to,
        notice_type: values.notice_type,
        correspondence_date: backendDateFormat(
          values.correspondence_date,
          date_format
        ),
        correspondence_time: values.correspondence_time,
        custom_fields:
          formik?.values?.custom_fields && !isNoAccessCustomField
            ? formatCustomFieldForRequest(
                formik.values?.custom_fields,
                componentList,
                date_format
              ).custom_fields
            : undefined,
        access_to_custom_fields:
          componentList.length && !isNoAccessCustomField ? 1 : 0,
      };
      const resData = (await addComplianceNotice(
        getValuableObj(formData)
      )) as Partial<IResponse<IaddComplienceApiRes>>;
      if (resData?.success && window.ENV.PAGE_IS_IFRAME) {
        parentPostMessage(sendMessageKeys?.modal_change, { open: false });
        setSubmitting(false);
      } else if (resData?.success) {
        navigate(
          `${routes?.MANAGE_RFI_NOTICES.url}/${resData?.data?.correspondence_id}`
        );
      } else {
        notification.error({
          description: resData?.message || "Something went wrong!",
        });
      }
      setSubmitting(false);
    },
  });

  const {
    handleSubmit,
    setFieldValue,
    values,
    errors,
    setFieldError,
    isSubmitting,
  } = formik;

  useEffect(() => {
    if (Number(is_custom_compliance_notice_id) === 2 && projectAutoNumber) {
      formik.setValues({
        ...formik.values,
        custom_correspondence_id: projectAutoNumber,
      });
    } else if (Number(is_custom_compliance_notice_id) === 0) {
      formik.setValues({
        ...formik.values,
        custom_correspondence_id: "",
      });
    } else {
      formik.setValues({
        ...formik.values,
        custom_correspondence_id: "",
      });
    }
  }, [
    is_custom_compliance_notice_id,
    projectAutoNumber,
    addComplianceNoticeOpen,
  ]);

  const selectedCustomerList = useMemo((): TselectedContactSendMail[] => {
    return Array.isArray(inputValues.user_details)
      ? (inputValues.user_details as TselectedContactSendMail[])
      : [];
  }, [inputValues.user_details]);

  const handleContact = (data: TselectedContactSendMail[]) => {
    const userIdsStringTo = data
      ?.map((user) => {
        if (user?.contact_id == 0) {
          return user?.user_id?.toString();
        } else {
          return `${user?.user_id}|${user?.contact_id}`;
        }
      })
      ?.join(",");
    setFieldValue("user_to", userIdsStringTo);
    setInputValues({
      ...inputValues,
      user_details: data.map((record: any) => ({
        user_id: record.user_id,
        display_name: record.display_name,
        contact_id: record.contact_id,
        type_name: record.type_name,
        image: record.image,
        type_key: record.type_key,
        parent_type_key: record.parent_type_key,
      })),
    });
  };

  const getAutoNumber = async () => {
    // setNumberLoading(true);
    const autoNumberRes = (await getModuleAutoNumber({
      module_id: module_id,
      module_key: "compliance_notice",
    })) as GetModuleAutoNumberApiResponse;
    if (autoNumberRes.success) {
      const newId =
        autoNumberRes?.data?.last_primary_id != null &&
        autoNumberRes?.data?.last_primary_id != undefined &&
        autoNumberRes.data?.need_to_increment != null &&
        autoNumberRes.data?.need_to_increment != undefined &&
        autoNumberRes.data?.last_primary_id === 0
          ? (
              Number(autoNumberRes.data.last_primary_id) +
              Number(autoNumberRes.data?.need_to_increment)
            ).toString()
          : "";
      setProjectAutoNumber(String(newId));
    }
    if (!autoNumberRes?.success) {
      notification.error({
        description: autoNumberRes.message || "Something went wrong",
      });
    }
    // setNumberLoading(false);
  };

  useEffect(() => {
    // if (rfiDetail?.custom_correspondence_id) {
    dispatch(
      getCustomData({
        types: [188, 226],
        moduleId: module_id,
      })
    );
    getAutoNumber();
    // }
  }, []);

  const onChangeProject = (projects: IProject[]) => {
    setSelectedProject(projects);
    if (projects.length) {
      setFieldValue("project_id", projects[0].id);
    } else {
      setFieldValue("project_id", null);
    }
  };

  return (
    <>
      <Drawer
        open={addComplianceNoticeOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-clipboard-check"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t("Add Compliance Notice")}
            </Header>
          </div>
        }
        closeIcon={
          window.ENV.PAGE_IS_IFRAME
            ? null
            : !isIframe && (
            <CloseButton onClick={() => setAddComplianceNoticeOpen(false)} />
              )
        }
      >
        <Form method="post" noValidate onSubmit={handleSubmit}>
          <div className="py-4">
            <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
              <div className="grid gap-4">
                <SidebarCardBorder addGap={true}>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Project")}
                      name="project"
                      id="project_id"
                      labelPlacement="top"
                      required={true}
                      onClick={() => setIsSelectProjectOpen(true)}
                      value={
                        selectedProject.length
                          ? HTMLEntities.decode(
                              sanitizeString(selectedProject?.[0]?.project_name)
                            )
                          : ""
                      }
                      errorMessage={!values.project_id ? errors.project_id : ""}
                      addonBefore={
                        selectedProject.length ? (
                          <ProjectFieldRedirectionIcon
                            projectId={`${selectedProject?.[0]?.id || ""}`}
                          />
                        ) : null
                      }
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Title")}
                      name="title"
                      id="title"
                      labelPlacement="top"
                      isRequired={true}
                      value={values?.title || ""}
                      errorMessage={!values?.title ? errors?.title : ""}
                      autoComplete="off"
                      onChange={(e) => {
                        const value = e.target.value;
                        setFieldValue("title", value);
                      }}
                      onBlur={(e) =>
                        setFieldValue("title", e.target.value.trim())
                      }
                    />
                  </div>
                  <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                    <div className="w-full">
                      <DatePickerField
                        label={_t("Date")}
                        labelPlacement="top"
                        name="correspondence_date"
                        isRequired={true}
                        placeholder=""
                        format={date_format}
                        inputReadOnly={true}
                        value={displayDateFormat(
                          values?.correspondence_date?.trim(),
                          date_format
                        )}
                        onChange={(_, dateString) =>
                          setFieldValue(
                            "correspondence_date",
                            dateString.toString()
                          )
                        }
                        errorMessage={
                          isSubmit && !values?.correspondence_date
                            ? errors?.correspondence_date
                            : ""
                        }
                      />
                    </div>
                    <div className="w-full">
                      <TimePickerField
                        label={_t("Time")}
                        labelPlacement="top"
                        isRequired={true}
                        placeholder=""
                        format="hh:mm A"
                        value={displayTimeFormat(
                          values?.correspondence_time?.trim()
                        )}
                        onChange={(_, val) => {
                          setFieldValue("correspondence_time", val);
                        }}
                        errorMessage={
                          !values?.correspondence_time && isSubmit
                            ? errors?.correspondence_time
                            : ""
                        }
                      />
                    </div>
                  </div>
                  <div className="w-full">
                    <MultiSelectButtonField
                      label={_t("To")}
                      name="user_to"
                      id="user_to"
                      labelPlacement="top"
                      required={true}
                      value={inputValues.user_details?.map((detail) => ({
                        ...detail,
                        label: detail.display_name,
                        avatarProps: {
                          user: {
                            name: detail.display_name,
                            image: detail.image,
                          },
                        },
                        value: detail.user_id,
                        onRemoveClick: () => {
                          const updatedList = inputValues?.user_details?.filter(
                            (item) => item.user_id !== detail.user_id
                          );
                          setInputValues((prev) => ({
                            ...prev,
                            user_details: updatedList,
                          }));
                          const userId = updatedList?.map(
                            (user) => user.user_id
                          );
                          formik.setFieldValue("user_to", userId?.join(","));
                        },
                      }))}
                      errorMessage={
                        !values?.user_to && isSubmit ? errors?.user_to : ""
                      }
                      onClick={() => {
                        setIsOpenSelectAssignedTo(true);
                      }}
                      // avatarProps={
                      //   inputValues.user_details?.length == 1
                      //     ? {
                      //         user: {
                      //           name: HTMLEntities.decode(
                      //             sanitizeString(
                      //               inputValues.user_details[0]?.display_name
                      //             )
                      //           ),
                      //           image: inputValues.user_details[0]?.image,
                      //         },
                      //       }
                      //     : undefined
                      // }
                      addonBefore={
                        <div className="flex items-center gap-1">
                          {inputValues?.user_details?.length === 1 && (
                            <>
                              <ContactDetailsButton
                                onClick={(event) => {
                                  event.stopPropagation();
                                  setSelectedContactId(
                                    inputValues?.user_details
                                      ? inputValues?.user_details?.[0]?.user_id?.toString()
                                      : ""
                                  );
                                  setSelectedAdditionalContactId(
                                    inputValues?.user_details
                                      ? inputValues?.user_details?.[0]?.contact_id?.toString() ||
                                          ""
                                      : ""
                                  );
                                  setIsOpenContactDetails(true);
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                className="!w-5 !h-5"
                                directoryId={
                                  inputValues?.user_details[0]?.user_id?.toString() ||
                                  ""
                                }
                                directoryTypeKey={
                                  inputValues?.user_details[0]?.dir_type == 1
                                    ? "employee"
                                    : inputValues?.user_details[0]?.type_key ||
                                      ""
                                }
                              />
                            </>
                          )}
                          {inputValues?.user_details &&
                            inputValues?.user_details?.length > 1 && (
                              <AvatarIconPopover
                                placement="bottom"
                                assignedTo={
                                  inputValues?.user_details as IAssignedToUsers[]
                                }
                                setSelectedUserId={(data) => {
                                  setSelectedContactId(
                                    data?.id?.toString() || ""
                                  );
                                  setSelectedAdditionalContactId(
                                    data?.contactId?.toString() || ""
                                  );
                                }}
                                setIsOpenContactDetails={
                                  setIsOpenContactDetails
                                }
                                redirectionIcon
                              />
                            )}
                        </div>
                      }
                    />
                  </div>
                  <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                    <div className="w-full">
                      <InputField
                        label={_t("Item") + " #"}
                        disabled={is_custom_compliance_notice_id == 0}
                        name="custom_correspondence_id"
                        id="custom_correspondence_id"
                        labelPlacement="top"
                        isRequired={true}
                        value={
                          is_custom_compliance_notice_id == 0
                            ? " Save To View"
                            : values?.custom_correspondence_id ?? ""
                        }
                        maxLength={20}
                        errorMessage={
                          isSubmit
                            ? errors?.custom_correspondence_id
                            : undefined
                        }
                        autoComplete="off"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFieldValue("custom_correspondence_id", value);
                          if (value.trim().length === 0) {
                            setFieldError(
                              "custom_correspondence_id",
                              "This field is required."
                            );
                          } else {
                            setFieldError("custom_correspondence_id", "");
                          }
                        }}
                      />
                    </div>
                    <div className="w-full">
                      <SelectField
                        label={_t("Compliance Type")}
                        labelPlacement="top"
                        name="notice_type"
                        value={values?.notice_type ?? ""}
                        isRequired={true}
                        errorMessage={
                          !values?.notice_type ? errors?.notice_type : undefined
                        }
                        options={
                          referralSourceData.map((type) => ({
                            label: type?.label,
                            value: type?.value,
                          })) ?? []
                        }
                        onChange={(e) => {
                          setFieldValue("notice_type", e);
                        }}
                      />
                    </div>
                  </div>
                </SidebarCardBorder>

                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                />
              </div>
            </div>
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                buttonText={_t("Create Compliance Notice")}
                onClick={() => {
                  setIsSubmit(true);
                }}
                disabled={isSubmitting}
                loading={isSubmitting}
              />
            </div>
          </div>
        </Form>
      </Drawer>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={false}
          module_key={module_key}
        />
      )}
      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={false}
          setCustomer={(data) => {
            handleContact(
              data.length ? (data as TselectedContactSendMail[]) : []
            );
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={selectedCustomerList}
          groupCheckBox={true}
          projectId={values?.project_id as number}
        />
      )}
      {isOpenContactDetails && !!selectedContactId && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={selectedContactId}
          onCloseModal={() => {
            setSelectedContactId("");
            setSelectedAdditionalContactId("");
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          additional_contact_id={selectedAdditionalContactId}
        />
      )}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default AddComplianceNotice;
