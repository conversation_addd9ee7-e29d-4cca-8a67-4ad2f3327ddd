// Hook
import { useTranslation } from "~/hook";

// Antd
import { type RadioChangeEvent } from "antd";

// atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";

// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// Other
import { useFormik } from "formik";
import { useEffect, useMemo, useRef, useState } from "react";
import type { InputRef } from "antd";
import {
  filterOptionBySubstring,
  getItemTypeIcon,
  onKeyDownCurrency,
  onKeyDownNumber,
} from "~/shared/utils/helper/common";
import { defaultConfig } from "~/data";
import isEmpty from "lodash/isEmpty";
import { useParams } from "@remix-run/react";
import {
  addSTItemsApi,
  updateSTItemsApi,
} from "../../../../redux/action/serviceTicketServiceItemAction";
import {
  addSTItemDetail,
  updateSTItemDetail,
} from "../../../../redux/slices/serviceTicketServiceItemSlice";
import { useAppSTDispatch, useAppSTSelector } from "../../../../redux/store";
import { getGConfig, getGSettings, getGTypes } from "~/zustand";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { formAddUpServiceTicketItemSchema } from "./utils";
import * as Yup from "yup";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { Tooltip } from "~/shared/components/atoms/tooltip";

type ModifiedCustomerEmail = Omit<CustomerEmail, "type"> & {
  type: string; // New type for `type`
};

const ServiceTicketItem = ({
  serviceTicketItem,
  setServiceTicketItem,
  editForm,
  handleUpdateForm,
  isViewOnly = false,
  formData,
  setSelectedServiceItem,
  serviceItemsList,
}: IServiceTicketItemProps) => {
  const { _t } = useTranslation();
  const params: RouteParams = useParams();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const gSettings: GSettings = getGSettings();
  const gConfig: GConfig = getGConfig();
  const currentCurrency = gSettings?.currency_symbol ?? "$";
  const {
    default_equipment_markup_percent,
    default_labor_markup_percent,
    default_material_markup_percent,
    default_other_item_markup_percent,
    default_sub_contractor_markup_percent,
    default_undefined_markup_percent,
  } = gSettings;

  const getDefaultMarkupByItemType = (itemType: string): string => {
    const markupMap: { [key: string]: string } = {
      item_equipment: default_equipment_markup_percent,
      item_labour: default_labor_markup_percent,
      item_material: default_material_markup_percent,
      item_sub_contractor: default_sub_contractor_markup_percent,
    };

    return markupMap[itemType] || default_undefined_markup_percent;
  };
  const { initialDefaultState, requiredForm } =
    formAddUpServiceTicketItemSchema();
  const [submitAction, setSubmitAction] = useState<string>("");
  const dispatch = useAppSTDispatch();
  const itemType: GType[] = getGTypes();
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const unitCostRef = useRef<InputRef>(null);
  const [customerData, setCustomerData] = useState<Partial<ISTCustomerDetails>>(
    {}
  );

  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [isMuPercentFieldChanged, setIsMuPercentFieldChanged] =
    useState<boolean>(false);
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [customerOptions, setCustomerOptions] = useState<CustomerEmailTab[]>(
    []
  );
  const [confirmSaveData, setConfirmSaveData] = useState<{
    rid: number;
    message?: string;
  }>({ rid: 0 });
  const [isExistingLoading, setIsExistingLoading] = useState<boolean>(false);
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const { codeCostData }: IGetCostCodeList = useAppSTSelector(
    (state) => state.costCode
  );
  const { itemTypes }: IGetItemTypeList = useAppSTSelector(
    (state) => state.itemTypes
  );
  const { serviceItems }: ISTItemInitialState = useAppSTSelector(
    (state) => state.serviceItemsDetails
  );
  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );
  const [valueChangeFlag, setValueChangeFlag] = useState<boolean>(false);
  const [assignToLoading, setAssignToLoading] = useState<boolean>(false);
  let { getExistingUsersWithApi } = useExistingCustomers();

  const [inputValues, setInputValues] =
    useState<ISTItemDetails>(initialDefaultState);
  const [markup, setMarkup] = useState<string>("markup_percent");
  const [mainTotal, setMainTotal] = useState<string | number>("");

  const ST_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {currentCurrency}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];
  const initialValues: ISTItemDetails = useMemo(() => {
    if (editForm) {
      return {
        ...formData,
        add_item_to_database: formData?.item_on_database || 0,
        unit_cost: (Number(formData?.unit_cost || 0) / 100)
          ?.toFixed(2)
          .toString(),
        markup: formData?.markup?.toString()
          ? formData?.is_markup_percentage?.toString() === "0"
            ? (Number(formData?.markup || 0) / 100)?.toString()
            : Number(formData?.markup)
          : "",
        total: Number(formData?.total) / 100,
      };
    }
    // For new items, set default markup based on item type
    const defaultMarkup = getDefaultMarkupByItemType("");

    return {
      ...initialDefaultState,
      is_markup_percentage: 1,
      markup: defaultMarkup,
    };
  }, [editForm, formData, inputValues]);

  useEffect(() => {
    formik.setValues(initialValues);
  }, [formData, serviceTicketItem]);

  useEffect(() => {
    if (editForm && formData) {
      setInputValues(formData);
    }
  }, [formData]);
  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units ?? []);
  };

  useEffect(() => {
    getUnit();
  }, []);

  useEffect(() => {
    if (editForm) {
      if (formData?.is_markup_percentage?.toString() === "1") {
        setMarkup("markup_percent");
      } else {
        setMarkup("markup_dolar");
      }
    }
  }, [formData?.is_markup_percentage]);

  const formik = useFormik({
    initialValues,
    validationSchema: Yup.object().shape({
      subject: Yup.string().trim().required("This field is required."),
      item_type: Yup.string().required("This field is required."),
      //  unit cost this field is not required as per all module
      // unit_cost: Yup.number().required("This field is required."),
    }),
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      if (!editForm) {
        const STItem = {
          service_ticket_id: Number(params?.id),
          items: [
            {
              subject: values?.subject?.trim(),
              quantity: values?.quantity,
              unit: values?.unit,
              unit_cost: Number(values?.unit_cost) * 100,
              cost_code_id: values?.cost_code_id || "",
              cost_code_name: values?.cost_code_name || "",
              markup: values?.markup?.toString()
                ? values?.is_markup_percentage?.toString() === "0"
                  ? (Number(values?.markup) * 100)?.toString()
                  : values?.markup?.toString()
                : null,
              is_markup_percentage: values?.is_markup_percentage,
              total: Number(mainTotal) * 100,
              item_type: values?.item_type,
              assigned_to: values?.assigned_to,
              assigned_to_display_name: values?.assigned_to_display_name,
              assignee_name: values?.assigned_to_display_name,
              dir_type: values?.dir_type,
              assigned_to_contact_id: values?.assigned_to_contact_id,
              contractor_id: "",
              reference_item_id: confirmSaveData.rid,
              add_item_to_database:
                confirmSaveData.rid != 0 ? 0 : values?.add_item_to_database,
              contractor_contact_id: 0,
              description: values?.description,
              internal_notes: values?.internal_notes,
            },
          ],
        };
        // const formData = getValuableObj(STItem);
        const response = (await addSTItemsApi(
          STItem
        )) as IServiceTicketItemApiRes;
        if (response?.success) {
          const newItem = {
            ...STItem.items[0],
            ...response.data,
            item_on_database: STItem.items[0].add_item_to_database,
          };
          dispatch(addSTItemDetail(newItem));
          resetForm();
          if (submitAction === "save_n_close") {
            setServiceTicketItem(false);
          } else {
            setSelectedServiceItem?.(null);
            handleUpdateForm(false);
            setCustomerData({});
            setMarkup("markup_percent");
            setSubmitAction("");
            formik.setValues(initialValues);
          }
        } else {
          if (response.statusCode === 403) {
            notification.error({
              description: response.message,
            });
          }
          setServiceTicketItem(true);

          if (Number(response?.data?.reference_item_id || "") > 0) {
            setConfirmSaveData({
              rid: Number(response?.data?.reference_item_id || ""),
              message: response?.message,
            });
            return false;
          }
        }
      } else {
        const STItem = {
          service_ticket_id: Number(params?.id),
          is_single_item: 1,
          items: [
            {
              item_id: values.item_id,
              subject: values?.subject?.trim(),
              quantity: values?.quantity,
              unit: values?.unit,
              unit_cost: Number(values?.unit_cost) * 100,
              cost_code_id: values?.cost_code_id || "",
              cost_code_name: values?.cost_code_name || "",
              markup: values?.markup?.toString()
                ? values?.is_markup_percentage?.toString() === "0"
                  ? (Number(values?.markup) * 100)?.toString()
                  : values?.markup?.toString()
                : null,
              is_markup_percentage: values?.is_markup_percentage,
              total: Number(mainTotal) * 100,
              item_type: values?.item_type,
              dir_type: values?.dir_type,
              assigned_to: values?.assigned_to,
              assigned_to_display_name: values?.assigned_to_display_name,
              assigned_to_contact_id: values?.assigned_to_contact_id,
              assignee_name: values?.assigned_to_display_name,
              contractor_id: "",
              contractor_contact_id: 0,
              description: values?.description,
              internal_notes: values?.internal_notes,
            },
          ],
        };
        // const formData = getValuableObj(STItem);
        const response = (await updateSTItemsApi(
          STItem
        )) as IServiceTicketItemApiRes;
        if (response?.success === true) {
          const newItem = {
            ...STItem.items[0],
            ...response.data[0],
            item_on_database: formik.values?.add_item_to_database || 0,
          };
          if (submitAction === "save_n_close") {
            setServiceTicketItem(false);
          } else {
            setSelectedServiceItem?.(null);
            handleUpdateForm(false);
            setCustomerData({});
            setSubmitAction("");
            setMarkup("markup_percent");
            formik.setValues(() => initialDefaultState);
          }
          dispatch(updateSTItemDetail(newItem));
        } else {
          if (response.statusCode === 403) {
            notification.error({
              description: response.message,
            });
          }
        }
      }
      resetForm({
        values: {
          ...initialDefaultState,
        },
      });
      setConfirmSaveData({ rid: 0 });
      setSubmitting(false);
    },
  });
  const { setFieldValue, errors, setFieldError } = formik;
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const codeCostList: ISelectList[] = useMemo(() => {
    let costCodeOpts = codeCostData?.map((item: ICostCode) => {
      return {
        label: `${item.cost_code_name}${
          item.csi_code ? ` (${item.csi_code})` : ""
        }${item.is_deleted === 1 ? ` (Archived)` : ""}`,
        value: item.code_id.toString(),
      };
    });

    if (editForm && formData?.cost_code_is_deleted === 1) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          label: `${formData?.cost_code_name}${
            formData.csi_code ? ` (${formData.csi_code})` : ""
          } (Archived)`,
          value: formData.cost_code_id as string,
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData]);

  const handleSaveItem = (key: string) => {
    if (formik.isSubmitting) return;
    setSubmitAction(key);
    setValueChangeFlag(false);
  };

  const handleCustomer = (data: ISTCustomerDetails) => {
    if (
      data?.user_id != formik.values.assigned_to ||
      data?.contact_id != formik.values.assigned_to_contact_id
    ) {
      formik.setValues((prevValues) => ({
        ...prevValues,
        assigned_to: data?.user_id?.toString() || "",
        assigned_to_contact_id: data?.contact_id?.toString(),
        assigned_to_display_name:
          !!data.contact_id &&
          data.company_name &&
          !data.display_name?.includes(data.company_name)
            ? `${data.display_name} (${data.company_name})`
            : data?.display_name,
        assignee_name:
          !!data.contact_id &&
          data.company_name &&
          !data.display_name?.includes(data.company_name)
            ? `${data.display_name} (${data.company_name})`
            : data?.display_name,
        dir_type: data?.type_name || "",
        assigned_to_company_name: data.company_name || "",
        assigned_to_dir_type: data?.type || "",
        user_image: data?.image,
      }));
      setValueChangeFlag(true);
      setCustomerOptions([]);
      setIsOpenSelectCustomer(false);
      setCustomerData(
        data?.user_id
          ? {
              user_id: data?.user_id,
              display_name: data?.display_name,
              company_name: data?.company_name,
              type_name: data?.type_name,
              image: data?.image,
              contact_id: data?.contact_id,
            }
          : {}
      );
    }
  };

  const assignedTo = useMemo(() => {
    if (
      formik.values?.assigned_to !== 0 &&
      formik.values?.assigned_to?.toString() !== "" &&
      formik.values?.assigned_to &&
      !!formik.values?.assignee_name &&
      !!formik?.values?.assigned_to_display_name
    ) {
      const assigned_to: Partial<ModifiedCustomerEmail>[] = [
        {
          display_name: formik?.values?.assigned_to_display_name,
          user_id: Number(formik?.values?.assigned_to),
          type: formik?.values?.assigned_to_dir_type,
          contact_id: formik?.values?.assigned_to_contact_id,
          type_key: getDirectaryKeyById(
            Number(formik?.values?.assigned_to_dir_type) === 1
              ? 2
              : Number(formik?.values?.assigned_to_dir_type),
            gConfig
          ),
          image:
            formik?.values?.assigned_to_contact_id?.toString() === "0" ||
            formik?.values?.assigned_to_contact_id === null ||
            !formik?.values?.assigned_to_contact_id
              ? formik?.values?.user_image
              : "",
        },
      ];

      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [
    formik?.values?.assigned_to,
    formik?.values.assignee_name,
    formik?.values?.assigned_to_contact_id,
    formik?.values?.assigned_to_dir_type,
  ]);

  const filteredItems =
    itemTypes?.length > 0
      ? itemTypes
          .filter((obj) => obj?.type_id?.toString() !== "")
          ?.map((item) => ({
            ...item,
            label: (
              <div className="flex items-center gap-1.5">
                {item?.type_id ? (
                  <FontAwesomeIcon
                    icon={getItemTypeIcon({ type: item.type_id.toString() })}
                  />
                ) : (
                  ""
                )}
                {item?.name as string}
              </div>
            ),
            value: item?.type_id?.toString() as string,
          }))
      : itemType
          ?.filter((item: Partial<GType>) => item?.type === "company_items")
          ?.map((item: Partial<GType>) => ({
            ...item,
            mark_up: null,
            label: (
              <div className="flex items-center gap-1.5">
                {item?.type_id ? (
                  <FontAwesomeIcon
                    icon={getItemTypeIcon({ type: item.type_id.toString() })}
                  />
                ) : (
                  ""
                )}
                {item?.name as string}
              </div>
            ),
            value: item?.type_id as string,
          }));

  const reorderedItems = (() => {
    if (!Array.isArray(filteredItems)) return [];
    const items = [...filteredItems];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();

  useEffect(() => {
    if (
      formik?.values?.quantity !== "" &&
      formik?.values?.unit_cost !== "" &&
      formik?.values?.quantity &&
      formik?.values?.unit_cost
    ) {
      const total =
        Number(formik?.values?.quantity) * Number(formik?.values?.unit_cost);
      formik?.setFieldValue("total", total.toString());

      const markupAmount = Number(formik?.values?.markup_amount);
      const parsedTotal = Number(total);
      setMainTotal(
        isNaN(markupAmount) || isNaN(parsedTotal)
          ? ""
          : markupAmount + parsedTotal
      );
    } else {
      formik?.setFieldValue("total", "");
      setMainTotal("");
    }
    if (
      formik?.values?.total !== "" &&
      formik?.values.total &&
      formik?.values?.markup !== "" &&
      formik?.values?.markup
    ) {
      const parsedTotal = Number(formik?.values?.total);
      const parsedMarkup = Number(formik?.values?.markup);

      if (markup === "markup_percent") {
        const markupAmount = (parsedTotal * parsedMarkup) / 100;
        formik?.setFieldValue("markup_amount", markupAmount);

        setMainTotal(
          isNaN(markupAmount) || isNaN(parsedTotal)
            ? ""
            : markupAmount + parsedTotal
        );
      } else {
        const markupAmount = parsedMarkup;
        if (markupAmount === 0) {
          formik?.setFieldValue("markup_amount", "0");
          setMainTotal(isNaN(parsedTotal) ? "" : parsedTotal);
        } else {
          const markupPercentage =
            parsedTotal !== 0 ? (markupAmount * 100) / parsedTotal - 100 : 0;

          formik?.setFieldValue(
            "markup_amount",
            isNaN(markupPercentage) ? "" : markupPercentage.toFixed(2)
          );

          setMainTotal(isNaN(markupAmount) ? "" : markupAmount);
        }
      }
    } else {
      formik?.setFieldValue("markup_amount", "");

      const parsedTotal = Number(formik?.values?.total);
      setMainTotal(isNaN(parsedTotal) ? "" : parsedTotal);
    }
  }, [
    formik?.values?.quantity,
    formik?.values?.unit_cost,
    formik?.values?.total,
    formik?.values?.markup,
    formik?.values?.is_markup_percentage,
    markup,
  ]);

  useMemo(() => {
    if (
      formik?.values?.unit_cost !== "" &&
      !isEmpty(formik?.values?.unit_cost) &&
      formik?.values?.unit_cost !== undefined &&
      formik?.values?.unit !== "" &&
      !isEmpty(formik?.values?.unit) &&
      formik?.values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [editForm, formData, formik?.values]);

  const currentItemIndex = useMemo(() => {
    const curItemIndex = serviceItemsList?.findIndex(
      (i: ISTItemDetails) => i.item_id === formData?.item_id
    );

    return curItemIndex;
  }, [serviceItemsList, formData]);

  const nextPreUpdateItem = async () => {
    const STItem = {
      service_ticket_id: Number(params?.id),
      is_single_item: 1,
      items: [
        {
          item_id: formik?.values.item_id,
          subject: formik?.values?.subject?.trim(),
          quantity: formik?.values?.quantity,
          unit: formik?.values?.unit,
          unit_cost: Number(formik?.values?.unit_cost) * 100,
          cost_code_id: formik?.values?.cost_code_id || "",
          cost_code_name: formik?.values?.cost_code_name || "",
          markup: formik?.values?.markup?.toString()
            ? formik?.values?.is_markup_percentage === 0
              ? (Number(formik?.values?.markup) * 100)?.toString()
              : formik?.values?.markup?.toString()
            : null,
          is_markup_percentage: formik?.values?.is_markup_percentage,
          total: Number(mainTotal) * 100,
          item_type: formik?.values?.item_type,
          dir_type: formik?.values?.dir_type,
          assigned_to: formik?.values?.assigned_to,
          assigned_to_display_name: formik?.values?.assigned_to_display_name,
          assigned_to_contact_id: formik?.values?.assigned_to_contact_id,
          contractor_id: "",
          contractor_contact_id: 0,
          description: formik?.values?.description,
          internal_notes: formik?.values?.internal_notes,
        },
      ],
    };
    // const formData = getValuableObj(STItem);
    const response = (await updateSTItemsApi(
      STItem
    )) as IServiceTicketItemApiRes;
    if (response?.success === true) {
      setValueChangeFlag(false);
      const newItem = {
        ...STItem.items[0],
        ...response.data[0],
        item_on_database: STItem.items[0].add_item_to_database,
      };

      dispatch(updateSTItemDetail(newItem));
    }
  };
  const handlePrevItem = () => {
    if (isEmpty(errors)) {
      setSelectedServiceItem?.(serviceItemsList?.[currentItemIndex - 1]);
    }
    if (valueChangeFlag) {
      nextPreUpdateItem();
    }
  };

  const handleNextItem = () => {
    if (isEmpty(errors)) {
      setSelectedServiceItem?.(serviceItemsList?.[currentItemIndex + 1]);
    }
    if (valueChangeFlag) {
      nextPreUpdateItem();
    }
  };

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show =
      !editForm ||
      (formik?.values?.reference_item_id === 0 &&
        formik?.values?.reference_module_item_id === 0);

    const disable = editForm && formik?.values?.reference_item_id !== 0;

    return { show, disable };
  }, [editForm, formik?.values]);

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (Number(formData?.unit_cost) === 0) {
        setInputValues({
          ...formData,
          unit_cost: "",
          total: "0",
        });
      }
      if (
        formik?.values?.unit_cost &&
        !isEmpty(formik?.values?.unit_cost) &&
        formik?.values.unit &&
        !isEmpty(formik?.values.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  const getItemTypeName = useMemo(() => {
    const findselectedItemType = reorderedItems?.find(
      (item) => item.value?.toString() === formik?.values?.item_type?.toString()
    );
    if (!editForm && !isMuPercentFieldChanged) {
      formik?.setFieldValue(
        "markup",
        findselectedItemType?.key === "item_material"
          ? default_material_markup_percent
          : findselectedItemType?.key === "item_equipment"
          ? default_equipment_markup_percent
          : findselectedItemType?.key === "item_labour"
          ? default_labor_markup_percent
          : findselectedItemType?.key === "item_sub_contractor"
          ? default_sub_contractor_markup_percent
          : findselectedItemType?.key === "item_other"
          ? default_other_item_markup_percent
          : default_undefined_markup_percent
      );
    }
    return findselectedItemType?.name;
  }, [formik?.values?.item_type]);
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  const isSubjectEmpty =
    requiredForm?.isRequiredSubject && !formik.values.subject?.trim();
  return (
    <>
      <Drawer
        open={serviceTicketItem}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-ticket"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {`${editForm ? "" : _t("Add")} ${HTMLEntities.decode(
                  sanitizeString(gConfig?.module_singular_name)
                )} ${_t("Item")}`}
              </Header>
              {editForm && (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Previous")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-left"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={handlePrevItem}
                    disabled={currentItemIndex === 0}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={_t("Next")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-chevron-right"
                    className="item-pre-next-button disabled:bg-transparent"
                    onClick={handleNextItem}
                    disabled={currentItemIndex === serviceItemsList.length - 1}
                  />
                </div>
              )}
            </div>
          </div>
        }
        closeIcon={
          <CloseButton
            onClick={() => {
              setServiceTicketItem(false);
              handleUpdateForm(false);
            }}
          />
        }
      >
        <form className="py-4" onSubmit={formik.handleSubmit}>
          <div className="sidebar-body overflow-y-auto h-[calc(100vh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    name="subject"
                    labelPlacement="top"
                    disabled={isViewOnly}
                    id="subject"
                    value={HTMLEntities.decode(
                      sanitizeString(formik?.values?.subject)
                    )}
                    errorMessage={
                      formik.touched?.subject ? formik?.errors?.subject : ""
                    }
                    onChange={(e) => {
                      setValueChangeFlag(true);
                      setFieldValue("subject", e.target.value);
                      if (e.target.value?.trim()) {
                        delete errors.subject;
                      } else {
                        setFieldError("subject", _t("This field is required."));
                      }
                    }}
                    onBlur={formik.handleBlur}
                    isRequired={requiredForm?.isRequiredSubject}
                    autoComplete="off"
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      name="item_type"
                      labelPlacement="top"
                      disabled={
                        isViewOnly || itemTypeAndSaveItemToListField.disable
                      }
                      showSearch={false}
                      options={reorderedItems}
                      value={HTMLEntities.decode(
                        sanitizeString(formik?.values?.item_type?.toString())
                      )}
                      errorMessage={
                        submitAction?.trim() ? formik?.errors?.item_type : ""
                      }
                      isRequired={requiredForm?.isRequiredItemType}
                      onChange={async (value: string | string[]) => {
                        formik?.setFieldValue("item_type", value);
                        await formik?.setFieldTouched("item_type", false); // Reset touched state
                        setValueChangeFlag(true);

                        // https://app.clickup.com/t/86cxuf3r8
                        const itemType = reorderedItems?.find(
                          (i) => i.type_id?.toString() === value
                        );

                        formik?.setFieldValue(
                          "markup",
                          itemType?.mark_up || ""
                        );

                        // Trigger field validation for item_type
                        await formik?.validateField("item_type");
                      }}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Assigned To")}
                      name="assigned_to"
                      isDisabled={isViewOnly}
                      labelPlacement="top"
                      addonBefore={
                        <>
                          {formik?.values?.assigned_to &&
                            formik?.values?.assigned_to !== 0 && (
                              <ContactDetailsButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setIsOpenContactDetails(true);
                                }}
                              />
                            )}
                        </>
                      }
                      value={HTMLEntities.decode(
                        sanitizeString(
                          formik?.values?.assigned_to !== 0
                            ? formik?.values?.assigned_to_display_name
                            : ""
                        )
                      )}
                      errorMessage={
                        formik?.touched?.assigned_to
                          ? formik?.errors?.assigned_to
                          : ""
                      }
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(
                              formik.values?.assigned_to_display_name
                            )
                          ),
                          image:
                            formik?.values?.assigned_to_contact_id?.toString() ===
                              "0" ||
                            formik?.values?.assigned_to_contact_id === null ||
                            !formik?.values?.assigned_to_contact_id
                              ? formik?.values?.user_image
                              : "",
                        },
                        loading: assignToLoading,
                      }}
                      onClick={() => {
                        setCustomerOptions([
                          defaultConfig.employee_key,
                          "my_crew",
                          defaultConfig.customer_key,
                          defaultConfig.contractor_key,
                          defaultConfig.vendor_key,
                          defaultConfig.misc_contact_key,
                          "by_service",
                          "my_project",
                        ]);
                        setIsOpenSelectCustomer(true);
                      }}
                    />
                  </div>
                </div>

                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    allowClear={true}
                    showSearch
                    disabled={isViewOnly}
                    options={codeCostList}
                    value={
                      formik?.values?.cost_code_id
                        ? codeCostList.filter((item) => {
                            return (
                              formik?.values?.cost_code_id?.toString() ===
                              item?.value?.toString()
                            );
                          })
                        : []
                    }
                    errorMessage={
                      formik?.touched?.cost_code_id
                        ? formik?.errors?.cost_code_id
                        : ""
                    }
                    isRequired={requiredForm?.isRequiredCostCodeId}
                    onChange={(value: string | string[]) => {
                      setValueChangeFlag(true);
                      if (!Array.isArray(value)) {
                        const costCodeName = codeCostData.find(
                          (item) =>
                            item?.code_id?.toString() === value?.toString()
                        );
                        setValueChangeFlag(true);
                        formik.setValues((prevValues) => ({
                          ...prevValues,
                          cost_code_id: value,
                          cost_code_name: value
                            ? `${costCodeName?.cost_code_name} ${
                                !!costCodeName?.csi_code
                                  ? "(" + costCodeName?.csi_code + ")"
                                  : ""
                              }`
                            : "",
                        }));
                      }
                    }}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    onClear={() => {
                      formik?.setFieldValue("cost_code_id", "");
                      formik?.setFieldValue("cost_code_name", "");
                    }}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("QTY")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                        placeholder={_t("Item Quantity")}
                        disabled={isViewOnly}
                        labelPlacement="left"
                        defaultValue={
                          Number(formik?.values?.quantity) === 0
                            ? ""
                            : formik?.values?.quantity
                        }
                        value={
                          Number(formik?.values?.quantity) === 0
                            ? ""
                            : formik?.values?.quantity
                        }
                        formatter={(value, info) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setValueChangeFlag(true);
                          formik?.setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        errorMessage={
                          formik?.touched?.quantity
                            ? formik?.errors?.quantity
                            : ""
                        }
                        onPaste={handlePaste}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits: 6,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                      >
                        {!isViewOnly && (
                          <>
                            {showUnitInputs ? (
                              <div className="flex gap-2">
                                <div className="w-[calc(100%-70px)]">
                                  <InputNumberField
                                    // ref={unitCostRef}
                                    name="unit_cost"
                                    id="unit_cost"
                                    rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                    placeholder={_t("Item Unit Cost")}
                                    disabled={isViewOnly}
                                    labelPlacement="left"
                                    autoFocus={Boolean(
                                      formik?.values?.unit_cost &&
                                        !isEmpty(formik?.values?.unit_cost) &&
                                        formik?.values.unit &&
                                        !isEmpty(formik?.values.unit)
                                    )}
                                    defaultValue={
                                      Number(formik?.values.unit_cost) !== 0
                                        ? formik?.values.unit_cost
                                        : ""
                                    }
                                    value={
                                      Number(formik?.values.unit_cost) !== 0
                                        ? formik?.values.unit_cost
                                        : ""
                                    }
                                    onPaste={handlePaste}
                                    onChange={(value) => {
                                      setValueChangeFlag(true);
                                      formik?.setFieldValue(
                                        "unit_cost",
                                        value?.toString()
                                      );
                                    }}
                                    formatter={(value, info) => {
                                      return inputFormatter(value?.toString())
                                        .value;
                                    }}
                                    parser={(value) => {
                                      const inputValue = value
                                        ? unformatted(value.toString())
                                        : "";
                                      return inputValue;
                                    }}
                                    onKeyDown={(event) =>
                                      onKeyDownCurrency(event, {
                                        integerDigits: 10,
                                        decimalDigits: 2,
                                        unformatted,
                                        allowNegative: false,
                                        decimalSeparator:
                                          inputFormatter().decimal_separator,
                                      })
                                    }
                                    onBlur={handleFocusOut}
                                    errorMessage={
                                      submitAction?.trim()
                                        ? formik?.touched?.unit_cost
                                          ? formik?.errors?.unit_cost
                                          : ""
                                        : ""
                                    }
                                    isRequired={
                                      requiredForm?.isRequiredUnitCost
                                    }
                                  />
                                </div>
                                <div className="w-[62px]">
                                  {!window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                    <InputField
                                      className={`!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                        !showUnitInputs && "!hidden"
                                      }`}
                                      placeholder={_t("Unit")}
                                      labelPlacement="left"
                                      readOnly={false}
                                      maxLength={15}
                                      name="unit"
                                      type="text"
                                      onBlur={handleFocusOut}
                                      disabled={isViewOnly}
                                      value={formik?.values?.unit}
                                      errorMessage={
                                        formik?.touched?.unit
                                          ? formik?.errors?.unit
                                          : ""
                                      }
                                      onChange={(e) => {
                                        formik.handleChange(e);
                                        setValueChangeFlag(true);
                                      }}
                                      isRequired={requiredForm?.isRequiredUnit}
                                    />
                                  ) : (
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      labelPlacement="left"
                                      maxLength={15}
                                      value={formik?.values?.unit || null}
                                      iconView={true}
                                      disabled={isViewOnly}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitData.map((type) => ({
                                          label: type.name.toString(),
                                          value: type.name.toString(),
                                        })) ?? []
                                      }
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      onChange={(value) => {
                                        formik?.setFieldValue(
                                          "unit",
                                          value?.toString()
                                        );
                                        setValueChangeFlag(true);
                                      }}
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onInputKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          const value =
                                            e?.currentTarget?.value?.trim();
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitData?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewTypeName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        }
                                      }}
                                      onClear={() => {
                                        formik.handleChange("");
                                      }}
                                      errorMessage={
                                        formik?.touched?.unit
                                          ? formik?.errors?.unit
                                          : ""
                                      }
                                      onBlur={handleFocusOut}
                                    />
                                  )}
                                </div>
                              </div>
                            ) : (
                              <Typography
                                className="text-[#008000] cursor-pointer text-13 font-medium"
                                onClick={handleParagraphClick}
                                disabled={isViewOnly}
                              >
                                {
                                  formatter(
                                    formatAmount(
                                      Number(formik?.values?.unit_cost).toFixed(
                                        2
                                      )
                                    )
                                  ).value_with_symbol
                                }
                                /{formik?.values?.unit}
                              </Typography>
                            )}
                          </>
                        )}

                        {isViewOnly &&
                          (!isEmpty(formik?.values?.unit_cost) &&
                          formik?.values?.unit_cost !== 0.0 &&
                          formik?.values?.unit_cost !== "0.00" &&
                          !isEmpty(formik?.values?.unit) &&
                          !!formik?.values?.unit ? (
                            <Typography className="text-[#008000] font-medium text-13 cursor-no-drop">
                              {formik?.values?.unit_cost}/{formik?.values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <InputField
                                ref={unitCostRef}
                                className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Item Unit Cost")}
                                type="number"
                                name="unit_cost"
                                id="unit_cost"
                                maxLength={10}
                                onPaste={handlePaste}
                                disabled={isViewOnly}
                                value={formik?.values?.unit_cost}
                                onChange={() => {}}
                              />
                              <div className="sm:w-14 w-14">
                                <InputField
                                  className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  name="unit"
                                  id="unit"
                                  disabled={isViewOnly}
                                  value={formik?.values?.unit}
                                  onPaste={handlePaste}
                                  type="text"
                                  onChange={() => {}}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {formik?.values?.total === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  Number(formik?.values?.total || 0).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
                <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          value={markup ? markup : ""}
                          options={ST_ITEMS_LIST_TAB}
                          disabled={isViewOnly}
                          className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5 flex items-center"
                          activeclassName="active:bg-[#ffffff]"
                          onChange={(e: RadioChangeEvent) => {
                            setMarkup(e.target.value);
                            setValueChangeFlag(true);
                            formik?.setFieldValue("markup", "");
                            setIsMuPercentFieldChanged(true);
                            if (e.target.value === "markup_percent") {
                              formik?.setFieldValue("is_markup_percentage", 1);
                              const itemType = reorderedItems?.find(
                                (i) =>
                                  i.type_id?.toString() ===
                                  formik?.values?.item_type?.toString()
                              );

                              formik?.setFieldValue(
                                "markup",
                                itemType?.mark_up || ""
                              );
                            } else {
                              formik?.setFieldValue("is_markup_percentage", 0);
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${currentCurrency} -- Add the ${currentCurrency} amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <div className="sm:w-40 w-28">
                      <InputField
                        className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                        placeholder={
                          markup === "markup_percent"
                            ? _t("Item Markup") + " %"
                            : _t("Total Sales Price")
                        }
                        labelPlacement="left"
                        type="text"
                        disabled={isViewOnly}
                        name="markup"
                        value={formik?.values?.markup ?? ""}
                        errorMessage={
                          formik?.touched?.markup ? formik?.errors?.markup : ""
                        }
                        onPaste={handlePaste}
                        // onChange={handleMarkUp}
                        onChange={(e) => {
                          setValueChangeFlag(true);
                          const value = e.target.value;

                          formik?.setFieldValue(
                            "markup",
                            markup === "markup_percent"
                              ? value === ""
                                ? ""
                                : Number(value)
                              : value
                          );
                        }}
                        isRequired={requiredForm?.isRequiredMarkup}
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          const val = event.currentTarget.value;

                          if (event.key === "-") {
                            event.preventDefault();
                          } else if (event.key === "Enter") {
                            if (val === "") {
                              // Allow empty input
                              formik?.setFieldValue("markup", "");
                            } else if (Number(val) >= 100) {
                              formik?.setFieldValue("markup", 100);
                            } else {
                              formik?.setFieldValue("markup", Number(val));
                            }
                          } else {
                            return onKeyDownNumber(event, {
                              decimalDigits:
                                markup !== "markup_percent" ? 2 : 0,
                              integerDigits:
                                markup !== "markup_percent" ? 8 : 3,
                              allowNegative: false,
                            });
                          }
                        }}
                      />
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("Markup")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={true}
                      >
                        {markup === "markup_percent"
                          ? formik?.values?.markup_amount === ""
                            ? `${
                                formatter(formatAmount("0.00"))
                                  .value_with_symbol
                              }`
                            : `${
                                formatter(
                                  formatAmount(
                                    Number(
                                      formik?.values?.markup_amount || 0
                                    ).toFixed(2)
                                  )
                                ).value_with_symbol
                              }`
                          : formik?.values?.markup_amount === ""
                          ? "0%"
                          : `${formatAmount(
                              Number(
                                formik?.values?.markup_amount || 0
                              )?.toFixed(2)
                            )}%`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-equals"
                    />
                  </li>
                </ul>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Revenue")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {mainTotal === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(Number(mainTotal || 0).toFixed(2))
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    disabled={isViewOnly}
                    name="description"
                    value={HTMLEntities.decode(
                      sanitizeString(formik?.values?.description)
                    )}
                    errorMessage={
                      formik?.touched?.description
                        ? formik?.errors?.description
                        : ""
                    }
                    onChange={(e) => {
                      formik.handleChange(e);
                      setValueChangeFlag(true);
                    }}
                    required={requiredForm?.isRequiredDescription}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Note")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    name="internal_notes"
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(formik?.values?.internal_notes)
                    )}
                    errorMessage={
                      formik?.touched?.internal_notes
                        ? formik?.errors?.internal_notes
                        : ""
                    }
                    onChange={(e) => {
                      formik.handleChange(e);
                      setValueChangeFlag(true);
                    }}
                    required={requiredForm?.isRequiredInternalNotes}
                  />
                </div>

                {getItemTypeName && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={!!formik?.values?.add_item_to_database}
                    onChange={(event) => {
                      setValueChangeFlag(true);
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      formik?.setFieldValue("add_item_to_database", valueToSet);
                    }}
                    disabled={
                      formData?.add_item_to_database == 1 ||
                      formData?.item_on_database == 1
                        ? true
                        : isViewOnly
                    }
                  >
                    {_t(
                      `Save this item into my ${
                        getItemTypeName || ""
                      } Items list?`
                    )}
                  </CheckBox>
                )}
              </SidebarCardBorder>
            </div>
          </div>
          {/* {!editForm && ( */}
          <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
            <PrimaryButton
              type="primary"
              className="w-full justify-center primary-btn"
              htmlType="submit"
              disabled={
                isViewOnly ||
                (formik.isSubmitting && submitAction === "save_n_close")
              }
              buttonText={_t("Save & Close")}
              isLoading={formik.isSubmitting && submitAction === "save_n_close"}
              onClick={() => handleSaveItem("save_n_close")}
              name="save_n_close"
            />
            <PrimaryButton
              type="primary"
              className="w-full justify-center primary-btn"
              htmlType="submit"
              disabled={isViewOnly || formik.isSubmitting}
              buttonText={_t("Save & Add Another Item")}
              isLoading={
                formik.isSubmitting && submitAction === "save_n_add_another"
              }
              onClick={() => handleSaveItem("save_n_add_another")}
              name="save_n_add_another"
            />
          </div>
        </form>
      </Drawer>
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            setIsOpenSelectCustomer(false);
            setCustomerOptions([]);
          }}
          singleSelecte={true}
          options={customerOptions?.length > 0 ? customerOptions : []}
          setCustomer={(data) => {
            handleCustomer(data.length ? (data[0] as ISTCustomerDetails) : {});
          }}
          projectId={details?.project_id}
          selectedCustomer={assignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          activeTab={CFConfig.contractor_key}
        />
      )}
      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={formik?.values?.assigned_to || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isViewOnly}
          additional_contact_id={formik?.values?.assigned_to_contact_id} // as per PHP additional contact will not selected in assinged to field
        />
      )}

      {confirmSaveData.rid > 0 && (
        <ConfirmModal
          isOpen={confirmSaveData?.rid > 0}
          modaltitle={_t("This Item Already Exists")}
          description={confirmSaveData?.message || ""}
          modalIcon="fa-regular fa-triangle-exclamation"
          yesButtonLabel={_t("Use Existing")}
          noButtonLabel={_t("Rename")}
          onAccept={() => {
            setIsExistingLoading(true);
            formik.handleSubmit();
          }}
          isLoading={!!formik.isSubmitting && isExistingLoading}
          onDecline={() => {
            setSubmitAction("");
            setConfirmSaveData({ rid: 0 });
          }}
          onCloseModal={() => {
            setSubmitAction("");
            setConfirmSaveData({ rid: 0 });
          }}
        />
      )}

      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                formik?.setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default ServiceTicketItem;
