import { useMemo, useState } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { useParams } from "@remix-run/react";

// Hooks and helpers
import { useTranslation } from "~/hook";

// atoms
import { Spin } from "~/shared/components/atoms/spin";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AddButton } from "~/shared/components/molecules/addButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";

// Other
import { AddDeliveredItem } from "../modal/addDeliveredItem";
import ItemList from "./ItemList";

// Redux store, slice and action
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  deleteDLSubOnjonSite<PERSON><PERSON>,
  updateDLMaterialDelivery<PERSON><PERSON>,
} from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  addUpMaterialDeliveredDataAct,
  deletematerialDeliDataAct,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/materialsSlice";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { getGlobalUser } from "~/zustand/global/user/slice";

dayjs.extend(utc);
dayjs.extend(timezone);
const MaterialItemsDeliveredCard = ({
  isReadOnly,
}: {
  isReadOnly: boolean;
}) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const { id }: RouteParams = useParams();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id } = user || {};
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [addDeliveredItemOpen, setAddDeliveredItemOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [selectedMaterialDeliId, setSelectedMaterialDeliId] =
    useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);

  const { deliveredMaterials, isMaterialTabLoading }: IDLMaterialsInitialState =
    useAppDLSelector((state) => state.dailyLogMaterials);

  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  const handleSaveMaterial = async (data: ICommonOpItemDelivery) => {
    setIsLoading(true);
    const materialDeliFrm: IDLMaterialDelivery[] = [
      {
        itemName: data?.subject || "",
        deliveredBy: data?.supplier_company_name || "",
        quantity: "",
        quantityBackorderd: "",
        qtyUsed: "",
        deliveryTime: currentDateTime.format("HH:mm A"),
        notes: data?.quantity?.toString()
          ? `Original QTY Ordered = ${data?.quantity?.toString()}`
          : "",
        referenceItemId: data?.reference_item_id || 0,
        referencePoItemId: data?.item_id || 0,
      },
    ];

    const addSubsEmpRes = (await updateDLMaterialDeliveryApi({
      materialDelivery: materialDeliFrm,
      logId: Number(id),
    })) as IMaterialDeliAddUpRes;
    setIsLoading(false);

    if (addSubsEmpRes?.success) {
      dispatch(resetDash());
      setAddDeliveredItemOpen(false);
      dispatch(
        addUpMaterialDeliveredDataAct({
          data: addSubsEmpRes?.data?.material_item_delivered,
          action: "add",
        })
      );
    } else {
      notification.error({
        description: addSubsEmpRes?.message,
      });
    }
  };

  const handleDeleteMaterialDeli = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteDLSubOnjonSiteApi({
        dailyLogId: Number(id),
        itemId: [selectedMaterialDeliId || 0],
      })) as IDLPeopleDetailsUpdateApiRes;

      if (deleteRes?.success) {
        dispatch(resetDash());
        handleDeleteMaterial([selectedMaterialDeliId || 0]);
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const handleAddUpMaterialDeli = (data: IDeliveredMaterial) => {
    dispatch(addUpMaterialDeliveredDataAct({ data, action: "update" }));
  };

  const handleDeleteMaterial = (id: number[]) => {
    dispatch(deletematerialDeliDataAct({ item_id: id }));
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Material Items Delivered")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-box-circle-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#FAC4501a_0%,#F8A9001a_100%)]",
          id: "material_items_delivered_box_circle_check",
          colors: ["#FAC450", "#F8A900"],
        }}
        headerRightButton={
          <AddButton
            disabled={isReadOnly}
            onClick={() => {
              if (isReadOnly) {
                return;
              }
              setAddDeliveredItemOpen(true);
            }}
          >
            {_t("Item")}
          </AddButton>
        }
      >
        <div className="pt-2">
          {isMaterialTabLoading && (
            <Spin className="w-full h-[70px] flex items-center justify-center" />
          )}
          {!isMaterialTabLoading && (
            <div className="grid gap-3">
              {deliveredMaterials.length > 0 ? (
                <>
                  {deliveredMaterials?.map((itmes: IDeliveredMaterial) => (
                    <ItemList
                      key={itmes?.item_id}
                      isLoading={isLoading}
                      materialItem={itmes}
                      isReadOnly={isReadOnly}
                      onAddUpMaterialDeli={handleAddUpMaterialDeli}
                      onDeleteMaterialDeli={() => {
                        setSelectedMaterialDeliId(Number(itmes?.item_id));
                        setIsDeleteConfirmOpen(true);
                      }}
                    />
                  ))}
                </>
              ) : (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-material-items-delivered.svg`}
                />
              )}
            </div>
          )}
        </div>
      </CrudCommonCard>
      {addDeliveredItemOpen && (
        <AddDeliveredItem
          isOpen={addDeliveredItemOpen}
          isLoading={isLoading}
          itemType={"material"}
          onSavematerialItem={handleSaveMaterial}
          onCloseModal={() => {
            setAddDeliveredItemOpen(false);
          }}
        />
      )}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteMaterialDeli}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default MaterialItemsDeliveredCard;
