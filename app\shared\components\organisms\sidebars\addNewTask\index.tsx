import { useMemo, useState } from "react";
import { Form, useNavigate } from "@remix-run/react";

// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";
import { CheckBox } from "~/shared/components/atoms/checkBox";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";

import { displayTimeFormat } from "~/shared/utils/helper/defaultDateFormat";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";

// Zustand
import { useGlobalMenuModule } from "~/zustand/global/menuModules/slice";
import { getGlobalTypes } from "~/zustand/global/types/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getGlobalProject } from "~/zustand/global/config/slice";

// lodash
import debounce from "lodash/debounce";

// formik
import { useFormik } from "formik";
import * as Yup from "yup";

// Other
import { useTranslation } from "~/hook";
import { ADD_NEW_TASK_FORM_TABS, DURATION_OPTIONS } from "./constant";
import { useDateFormatter } from "~/hook/date-formatter-d";
import dayjs from "dayjs";
import { Number, sanitizeString } from "~/helpers/helper";
import { apiRoutes, routes } from "~/route-services/routes";
import { useExistingProjects } from "~/zustand";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { OpportunityFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/opportunityFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { getAssigneeValue } from "~/modules/projectManagement/pages/todo/utils/common";
import { AvatarIconPopover } from "../../avatarIconPopover";
import { faTrumpet } from "@fortawesome/pro-regular-svg-icons";

const AddNewTask = () => {
  const { modules, checkGlobalMenuModulePermissionByKey } =
    useGlobalMenuModule();
  const navigate = useNavigate();
  const globalTypes: IInitialGlobalData["type"]["types"] = getGlobalTypes();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const globalProject: IInitialGlobalData["config"]["global_project"] =
    getGlobalProject();
  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
    useExistingProjects("project,opportunity");
  const { _t } = useTranslation();

  const [loading, setLoading] = useState<boolean>(false);
  const [submitState, setSubmitState] = useState({
    submit: false,
    submitOpen: false,
  });
  const [isSubmitOpen, setIsSubmitOpen] = useState<boolean>(false);
  const [addNewTaskOpen, setAddNewTaskOpen] = useState<boolean>(false);
  const [currentForm, setCurrentForm] = useState<string>("");
  const [selectDirectory, setSelectDirectory] = useState<string>("");
  const [contactType, setContactType] = useState<string>("");
  const [contactId, setContactID] = useState<string>("");
  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [defaultProjectObj, setDefaultProjectObj] = useState<Partial<IProject>>(
    {}
  );
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const dateFormatter = useDateFormatter();

  const getData = (date: string): string | undefined => {
    if (typeof date === "string" && date.trim()) {
      if (date !== "00/00/0000") {
        return dateFormatter({
          date,
          dateFormat: CFConfig.luxon_date_format,
          format: "yyyy-MM-dd",
        });
      }
    }
    return dateFormatter();
  };

  const handleClose = () => {
    calendarFormik.resetForm();
    salesActivityFormik.resetForm();
    todoFormik.resetForm();
    setDefaultProjectObj({});
    setAddNewTaskOpen(false);
    setSubmitState({
      submit: false,
      submitOpen: false,
    });
    setLoading(false);
    setIsSubmitOpen(false);
    setCurrentForm(Object.keys(formOptions).find((a) => a) || "");
  };

  const defaultAssigned: Partial<CustomerSelectedData> = useMemo(() => {
    if (user) {
      const {
        user_id,
        display_name,
        first_name,
        last_name,
        company_name,
        company_display_name,
        type,
      } = user || {};
      return {
        user_id,
        display_name: `${first_name} ${last_name} (${company_name})`,
        first_name,
        last_name,
        company: company_name,
        company_name: company_display_name,
        type,
        type_key: "employee",
        type_name: "Employee",
      };
    }
    return {};
  }, [JSON.stringify(user || {})]);

  const selectProject = debounce(async (project_id: string) => {
    const projects = await getExistingProjectsWithApi(project_id);
    if (projects && projects.length) {
      const reponseProject = projects.shift() || {};
      if (Object.keys(reponseProject).length) {
        const project: Partial<IProject> = {
          ...(reponseProject || {}),
          billed_to_name: reponseProject?.billed_to_name || "",
          view_in_calendar: Number(reponseProject?.view_in_calendar),
          view_in_schedule: Number(reponseProject?.view_in_schedule),
          retention: reponseProject?.retention || "",
          allow_overbilling: Number(reponseProject?.allow_overbilling),
          is_assigned_project: Number(reponseProject?.is_assigned_project),
          start_date: reponseProject?.start_date || "",
          end_date: reponseProject?.end_date || "",
        };
        setDefaultProjectObj(project);
        salesActivityFormik.setValues((prev) => ({
          ...prev,
          project,
        }));
        todoFormik.setValues((prev) => ({
          ...prev,
          project,
        }));
        calendarFormik.setValues((prev) => ({
          ...prev,
          project,
        }));
      } else {
        setDefaultProjectObj({});
        salesActivityFormik.setValues((prev) => ({
          ...prev,
          project: {},
        }));
        todoFormik.setValues((prev) => ({
          ...prev,
          project: {},
        }));
        calendarFormik.setValues((prev) => ({
          ...prev,
          project: {},
        }));
      }
    }
  });

  const defaultProject: Partial<IProject> = useMemo(() => {
    if (globalProject) {
      const { project_id, project_name } = globalProject || {};
      const id = Number(project_id);
      if (id) {
        selectProject.cancel();
        selectProject(project_id);
        return { id, project_name };
      }
    }
    return {};
  }, [JSON.stringify(globalProject || {})]);
  const submitForm = async (form: IAddNewTaskFormParams) => {
    setLoading(true);
    try {
      const apiParams = await getWebWorkerApiParams({
        otherParams: {
          ...form,
          show_on_calendar:
            "show_on_calendar" in form ? form.show_on_calendar : 1,
          item_type: currentForm,
          status: CFConfig.default_lead_sales_status,
          global_project: defaultProject?.id || "",
        },
      });

      const { success, message, data } = (await webWorkerApi({
        url: apiRoutes.COMMON.add_lead_activity,
        method: "post",
        data: apiParams,
      })) as IAddLeadActivityApiResponse;
      if (success) {
        if (submitState.submitOpen) {
          navigate(`${routes.MANAGE_TODO.url}/${data?.todo_id}`);
          handleClose();
        } else {
          handleClose();
        }
      } else {
        setLoading(false);
        notification.error({
          description: message,
        });
      }
    } catch (error) {
      setLoading(false);
      notification.error({
        description: (error as Error).message,
      });
      return false;
    }
  };

  function getCurrentDataTime() {
    return [
      dateFormatter({
        format: CFConfig.luxon_date_format,
        zone: true,
      }),

      dateFormatter({ format: "hh:mm a", zone: true }),
    ];
  }

  const [date, time] = useMemo(() => {
    return getCurrentDataTime();
  }, [addNewTaskOpen]);

  const calendarFormik = useFormik({
    initialValues: {
      time: time,
      duration: "900",
      title: "",
      assigned: defaultAssigned,
      project: defaultProject,
      date: date,
      notes: "",
    },
    validationSchema: Yup.object({
      title: Yup.string()
        .trim()
        .when("currentForm", {
          is: (currentForm: string) =>
            currentForm !== ADD_NEW_TASK_FORM_TABS.sales_activity &&
            currentForm !== ADD_NEW_TASK_FORM_TABS.todo,
          then: (schema) =>
            schema
              .min(3, _t("Title should be at least 3 characters long."))
              .required(_t("This field is required.")),
          otherwise: (schema) => schema.required(_t("This field is required.")),
        }),
      date: Yup.string().required(_t("This field is required.")),
      time: Yup.string().required(_t("This field is required.")),
      project: Yup.object().shape({
        id: Yup.number().notOneOf([0], "Select any project."),
      }),
      assigned: Yup.object().shape({
        user_id: Yup.number()
          .required("This field is required.")
          .notOneOf([0], "Select any agency."),
      }),
    }),
    onSubmit: async (values) => {
      await submitForm({
        time: values.time,
        duration: values.duration,
        title: values.title,
        assigned_to: values.assigned?.user_id?.toString() || "",
        project_id: values.project?.id?.toString() || "",
        date: getData(values.date) || "",
        notes: values.notes,
      });
    },
    enableReinitialize: true,
  });

  const salesActivityFormik = useFormik({
    initialValues: {
      time: time,
      type: "252",
      directory: undefined as Partial<CustomerSelectedData> | undefined,
      title: "",
      assigned: defaultAssigned,
      project: defaultProject,
      date: date,
      notes: "",
      show_on_calendar: 1,
    },
    validationSchema: Yup.object({
      title: Yup.string().trim().required(_t("This field is required.")),
      date: Yup.string().required(_t("This field is required.")),
      time: Yup.string().required(_t("This field is required.")),
      project: Yup.object().shape({
        id: Yup.number().notOneOf([0], "Select any project."),
      }),
      assigned: Yup.object().shape({
        user_id: Yup.number()
          .required("This field is required.")
          .notOneOf([0], "Select any agency."),
      }),
      directory: Yup.object().shape({
        user_id: Yup.number()
          .required("This field is required.")
          .notOneOf([0], "Select any agency."),
      }),
    }),
    onSubmit: async (values) => {
      await submitForm({
        time: values.time,
        type: values.type,
        directory_id: values.directory?.user_id?.toString() || "",
        title: values.title,
        assigned_to: values.assigned?.user_id?.toString() || "",
        project_id: values.project?.id?.toString() || "",
        date: getData(values.date) || "",
        notes: values.notes,
        show_on_calendar: values.show_on_calendar,
      });
    },
    enableReinitialize: true,
  });

  const todoFormik = useFormik({
    initialValues: {
      due_time: "",
      title: "",
      assigned: defaultAssigned,
      project: defaultProject,
      date: date,
      notes: "",
    },
    validationSchema: Yup.object({
      title: Yup.string().trim().required(_t("This field is required.")),
      date:
        ADD_NEW_TASK_FORM_TABS.todo === currentForm
          ? Yup.string()
          : Yup.string().required(_t("This field is required.")),
      project: Yup.object().shape({
        id: Yup.number().notOneOf([0], "Select any project."),
      }),
    }),
    onSubmit: async (values) => {
      await submitForm({
        due_time: values.due_time,
        title: values.title,
        assigned_to: Array.isArray(values?.assigned)
          ? values.assigned.map((assignee) => assignee?.user_id).join(",")
          : !!values?.assigned?.user_id?.toString()
          ? values.assigned.user_id.toString()
          : "",
        project_id: values.project?.id?.toString() || "",
        date: !!values?.date ? getData(values.date) || "" : "",
        notes: values.notes,
      });
    },
    enableReinitialize: true,
  });

  const {
    values: formikValues,
    errors: formikErrors,
    handleSubmit,
    setFieldValue,
    handleChange,
    setErrors,
    setValues,
  } = ADD_NEW_TASK_FORM_TABS.sales_activity === currentForm
    ? salesActivityFormik
    : ADD_NEW_TASK_FORM_TABS.todo === currentForm
    ? todoFormik
    : calendarFormik;

  const formOptions = useMemo(() => {
    const hasModulePermission = (permission: TModuleAccessStatus) => {
      return ["full_access", "own_data_access"].includes(permission);
    };
    const permissions = {
      corporate_calendar_module: checkGlobalMenuModulePermissionByKey(
        CFConfig.corporate_calendar_module
      ),
      directory_module: checkGlobalMenuModulePermissionByKey(
        CFConfig.directory_module
      ),
      leads_module: checkGlobalMenuModulePermissionByKey(CFConfig.leads_module),
      todo_module: checkGlobalMenuModulePermissionByKey(CFConfig.todo_module),
    };
    let options = {};

    if (hasModulePermission(permissions.corporate_calendar_module)) {
      options = {
        ...options,
        [ADD_NEW_TASK_FORM_TABS.calendar]: {
          value: ADD_NEW_TASK_FORM_TABS.calendar,
          id: "calendar_item_task",
          name: "calendar_item_task",
          label: "Calendar Entry",
        },
      };
    }
    if (
      hasModulePermission(permissions.directory_module) &&
      hasModulePermission(permissions.leads_module)
    ) {
      options = {
        ...options,
        [ADD_NEW_TASK_FORM_TABS.sales_activity]: {
          value: ADD_NEW_TASK_FORM_TABS.sales_activity,
          id: "sales_activity_task",
          className: "!mr-0",
          name: "sales_activity_task",
          label: (
            <>
              {_t("Sales Activity")}{" "}
              <Typography>{_t("(Added to Contact's record)")}</Typography>
            </>
          ),
        },
      };
    }
    if (hasModulePermission(permissions.todo_module)) {
      options = {
        ...options,
        [ADD_NEW_TASK_FORM_TABS.todo]: {
          value: ADD_NEW_TASK_FORM_TABS.todo,
          id: "task_item_type",
          name: "task_item_type",
          label: "To-Do",
        },
      };
    }

    setCurrentForm(Object.keys(options).shift() || "");
    return options;
  }, [JSON.stringify(modules)]);

  const selectTask = useMemo(() => {
    let tempSelectTask = [
      {
        label: "Select Task Type",
        value: "0",
      },
    ];
    if (globalTypes) {
      tempSelectTask = [
        ...tempSelectTask,
        ...(globalTypes
          .filter((type) => type?.type === CFConfig.lead_activity_type)
          .map((type) => ({
            label: type.name,
            value: type.type_id,
          })) ?? []),
      ];
    }
    return tempSelectTask;
  }, [JSON.stringify(globalTypes)]);

  const todoOptions: CustomerTabs[] = [
    CFConfig.employee_key,
    "my_crew",
    CFConfig.customer_key,
    CFConfig.misc_contact_key,
    CFConfig.contractor_key,
    CFConfig.vendor_key,
    "by_service",
    "my_project",
  ];

  const options: CustomerTabs[] = [
    CFConfig.employee_key,
    "my_crew",
    "my_project",
  ];

  const directoryOptions: CustomerTabs[] = [
    CFConfig.customer_key,
    CFConfig.lead_key,
    "my_project",
  ];

  const selectedDirectory =
    selectDirectory.trim() === "directory_id" &&
    "directory" in formikValues &&
    formikValues.directory
      ? formikValues.directory
      : selectDirectory.trim() === "assigned_id"
      ? formikValues.assigned
      : undefined;

  const AssignedField = () => {
    return (
      "assigned" in formikValues && (
        <div className="w-full">
          <ButtonField
            value={
              Array.isArray(formikValues?.assigned)
                ? getAssigneeValue(formikValues?.assigned || [])
                : `${HTMLEntities.decode(
                    sanitizeString(formikValues.assigned?.display_name) || ""
                  )}`
            }
            label={_t("Assigned To")}
            required={currentForm !== ADD_NEW_TASK_FORM_TABS.todo}
            name="assigned_id"
            labelPlacement="top"
            onClick={() => setSelectDirectory("assigned_id")}
            avatarProps={
              formikValues?.assigned && !Array.isArray(formikValues.assigned)
                ? {
                    user: {
                      name: HTMLEntities.decode(
                        sanitizeString(formikValues.assigned.display_name)
                      ),
                      image: formikValues.assigned.image,
                    },
                  }
                : undefined
            }
            addonBefore={
              !!formikValues?.assigned && (
                <div className="flex items-center gap-1">
                  {!!formikValues?.assigned?.user_id ? (
                    <div className="flex gap-1 items-center">
                      <ContactDetailsButton
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsContactDetails(true);
                          setContactID(
                            formikValues?.assigned?.user_id
                              ? formikValues?.assigned?.user_id?.toString()
                              : ""
                          );
                        }}
                      />
                      <DirectoryFieldRedirectionIcon
                        className="!w-5 !h-5"
                        directoryId={
                          formikValues?.assigned?.user_id.toString() || ""
                        }
                        directoryTypeKey={
                          formikValues?.assigned?.type_key !== "contact"
                            ? formikValues?.assigned?.type_key || ""
                            : formikValues?.assigned?.parent_type_key || ""
                        }
                      />
                    </div>
                  ) : (
                    <AvatarIconPopover
                      placement="bottom"
                      redirectionIcon={true}
                      assignedTo={formikValues.assigned as IAssignedToUsers[]}
                      setSelectedUserId={(data) => {
                        setContactID(data?.id?.toString());
                      }}
                      setIsOpenContactDetails={setIsContactDetails}
                    />
                  )}
                </div>
              )
            }
            errorMessage={
              submitState.submit && "assigned" in formikErrors
                ? formikErrors.assigned?.user_id || ""
                : ""
            }
          />
        </div>
      )
    );
  };

  const ProjectField = () => {
    return (
      "project" in formikValues && (
        <div className="w-full">
          <ButtonField
            value={HTMLEntities.decode(
              sanitizeString(formikValues.project?.project_name) || ""
            )}
            label={
              formikValues.project.prj_record_type === "opportunity"
                ? _t("Opportunity")
                : _t("Project")
            }
            name="project_id"
            labelPlacement="top"
            onClick={() => setIsSelectProOpen(true)}
            errorMessage={
              submitState.submit && "project" in formikErrors
                ? formikErrors.project?.id || ""
                : ""
            }
            addonBefore={
              formikValues.project?.id && Number(formikValues.project.id) ? (
                formikValues.project.prj_record_type == "opportunity" ? (
                  <OpportunityFieldRedirectionIcon
                    projectId={formikValues.project.id.toString() || ""}
                  />
                ) : (
                  <ProjectFieldRedirectionIcon
                    projectId={formikValues.project.id.toString()}
                  />
                )
              ) : null
            }
          />
        </div>
      )
    );
  };

  return (
    <>
      <ButtonWithTooltip
        tooltipPlacement="top"
        tooltipTitle={_t("Add a New Task")}
        icon="fa-regular fa-calendar-plus"
        className={`transition-all duration-300 ease-in-out !w-[30px] !h-[30px] !bg-transparent hover:!bg-[#384a66] dark:hover:!bg-dark-900 focus:!bg-primary-900 focus:!text-white ${
          addNewTaskOpen ? "!bg-[#384a66] dark:!bg-dark-900" : ""
        }`}
        iconClassName={`text-base flex w-[18px] h-[18px] !text-[#878a92] dark:!text-[#dcdcdd] group-hover/buttonHover:!text-white ${
          addNewTaskOpen ? "!text-white" : ""
        }`}
        onClick={() => setAddNewTaskOpen(!addNewTaskOpen)}
      />
      {addNewTaskOpen && (
        <Drawer
          open={addNewTaskOpen}
          className="!h-dvh"
          maskClosable={true}
          width="700px"
          rootClassName="drawer-open h-dvh"
          classNames={{
            body: "!p-0 !h-[calc(100dvh-52px)]",
          }}
          title={
            <div className="flex items-center">
              <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
                <FontAwesomeIcon
                  className="w-4 h-4 text-primary-900"
                  icon="fa-regular fa-calendar-plus"
                />
              </div>
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t(`Create Task`)}
              </Header>
            </div>
          }
          closeIcon={
            <CloseButton
              onClick={() => {
                handleClose();
              }}
            />
          }
        >
          <Form
            onSubmit={handleSubmit}
            method="post"
            noValidate
            className="py-4"
          >
            <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
              <SidebarCardBorder addGap={true}>
                <RadioGroupList
                  options={Object.values(formOptions)}
                  view="row"
                  defaultValue={currentForm}
                  onChange={(e) => {
                    setCurrentForm(e.target.value);
                    const [date, time] = getCurrentDataTime();
                    calendarFormik.setValues((prev) => ({
                      ...prev,
                      time: time,
                      date: date,
                    }));
                    salesActivityFormik.setValues((prev) => ({
                      ...prev,
                      time: time,
                      date: date,
                    }));
                    todoFormik.setValues((prev) => ({ ...prev, date: date }));
                    setErrors({});
                  }}
                />
                <div className="flex flex-col gap-5">
                  {"type" in formikValues && (
                    <div className="w-full">
                      <SelectField
                        label={_t("Task Type")}
                        name="type"
                        labelPlacement="top"
                        options={selectTask}
                        value={formikValues.type}
                        showSearch
                        filterOption={(input, option) =>
                          filterOptionBySubstring(
                            input,
                            option?.label as string
                          )
                        }
                        onChange={(value) => {
                          setFieldValue("type", value);
                        }}
                      />
                    </div>
                  )}
                  {"title" in formikValues && (
                    <div className="w-full">
                      <InputField
                        label={
                          ADD_NEW_TASK_FORM_TABS.sales_activity === currentForm
                            ? _t("Subject")
                            : ADD_NEW_TASK_FORM_TABS.todo === currentForm
                            ? _t("Task Name")
                            : _t("Title")
                        }
                        size="middle"
                        isRequired
                        name="title"
                        errorMessage={
                          submitState.submit && "title" in formikErrors
                            ? formikErrors.title
                            : ""
                        }
                        labelClass="dark:text-white/90"
                        value={formikValues.title}
                        onChange={handleChange}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                          }
                        }}
                      />
                    </div>
                  )}
                  {currentForm === ADD_NEW_TASK_FORM_TABS.todo && (
                    <>
                      <ProjectField />
                      <AssignedField />
                    </>
                  )}
                  {"date" in formikValues &&
                    ("time" in formikValues || "due_time" in formikValues) && (
                      <div className="flex md:flex-row flex-col md:gap-5 gap-5">
                        <div className="w-full">
                          <DatePickerField
                            label={
                              ADD_NEW_TASK_FORM_TABS.todo === currentForm
                                ? _t("Due Date")
                                : _t("Date")
                            }
                            placeholder=""
                            labelPlacement="top"
                            name="date"
                            isRequired={
                              ADD_NEW_TASK_FORM_TABS.todo === currentForm
                                ? false
                                : true
                            }
                            value={
                              formikValues.date
                                ? dayjs(
                                    dateFormatter({
                                      date: formikValues.date,
                                      format: CFConfig.luxon_date_format,
                                      zone: true,
                                    }),
                                    CFConfig.day_js_date_format
                                  )
                                : undefined
                            }
                            inputReadOnly={true}
                            format={CFConfig.day_js_date_format}
                            onChange={(_, dateString) => {
                              setFieldValue("date", dateString);
                            }}
                            errorMessage={
                              ADD_NEW_TASK_FORM_TABS.todo === currentForm
                                ? ""
                                : submitState.submit && "date" in formikErrors
                                ? formikErrors.date
                                : ""
                            }
                          />
                        </div>
                        <div className="w-full">
                          <TimePickerField
                            label={
                              ADD_NEW_TASK_FORM_TABS.todo === currentForm
                                ? _t("Due Time")
                                : _t("Time")
                            }
                            use12Hours
                            name={
                              "due_time" in formikValues ? "due_time" : "time"
                            }
                            isRequired={
                              "due_time" in formikValues ? false : true
                            }
                            placeholder=""
                            labelPlacement="top"
                            format="hh:mm A"
                            value={displayTimeFormat(
                              "due_time" in formikValues
                                ? formikValues.due_time?.trim()
                                : formikValues.time?.trim()
                            )}
                            onChange={(_, val) => {
                              setFieldValue(
                                "due_time" in formikValues
                                  ? "due_time"
                                  : "time",
                                val
                              );
                            }}
                            errorMessage={
                              "time" in formikErrors && submitState.submit
                                ? formikErrors.time
                                : ""
                            }
                          />
                        </div>
                        {"duration" in formikValues && (
                          <div className="w-full">
                            <SelectField
                              label={_t("Task Type")}
                              name="type"
                              labelPlacement="top"
                              options={DURATION_OPTIONS}
                              value={formikValues.duration}
                              showSearch
                              filterOption={(input, option) =>
                                filterOptionBySubstring(
                                  input,
                                  option?.label as string
                                )
                              }
                              onChange={(value) => {
                                setFieldValue("duration", value);
                              }}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  {"directory" in formikValues && (
                    <div className="w-full">
                      <ButtonField
                        value={HTMLEntities.decode(
                          sanitizeString(
                            formikValues.directory?.display_name
                          ) || ""
                        )}
                        label={_t("Contact")}
                        name="directory_id"
                        labelPlacement="top"
                        required={true}
                        onClick={() => setSelectDirectory("directory_id")}
                        addonBefore={
                          formikValues.directory?.user_id ? (
                            <div className="flex gap-1 items-center">
                              <ContactDetailsButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if ("directory" in formikValues) {
                                    setIsContactDetails(true);
                                    setContactID(
                                      formikValues.directory?.user_id?.toString() ||
                                        ""
                                    );
                                  }
                                }}
                              />
                              {formikValues.directory?.user_id && (
                                <DirectoryFieldRedirectionIcon
                                  className="!w-5 !h-5"
                                  directoryId={
                                    formikValues?.directory?.user_id.toString() ||
                                    ""
                                  }
                                  directoryTypeKey={
                                    formikValues?.directory?.type_key !==
                                    "contact"
                                      ? formikValues?.directory?.type_key || ""
                                      : formikValues?.directory
                                          ?.parent_type_key || ""
                                  }
                                />
                              )}
                            </div>
                          ) : null
                        }
                        avatarProps={
                          formikValues.directory?.display_name
                            ? {
                                user: {
                                  name: HTMLEntities.decode(
                                    sanitizeString(
                                      formikValues.directory?.display_name
                                    )
                                  ),
                                  image: formikValues?.directory?.image,
                                },
                              }
                            : undefined
                        }
                        errorMessage={
                          submitState.submit && !contactType
                            ? "This field is required."
                            : ""
                        }
                      />
                    </div>
                  )}
                  {currentForm !== ADD_NEW_TASK_FORM_TABS.todo && (
                    <>
                      <ProjectField />
                      <AssignedField />
                    </>
                  )}
                  {"notes" in formikValues && (
                    <div className="w-full">
                      <TextAreaField
                        label={
                          ADD_NEW_TASK_FORM_TABS.sales_activity === currentForm
                            ? _t("Notes")
                            : _t("Description")
                        }
                        name="notes"
                        labelClass="dark:text-white/90"
                        value={formikValues.notes}
                        onChange={handleChange}
                      />
                    </div>
                  )}

                  {"show_on_calendar" in formikValues && (
                    <div className="w-full">
                      <CheckBox
                        className="gap-1.5 text-primary-900 dark:text-white/90"
                        children={_t("Show on Calendar")}
                        checked={Boolean(formikValues.show_on_calendar)}
                        onChange={(event) =>
                          setFieldValue(
                            "show_on_calendar",
                            Number(event.target.checked)
                          )
                        }
                      />
                    </div>
                  )}
                </div>
              </SidebarCardBorder>
            </div>
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4 gap-4">
              <PrimaryButton
                htmlType="submit"
                buttonText={_t("Save")}
                disabled={loading}
                onClick={() =>
                  setSubmitState({ submit: true, submitOpen: false })
                }
              />
              {ADD_NEW_TASK_FORM_TABS.todo === currentForm ? (
                <PrimaryButton
                  htmlType="submit"
                  disabled={loading}
                  buttonText={_t("Save & Open")}
                  onClick={() =>
                    setSubmitState({ submit: true, submitOpen: true })
                  }
                />
              ) : (
                ""
              )}
            </div>
          </Form>
        </Drawer>
      )}
      {selectDirectory.trim() && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setSelectDirectory("");
          }}
          singleSelecte={
            ADD_NEW_TASK_FORM_TABS.todo === currentForm ? false : true
          }
          openSelectCustomerSidebar={Boolean(selectDirectory.trim())}
          options={
            selectDirectory.trim() === "directory_id"
              ? directoryOptions
              : currentForm === ADD_NEW_TASK_FORM_TABS.todo
              ? todoOptions
              : options
          }
          setCustomer={(data) => {
            let selectedCustomer:
              | Partial<CustomerSelectedData>
              | TselectedContactSendMail[];
            if (ADD_NEW_TASK_FORM_TABS.todo === currentForm) {
              if (Array.isArray(data) && data?.length > 1) {
                selectedCustomer =
                  currentForm === ADD_NEW_TASK_FORM_TABS.todo
                    ? (data as TselectedContactSendMail[])
                    : (data[0] as Partial<CustomerSelectedData>);
              } else {
                selectedCustomer = data[0] as Partial<CustomerSelectedData>;
              }
            } else {
              selectedCustomer = data[0] as Partial<CustomerSelectedData>;
            }
            setFieldValue(
              selectDirectory.trim() === "directory_id"
                ? "directory"
                : "assigned",
              !!selectedCustomer ? selectedCustomer : ""
            );
            if (selectDirectory.trim() === "directory_id") {
              setContactType(selectedCustomer?.user_id?.toString() ?? "");
            }
            if (selectDirectory.trim() === "directory_id") {
              setFieldValue("project", {});
            }
          }}
          selectedCustomer={
            selectedDirectory
              ? currentForm === ADD_NEW_TASK_FORM_TABS.todo &&
                Array.isArray(selectedDirectory)
                ? (selectedDirectory ?? [])?.map((assignee) => ({
                    display_name: assignee?.display_name,
                    type_name: assignee?.type_name,
                    user_id: assignee?.user_id,
                    image: assignee?.image,
                    type_key: assignee?.type_key,
                  }))
                : [selectedDirectory]
              : []
          }
          groupCheckBox={false}
          projectId={Number(formikValues.project?.id) || undefined}
          additionalContactDetails={0}
        />
      )}
      {isContactDetails && contactId?.trim() && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          contactId={Number(contactId) || undefined}
          onCloseModal={() => {
            setIsContactDetails(false);
          }}
          additional_contact_id={0} // as we don't have additional contact here same as php
        />
      )}
      {isSelectProOpen && (
        <SelectProject
          isShowProjectType={true}
          isSingleSelect={true}
          searchPlcaeHolder={"Search All Project or Opportunity"}
          open={isSelectProOpen}
          setOpen={setIsSelectProOpen}
          selectedProjects={
            formikValues.project.id ? [formikValues.project as IProject] : []
          }
          onProjectSelected={(projects: IProject[]) => {
            let selectedProject: Partial<IProject>;
            if (Array.isArray(projects)) {
              selectedProject = projects[0];
            } else {
              selectedProject = projects;
            }
            setFieldValue("project", selectedProject || {});
          }}
          customer_id={
            "directory" in formikValues &&
            formikValues?.directory?.user_id?.toString()
              ? formikValues.directory.user_id.toString()
              : ""
          }
          genericProjects="project,opportunity"
          module_key={
            ADD_NEW_TASK_FORM_TABS.todo === currentForm
              ? CFConfig.todo_module
              : ADD_NEW_TASK_FORM_TABS.calendar === currentForm
              ? CFConfig.corporate_calendar_module
              : ""
          }
        />
      )}
    </>
  );
};

export default AddNewTask;
