// Hook
import { useTranslation } from "~/hook";

// atoms
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Search } from "~/shared/components/atoms/search";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DrawerVericalOptions } from "~/shared/components/molecules/drawerVericalOptions";

// Other
import { useCallback, useEffect, useState } from "react";
import { AddEquipmentItem } from "../addEquipmentItem";
import cloneDeep from "lodash/cloneDeep";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { useAppDLSelector } from "../../../../redux/store";
import useFetch from "../../equipment/hooks/useFetch";
import { dailyLogRoutes } from "~/route-services/daily-log.routes";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { useGModules } from "~/zustand";
import { defaultConfig } from "~/data";

const EquipmentItem = ({
  equipmentItemOpen,
  setEquipmentItemOpen,
  loadingComponent,
  options,
  projectId,
  saveCheckedEquipmentItemsHandler,
  defaultCheckedItems,
  isEquipmentLoading,
}: IEquipmentItemProps) => {
  const { _t } = useTranslation();
  const { getGModuleByKey } = useGModules();
  const [sideMenuOpen, setSideMenuOpen] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [addEquipmentItem, setAddEquipmentItem] = useState<boolean>(false);
  const [checkedItems, setCheckedItems] =
    useState<TEquipmentTimeCardsDetailData>({
      equipment_item: [],
      estimate_equipment_item: [],
      change_order_equipment_item: [],
    });
  const equipmentItemsAlias: {
    [key: string]: keyof TEquipmentTimeCardsDetailData;
  } = {
    estimate_items: "estimate_equipment_item",
    change_order_items: "change_order_equipment_item",
    cost_items_database: "equipment_item",
  };

  const SELECT_OPTIONS: ICustomerSelectOption<TEquipmentItemTabsValues>[] = [
    {
      name: "All",
      addTooltipContent: "Equipment Item",
      value: "all",
      icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-id-badge" />,
      addButton: true,
    },
    {
      name: `${
        getGModuleByKey(defaultConfig.estimate_module)?.module_name
      } Items`,
      value: "estimate_items",
      icon: (
        <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-file-user" />
      ),
    },
    {
      name: `${
        getGModuleByKey(defaultConfig.change_order_module)?.module_name
      } Items`,
      value: "change_order_items",
      icon: (
        <FontAwesomeIcon
          className="w-5 h-5"
          icon="fa-solid fa-arrow-right-arrow-left"
        />
      ),
    },
    {
      name: "Cost Items Database",
      addTooltipContent: "Equipment Item",
      value: "cost_items_database",
      icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-database" />,
      addButton: true,
    },
  ];

  const [equipmentSearchDebounce, setEquipmentSearchDebounce] =
    useState<string>("");
  const { equipmentId }: IDLEquipmentsInitialState = useAppDLSelector(
    (state) => state.dailyLogeEuipments
  );
  const [search, setSearch] = useState<string>("");
  const {
    data: equipmentItems,
    loading: equipmentItemsLoading,
    fetchData: fetchEquipmentTimeCardsDetail,
  } = useFetch<TIEquipmentTimeCardsDetailResponse>(
    dailyLogRoutes.get_equipment_timecards,
    { projectId },
    !isEmpty(equipmentItemOpen),
    equipmentSearchDebounce
  );
  const checkBoxHandler = (
    e: CheckboxChangeEvent,
    item: TAllEquipmentItem,
    id: number,
    equipmentKey?: keyof TCheckedEquipmentTimeCardsDetailData
  ) => {
    const { checked }: { checked: boolean } = e.target;
    if (checked) {
      if (equipmentKey) {
        setCheckedItems((prevData) => {
          return {
            ...prevData,
            [equipmentKey]:
              checkedItems && checkedItems[equipmentKey]
                ? [...checkedItems[equipmentKey], item]
                : [item],
          };
        });
      }
    } else {
      if (equipmentKey) {
        setCheckedItems((prev) => {
          const selectedCategoryArray =
            checkedItems && checkedItems[equipmentKey];
          const index =
            selectedCategoryArray &&
            selectedCategoryArray.findIndex((i) => {
              if ("item_id" in i) {
                return i.item_id === id;
              } else {
                return i.equipment_id === id;
              }
            });

          let copy = JSON.parse(JSON.stringify(prev));
          copy[equipmentKey].splice(index, 1);
          return copy;
        });
      }
    }
  };

  const checkBoxElementReturner = (
    item: TAllEquipmentItem,
    equipmentKey: keyof TEquipmentTimeCardsDetailData
  ) => {
    if ("item_id" in item) {
      const id = item.item_id;
      const name = item.subject;
      return (
        <CheckBox
          key={id}
          className="w-full gap-2 px-4 py-2 border-b border-solid border-[#F1F2F3] last:border-b-0"
          onChange={(e) => {
            checkBoxHandler(e, item, id, equipmentKey);
          }}
          checked={checkedItems[equipmentKey].some((i) => {
            if ("item_id" in i) {
              return i.item_id === id;
            } else {
              return i.equipment_id === id;
            }
          })}
        >
          {name !== null ? replaceDOMParams(sanitizeString(name)) : ""}
        </CheckBox>
      );
    } else {
      const id = item.equipment_id;
      const name = item.name;
      return (
        <CheckBox
          key={id}
          className="w-full gap-2 px-4 py-2 border-b border-solid border-[#F1F2F3] last:border-b-0"
          onChange={(e) => {
            checkBoxHandler(e, item, id, equipmentKey);
          }}
          checked={checkedItems[equipmentKey].some((i) => {
            if ("item_id" in i) {
              return i.item_id === id;
            } else {
              return i.equipment_id === id;
            }
          })}
        >
          {name !== null ? replaceDOMParams(sanitizeString(name)) : ""}
        </CheckBox>
      );
    }
  };

  const equipmentSearchHandler = useCallback(
    debounce((search: string) => {
      setEquipmentSearchDebounce(search);
    }, 1000),
    []
  );

  useEffect(() => {
    setCheckedItems(cloneDeep(defaultCheckedItems));
  }, []);

  useEffect(() => {
    if (equipmentId > 0) {
      fetchEquipmentTimeCardsDetail();
    }
  }, [equipmentId]);

  // When a new item is added, find it in the equipmentItems and check it
  useEffect(() => {
    if (equipmentId && equipmentItems) {
      const newlyAddedItem = Object.keys(equipmentItems).reduce((acc, key) => {
        const equipmentList =
          equipmentItems[key as keyof TEquipmentTimeCardsDetailData];
        const foundItem = equipmentList.find((item: TAllEquipmentItem) => {
          return "item_id" in item
            ? item.item_id === equipmentId
            : item.equipment_id === equipmentId;
        });
        if (foundItem) {
          acc = {
            item: foundItem,
            key: key as keyof TEquipmentTimeCardsDetailData,
          };
        }
        return acc;
      }, {} as { item: TAllEquipmentItem; key: keyof TEquipmentTimeCardsDetailData });

      if (newlyAddedItem.item) {
        setCheckedItems((prevData) => ({
          ...prevData,
          [newlyAddedItem.key]: [
            ...prevData[newlyAddedItem.key],
            newlyAddedItem.item,
          ],
        }));
      }
    }
  }, [equipmentId, equipmentItems]);

  return (
    <>
      <Drawer
        open={!isEmpty(equipmentItemOpen)}
        rootClassName="drawer-open"
        width={870}
        classNames={{
          header: "!hidden",
          body: "!p-0 !overflow-hidden",
        }}
      >
        <div className="sidebar-body">
          <div className="flex">
            <DrawerVericalOptions<TEquipmentItemTabsValues | "">
              sideMenuOpen={sideMenuOpen}
              setSideMenuOpen={setSideMenuOpen}
              defaultOptions={SELECT_OPTIONS}
              options={options}
              selectedOption={equipmentItemOpen}
              onClick={(value: TEquipmentItemTabsValues | "") => {
                setEquipmentItemOpen(value);
              }}
              addFunction={() => setAddEquipmentItem(true)}
              buttonClassName="whitespace-normal"
            />

            <div className="flex w-full max-w-[630px] flex-[1_0_0%] overflow-hidden flex-col">
              <div className="md:px-4 pl-10 pr-5 py-2.5 border-b border-gray-300 dark:border-white/10 flex items-center relative justify-start">
                <Button
                  className="md:!hidden flex !w-6 h-6 !absolute left-2.5"
                  type="text"
                  onClick={() => setSideMenuOpen((prev) => !prev)}
                  icon={
                    <FontAwesomeIcon
                      className="text-base w-[18px] h-[18px] text-primary-gray-80 dark:text-white/90"
                      icon="fa-regular fa-bars"
                    />
                  }
                />
                <div className="flex items-center text-primary-900 dark:text-white/90">
                  <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-gray-200/50 dark:bg-dark-500">
                    <FontAwesomeIcon
                      className="w-4 h-4 !text-primary-900 dark:!text-white/90"
                      icon="fa-regular fa-screwdriver-wrench"
                    />
                  </div>
                  <Header
                    level={5}
                    className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
                  >
                    {_t("Select Equipment Items")}
                  </Header>
                </div>
                <div className="md:relative md:right-[18px] !absolute right-2.5">
                  <CloseButton
                    onClick={() => {
                      // this is will NF once testing done it will be merge on dev
                      // handleCloseDrawer();
                      setEquipmentItemOpen("");
                    }}
                  />
                </div>
              </div>
              <div className="p-4 flex items-center justify-center w-7 md:w-full">
                <Button
                  htmlType="button"
                  className={`w-7 !m-0 group/handle-visible-change max-w-[28px] max-h-[28px] block md:hidden ${
                    viewSearch ? "" : ""
                  }`}
                  type="text"
                  onClick={() => {
                    setViewSearch((prev) => !prev);
                  }}
                >
                  <FontAwesomeIcon
                    className="text-base w-[18px] h-[18px] !text-primary-gray-80 dark:!text-white/90"
                    icon="fa-regular fa-magnifying-glass"
                  />
                </Button>
                <div
                  className={`md:static md:translate-x-0 sm:px-0 px-2.5 absolute w-full z-10 md:!bg-transparent bg-white dark:bg-[#1E2732] -translate-x-full ease-in left-0 ${
                    viewSearch ? "translate-x-0" : ""
                  }`}
                >
                  <div className="flex items-center">
                    <Button
                      htmlType="button"
                      className="w-6 group/handle-visible-change max-w-[24px] max-h-[24px] md:hidden"
                      type="text"
                      onClick={() => {
                        setViewSearch((prev) => !prev);
                      }}
                    >
                      <FontAwesomeIcon
                        className="text-base w-[18px] h-[18px] text-primary-900/80 group-hover/handle-visible-change:text-primary-900 dark:!text-white/90"
                        icon="fa-regular fa-angle-left"
                      />
                    </Button>

                    <Search
                      placeholder="Search for Equipments"
                      variant="borderless"
                      className="search-input-borderless relative dark:bg-white/5 rounded-md border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 dark:before:bg-[#696b6e]"
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        equipmentSearchHandler(e.target.value);
                        setSearch(e.target.value);
                      }}
                      value={search}
                      allowClear={true}
                    />
                  </div>
                </div>
              </div>
              <div className="grid gap-1.5">
                <FieldLabel labelClass="px-4">{_t("Item Name")}</FieldLabel>
                <div className="h-[calc(100dvh-215px)] overflow-y-auto overflow-hidden">
                  {equipmentItemsLoading ? (
                    loadingComponent
                  ) : equipmentItemOpen === "all" ? (
                    equipmentItems &&
                    Object.keys(equipmentItems).length > 0 &&
                    Object.keys(equipmentItems).some(
                      (equipmentKey) =>
                        equipmentItems[
                          equipmentKey as keyof TEquipmentTimeCardsDetailData
                        ].length > 0
                    ) ? (
                      (
                        Object.keys(
                          equipmentItems
                        ) as (keyof TEquipmentTimeCardsDetailData)[]
                      ).map((equipmentKey) => {
                        return equipmentItems[equipmentKey].map(
                          (item: TAllEquipmentItem) => {
                            return checkBoxElementReturner(item, equipmentKey);
                          }
                        );
                      })
                    ) : (
                      <div className="h-full flex items-center justify-center">
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-equipment-items-delivered.svg`}
                        />
                      </div>
                    )
                  ) : equipmentItems &&
                    Array.isArray(
                      equipmentItems[equipmentItemsAlias[equipmentItemOpen]]
                    ) &&
                    equipmentItems[equipmentItemsAlias[equipmentItemOpen]]
                      .length > 0 ? (
                    equipmentItems[equipmentItemsAlias[equipmentItemOpen]].map(
                      (item: TAllEquipmentItem) => {
                        let equipmentKey =
                          equipmentItemsAlias[equipmentItemOpen];
                        return checkBoxElementReturner(item, equipmentKey);
                      }
                    )
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <NoRecords
                        image={`${window.ENV.CDN_URL}assets/images/no-records-equipment-items-delivered.svg`}
                      />
                    </div>
                  )}
                </div>
                <div className="sidebar-footer flex items-center justify-center w-full p-4">
                  <PrimaryButton
                    type="primary"
                    className="w-full justify-center primary-btn"
                    htmlType="button"
                    onClick={() => {
                      saveCheckedEquipmentItemsHandler(checkedItems);
                    }}
                    disabled={
                      !(
                        checkedItems?.equipment_item?.length > 0 ||
                        checkedItems?.estimate_equipment_item?.length > 0 ||
                        checkedItems?.change_order_equipment_item?.length > 0 ||
                        equipmentId > 0
                      ) ||
                      equipmentItemsLoading ||
                      isEquipmentLoading
                    }
                    isLoading={isEquipmentLoading}
                  >
                    {_t("Save")}
                  </PrimaryButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Drawer>
      {addEquipmentItem && (
        <AddEquipmentItem
          addEquipmentItem={addEquipmentItem}
          setAddEquipmentItem={setAddEquipmentItem}
        />
      )}
    </>
  );
};

export default EquipmentItem;
