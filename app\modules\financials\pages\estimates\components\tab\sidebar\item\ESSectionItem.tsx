import { type RadioChangeEvent } from "antd";
import isEmpty from "lodash/isEmpty";
import * as Yup from "yup";
import { useTranslation } from "~/hook";

// Atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Drawer } from "~/shared/components/atoms/drawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { SelectField } from "~/shared/components/molecules/selectField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";

// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

import { Form } from "@remix-run/react";
import type { InputRef } from "antd";
import { useFormik } from "formik";
import debounce from "lodash/debounce";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import {
  filterOptionBySubstring,
  generateCostCodeLabel,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { getGSettings } from "~/zustand";
import {
  addEstimateItems,
  getItemVariants,
  updateEstimateItems,
} from "../../../../redux/action/ESItemAction";
import { addItems, updateItem } from "../../../../redux/slices/ESItemSlice";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import { isValidId, toBoolean } from "../../../../utils/common";
import { itemTotalCalculator } from "../../details/EstimatesCalc";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { getGlobalTypes } from "~/zustand/global/types/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { useGlobalModule } from "~/zustand/global/modules/slice";

const ESSectionItem = ({
  itemOpen,
  setItemOpen,
  onClose = () => {},
  isViewOnly,
  formData,
  isItemAdd = false,
  isItemDetailDrawer = false,
  isReadOnly,
  setEstimateItemToView,
  descEditable = false,
  bidItemformik,
  ParentIsReadOnly = false,
}: IItemOpenProps) => {
  // const setEstimateItemToView = Object.keys(formData??{})?.length > 0
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );

  const { module_name: estimate_module_name = "" } = EstimateModule || {};
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );
  // const variantOptions = [];
  const gSettings: GSettings = getGSettings();
  const { is_cidb_auto_save } = gSettings;
  const [markupType, setMarkupType] = useState<string>("markup_percent");
  const itemTypes: GType[] = getGlobalTypes();
  const itemTypesWithMarkup = useAppESSelector(
    (state) => state.itemTypes.itemTypes
  );
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [isAddItem, setIsAddItem] = useState<boolean>(isItemAdd);
  const [inputValues, setInputValues] = useState<Partial<ESEstimateItem>>({});
  const [submitAction, setSubmitAction] = useState<string>("");
  const [variantOptions, setVariantOptions] = useState<VariaonsofItem[]>([]);
  const [variantloading, setVariantloading] = useState(false);
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isMuPercentFieldChanged, setIsMuPercentFieldChanged] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});

  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const unitCostRef = useRef<InputRef>(null);
  const [costchangeConfirmOpen, setCostChangeConfirmation] =
    useState<Partial<ESEstimateItem> | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [cidbItemExistPopupData, setCidbItemExistPopupData] =
    useState<ICidbItemExistPopupData | null>();
  const [showCostChangeTooltip, setShowCostChangeTooltip] = useState(false);

  const { estimateDetail: details } = useAppESSelector(
    (state) => state.estimateDetail
  );

  const { codeCostData } = useAppESSelector((state) => state.costCode);
  const { sections, flags } = useAppESSelector((state) => state.estimateItems);
  const items =
    sections
      ?.find((el) => formData?.section_id === el?.section_id)
      ?.items?.filter((ele) => {
        if (flags?.showZeroQuantityItemsOnly) {
          const isZeroQuantity =
            isNaN(Number(ele?.quantity)) ||
            isNaN(Number(ele?.unit_cost)) ||
            Number(ele?.quantity) == 0 ||
            Number(ele?.unit_cost) == 0 ||
            Number(ele?.quantity) * Number(ele?.unit_cost) == 0;
          return isZeroQuantity;
        }
        return true;
      }) ?? [];

  const dispatch = useAppESDispatch();

  const filteredItemsTypes = useMemo(
    () =>
      itemTypes?.filter(
        (item: Partial<GType>) => item?.type === "company_items"
      ),
    [itemTypes]
  );
  const reorderedItemsTypes = (() => {
    if (!Array.isArray(filteredItemsTypes)) return [];
    const items = [...filteredItemsTypes];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();
  const undefinedTypeMarkup = useMemo(() => {
    const marupUndefined = itemTypesWithMarkup?.find(
      (i: IWorkOrderType) => i.type_id?.toString() === ""
    )?.mark_up;
    return marupUndefined !== "null" && marupUndefined
      ? marupUndefined?.toString()
      : "";
  }, [itemTypesWithMarkup]);
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units);
  };

  useEffect(() => {
    getUnit();
  }, []);

  const costCodeOptions = useMemo(() => {
    const filteredCodeCostData = codeCostData?.filter(
      (item) =>
        (item?.cost_code_name?.toString() !== "" ||
          item?.csi_code?.toString() !== "") &&
        Number(item?.parent_id) > 0
    );
    let costCodeOpts = filteredCodeCostData?.map((item: ICostCode) => {
      return {
        // label: `${item?.cost_code_name}${
        //   item?.csi_code ? ` (${item?.csi_code})` : ""
        // }${item?.is_deleted === 1 ? ` (Archived)` : ""}`,
        label: `${generateCostCodeLabel({
          name: item?.cost_code_name,
          code: item?.csi_code,
          isArchived: false,
          isAllowCodeWithoutName: true,
        })}`,
        value: item?.code_id,
      };
    });

    if (!isAddItem && formData?.is_deleted === 1) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          // label: `${formData?.cost_code_name}${
          //   formData?.csi_code ? ` (${formData?.csi_code})` : ""
          // } (Archived)`,
          label: `${generateCostCodeLabel({
            name: formData?.cost_code_name,
            code: formData?.csi_code,
            isArchived: false,
          })}`,
          value: formData?.cost_code_id?.toString() ?? "",
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData]);

  useEffect(() => {
    if (formData) {
      setInputValues(formData);
    }
  }, [formData]);

  useEffect(() => {
    if (isAddItem === false) {
      if (formData?.is_markup_percentage === 1) {
        setMarkupType("markup_percent");
      } else {
        setMarkupType("markup_dolar");
      }
    } else {
      setMarkupType("markup_percent");
    }
  }, [formData?.is_markup_percentage, isAddItem, formData]);
  // Keep it for future use
  // const intialMarkupForAdd = useMemo(() => {
  //   return itemTypes?.find((i) => !i?.type_id)?.mark_up || "";
  // }, [itemTypes]);

  const initialValues: Partial<IEstimateAddItem> = useMemo(() => {
    return isAddItem === false
      ? {
          ...inputValues,
          is_optional_item: 0,
          is_markup_percentage: 1,
          ...formData,
          unit_cost:
            typeof formData?.unit_cost === "undefined"
              ? ""
              : (Number(formData?.unit_cost || 0) / 100)?.toFixed(2).toString(),
          markup: formData?.markup?.toString()
            ? formData.is_markup_percentage === 0
              ? (Number(formData.markup) / 100).toString()
              : formData.markup
            : "",
          // add_item_to_database:
          //   formData?.item_on_database ||
          //   isValidId(formData?.reference_item_id),
          add_item_to_database: formData?.item_on_database,
        }
      : {
          ...inputValues,
          subject: "",
          item_type: "",
          is_optional_item: 0,
          is_markup_percentage: 1,
          add_item_to_database: is_cidb_auto_save,
          markup: undefinedTypeMarkup,
        };
  }, [isAddItem, formData, inputValues, is_cidb_auto_save]);

  const valuesToResetatFormReset: Partial<IEstimateAddItem> = {
    quantity: undefined,
    unit_cost: undefined,
    unit: undefined,
    cost_code: undefined,
    cost_code_name: undefined,
    cost_code_id: undefined,
    assigned_to: "0",
    assigned_to_company_name: undefined,
    assigned_to_contact_id: undefined,
    assigned_to_name_only: undefined,
    apply_global_tax: 0,
    variation_id: undefined,
    variation_name: undefined,
    description: undefined,
    internal_notes: undefined,
    reference_item_id: 0,
  };

  const validationSchema = Yup.object().shape({
    subject: Yup.string()
      .required("This field is required.")
      .test("not-blank", "This field is required.", (value) => {
        return !isEmpty(value) && !!value?.trim().length;
      }),
    item_type: Yup.string().required("This field is required."),
    // unit_cost: Yup.number()
    //   // .positive("Unit cost must be positive")
    //   .test(
    //     "len",
    //     "Unit cost must be less than or equal to 10 digits",
    //     (val) => !val || val?.toString()?.length <= 10
    //   )
    //   .test(
    //     "is-zero-valid",
    //     "Unit cost cannot be negative or invalid",
    //     (val) => Number(val) === 0 || Number(val) > 0
    //   ),
  });

  // useEffect(() => {
  //   dispatch(getCostCode({ project_id: +(details?.project_id || 0) }));
  // }, [details?.project_id]);

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      if (!formData?.section_id) {
        notification.error({
          description: "No valid section ID found",
        });
        return;
      }
      if (ParentIsReadOnly) {
        return;
      }
      if (isAddItem) {
        let _markup = values?.markup?.toString()
          ? values?.markup == "-" || !values?.markup
            ? 0
            : values.markup
          : "";

        // if (!_markup) {
        //   if (
        //     values?.type_name === "Material" &&
        //     default_material_markup_percent
        //   ) {
        //     _markup = default_material_markup_percent;
        //   } else if (
        //     values?.type_name === "Labor" &&
        //     default_labor_markup_percent
        //   ) {
        //     _markup = default_labor_markup_percent;
        //   } else if (
        //     values?.type_name === "Subcontractor" &&
        //     default_sub_contractor_markup_percent
        //   ) {
        //     _markup = default_sub_contractor_markup_percent;
        //   } else if (
        //     values?.type_name === "Equipment" &&
        //     default_equipment_markup_percent
        //   ) {
        //     _markup = default_equipment_markup_percent;
        //   } else if (
        //     values?.type_name === "Other Items" &&
        //     default_other_item_markup_percent
        //   ) {
        //     _markup = default_other_item_markup_percent;
        //   } else if (undefinedTypeMarkup) {
        //     _markup = undefinedTypeMarkup;
        //   }
        // }

        const costcodeId = values?.cost_code_id ?? "";
        const estimateItem = {
          estimate_id: Number(estimateDetail?.estimate_id),
          items: [
            {
              ...values,
              cost_code_id: costcodeId,
              section_id: formData?.section_id,
              estimate_item_no: items?.length + 1,
              unit_cost: Number(values?.unit_cost) * 100,
              quantity: values?.quantity ? Number(values?.quantity) : "",
              markup: _markup?.toString()
                ? values?.is_markup_percentage === 0
                  ? (Number(_markup) * 100)?.toString()
                  : _markup?.toString()
                : null,
              // markup:
              //   values?.is_markup_percentage === 0
              //     ? _markup == ""
              //       ? null
              //       : (Number(_markup) * 100)?.toString()
              //     : _markup?.toString(),
              total: Number(mainTotal) * 100,
            },
          ],
        };
        if (estimateItem?.items?.[0]?.variation_id) {
          estimateItem.items[0].variation_name = variantOptions?.find(
            (el) => el?.variation_id === estimateItem?.items?.[0]?.variation_id
          )?.name;
        }
        const reqPayload = getValuableObj(estimateItem);
        const response: any = await dispatch(addEstimateItems(reqPayload));
        if (response?.payload?.success) {
          const newItem = response.payload?.data?.[0];
          // const newItems = [...items, newItem];
          // dispatch(
          //   assignSectionItems({
          //     sectionId: formData?.section_id,
          //     items: newItems,
          //   })
          // );
          dispatch(
            addItems({
              sectionId: formData?.section_id,
              itemData: newItem,
            })
          );

          if (submitAction === "save_n_close") {
            onClose(descEditable ? { ...values } : "");
          } else if (submitAction === "save_n_add_another") {
            resetForm({
              values: {
                ...initialValues,
                ...valuesToResetatFormReset,
              },
            });
            setIsAddItem(true);
            setCostChangeConfirmation(null);
            setShowCostChangeTooltip(false);
          } else {
            const nextItemIndex = formik.status;
            resetForm({
              values: {
                ...initialValues,
                ...valuesToResetatFormReset,
              },
            });
            setEstimateItemToView?.(items?.[nextItemIndex] ?? {});
          }
          // await dispatch(getEstimateItems({ estimate_id }));
        } else {
          if (isValidId(response?.payload?.data?.reference_item_id)) {
            setCidbItemExistPopupData(response?.payload);
          } else {
            notification.error({
              description: response?.payload?.message,
            });
          }
          setSubmitting(false);
        }
      } else {
        if (formData?.is_added_item === 1) {
          bidItemformik?.values?.bid_items?.map((item: any) => {
            if (item?.estimate_item_id == formData?.estimate_item_id) {
              item.description = values?.description;
            }
          });
          onClose();
          return;
        }
        const _markup = values?.markup?.toString()
          ? values?.markup == "-" || !values?.markup
            ? 0
            : values.markup
          : "";
        const costcodeId = values?.cost_code_id ?? "";
        const variatonId = values?.variation_id ?? null;
        const estimateItem = {
          estimate_id: estimateDetail?.estimate_id,
          is_single_item: 1,
          items: [
            {
              ...values,
              cost_code_id: costcodeId,
              variation_id: variatonId,
              section_id: formData?.section_id,
              // estimate_item_no: items?.length + 1,
              unit_cost: Number(values?.unit_cost) * 100,
              quantity: values?.quantity ? Number(values?.quantity) : "",
              markup: _markup?.toString()
                ? values?.is_markup_percentage === 0
                  ? (Number(_markup) * 100)?.toString()
                  : _markup?.toString()
                : null,
              // values?.is_markup_percentage === 0
              //   ? _markup == ""
              //     ? null
              //     : (Number(_markup) * 100)?.toString()
              //   : _markup?.toString(),
              total: Number(mainTotal) * 100,
            },
          ],
        };
        const reqPayload = getValuableObj(estimateItem) as IESUpdateItemParams;
        const response = await dispatch(updateEstimateItems(reqPayload));
        const payload = response?.payload as IEstimatesItemUpdateApiRes;
        if (payload?.success) {
          const updatedItem = payload?.data?.[0] ?? estimateItem.items[0];
          if (values?.cost_code_id) {
            const costDetailOne = codeCostData?.find((item) => {
              return (
                values?.cost_code_id?.toString() === item?.code_id?.toString()
              );
            });
            if (costDetailOne && updatedItem) {
              updatedItem.cost_code = costDetailOne.csi_code;
              updatedItem.cost_code_name = costDetailOne.cost_code_name;
            }
          } else if (!values?.cost_code_id) {
            updatedItem.cost_code_id = "";
            updatedItem.cost_code = "";
            updatedItem.cost_code_name = "";
          } else if (!values?.variation_id) {
            updatedItem.variation_id = null;
            updatedItem.variation_name = "";
          }
          if (updatedItem?.add_item_to_database) {
            updatedItem.item_on_database = updatedItem?.add_item_to_database;
          }
          dispatch(
            updateItem({
              sectionId: formData?.section_id,
              itemId: updatedItem.item_id,
              updatedItem,
            })
          );
          if (submitAction === "save_n_close") {
            onClose(descEditable ? { ...values } : "");
          } else if (submitAction === "save_n_add_another") {
            resetForm({
              values: {
                ...initialValues,
                ...valuesToResetatFormReset,
              },
            });
            setIsAddItem(true);
            setCostChangeConfirmation(null);
            setShowCostChangeTooltip(false);
          } else {
            const nextItemIndex = formik.status;
            resetForm({
              values: {
                ...initialValues,
                ...valuesToResetatFormReset,
              },
            });
            setEstimateItemToView?.(items?.[nextItemIndex] ?? {});
          }
          // await dispatch(getEstimateItems({ estimate_id }));
        } else {
          const payload = response?.payload as ICidbItemExistPopupData;
          if (isValidId(payload?.data?.reference_item_id)) {
            setCidbItemExistPopupData(payload);
          } else {
            notification.error({
              description: payload?.message,
            });
          }
        }
      }

      setSubmitting(false);
    },
  });

  const {
    handleSubmit,
    setFieldValue,
    values,
    errors,
    isSubmitting,
    resetForm,
  } = formik;

  useEffect(() => {
    setIsAddItem(isItemAdd);
    formik.setValues(initialValues);
  }, [formData, itemOpen]);

  useEffect(() => {
    if (isAddItem) {
      resetForm({
        values: {
          ...initialValues,
          ...valuesToResetatFormReset,
        },
      });
    }
  }, [isAddItem]);

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (Number(formData?.unit_cost) === 0) {
        setInputValues({
          ...formData,
          unit_cost: "",
          total: 0,
        });
      }
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values?.unit &&
        !isEmpty(values?.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  const saveItemKey = useMemo(() => {
    const itemType = itemTypes?.find(
      (i: GType) => i.type_id?.toString() === values?.item_type?.toString()
    );

    return itemType?.name;
  }, [itemTypes, values?.item_type]);

  useEffect(() => {
    if (
      values?.quantity !== "" &&
      values?.unit_cost !== "" &&
      Number(values?.quantity) &&
      Number(values?.unit_cost)
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
      setMainTotal(Number(values?.markup_amount) + Number(values?.total));
    } else {
      setFieldValue("total", "");
      setMainTotal("");
    }
    if (
      values?.total?.toString() !== "" &&
      values?.total &&
      Number(values?.total) &&
      Number(values?.quantity) &&
      Number(values?.unit_cost) &&
      values?.markup !== "" &&
      values?.markup
    ) {
      const _markup =
        values?.markup == "-" || !values?.markup ? 0 : values.markup;

      if (markupType === "markup_percent") {
        const markup = (Number(values?.total) * Number(_markup)) / 100;
        setFieldValue("markup_amount", markup);
        setMainTotal(Number(markup) + Number(values?.total));
      } else {
        const markup = Number(_markup);
        const markupPercentage =
          Number(markup) === 0
            ? 0
            : (Number(markup) * 100) / (Number(values?.total) || 1) - 100;
        setFieldValue("markup_amount", markupPercentage.toFixed(2));
        const total = Number(values?.quantity) * Number(values?.unit_cost);
        setMainTotal(markup === 0 ? total : markup);
      }
    } else {
      setFieldValue("markup_amount", "");
      setMainTotal(Number(values?.total));
    }
  }, [
    values?.quantity,
    values?.unit_cost,
    values?.total,
    values?.markup,
    values?.is_markup_percentage,
    markupType,
  ]);

  const assignedTo = useMemo(() => {
    if (
      values?.assigned_to &&
      values.assigned_to?.toString() &&
      values.assigned_to.toString() !== "0"
    ) {
      const assigned_to = [
        {
          display_name: values?.assignee_name,
          user_id: Number(values?.assigned_to),
          image: values?.assigned_to_contact_id == 0 ? values?.user_image : "",
          contact_id: Number(values?.assigned_to_contact_id) ?? 0,
          type: values?.assignee_type,
          type_key: getDirectaryKeyById(
            Number(values?.assignee_type),
            undefined
          ),
          type_name: values?.type_name,
        },
      ];
      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [
    values?.assigned_to,
    values?.assignee_name,
    values?.assignee_type,
    values?.assigned_to_contact_id,
    values?.type_name,
  ]);

  useMemo(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [isAddItem, formData, values]);

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show = isAddItem || Number(values?.reference_item_id ?? 0) === 0;

    const disable =
      !isAddItem &&
      (!!values?.item_on_database ||
        (!!values?.reference_item_id && Number(values?.reference_item_id) > 0));

    return { show, disable };
  }, [isAddItem, values?.reference_item_id, values?.item_on_database]);

  const handleSaveItem = (key: string) => {
    setSubmitAction(key);
  };

  const currentItemIndex = useMemo(() => {
    const curItemIndex = items?.findIndex(
      (i) => i?.item_id === formData?.item_id
    );

    return curItemIndex;
  }, [items, formData]);

  const handlePrevItem = useCallback(() => {
    if (Number(currentItemIndex) && Number(currentItemIndex) > 0) {
      if (formik.dirty && (!isViewOnly || !ParentIsReadOnly)) {
        formik.submitForm();
        handleSaveItem("save_n_next_prev");
        formik.setStatus(Number(currentItemIndex) - 1);
      } else {
        setEstimateItemToView?.(items?.[Number(currentItemIndex) - 1] ?? {});
      }
    }
  }, [isViewOnly, ParentIsReadOnly, currentItemIndex, formik, items]);

  const handleNextItem = useCallback(() => {
    if (Number(currentItemIndex) < (items?.length ?? 0) - 1) {
      if (formik.dirty && (!isViewOnly || !ParentIsReadOnly)) {
        formik.submitForm();
        handleSaveItem("save_n_next_prev");
        formik.setStatus(Number(currentItemIndex) + 1);
      } else {
        setEstimateItemToView?.(items?.[Number(currentItemIndex) + 1] ?? {});
      }
    }
  }, [isViewOnly, ParentIsReadOnly, currentItemIndex, formik, items]);

  const MARKUP_OPTIONS = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {formatter().currency_symbol}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];
  const memoizedFormData = useMemo(
    () => formData,
    [formData?.reference_item_id, formData?.item_id, formData?.item_type]
  );

  const debouncedSetAndGetVariants = useCallback(
    debounce(async (values) => {
      const respose = (await getItemVariants({
        item_id: values?.reference_item_id,
        item_type: values?.item_type,
      })) as IEstimateItemVariantApiRes;
      if (respose?.data?.length) {
        setVariantOptions(
          respose?.data?.map((el) => ({
            ...el,
            label: el?.name,
            value: el?.variation_id,
          }))
        );
      } else {
        setVariantOptions([]);
      }
      setVariantloading(false);
    }, 300),
    []
  );

  const applyGlobalTax = useMemo(() => {
    const taxSettingsMap: Record<number, number> = {
      [CFConfig.material_teb_id]: gSettings.is_taxable_material_items,
      [CFConfig.equipment_teb_id]: gSettings.is_taxable_equipment_items,
      [CFConfig.labor_teb_id]: gSettings.is_taxable_labor_items,
      [CFConfig.subcontractor_teb_id]: gSettings.is_taxable_subcontractor_items,
      [CFConfig.other_items_teb_id]: gSettings.is_taxable_other_items,
    };
    return taxSettingsMap[Number(values.item_type)] ?? 0;
  }, [values?.item_type, gSettings]);

  useEffect(() => {
    if (isAddItem) {
      setFieldValue("apply_global_tax", applyGlobalTax);
    }
  }, [applyGlobalTax, memoizedFormData, setFieldValue]);

  useEffect(() => {
    if (!isAddItem && formData?.reference_item_id && formData?.item_type) {
      if (formData?.variation_id) {
        setVariantOptions([
          {
            label: formData?.variation_name,
            value: formData?.variation_name,
          },
        ]);
      } else {
        setVariantOptions([]);
      }
      setVariantloading(true);
      debouncedSetAndGetVariants(formData);
    }
  }, [isAddItem, memoizedFormData]);

  useEffect(() => {
    if (
      formData?.updated_unit_cost &&
      Math.round(Number(values?.unit_cost) * 100) !==
        Math.round(Number(formData?.updated_unit_cost)) &&
      (!formData?.one_build_id || formData?.one_build_id === undefined) &&
      !isReadOnly &&
      isValidId(formData?.reference_item_id) &&
      !isAddItem
    ) {
      setShowCostChangeTooltip(true);
    } else {
      setShowCostChangeTooltip(false);
    }
  }, [formData, values?.unit_cost, isReadOnly]);
  const formattedMarkup =
    markupType === "markup_percent"
      ? values?.markup_amount?.toString()
        ? formatter(formatAmount(Number(values.markup_amount).toFixed(2)))
            .value_with_symbol
        : formatter(formatAmount("0.00")).value_with_symbol
      : !values?.markup_amount || Number(values?.markup_amount) === 0
      ? "0%"
      : `${Number(values?.markup_amount || 0).toFixed(2)}%`;

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  return (
    <>
      <Drawer
        open={itemOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {!isAddItem
                  ? _t(`${estimate_module_name} Item`)
                  : _t(`Add ${estimate_module_name} Item`)}
              </Header>
              {!isAddItem ? (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  {formik.isSubmitting &&
                  submitAction === "save_n_next_prev" ? (
                    <FontAwesomeIcon
                      className="w-3.5 h-3.5 fa-spin"
                      icon="fa-duotone fa-solid fa-spinner-third"
                    />
                  ) : (
                    ""
                  )}
                  {items?.length && items.length > 1 ? (
                    <>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Previous")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-chevron-left"
                        className="item-pre-next-button disabled:bg-transparent"
                        onClick={handlePrevItem}
                        disabled={
                          Number(currentItemIndex) <= 0 || formik.isSubmitting
                        }
                      />
                      <ButtonWithTooltip
                        tooltipTitle={_t("Next")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-chevron-right"
                        className="item-pre-next-button disabled:bg-transparent"
                        onClick={handleNextItem}
                        disabled={
                          Number(currentItemIndex) >=
                            (items?.length ?? 0) - 1 || formik.isSubmitting
                        }
                      />
                    </>
                  ) : (
                    ""
                  )}
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <Form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    id="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    errorMessage={formik.touched?.subject ? errors.subject : ""}
                    disabled={isViewOnly}
                    isRequired={true}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value?.trimStart());
                    }}
                    // onBlur={formik.handleBlur}
                    autoComplete="off"
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div
                  className={`grid ${
                    isItemDetailDrawer ? "md:grid-cols-1" : "md:grid-cols-2"
                  } md:gap-5 gap-5`}
                >
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      name="item_type"
                      id="item_type"
                      errorMessage={
                        values?.item_type?.toString() === "163"
                          ? "NOTE: Labor cannot be Lump Sum in Estimate or SOV. Labor Report will not calculate correctly if it is Lump Sum."
                          : formik.touched?.item_type && !values.item_type
                          ? errors.item_type
                          : ""
                      }
                      disabled={
                        isViewOnly ||
                        !itemTypeAndSaveItemToListField.show ||
                        itemTypeAndSaveItemToListField.disable
                      }
                      value={HTMLEntities.decode(
                        sanitizeString(values?.item_type?.toString())
                      )}
                      onChange={(value) => {
                        setFieldValue("item_type", value);

                        // const itemType = itemTypes?.find(
                        //   (i) => i?.value?.toString() === value?.toString()
                        // );

                        // if (isAddItem && !isMuPercentFieldChanged) {
                        //   setFieldValue("markup", itemType?.mark_up || "");
                        // }

                        const itemType = itemTypesWithMarkup?.find(
                          (i: IWorkOrderType) =>
                            i.type_id?.toString() === value?.toString()
                        );

                        setFieldValue("markup", itemType?.mark_up || "");
                        setFieldValue(
                          "unit",
                          value?.toString() === "163" ? "Hrs" : ""
                        );
                      }}
                      // onBlur={formik.handleBlur}
                      options={reorderedItemsTypes.map((item: GType) => ({
                        label: (
                          <div className="flex items-center gap-1.5">
                            <FontAwesomeIcon
                              icon={getItemTypeIcon({
                                type: item?.type_id?.toString(),
                              })}
                            />
                            {item?.name}
                          </div>
                        ),
                        value: item.type_id,
                        ...item,
                      }))}
                      notFoundContent={
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                        />
                      }
                    />
                  </div>
                  {!isItemDetailDrawer && (
                    <div className="w-full">
                      <ButtonField
                        label={_t("Assigned To")}
                        name="assigned_to"
                        labelPlacement="top"
                        // disabled={isViewOnly}
                        avatarProps={{
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(values?.assignee_name)
                            ),
                            image:
                              values?.assigned_to_contact_id == 0
                                ? values?.user_image
                                : "",
                          },
                        }}
                        value={HTMLEntities.decode(
                          sanitizeString(
                            values?.assigned_to != "0"
                              ? values?.assignee_name
                              : ""
                          )
                        )}
                        disabled={ParentIsReadOnly}
                        onClick={() => {
                          setIsOpenSelectAssignedTo(true);
                        }}
                        addonBefore={
                          values?.assigned_to && values?.assigned_to != "0" ? (
                            <div className="flex items-center gap-1">
                              <ContactDetailsButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setContactDetailDialogOpen(true);
                                }}
                              />
                              {/* <DirectoryFieldRedirectionIcon
                                directoryId={
                                  values?.assigned_to?.toString() || ""
                                }
                                directoryTypeKey={values?.type_key || ""}
                              /> */}
                            </div>
                          ) : (
                            <></>
                          )
                        }
                      />
                    </div>
                  )}
                </div>
                <div
                  className={`grid ${isAddItem ? "" : "md:grid-cols-2 gap-5"}`}
                >
                  <div className="w-full">
                    <SelectField
                      label={_t("Cost Code")}
                      labelPlacement="top"
                      value={
                        isItemDetailDrawer
                          ? values?.cost_code_name
                          : values?.cost_code_id
                          ? (() => {
                              const selectedOption = costCodeOptions?.find(
                                (item) =>
                                  values?.cost_code_id?.toString() ===
                                  item?.value?.toString()
                              );
                              return selectedOption
                                ? selectedOption
                                : [
                                    {
                                      label: HTMLEntities.decode(
                                        sanitizeString(
                                          `${values?.cost_code_name} (Archived)`
                                        )
                                      ),
                                      id: values?.cost_code_id,
                                    },
                                  ];
                            })()
                          : []
                      }
                      onChange={(value) => {
                        setFieldValue("cost_code_id", value);
                      }}
                      showSearch
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      options={costCodeOptions}
                      disabled={isItemDetailDrawer || ParentIsReadOnly}
                      allowClear={true}
                      onClear={() => {
                        setFieldValue("cost_code_id", "");
                        setFieldValue("cost_code", "");
                        setFieldValue("cost_code_name", "");
                      }}
                    />
                  </div>
                  {!isAddItem && (
                    <div className="w-full">
                      <SelectField
                        label={_t("Variation")}
                        labelPlacement="top"
                        value={
                          values?.variation_id && values?.variation_name
                            ? variantOptions?.filter((item) => {
                                return (
                                  values?.variation_id?.toString() ===
                                  item?.value?.toString()
                                );
                              })
                            : []
                        }
                        loading={variantloading}
                        onChange={(value) => {
                          setFieldValue("variation_id", value);
                          const selectedOption = variantOptions.find(
                            (item) => item.value === value
                          );
                          setFieldValue(
                            "variation_name",
                            selectedOption ? selectedOption.label : ""
                          );
                        }}
                        showSearch
                        filterOption={(input, option) =>
                          filterOptionBySubstring(
                            input,
                            option?.label as string
                          )
                        }
                        options={variantOptions as IOption[]}
                        disabled={isReadOnly}
                        allowClear={true}
                        onClear={() => {
                          setFieldValue("variation_id", null);
                          setFieldValue("variation_name", "");
                        }}
                      />
                    </div>
                  )}
                </div>
              </SidebarCardBorder>

              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("QTY")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName={`!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input`}
                        placeholder={_t("Item Quantity")}
                        disabled={isViewOnly}
                        onPaste={handlePaste}
                        errorMessage={errors.quantity}
                        // min={0}
                        // max={999999}
                        // maxLength={6}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ? "flex items-center justify-end" : ""
                        }
                        defaultValue={
                          isNaN(Number(values?.quantity)) ||
                          typeof values?.quantity === "undefined" ||
                          Number(values?.quantity) == 0
                            ? ""
                            : Number(values?.quantity)
                        }
                        value={
                          isNaN(Number(values?.quantity)) ||
                          typeof values?.quantity === "undefined" ||
                          Number(values?.quantity) == 0
                            ? ""
                            : Number(values?.quantity)
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits: 6,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <Typography className="text-13 block text-primary-900 dark:text-white/90">
                        {_t("Unit Cost/Unit")}
                      </Typography>
                      {showCostChangeTooltip && (
                        <Tooltip
                          title={_t(
                            "The Cost is different than the Cost defined within the Cost Items Database. Click to update here."
                          )}
                          placement="top"
                        >
                          <FontAwesomeIcon
                            className="ml-1 w-3.5 h-3.5 text-deep-orange-500 cursor-pointer"
                            icon="fa-solid fa-triangle-exclamation"
                            onClick={() => {
                              setCostChangeConfirmation(formData);
                            }}
                          />
                        </Tooltip>
                      )}
                    </div>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className={`text-right !text-[#008000] leading-[22px] font-semibold text-sm`}
                      >
                        {!isViewOnly && (
                          <>
                            {showUnitInputs ? (
                              <div className="flex gap-2">
                                <InputNumberField
                                  name="unit_cost"
                                  id="unit_cost"
                                  rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                  placeholder={_t("Item Unit Cost")}
                                  disabled={isViewOnly}
                                  // min={0}
                                  // max={999999}
                                  // maxLength={10}
                                  onPaste={handlePaste}
                                  autoFocus={Boolean(
                                    values?.unit_cost &&
                                      !isEmpty(values?.unit_cost) &&
                                      values?.unit &&
                                      !isEmpty(values?.unit)
                                  )}
                                  labelPlacement="left"
                                  errorMessage={errors.unit_cost}
                                  defaultValue={
                                    Number(values?.unit_cost) !== 0
                                      ? Number(values?.unit_cost)
                                      : ""
                                  }
                                  value={
                                    Number(values?.unit_cost) !== 0
                                      ? Number(values?.unit_cost)
                                      : ""
                                  }
                                  onChange={(value) => {
                                    setFieldValue(
                                      "unit_cost",
                                      value?.toString()
                                    );
                                  }}
                                  formatter={(value, info) => {
                                    return inputFormatter(value?.toString())
                                      .value;
                                  }}
                                  parser={(value) => {
                                    const inputValue = value
                                      ? unformatted(value.toString())
                                      : "";
                                    return inputValue;
                                  }}
                                  onKeyDown={(event) =>
                                    onKeyDownCurrency(event, {
                                      integerDigits: 10,
                                      decimalDigits: 2,
                                      unformatted,
                                      allowNegative: false,
                                      decimalSeparator:
                                        inputFormatter().decimal_separator,
                                    })
                                  }
                                  onBlur={handleFocusOut}
                                />
                                <div className="w-[62px]">
                                  {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      disabled={isViewOnly}
                                      labelPlacement="left"
                                      maxLength={15}
                                      value={values?.unit || null}
                                      iconView={true}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitData.map((type) => ({
                                          label: type.name?.toString(),
                                          value: type.name?.toString(),
                                        })) ?? []
                                      }
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      onChange={(value) => {
                                        setFieldValue("unit", value);
                                      }}
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onInputKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          const value =
                                            e?.currentTarget?.value?.trim();
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitData?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewTypeName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        }
                                      }}
                                      onClear={() => {
                                        setFieldValue("unit", "");
                                      }}
                                      errorMessage={errors.unit}
                                      onBlur={handleFocusOut}
                                    />
                                  ) : (
                                    <InputField
                                      className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                        !showUnitInputs && "!hidden"
                                      }`}
                                      placeholder={_t("Unit")}
                                      labelPlacement="left"
                                      errorMessage={errors.unit}
                                      maxLength={15}
                                      onBlur={handleFocusOut}
                                      value={values?.unit}
                                      disabled={isViewOnly}
                                      type="text"
                                      onChange={(e) => {
                                        setFieldValue("unit", e.target.value);
                                      }}
                                      onPaste={handlePaste}
                                      onPressEnter={handleEnterKeyPress}
                                    />
                                  )}
                                </div>
                              </div>
                            ) : (
                              <Typography
                                className="text-[#008000] cursor-pointer text-13 font-medium"
                                onClick={handleParagraphClick}
                                disabled={isViewOnly}
                              >
                                {
                                  formatter(
                                    formatAmount(
                                      Number(values?.unit_cost).toFixed(2)
                                    )
                                  ).value
                                }
                                /{values?.unit}
                              </Typography>
                            )}
                          </>
                        )}

                        {isViewOnly &&
                          (!isEmpty(values?.unit_cost) &&
                          values?.unit_cost !== 0.0 &&
                          values?.unit_cost !== "0.00" &&
                          !isEmpty(values?.unit) &&
                          !!values?.unit ? (
                            <Typography
                              className={`text-[#008000] font-medium text-13 ${
                                isViewOnly ? "cursor-no-drop" : ""
                              }`}
                            >
                              {values?.unit_cost}/{values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <InputField
                                ref={unitCostRef}
                                className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Item Unit Cost")}
                                type="number"
                                name="unit_cost"
                                id="unit_cost"
                                // maxLength={10}
                                // min={0}
                                // max={999999}
                                disabled={isViewOnly}
                                onPaste={handlePaste}
                                value={values?.unit_cost}
                                onChange={() => {}}
                                onPressEnter={handleEnterKeyPress}
                              />
                              <div className="w-[40px] min-w-[40px]">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  onPaste={handlePaste}
                                  name="unit"
                                  id="unit"
                                  disabled={isViewOnly}
                                  value={values?.unit}
                                  type="text"
                                  onChange={() => {}}
                                  onPressEnter={handleEnterKeyPress}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {values?.total?.toString() === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  Number(values?.total || 0).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>

                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
                <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          // value={markup ? markup : "markup_percent"}
                          value={markupType}
                          options={MARKUP_OPTIONS}
                          className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                          activeclassName="active:bg-[#ffffff]"
                          disabled={isViewOnly}
                          onChange={(e: RadioChangeEvent) => {
                            setMarkupType(e.target.value);
                            setFieldValue("markup", "");
                            setIsMuPercentFieldChanged(true);
                            if (e.target.value === "markup_percent") {
                              setFieldValue("is_markup_percentage", 1);
                            } else {
                              setFieldValue("is_markup_percentage", 0);
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${
                              formatter().currency_symbol
                            } -- Add the ${
                              formatter().currency_symbol
                            } amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <div className="sm:w-40 w-28">
                      <InputField
                        className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                        placeholder={
                          markupType === "markup_percent"
                            ? _t("Item Markup") + " %"
                            : _t("Total Sales Price")
                        }
                        onPaste={handlePaste}
                        onChange={(e) => {
                          const inputValue = e.target.value;
                          if (values?.markup === "0" && inputValue === "-") {
                            return;
                          }
                          if (
                            inputValue === "" ||
                            inputValue === "-" ||
                            !isNaN(Number(inputValue))
                          ) {
                            setFieldValue("markup", inputValue);
                          }
                        }}
                        value={values?.markup}
                        labelPlacement="left"
                        type="text"
                        disabled={isViewOnly}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits:
                              markupType === "markup_percent" ? 3 : 8,
                            decimalDigits:
                              markupType === "markup_percent" ? 0 : 2,
                            unformatted,
                            allowNegative: false,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                      />
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("Markup")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={true}
                      >
                        {formattedMarkup}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-equals"
                    />
                  </li>
                </ul>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Revenue ")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={true}
                      >
                        {mainTotal === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(Number(mainTotal || 0).toFixed(2))
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>

              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    required={false}
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    disabled={
                      isViewOnly &&
                      formData?.is_added_item !== 1 &&
                      !descEditable
                    }
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value?.trimStart());
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    onChange={(e) => {
                      setFieldValue(
                        "internal_notes",
                        e.target.value?.trimStart()
                      );
                    }}
                  />
                </div>
                {!isItemDetailDrawer && values.item_type && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={
                      !!values?.add_item_to_database ||
                      isValidId(values?.reference_item_id)
                    }
                    disabled={
                      !isAddItem &&
                      (isViewOnly ||
                        toBoolean(formData?.item_on_database) ||
                        isValidId(formData?.reference_item_id))
                      // (toBoolean(formData?.one_build_id) &&
                      //   !isValidId(formData?.reference_item_id)))
                    }
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("add_item_to_database", valueToSet);
                    }}
                  >
                    {_t(`Save this item into my ${saveItemKey} Items list?`)}
                  </CheckBox>
                )}
                {!isItemDetailDrawer && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={!!values?.is_optional_item}
                    disabled={isViewOnly}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("is_optional_item", valueToSet);
                    }}
                  >
                    {_t("Make Item Optional")}
                  </CheckBox>
                )}
                {!isItemDetailDrawer && (
                  <CheckBox
                    disabled={isViewOnly}
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={!!values?.apply_global_tax}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("apply_global_tax", valueToSet);
                    }}
                  >
                    {_t("Collect Tax on this Item?")}
                  </CheckBox>
                )}
                {/* {values?.item_type && (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={!!values?.add_item_to_database}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("add_item_to_database", valueToSet);
                    }}
                    disabled={
                      isViewOnly ||
                      !itemTypeAndSaveItemToListField.show ||
                      itemTypeAndSaveItemToListField.disable
                    }
                  >
                    {_t(`Save this item into my ${saveItemKey} Items list?`)}
                  </CheckBox>
                )} */}
              </SidebarCardBorder>
            </div>
          </div>
          {((!isItemDetailDrawer && isAddItem) || descEditable) && (
            <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                disabled={isSubmitting || ParentIsReadOnly}
                isLoading={submitAction === "save_n_close" && isSubmitting}
                buttonText={_t("Save & Close")}
                onClick={() => handleSaveItem("save_n_close")}
              />
              {!isItemDetailDrawer && isAddItem && (
                <PrimaryButton
                  htmlType="submit"
                  buttonText={_t("Save & Add Another Item")}
                  disabled={isSubmitting || ParentIsReadOnly}
                  isLoading={
                    submitAction === "save_n_add_another" && isSubmitting
                  }
                  onClick={() => {
                    handleSaveItem("save_n_add_another");
                    setSubmitAction("save_n_add_another"); // Set the submit action
                  }}
                />
              )}
            </div>
          )}
          {isAddItem === false && (
            <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                buttonText={_t("Save & Close")}
                onClick={() => handleSaveItem("save_n_close")}
                isLoading={
                  submitAction === "save_n_close" && formik.isSubmitting
                }
                disabled={
                  isSubmitting || ParentIsReadOnly
                  // ||isReadOnly
                }
              />
              {!isItemDetailDrawer && (
                <PrimaryButton
                  htmlType="submit"
                  buttonText={_t("Save & Add Another Item")}
                  disabled={isSubmitting || isReadOnly || ParentIsReadOnly}
                  isLoading={
                    submitAction === "save_n_add_another" && isSubmitting
                  }
                  onClick={() => handleSaveItem("save_n_add_another")}
                />
              )}
            </div>
          )}
        </Form>
      </Drawer>

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            if (data?.[0]) {
              // const assigneeName = `${data[0].display_name} ${
              //   (data as CustomerEmail[])[0]?.company_name
              //     ? `(${(data as CustomerEmail[])[0]?.company_name})`
              //     : ""
              // }`;
              // if (data?.[0].contact_id == "0") {
              setFieldValue("assigned_to_contact_id", data[0].contact_id);
              // }
              setFieldValue("assigned_to", data[0].user_id);
              setFieldValue("assignee_name", data[0].display_name);
              setFieldValue(
                "assignee_type",
                data[0].type ||
                  getDirectaryIdByKey(
                    data[0].type_key as CustomerTabs,
                    undefined
                  )
              );
              setFieldValue("type_key", data[0].type_key);
              setFieldValue("type_name", data[0].type_name);
              setFieldValue(
                "user_image",
                data?.[0]?.contact_id == 0 ? data?.[0]?.image : ""
              );
            } else {
              setFieldValue("assigned_to", 0);
              setFieldValue("assignee_name", "");
              setFieldValue("assigned_to_contact_id", 0);
            }
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.misc_contact_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={assignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={details?.project_id as number}
          activeTab={CFConfig.contractor_key}
        />
      )}
      {contactDetailDialogOpen && (
        <ContactDetails
          isOpenContact={contactDetailDialogOpen}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isReadOnly}
          additional_contact_id={values?.assigned_to_contact_id}
        />
      )}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          CFConfig.employee_key,
          "my_crew",
          CFConfig.customer_key,
          CFConfig.lead_key,
          CFConfig.contractor_key,
          CFConfig.vendor_key,
          CFConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
      <ConfirmModal
        isOpen={Boolean(
          costchangeConfirmOpen !== null &&
            costchangeConfirmOpen?.item_id &&
            !isReadOnly
        )}
        modaltitle={_t("Confirmation")}
        isLoading={loading}
        //  isLoading={isLoadingCheckBox?.[costchangeConfirmOpen?.item_id]}
        description={`The item price in the Estimate does not match the price in your Cost Items Database. Do you want to import the current price of ${
          formatter(
            formatAmount(
              (
                +(costchangeConfirmOpen?.updated_unit_cost ?? "0") / 100
              )?.toFixed(2)
            )
          ).value_with_symbol
        }?`}
        onAccept={async () => {
          setLoading(true);
          if (
            costchangeConfirmOpen &&
            costchangeConfirmOpen?.item_id &&
            estimateDetail?.estimate_id
          ) {
            const updatedItem: Partial<ESEstimateItem> = {
              unit_cost: `${Number(costchangeConfirmOpen?.updated_unit_cost)}`,
            };

            updatedItem["total"] = itemTotalCalculator(
              {
                ...costchangeConfirmOpen,
                ...updatedItem,
              },
              true
              // !!costchangeConfirmOpen?.hidden_markup
            );

            const apiRes = await dispatch(
              updateEstimateItems({
                estimate_id: estimateDetail?.estimate_id,
                items: [
                  {
                    ...updatedItem,
                    section_id: costchangeConfirmOpen?.section_id,
                    item_id: costchangeConfirmOpen?.item_id,
                  },
                ],
              })
            );

            const response = apiRes?.payload as IEstimatesUpdateItemsApiRes;
            if (response?.success) {
              const updatedItemAPI = response?.data?.[0] ?? updatedItem;
              await dispatch(
                updateItem({
                  sectionId: costchangeConfirmOpen?.section_id,
                  itemId: costchangeConfirmOpen?.item_id,
                  updatedItem: updatedItemAPI,
                })
              );
              setEstimateItemToView?.({});
              if (items?.[currentItemIndex]) {
                setEstimateItemToView?.({
                  ...(items?.[Number(currentItemIndex)] ?? {}),
                  ...updatedItemAPI,
                });
                // setItemOpen(true)
              }
              // setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
            } else {
              notification.error({
                description: response?.message,
              });
              // setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
            }
          }
          setLoading(false);
          setCostChangeConfirmation(null);
        }}
        onDecline={() => setCostChangeConfirmation(null)}
        onCloseModal={() => setCostChangeConfirmation(null)}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-file-check"
      />

      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}

      {isValidId(cidbItemExistPopupData?.data?.reference_item_id) && (
        <ConfirmModal
          isOpen={isValidId(cidbItemExistPopupData?.data?.reference_item_id)}
          modaltitle={_t("This item already exists")}
          description={
            cidbItemExistPopupData?.message ??
            `There is already an item associated with this name in your CIDB. Would you like to rename the current item or import the existing item from your CIDB?`
          }
          onAccept={() => {
            setFieldValue(
              "reference_item_id",
              cidbItemExistPopupData?.data?.reference_item_id
            );
            setFieldValue("add_item_to_database", 0);
            setCidbItemExistPopupData(null);
            handleSubmit();
          }}
          yesButtonLabel="Use Existing"
          noButtonLabel="Rename"
          onDecline={() => setCidbItemExistPopupData(null)}
          onCloseModal={() => setCidbItemExistPopupData(null)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-trash-can"
        />
      )}
    </>
  );
};

export default ESSectionItem;
