import { useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
// Organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Hook
import { useTranslation } from "~/hook";
// Other
import {
  useWoAppSelector,
  useWoAppDispatch,
} from "~/modules/projectManagement/pages/workorder/redux/store";
import { getGConfig, useGModules } from "~/zustand";
import {
  workorderDetailsField,
  workorderfieldStatus,
} from "../../../utils/constasnts";
import { defaultConfig } from "~/data";
import { Number, sanitizeString } from "~/helpers/helper";
import {
  generateHttpsLink,
  getStatusForField,
  websiteRegex,
} from "~/shared/utils/helper/common";
import delay from "lodash/delay";
import {
  updateWorkOrderDetailApi,
  fetchWorkorderDetails,
} from "../../../redux/action/workorderDetailsAction";
import { useParams } from "@remix-run/react";
import { updateWorkorderDetail } from "../../../redux/slices/workorderDetailsSlice";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import type { InputRef } from "antd";

const WorkorderSiteDetails = () => {
  const { _t } = useTranslation();
  const { module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const [inputValues, setInputValues] = useState<IWorkorderDetails>(
    workorderDetailsField
  );
  const gConfig: GConfig = getGConfig();
  const params = useParams();

  const { details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );

  const inputURLRef = useRef<InputRef>(null);
  const [isURLEditing, setIsURLEditing] = useState<boolean>(false);

  const [isOpenSelectIssuedBy, setIsOpenSelectIssuedBy] =
    useState<boolean>(false);
  const [isOpenSelectApprovedBy, setIsOpenSelectApprovedBy] =
    useState<boolean>(false);
  const [isOpenSelectBillTo, setIsOpenSelectBillTo] = useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [assignedTo, setAssignedTo] = useState<TselectedContactSendMail[]>([]);
  const [isDatePVisible, setIsDatePVisible] = useState<boolean>(false);
  const [dateBeingChanged, setDateBeingChanged] = useState<boolean>(false);
  const [isEndDatePickerOpened, setIsEndDatePickerOpened] =
    useState<boolean>(false);

  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(workorderfieldStatus);
  const [additionalContact, setAdditionContact] = useState<number>(0);

  const dispatch = useWoAppDispatch();
  const dtDivRef = useRef<HTMLLIElement>(null);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const handleDateFieldOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;
    const targetElement = document.querySelector(".ant-picker-dropdown");

    if (targetElement && targetElement.contains(clickedElement)) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg" &&
      !isEndDatePickerOpened
    ) {
      setIsDatePVisible(false);
      return;
    }

    if (
      dtDivRef.current &&
      !dtDivRef.current.contains(event.target as Node) &&
      !dateBeingChanged
    ) {
      setIsDatePVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleDateFieldOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleDateFieldOutsideClick);
    };
  }, []);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleIssuedBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (data?.user_id != inputValues.issued_by) {
        setInputValues({
          ...inputValues,
          issued_by: data?.user_id,
          issued_by_name: data?.display_name,
          issued_by_type:
            data?.orig_type?.toString() ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          issued_by: data.user_id,
          issued_by_contact: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "issued_by",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          issued_by: details.issued_by,
          issued_by_name: details.issued_by_name,
        });
      }
    } else {
      handleUpdateField({ issued_by: "" });
      setInputValues({
        ...inputValues,
        issued_by: "",
        issued_by_name: "",
      });
    }
  };

  const handleBilledTo = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (
        data?.user_id?.toString() != details.billed_to ||
        data?.contact_id?.toString() !== details.billed_to_contact
      ) {
        setInputValues({
          ...inputValues,
          billed_to: data?.user_id,
          billed_to_name:
            !!data.contact_id &&
            data.company_name &&
            !data.display_name?.includes(data.company_name)
              ? `${data.display_name} (${data.company_name})`
              : data?.display_name,
          billed_to_contact: data?.contact_id,
          billed_to_dir_type:
            data?.orig_type ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          billed_to: data?.user_id?.toString(),
          billed_to_contact: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "billed_to",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          billed_to: details.billed_to,
          billed_to_name: details.billed_to_name,
          billed_to_contact: details.billed_to_contact,
        });
      }
    } else {
      handleUpdateField({
        billed_to: "",
        billed_to_contact: 0,
      });
      setInputValues({
        ...inputValues,
        billed_to: "",
        billed_to_name: "",
      });
    }
  };

  const handleApprovedBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (
        data?.user_id?.toString() != details.approved_by ||
        data?.contact_id?.toString() !== details.approved_by_contact_id
      ) {
        setInputValues({
          ...inputValues,
          approved_by: data?.user_id,
          approved_by_name:
            !!data.contact_id &&
            data.company_name &&
            !data.display_name?.includes(data.company_name)
              ? `${data.display_name} (${data.company_name})`
              : data?.display_name,
          approved_by_contact_id: data?.contact_id,
          approved_by_dir_type:
            data?.orig_type ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          approved_by: data?.user_id?.toString(),
          approved_by_contact_id: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "approved_by",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          approved_by: details.approved_by,
          approved_by_name: details.approved_by_name,
          approved_by_contact_id: details.approved_by_contact_id,
        });
      }
    } else {
      handleUpdateField({
        approved_by: "",
        approved_by_contact_id: 0,
      });
      setInputValues({
        ...inputValues,
        approved_by: "",
        approved_by_name: "",
      });
    }
  };

  const handleAssignedTo = (data: TselectedContactSendMail[]) => {
    if (data?.length !== 0) {
      const assignedToArray = data.map((user) => {
        return {
          dir_type:
            user?.orig_type?.toString() ||
            getDirectaryIdByKey(user.type_key as CustomerTabs, gConfig),
          dir_type_name: user.type_name,
          directory_id:
            user.contact_id?.toString() === "0"
              ? user.user_id
              : user.contact_id,
          display_name: user.display_name,
          user_id: user.user_id,
        };
      });
      setInputValues({
        ...inputValues,
        assigned_to_arr: assignedToArray,
        assigned_to: data?.map((user) => user.user_id).join(","),
      });
      handleUpdateField({
        assigned_to: data?.map((user) => user.user_id).join(","),
      });
    } else {
      handleUpdateField({
        assigned_to: "",
      });
      handleChangeFieldStatus({
        field: "assigned_to",
        status: "button",
        action: "BLUR",
      });
      setInputValues({
        ...inputValues,
        assigned_to: "",
        assigned_to_arr: [],
      });
    }
  };

  const handleInpOnChange = ({
    target: { name, value },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleUpdateField = async (data: IWorkorderDetails) => {
    const field = Object.keys(data)[0] as keyof IWorkorderDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateWorkOrderDetailApi({
      id: params.id,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateWorkorderDetail(data));
      dispatch(fetchWorkorderDetails({ id: params.id, shouldLoading: false }));
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 1000);
  };

  useEffect(() => {
    const woDetails = {
      ...details,
      response_name:
        details.response_name === "Accept"
          ? "Accepted"
          : details.response_name === "Reject"
          ? "Rejected"
          : details.response_name,
    };
    setInputValues(woDetails);
  }, []);

  useEffect(() => {
    if (inputValues?.assigned_to_arr?.length !== 0) {
      const assigned_to = inputValues?.assigned_to_arr?.map((assignee) => {
        const supplierKey: string = getDirectaryKeyById(
          Number(assignee?.dir_type),
          gConfig
        );
        return {
          display_name:
            HTMLEntities.decode(
              sanitizeString(assignee?.display_name?.trim())
            ) ?? "",
          type: Number(assignee.dir_type),
          type_key: supplierKey,
          user_id: Number(assignee.user_id),
        };
      });
      setAssignedTo(assigned_to as TselectedContactSendMail[]);
    }
  }, [inputValues?.assigned_to_arr]);

  const handleWSIntClick = () => {
    if (isReadOnly) {
      return false;
    }
    setIsURLEditing(true);
    setTimeout(() => inputURLRef?.current?.focus(), 0); // Ensure the input is focused after rendering
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Site Details")}
        iconProps={{
          icon: "fa-solid fa-memo",
          containerClassName:
            "bg-[linear-gradient(180deg,#B974FF1a_0%,#8308FF1a_100%)]",
          id: "site_details_icon",
          colors: ["#B974FF", "#8308FF"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <InputField
                  label={_t("Location")}
                  placeholder={_t("Location")}
                  name="location"
                  labelPlacement="left"
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.location)
                  )}
                  editInline={true}
                  iconView={true}
                  onChange={handleInpOnChange} // change this in future ( temporary resolve type issue )
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "location")}
                  maxLength={200}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "location",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "location",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "location",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.location) {
                      handleUpdateField({ location: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "location",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        location: details.location,
                      });
                    }
                  }}
                />
              </li>
              <li>
                <InputField
                  label={_t("Drawing")}
                  placeholder={_t("Drawing")}
                  name="drawing"
                  labelPlacement="left"
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.drawing)
                  )}
                  editInline={true}
                  iconView={true}
                  onChange={handleInpOnChange} // change this in future ( temporary resolve type issue )
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "drawing")}
                  maxLength={200}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "drawing",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "drawing",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "drawing",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.drawing) {
                      handleUpdateField({ drawing: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "drawing",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        drawing: details.drawing,
                      });
                    }
                  }}
                />
              </li>
              <li>
                <InputField
                  label={_t("Type")}
                  placeholder={_t("Type")}
                  name="type"
                  value={HTMLEntities.decode(sanitizeString(inputValues.type))}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  onChange={handleInpOnChange}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "type")}
                  maxLength={20}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "type",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "type",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "type",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.type) {
                      handleUpdateField({ type: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "type",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        type: details.type,
                      });
                    }
                  }}
                />
              </li>
              <li>
                <InputField
                  label={_t("Page")}
                  placeholder={_t("Page")}
                  name="page"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  value={inputValues?.page}
                  readOnly={isReadOnly}
                  onChange={handleInpOnChange}
                  fixStatus={getStatusForField(loadingStatus, "page")}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "page",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "page",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "page",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.page) {
                      handleUpdateField({ page: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "page",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        page: details.page,
                      });
                    }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <InlineField
                  label={_t("URL")}
                  labelPlacement="left"
                  field={
                    <div
                      className={`flex items-center gap-1 overflow-hidden ${
                        isReadOnly ? "" : "focus-within:w-full hover:w-full"
                      } ${
                        !inputValues?.url?.trim() ? "w-full" : "sm:w-fit w-full"
                      }`}
                    >
                      {isURLEditing ? (
                        <InputField
                          ref={inputURLRef}
                          value={inputValues?.url}
                          name="url"
                          placeholder={_t("https://abc.com")}
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          readOnly={isReadOnly}
                          fixStatus={getStatusForField(loadingStatus, "url")}
                          onFocus={() =>
                            handleChangeFieldStatus({
                              field: "url",
                              status: "save",
                              action: "FOCUS",
                            })
                          }
                          onChange={handleInpOnChange}
                          onBlur={(e) => {
                            setIsURLEditing(false);
                            const value = e?.target?.value.trim();

                            if (value !== details?.url) {
                              if (
                                !websiteRegex.websiteRegularExpression.test(
                                  value
                                ) &&
                                value
                              ) {
                                notification.error({
                                  description: _t("Enter valid URL"),
                                });
                                setInputValues({
                                  ...inputValues,
                                  url: details.url,
                                });
                                return false;
                              }
                              handleUpdateField({ url: value });
                            } else {
                              handleChangeFieldStatus({
                                field: "url",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                url: details.url,
                              });
                            }
                          }}
                        />
                      ) : (
                        <div
                          className={`${
                            isReadOnly
                              ? "cursor-default"
                              : "focus-within:w-full hover:w-full cursor-text"
                          } ${
                            !inputValues?.url?.trim()
                              ? "w-full"
                              : "max-w-[calc(100%-28px)] sm:w-fit w-full"
                          }`}
                        >
                          <div
                            className={`text-sm text-primary-900 px-1.5 flex justify-between items-center h-[34px] ${
                              !isReadOnly &&
                              "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] focus-within:bg-[#f4f5f6]"
                            } `}
                            tabIndex={0}
                            onFocus={handleWSIntClick}
                            onMouseEnter={() => {
                              if (isReadOnly) {
                                return false;
                              }
                              handleChangeFieldStatus({
                                field: "url",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeave={() => {
                              if (isReadOnly) {
                                return false;
                              }
                              handleChangeFieldStatus({
                                field: "url",
                                status: "button",
                                action: "ML",
                              });
                            }}
                          >
                            <Tooltip title={inputValues.url} placement="top">
                              <Typography
                                className={
                                  inputValues?.url?.trim()
                                    ? "truncate text-primary-900 block"
                                    : "text-[#bdbdbd] tracking-[0.3px] font-light"
                                }
                              >
                                {inputValues?.url?.trim() ||
                                  _t("https://abc.com")}
                              </Typography>
                            </Tooltip>
                            <FieldStatus
                              className="ml-2"
                              status={getStatusForField(loadingStatus, "url")}
                            />
                          </div>
                        </div>
                      )}
                      <a
                        href={generateHttpsLink(inputValues.url || "#")}
                        target="_blank"
                        className={`py-px focus-within:bg-[#f0f0f0] rounded focus-visible:outline-none hover:!bg-[#f0f0f0] ${
                          inputValues?.url?.trim() ? "" : "hidden"
                        }`}
                        onClick={(e) => {
                          e.currentTarget.blur(); // Remove focus after click
                        }}
                      >
                        <Tooltip
                          title={_t("Open Link in a new tab.")}
                          placement="top"
                        >
                          <div className="!w-6 !h-6 cursor-pointer flex items-center justify-center ">
                            <FontAwesomeIcon
                              icon="fa-regular fa-arrow-up-right-from-square"
                              className="w-3 h-3 text-primary-900/80 group-hover/buttonHover:text-primary-900"
                            />
                          </div>
                        </Tooltip>
                      </a>
                    </div>
                  }
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Description")}
                  placeholder={_t("Work Order Description")}
                  name="description"
                  labelPlacement="left"
                  disabled={isReadOnly}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.description)
                  )}
                  editInline={true}
                  iconView={true}
                  onChange={(e) => {
                    setInputValues({
                      ...inputValues,
                      [e.target.name]: e.target.value,
                    });
                  }}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "description")}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "description",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "description",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "description",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.description) {
                      handleUpdateField({ description: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "description",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        description: details.description,
                      });
                    }
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />

      {isOpenSelectIssuedBy && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectIssuedBy}
          closeDrawer={() => {
            setIsOpenSelectIssuedBy(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleIssuedBy(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          selectedCustomer={
            inputValues.issued_by && inputValues?.issued_by !== ""
              ? ([
                  {
                    display_name: inputValues?.issued_by_name,
                    user_id: inputValues?.issued_by,
                    type: inputValues.issued_by_type,
                    type_key: getDirectaryKeyById(
                      inputValues.issued_by_type === 1
                        ? 2
                        : Number(inputValues.issued_by_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
          additionalContactDetails={0}
        />
      )}

      {isOpenSelectBillTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectBillTo}
          closeDrawer={() => {
            setIsOpenSelectBillTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleBilledTo(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          additionalContactDetails={1}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.billed_to && inputValues?.billed_to !== ""
              ? ([
                  {
                    display_name: inputValues?.billed_to_name,
                    user_id: inputValues?.billed_to,
                    contact_id: inputValues?.billed_to_contact,
                    type: inputValues.billed_to_dir_type,
                    type_key: getDirectaryKeyById(
                      inputValues.billed_to_dir_type === 1
                        ? 2
                        : Number(inputValues.billed_to_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
        />
      )}

      {isOpenSelectApprovedBy && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectApprovedBy}
          closeDrawer={() => {
            setIsOpenSelectApprovedBy(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleApprovedBy(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.approved_by && inputValues?.approved_by !== ""
              ? ([
                  {
                    display_name: inputValues?.approved_by_name,
                    user_id: inputValues?.approved_by,
                    contact_id: inputValues?.approved_by_contact_id,
                    type: inputValues.approved_by_dir_type,
                    type_key: getDirectaryKeyById(
                      inputValues.approved_by_dir_type === 1
                        ? 2
                        : Number(inputValues.approved_by_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
        />
      )}

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={false}
          setCustomer={(data) => {
            handleAssignedTo(data);
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.contractor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.assigned_to_arr &&
            inputValues.assigned_to_arr.length !== 0
              ? assignedTo
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
        />
      )}

      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => {
            setcontactId(0);
            setIsContactDetails(false);
          }}
          contactId={contactId}
          readOnly={isReadOnly}
          additional_contact_id={additionalContact}
        />
      )}
    </>
  );
};

export default WorkorderSiteDetails;
