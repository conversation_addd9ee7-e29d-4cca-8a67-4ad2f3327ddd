import { useState, useEffect } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { InvoiceFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/invoiceFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import { getDefaultStatuscolor, Number } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { useParams } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { getGConfig } from "~/zustand";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { sendMessageKeys } from "~/components/page/$url/data";

const InvoicesTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { getGlobalModuleByKey } = useGlobalModule();
  const invoiceModule = getGlobalModuleByKey(CFConfig.invoice_merge_module_key);
  const [isShowingMore, setIsShowingMore] = useState(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const invoices = financialData?.invoices ?? [];
  const { id } = useParams();
  const [allInvoices, setAllInvoices] = useState<IProjectFilesData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const displayedInvoices = isShowingMore
    ? allInvoices
    : allInvoices?.slice(0, dataLimit);
  const totalCount = Number(
    financialData?.invoices_count?.[0]?.number_of_invoice ?? 0
  );
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const totalAmount = Number(financialData?.invoices_count?.[0]?.total ?? 0);

  const [collapse, setCollapse] = useState<string[]>([]);

  useEffect(() => {
    if (isInitialLoad) {
      setAllInvoices(invoices);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(invoices?.map((inv) => [inv?.invoice_id, inv]));

    const mergedInvoices = allInvoices?.map((existing) => {
      const updated = updatedMap?.get(existing?.invoice_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(allInvoices?.map((inv) => inv?.invoice_id));
    const newInvoices = invoices?.filter(
      (inv) => !existingIds?.has(inv?.invoice_id)
    );

    const nextAll = [...mergedInvoices, ...newInvoices];

    const hasChanged =
      nextAll?.length !== allInvoices?.length ||
      nextAll?.some(
        (inv, i) => JSON.stringify(inv) !== JSON.stringify(allInvoices[i])
      );

    if (hasChanged) {
      setAllInvoices(nextAll);
    }
  }, [invoices, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "invoices") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allInvoices.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["invoice_merge"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "prefix_company_invoice_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const InvId = `Inv. #${value}`;
        return value ? (
          <Tooltip title={InvId}>
            <Typography className="table-tooltip-text">{InvId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "invoice_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: _t("Due Date"),
      field: "due_date",
      minWidth: 320,
      flex: 1,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: _t("Status"),
      field: "approval_type_name",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellRenderer: ({ data }: { data: Invoices }) => {
        const status = data.approval_type_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.status_color || ""
        );

        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <div className="table-tooltip-text text-center">-</div>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formattedValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Balance"),
      field: "paymentTotal",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: { data: Invoices }) => {
        const formattedValue = formatter(
          Number(data.total) !== 0 || Number(data.paymentTotal) !== 0
            ? ((Number(data.total) - Number(data.paymentTotal)) / 100)?.toFixed(
                2
              )
            : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "invoice_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: Invoices }) => (
        <div className="flex items-center gap-1.5 justify-center">
          <ButtonWithTooltip
            tooltipTitle={_t("View")}
            tooltipPlacement="top"
            icon="fa-solid fa-eye"
            onClick={async () => {
              let tempAuthorization = authorization;
              const isExpired = isExpiredAuthorization();
              if (isExpired) {
                const response = (await webWorkerApi({
                  url: "/api/auth/token",
                })) as IGetTokenFromNode;
                if (response.success) {
                  tempAuthorization = response.data.accessToken;
                  setAuthorizationExpired(response.data.accessTokenExpired);
                }
              }
              const newURL = new URL(
                routes.MANAGE_INVOICE.url +
                  "/" +
                  (data.invoice_id?.toString() || ""),
                window.location.origin
              );
              newURL.searchParams.set("authorize_token", tempAuthorization);
              newURL.searchParams.set("iframecall", "1");
              newURL.searchParams.set("from_remix", "1");
              setIframeData({
                url: newURL.toString(),
                title: String(id),
              });
            }}
          />
          <InvoiceFieldRedirectionIcon
            iconClassName="!w-3.5 !h-3.5"
            invoiceId={data.invoice_id?.toString() ?? ""}
          />
        </div>
      ),
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(invoiceModule?.plural_name ?? "Invoices")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(invoiceModule?.module_name ?? "Invoices")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_INVOICE.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_INVOICE.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={displayedInvoices}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-invoices.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["invoice_merge", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, [
                "invoice_merge",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default InvoicesTable;
