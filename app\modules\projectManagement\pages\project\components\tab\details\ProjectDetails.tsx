import { useMemo, useEffect, useRef, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// Other
import { ADD_PROJECT_TYPE, initialLatLong } from "../../../utils/constants";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { customDataTypesByKey } from "~/utils/constasnts";
import { sanitizeString } from "~/helpers/helper";
import { addCustomData } from "~/redux/action/customDataAction";
import { addCustomProjectType } from "../../../redux/slices/proDashWidgetsSlice";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

const ProjectDetails = () => {
  const { _t } = useTranslation();

  const dispatch = useAppProDispatch();
  const { projectTypes } = useAppProSelector((state) => state.proDashboard);
  const { details } = useAppProSelector((state) => state.proDetails);

  const [viewUnit, setViewUnit] = useState<boolean>(false);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [locationLatLong, setLocationLatLong] =
    useState<ILatLongItf>(initialLatLong);
  const addressContainerRef = useRef<HTMLDivElement | null>(null);

  const initialValues: Partial<IProjectDetails> = useMemo(
    () => ({
      address1: details.address1 || "",
      address2: details.address2 || "",
      city: details.city || "",
      state: details.state || "",
      zip: details.zip || "",
    }),
    [details]
  );
  const [inputValues, setInputValues] =
    useState<Partial<IProjectDetails>>(initialValues);

  const projectTypesOptions = useMemo(() => {
    if (!projectTypes?.length) return [];

    let projectTypesOpts = projectTypes
      .filter((item) => Number(item.is_status) === 0)
      .map((item) => {
        return {
          label: HTMLEntities.decode(sanitizeString(item.name)),
          value: item.key,
        };
      });

    const isTypeArchived = projectTypes.find(
      (item) => item.key.toString() === details.project_type?.toString()
    );

    if (!isTypeArchived) {
      projectTypesOpts = [
        ...projectTypesOpts,
        {
          label: `${HTMLEntities.decode(
            sanitizeString(details?.project_type_name)
          )} (Archived)`,
          value: details.project_type as string,
        },
      ];
    }

    return projectTypesOpts;
  }, [projectTypes, details.project_type, details?.project_type_name]);

  useEffect(() => {
    setInputValues((prev: Partial<IProjectDetails>) => ({
      ...prev,
      address1: details.address1 || "",
      address2: details.address2 || "",
      city: details.city || "",
      state: details.state || "",
      zip: details.zip || "",
    }));
  }, [details, viewUnit]);

  useEffect(() => {
    if (addressContainerRef?.current) {
      const mouseEventHandler = (e: MouseEvent) => {
        if (!addressContainerRef?.current?.contains(e.target as Node)) {
          setViewUnit(false);
        }
      };
      window.addEventListener("mousedown", mouseEventHandler);
      return () => {
        window.removeEventListener("mousedown", mouseEventHandler);
      };
    }
  }, [addressContainerRef]);

  const handleAddProjectType = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        projectTypesOptions || []
      );
      if (newType) {
        setCustomDataAdd({
          itemType: customDataTypesByKey.projectTypeKeyID,
          name: HTMLEntities.encode(event?.currentTarget?.value),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const {
    handleUpdateField,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    onFocusUpdateFieldStatus,
    updateInputFieldOnBlur,
    onBlurUpdateFieldStatus,
    onChangeInputField,
    loadingStatus,
    isReadOnly,
    inputVals,
    setInputVals,
    updateDataInStore,
  } = useProjectDetail();

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addCustomProjectType(cDataRes?.data));
        delay(() => {
          updateInputFieldOnBlur({
            field: "project_type",
            value: cDataRes?.data?.item_id,
            message: "Project Type field is required.",
            required: true,
          });
        }, 500);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  const fullAddress = useMemo(() => {
    return `${
      inputVals.address1 !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.address1)) + "\n"
        : ""
    }${
      inputVals.address2 !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.address2)) + "\n"
        : ""
    }${
      inputVals.city !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.city)) + ", "
        : ""
    }${
      inputVals.state !== ""
        ? HTMLEntities.decode(sanitizeString(inputVals.state)) + " "
        : ""
    }${inputVals.zip !== "" ? inputVals.zip : ""}`;
  }, [inputVals]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleSelectedLocation = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });

    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const address1 = `${streetNumber}${getAddressComponent(place, "route")}`;
    const address2 = "";
    const city =
      getAddressComponent(place, "locality") ||
      getAddressComponent(place, "sublocality");
    const getState = getAddressComponent(place, "administrative_area_level_1");
    const zip = getAddressComponent(place, "postal_code");
    if (
      address1?.trim() === details.address1?.trim() &&
      address2?.trim() === details.address2?.trim() &&
      getState?.trim() === details.state?.trim() &&
      city?.trim() === details.city?.trim() &&
      zip?.trim() === details.zip?.trim()
    ) {
      return;
    }
    await handleUpdateField({
      data_to_send_in_api: {
        address1: address1,
        address2: address2,
        city: city,
        state: getState,
        zip: zip,
      },
      data_to_update_in_store: {
        address1: address1,
        address2: address2,
        city: city,
        state: getState,
        zip: zip,
      },
    });
  };

  const normalize = (val?: string | null) => (val ?? "").trim();

  const handleInputBlur = async (data?: boolean) => {
    if (viewUnit && data != true) {
      return false;
    }
    const updateValues: Partial<IProjectDetails> = {
      ...inputValues,
    };

    if (
      inputValues &&
      normalize(inputValues.address1) === normalize(details.address1) &&
      normalize(inputValues.address2) === normalize(details.address2) &&
      normalize(inputValues.state) === normalize(details.state) &&
      normalize(inputValues.city) === normalize(details.city) &&
      normalize(inputValues.zip) === normalize(details.zip)
    ) {
      return;
    }

    await handleUpdateField({
      data_to_update_in_store: updateValues,
      data_to_send_in_api: updateValues,
    });
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Project Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#9BC3FF1a_0%,#418CFD1a_100%)]",
          id: "project_details_icon",
          colors: ["#9BC3FF", "#418CFD "],
        }}
        children={
          <div className="pt-2 flex flex-col gap-1">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <SelectField
                  label={_t("Project Type")}
                  formInputClassName={
                    details.address1 ||
                    details.address2 ||
                    details.city ||
                    details.state ||
                    details.zip
                      ? "2xl:w-[calc(100%-308px)]"
                      : ""
                  }
                  placeholder={_t("Select Project Type")}
                  name="project_type"
                  id="project_type"
                  labelPlacement="left"
                  labelClass="sm:w-[100px] sm:max-w-[100px]"
                  showSearch
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  addItem={ADD_PROJECT_TYPE}
                  options={projectTypesOptions}
                  allowClear={false}
                  onInputKeyDown={(e) => handleAddProjectType(e)}
                  value={
                    details.project_type
                      ? projectTypesOptions.filter((item) => {
                          return (
                            details?.project_type?.toString() ===
                            item?.value?.toString()
                          );
                        })
                      : []
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onSelect={(value) => {
                    if (value !== details?.project_type) {
                      updateInputFieldOnBlur({
                        field: "project_type",
                        value,
                        message: "Project Type field is required.",
                        required: true,
                      });
                    } else {
                      updateDataInStore({
                        project_type: details.project_type,
                      });
                      onBlurUpdateFieldStatus({
                        field: "project_type",
                      });
                    }
                  }}
                  fixStatus={getStatusForField(loadingStatus, "project_type")}
                />
              </li>
              <li>
                <InlineField
                  label={_t("Address")}
                  labelPlacement="left"
                  labelClass="sm:w-[100px] sm:max-w-[100px]"
                  field={
                    <ul
                      className={`flex items-start w-full ${
                        viewUnit ? "" : "2xl:flex-row flex-col gap-2"
                      }`}
                    >
                      <li
                        className={`w-full gap-1 flex justify-between p-1.5 pt-2 ${
                          isReadOnly
                            ? "cursor-default sm:bg-transparent bg-[#f4f5f6]"
                            : "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] cursor-pointer"
                        } ${viewUnit ? "hidden" : ""} ${
                          details.address1 ||
                          details.address2 ||
                          details.city ||
                          details.state ||
                          details.zip
                            ? "2xl:w-[calc(100%-300px)]"
                            : ""
                        }`}
                        onClick={() => {
                          if (isReadOnly) {
                            return false;
                          }
                          setViewUnit(true);
                        }}
                        onMouseEnter={() => {
                          onMouseEnterUpdateFieldStatus({
                            field: "address1",
                          });
                        }}
                        onMouseLeave={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "address1",
                          });
                        }}
                      >
                        <ul className="w-full">
                          {isEmpty(details.address1) &&
                          isEmpty(details.address2) &&
                          isEmpty(details.city) &&
                          isEmpty(details.state) &&
                          isEmpty(details.zip) ? (
                            "-"
                          ) : (
                            <>
                              <li className="text-primary-900 text-sm">
                                {details.address1}
                              </li>
                              <li className="text-primary-900 text-sm">
                                {details.address2}
                              </li>
                              <li>
                                <Typography className="text-primary-900 text-sm">
                                  {details.city}
                                </Typography>
                                <Typography className="text-primary-900 text-sm">
                                  {details.city && details.state
                                    ? `, ${details.state}`
                                    : details.state}
                                  {""}
                                </Typography>
                                <Typography className="text-primary-900 text-sm">
                                  {details.zip ? ` ${details.zip}` : ""}
                                </Typography>
                              </li>
                            </>
                          )}
                        </ul>
                        {!isReadOnly && (
                          <FieldStatus
                            status={getStatusForField(
                              loadingStatus,
                              "address1"
                            )}
                          />
                        )}
                      </li>
                      <li
                        className={`flex 2xl:flex-row flex-col gap-2 ${
                          viewUnit ? "w-full" : "2xl:w-fit w-full"
                        }`}
                      >
                        <GoogleMap
                          ref={addressContainerRef}
                          mapClassName="2xl:-mt-[39px]"
                          // cssStyle={{ height: "150px" }}
                          addressInfo={inputValues as IDirAddrInfo}
                          mapAddress={{
                            address1: details.address1 ? details.address1 : "",
                            address2: details.address2 ? details.address2 : "",
                            city: details.city ? details.city : "",
                            state: details.state ? details.state : "",
                            zip: details.zip ? details.zip : "",
                          }}
                          cssStyle={{ height: "152px", minWidth: "300px" }}
                          id="projectAddressMap"
                          handleInputChange={handleInputChange}
                          handleSelectedLocation={handleSelectedLocation}
                          isEditable={viewUnit}
                          handleInputBlur={handleInputBlur}
                          title={[
                            details.address1,
                            details.address2,
                            details.city,
                            details.state,
                            details.zip,
                          ]
                            .filter((value) => !!value)
                            .join(", ")}
                        />
                      </li>
                    </ul>
                  }
                />
              </li>
            </ul>
          </div>
        }
      />

      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setIsConfirmDialogOpen(false)}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => setIsConfirmDialogOpen(false)}
        />
      )}
    </>
  );
};

export default ProjectDetails;
