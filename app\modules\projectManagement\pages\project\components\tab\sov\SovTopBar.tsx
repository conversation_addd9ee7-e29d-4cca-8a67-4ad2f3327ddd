// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { useMemo } from "react";
import { Number } from "~/helpers/helper";
import { useSummary } from "./use-summary";

const SovTopBar = () => {
  const { _t } = useTranslation();

  const { formatter } = useCurrencyFormatter();

  const { valueToBeConsidered: summaryData } = useSummary();

  const invoiced = useMemo(() => {
    const total_billed = Number(summaryData?.total_billed);

    return formatter(
      total_billed !== 0 ? (total_billed / 100).toFixed(2) : "0.00"
    ).value_with_symbol;
  }, [summaryData?.total_billed]);

  const invoicedPercent = useMemo(() => {
    const sub_total = Number(summaryData?.sub_total);
    const total_invoiced = Number(summaryData?.total_billed);

    return sub_total !== 0 ? (total_invoiced * 100) / sub_total : 0;
  }, [summaryData]);

  const remaining = useMemo(() => {
    const total_remain = Number(summaryData?.total_remain);

    return formatter(
      total_remain !== 0 ? (total_remain / 100).toFixed(2) : "0.00"
    ).value_with_symbol;
  }, [summaryData?.total_remain]);

  const remainingPercent = useMemo(() => {
    const sub_total = Number(summaryData?.sub_total);
    const total_remain = Number(summaryData?.total_remain);

    return sub_total !== 0 ? (total_remain * 100) / sub_total : 0;
  }, [summaryData]);

  const subtotal = useMemo(() => {
    const sub_total = Number(summaryData?.sub_total);

    return formatter(sub_total !== 0 ? (sub_total / 100).toFixed(2) : "0.00")
      .value_with_symbol;
  }, [summaryData?.sub_total]);

  const estProfit = useMemo(() => {
    const total_profit = Number(summaryData?.total_profit);

    return formatter(
      total_profit !== 0 ? (total_profit / 100).toFixed(2) : "0.00"
    ).value_with_symbol;
  }, [summaryData?.total_profit]);

  return (
    <div className="flex sm:flex-row flex-col sm:items-center items-end gap-2 flex-wrap sm:w-fit w-full">
      <div className="py-[3px] px-2.5 border border-solid rounded border-[#008000] bg-white">
        <Typography className="text-[#008000] font-semibold text-13">
          {_t("Invoiced") + ": "}
          {invoiced} ({Math.round(invoicedPercent)}%)
        </Typography>
      </div>
      <FontAwesomeIcon
        className="w-[13px] h-[13px] text-primary-900 font-semibold dark:text-white/90"
        icon="fa-regular fa-plus"
      />
      <div className="py-[3px] px-2.5 border border-solid rounded border-deep-orange-500 bg-white">
        <Typography className="text-deep-orange-500 font-semibold text-13">
          {_t("Remain") + ": "}
          {remaining} ({Math.round(remainingPercent)}%)
        </Typography>
      </div>
      <FontAwesomeIcon
        className="w-[13px] h-[13px] text-primary-900 font-semibold dark:text-white/90"
        icon="fa-regular fa-equals"
      />
      <div className="py-[3px] px-2.5 border border-solid rounded border-primary-900 bg-white">
        <Typography className="text-primary-900 font-semibold text-13">
          {_t("Sub Total") + ": "}
          {subtotal}
        </Typography>
      </div>
      <div className="sm:pl-4 sm:border-l sm:border-primary-900/25 sm:ml-2">
        <div className="bg-[#00800024] py-1 px-2.5 rounded-sm flex items-center gap-1 flex-wrap">
          <Typography className="text-green-700 text-13 font-semibold">
            {_t("Est. Profit")} (
            {Math.round(Number(summaryData?.profit_percentage))}%):
          </Typography>
          <Typography className="text-green-700 text-13 font-semibold">
            {estProfit}
          </Typography>
        </div>
      </div>
    </div>
  );
};

export default SovTopBar;
