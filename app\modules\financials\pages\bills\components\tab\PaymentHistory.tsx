import GradientIconCard from "~/components/common/gradient-icon-card";
import DateTimeIcon from "~/components/common/date-time-icon";
import { faTrashCan } from "@fortawesome/pro-regular-svg-icons/faTrashCan";
import { useTranslation } from "~/hook";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import {
  setDeleteBillPaymentItem,
  updatePaymentAPI,
} from "~/modules/financials/pages/bills/redux/action/billDetailAction";
import { getGConfig, getGModuleByKey } from "~/zustand";
import { SetStateAction, useEffect, useMemo, useState } from "react";
import { useParams } from "@remix-run/react";
import PostPayment from "~/modules/financials/pages/bills/components/tab/details/PostPayment";
import { getFormat, Number, sanitizeString } from "~/helpers/helper";
import isEqual from "lodash/isEqual";
import isEmpty from "lodash/isEmpty";
import { useDateFormatter } from "~/hook";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import { getBillDetailAPI } from "../../redux/action/billDetailAction";

import { getCostCodeList } from "~/redux/action/getCostCodeListAction";
import {
  deleteBillDetail,
  updatebillDetail,
  updateBillDetailItem,
} from "../../redux/slices/billDetailSlice";
import { ColDef, ValueSetterParams } from "ag-grid-community";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { calculateBalanceDue } from "../../utils/common";

const PaymentHistoryCard = ({ totalAmountForHeader }: IBillDetailsTabProps) => {
  const { _t } = useTranslation();
  const { billDetail }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const dispatch = useAppBillDispatch();
  const params: Partial<RouteParams> = useParams();
  const { formatter, unformatted } = useCurrencyFormatter();
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);
  const [paymentsData, setPaymentsData] = useState(billDetail?.data?.payments);
  const [deletePaymentLoader, setDeletePaymentLoader] =
    useState<boolean>(false);
  const [itemBox, setItemBox] = useState<boolean>(false);
  const [isView, setIsView] = useState<boolean>(false);
  const [selectPayment, setSelectPayment] = useState<null>(null);
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { user_id = 0 } = user || {};
  const gConfig: GConfig = getGConfig();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { quickbook_sync, date_format } = appSettings || {};
  const dateFormat = useDateFormatter();
  const [costCodes, setCostCode] = useState<ICostCode[]>([]);

  const formatterWithTwoDigit = (value: string | number) => {
    return formatter(Number(value).toFixed(2));
  };
  const paymentModule: GModule | undefined = getGModuleByKey(
    CFConfig.payment_module
  );
  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };
  const handleBillDetails = async () => {
    const updatedBillDetails = await getBillDetailAPI({
      bill_id: params?.id || "",
    });

    dispatch(updatebillDetail(updatedBillDetails?.data));
  };
  const handleDeleteBillPaymentItem = async () => {
    try {
      setDeletePaymentLoader(true);
      const response = await setDeleteBillPaymentItem({
        gUser: user,
        selectPayment,
        setOpenConfirmModal: true,
      });

      if (response.success) {
        setDeletePaymentLoader(false);
        dispatch(
          deleteBillDetail({
            bill_id: selectPayment?.bill_id,
          })
        );
        setOpenConfirmModal(false);
        handleBillDetails();
      } else {
        setDeletePaymentLoader(false);
      }
    } catch (error) {
      setDeletePaymentLoader(false);
      console.error("Error: ", error);
    }
  };

  useEffect(() => {
    if (!isEqual(paymentsData, billDetail?.data?.payments))
      setPaymentsData(billDetail?.data?.payments);
  }, [billDetail?.data?.payments]);
  const getCostCode = async () => {
    const costCodeList = (await getCostCodeList({
      daily_equipment_cost_code: 1,
    })) as ICostCodeList;
    setCostCode(costCodeList?.data);
  };

  const handlePaymentUpdate = async (params: {
    data: {
      amount: number;
      payment_date: string;
      payment_id: string;
      payment_notes: string;
      bill_credit_account_id: string;
      bill_id: string;
    };
    newValue: string;
  }) => {
    const isDateInYYYYMMDDFormat = (date: string | undefined): boolean => {
      const dateFormatRegex = /^\d{4}-\d{2}-\d{2}$/;
      return date !== undefined && dateFormatRegex.test(date);
    };

    const getData = async (date: string): Promise<string | undefined> => {
      if (!isEmpty(date)) {
        if (date === "00/00/0000") {
          return undefined;
        }

        const isNotCustomFormat = isDateInYYYYMMDDFormat(date);

        if (isNotCustomFormat) {
          return date;
        } else {
          return dateFormat({
            date,
            dateFormat: getFormat(date_format),
            format: "yyyy-MM-dd",
          });
        }
      }
      return undefined;
    };

    if (params?.newValue !== undefined) {
      const form = {
        user_id,
        bill_id: params?.data?.bill_id,
        payment_id: params?.data?.payment_id,
        payment_date: getData(params?.data?.payment_date),
        amount: unformatted((Number(params?.newValue) * 100).toString()),
        payment_notes: params?.data?.payment_notes,
        bill_credit_account_id: params?.data?.bill_credit_account_id,
      };

      try {
        const response = await updatePaymentAPI(form);
        if (response?.success) {
          await dispatch(
            updateBillDetailItem({
              bill_id: form?.bill_id,
              user_id: form?.user_id,
              updatedData: {
                amount: form?.amount,
                payment_id: form?.payment_id,
                payment_date: form?.payment_date,
                payment_notes: form?.payment_notes,
              },
            })
          );

          // setTimeout(async () => {
          const updatedBillDetails = await getBillDetailAPI({
            bill_id: params?.data.bill_id || "",
          });

          dispatch(updatebillDetail({ bill_payment: updatedBillDetails }));
          getCostCode();
          // }, 300);
        }
      } catch (error) {
        console.error("Error updating payment:", error);
      }
      return true;
    }
    return false;
  };

  const columnDefs = [
    {
      headerName: "Payment Date",
      field: "date",
      minWidth: 130,
      maxWidth: 150,
      flex: 1,
      cellRenderer: (params: {
        data: {
          payment_date: string;
        };
      }) => {
        return params?.data?.payment_date ? (
          <div className="text-left">
            <DateTimeIcon format="date" date={params?.data?.payment_date} />
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Amount"),
      field: "amount",
      minWidth: 150,
      maxWidth: 150,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      editable: true,
      suppressKeyboardEvent,
      stopEditingWhenGridLosesFocus: true,
      valueGetter: (params: {
        data: {
          amount: number;
        };
      }) => {
        return +(params?.data?.amount / 100).toFixed(2);
      },
      valueSetter: async (params: ValueSetterParams) => {
        const newAmount = Number(params.newValue) * 100;

        const updatedData = {
          ...params.data,
          amount: newAmount,
        };
        const response = await updatePaymentAPI(updatedData);
        if (response?.success) {
          handleBillDetails();
        }
        params?.node?.setData(updatedData);
        return true;
      },
      cellRenderer: (params: {
        data: {
          amount: number;
        };
      }) => {
        const total = formatter(
          Number(params?.data?.amount / 100) == 0
            ? Number(params?.data?.amount / 100).toFixed(0)
            : Number(params?.data?.amount / 100).toFixed(2)
        ).value_with_symbol;
        return (
          <>
            <Tooltip title={total}>
              <Typography className="table-tooltip-text text-center">
                {total || "-"}
              </Typography>
            </Tooltip>
          </>
        );
      },
    },
    {
      headerName: _t("Payment Notes"),
      field: "come_from",
      minWidth: 200,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({
        data,
      }: {
        data: { payment_notes: string; come_from: string };
      }) => {
        const notes = data?.payment_notes?.trim();
        const hasNotes = !!notes;

        const comeFrom =
          quickbook_sync?.toString() === "1"
            ? _t(
                data?.come_from === "QuickBooks"
                  ? "Payment from QuickBooks"
                  : "Payment from CF"
              )
            : "";
        return (
          <div className="flex gap-1 overflow-hidden w-full">
            {hasNotes ? (
              <Tooltip title={notes}>
                <Typography className="table-tooltip-text !max-w-[calc(100%-55px)] block truncate">
                  {notes}
                </Typography>
              </Tooltip>
            ) : (
              <Typography className="table-tooltip-text">-</Typography>
            )}

            {comeFrom && (
              <Typography className="text-13">({comeFrom})</Typography>
            )}
          </div>
        );
      },
    },
    {
      headerName: "",
      maxWidth: 100,
      minWidth: 100,
      field: "action",
      headerClass: "ag-header-center",
      cellRenderer: (params: { data: SetStateAction<null> }) => {
        return (
          <div className="justify-center flex gap-1">
            {!gConfig?.module_read_only ? (
              // will remove after testing
              // !(Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0) ? (
              <>
                <ButtonWithTooltip
                  tooltipTitle={_t("view")}
                  tooltipPlacement="top"
                  icon="fa-solid fa-eye"
                  onClick={() => {
                    setSelectPayment(params?.data);
                    setItemBox(true);
                    setIsView(false);
                  }}
                />
                <ButtonWithTooltip
                  tooltipTitle={_t("Delete")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-trash-can"
                  onClick={() => {
                    setSelectPayment(params?.data);
                    setOpenConfirmModal(true);
                  }}
                />
              </>
            ) : (
              // ) : (
              //   ""
              // )
              "-"
            )}
          </div>
        );
      },
    },
  ];

  const billItems = useMemo(
    () =>
      billDetail?.data?.items?.map((billItem: any) => ({
        ...billItem,
        total: Number(billItem?.total),
      })),
    [billDetail?.data?.items]
  );

  return (
    <>
      <GradientIconCard
        title={
          paymentModule?.plural_name
            ? `${HTMLEntities.decode(
                sanitizeString(paymentModule?.plural_name)
              )} ${_t("History")}`
            : _t("Payments History")
        }
        iconProps={{
          icon: "fa-solid fa-timer",
          containerClassName:
            "bg-[linear-gradient(180deg,#95A0B41a_0%,#65779B1a_100%)]",
          id: "payment_history_card_icon",
          colors: ["#95A0B4", "#65779B"],
        }}
        headerRightButton={
          <div className="flex justify-end">
            <Typography
              title="small"
              className="bg-blue-100 py-[3px] px-[9px] rounded block font-medium dark:bg-dark-500 text-sm text-primary-900 dark:text-white/90"
            >
              {_t("Balance Due")}:{" "}
              {calculateBalanceDue({
                totalAmountForHeader,
                billItemsWithoutGlobalTaxAndRetainageItems:
                  billItems?.filter(
                    (data?: BillItem) =>
                      data?.apply_global_tax !== "0" &&
                      data?.is_retainage_item !== "1"
                  ) ?? [],

                totalTaxRate: Number(billDetail?.data?.total_tax_rate) || 0,
                billPayment: Number(billDetail?.data?.bill_payment) || 0,
              })}
            </Typography>
          </div>
        }
      />
      <div className="pt-2">
        <div className="ag-theme-alpine">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            rowData={paymentsData}
            noRowsOverlayComponent={() => (
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
              />
            )}
          />
        </div>
        <ConfirmModal
          isOpen={openConfirmModal}
          modalIcon="fa-regular fa-trash-can"
          description="Are you sure you want to delete this payment history?"
          onCloseModal={() => setOpenConfirmModal(false)}
          onAccept={() => {
            handleDeleteBillPaymentItem();
          }}
          isLoading={deletePaymentLoader}
          onDecline={() => {
            setOpenConfirmModal(false);
          }}
        />
      </div>
      {itemBox && (
        <PostPayment
          setItemBox={setItemBox}
          itemBox={itemBox}
          billDetail={selectPayment}
          isViewEdit={true}
          isEdit={true}
          isView={isView}
        />
      )}
    </>
  );
};

export default PaymentHistoryCard;
