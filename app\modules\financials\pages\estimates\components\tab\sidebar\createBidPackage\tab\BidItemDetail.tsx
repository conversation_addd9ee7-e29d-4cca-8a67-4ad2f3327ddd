import isEmpty from "lodash/isEmpty";
import * as Yup from "yup";
import { useTranslation } from "~/hook";

// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Header } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";

// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

import { Form } from "@remix-run/react";
import type { InputRef } from "antd";
import { useFormik } from "formik";
import { useEffect, useMemo, useRef, useState } from "react";
import { Number, sanitizeString } from "~/helpers/helper";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import {
  filterOptionBySubstring,
  generateCostCodeLabel,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getGSettings } from "~/zustand";
import { useAppESDispatch, useAppESSelector } from "../../../../../redux/store";
import { AFTER_APPROVAL_STATUS } from "../../../../../utils/constants";

const BidItemDetail = ({
  itemOpen,
  setItemOpen,
  onClose = () => {},
  isViewOnly,
  bidItems,
  viewItemRowIndex,
  setViewItemRowIndex,
  isItemAdd = false,
  isItemDetailDrawer = false,
  isReadOnly,
  setEstimateItemToView = () => {},
  descEditable = false,
  bidItemformik,
}: IBidItemDetailProps) => {
  // const setEstimateItemToView = Object.keys(formData??{})?.length > 0
  const { _t } = useTranslation();
  // const variantOptions = [];
  const gSettings: GSettings = getGSettings();
  const {
    is_cidb_auto_save,
    default_equipment_markup_percent,
    default_labor_markup_percent,
    default_material_markup_percent,
    default_other_item_markup_percent,
    default_undefined_markup_percent,
    default_sub_contractor_markup_percent,
  } = gSettings;

  const formData = bidItems?.[viewItemRowIndex ?? 0];
  const [markup, setMarkup] = useState<string>("markup_percent");
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [isAddItem, setIsAddItem] = useState<boolean>(isItemAdd);
  const [inputValues, setInputValues] = useState<Partial<ESEstimateItem>>({});
  const [submitAction, setSubmitAction] = useState<string>("");

  const [isMuPercentFieldChanged, setIsMuPercentFieldChanged] =
    useState<boolean>(false);
  const [itemType, setItemType] = useState<string>("");

  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const unitCostRef = useRef<InputRef>(null);

  const { estimateDetail: details } = useAppESSelector(
    (state) => state.estimateDetail
  );

  const { codeCostData } = useAppESSelector((state) => state.costCode);
  const items = bidItems;

  const dispatch = useAppESDispatch();
  const itemTypes = [
    {
      label: (
        <div className="flex items-center gap-1.5">
          <FontAwesomeIcon icon="fa-regular fa-block-brick" />
          {_t("Material")}
        </div>
      ),
      value: "161",
      name: _t("Material"),
      mark_up: default_material_markup_percent,
    },
    {
      label: (
        <div className="flex items-center gap-1.5">
          <FontAwesomeIcon icon="fa-regular fa-user-helmet-safety" />
          {_t("Labor")}
        </div>
      ),
      value: "163",
      name: _t("Labor"),
      mark_up: default_labor_markup_percent,
    },
    {
      label: (
        <div className="flex items-center gap-1.5">
          <FontAwesomeIcon icon="fa-regular fa-screwdriver-wrench" />
          {_t("Equipment")}
        </div>
      ),
      value: "162",
      name: _t("Equipment"),
      mark_up: default_equipment_markup_percent,
    },

    {
      label: (
        <div className="flex items-center gap-1.5">
          <FontAwesomeIcon icon="fa-regular fa-file-signature" />
          {_t("Subcontractor")}
        </div>
      ),
      value: "164",
      name: _t("Subcontractor"),
      mark_up: default_sub_contractor_markup_percent,
    },
    {
      label: (
        <div className="flex items-center gap-1.5">
          <FontAwesomeIcon icon="fa-regular fa-boxes-stacked" />
          {_t("Other")}
        </div>
      ),
      value: "165",
      name: _t("Other"),
      mark_up: default_other_item_markup_percent,
    },
  ];

  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const { currency_symbol }: GSettings = getGSettings();

  const isAfterApproved = AFTER_APPROVAL_STATUS.includes(
    details?.approval_type_key ?? ""
  );

  const handleParagraphClick = () => {
    if (!isViewOnly) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const costCodeOptions = useMemo(() => {
    const filteredCodeCostData = codeCostData?.filter(
      (item) => item?.cost_code_name !== "" || item?.csi_code !== ""
    );
    let costCodeOpts = filteredCodeCostData?.map((item: ICostCode) => {
      return {
        label: generateCostCodeLabel({
          name: item?.cost_code_name ?? "",
          code: item?.csi_code,
          isAllowCodeWithoutName: true,
        }),
        // label: `${item?.cost_code_name}${
        //   item?.csi_code ? ` (${item?.csi_code})` : ""
        // }${item?.is_deleted === 1 ? ` (Archived)` : ""}`,
        value: item?.code_id,
      };
    });

    return costCodeOpts;
  }, [codeCostData]);

  useEffect(() => {
    if (formData) {
      setInputValues(formData);
    }
  }, [formData]);

  const initialValues: Partial<IEstimateAddItem> = useMemo(() => {
    return isAddItem === false
      ? {
          ...inputValues,
          is_optional_item: 0,
          is_markup_percentage: 1,
          ...formData,
        }
      : {
          ...inputValues,
          is_optional_item: 0,
          is_markup_percentage: 1,
          add_item_to_database: is_cidb_auto_save,
          markup: default_undefined_markup_percent,
        };
  }, [isAddItem, formData, inputValues, is_cidb_auto_save]);

  const validationSchema = Yup.object().shape({
    subject: Yup.string().required("This field is required."),
    item_type: Yup.string().required("This field is required."),
    unit_cost: Yup.number()
      // .positive("Unit cost must be positive")
      .test(
        "len",
        "Unit cost must be less than or equal to 6 digits",
        (val) => !val || val?.toString()?.length <= 10
      )
      .test(
        "is-zero-valid",
        "Unit cost cannot be negative or invalid",
        (val) => Number(val) === 0 || Number(val) > 0
      ),
  });

  // useEffect(() => {
  //   dispatch(getCostCode({ project_id: +(details?.project_id || 0) }));
  // }, [details?.project_id]);

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      await addOnSubmit();
      await setSubmitting(false);
    },
  });

  const {
    handleSubmit,
    setFieldValue,
    values,
    errors,
    isSubmitting,
    resetForm,
  } = formik;
  const addOnSubmit = async () => {
    if (formData?.is_added_item === 1) {
      bidItemformik?.setFieldValue(`bid_items.${viewItemRowIndex}`, {
        ...values,
      });
      onClose();
      return;
    }
  };

  useEffect(() => {
    setIsAddItem(isItemAdd);
    formik.setValues(initialValues);
  }, [formData, itemOpen]);

  useEffect(() => {
    if (isAddItem) {
      resetForm();
    }
  }, [isAddItem]);

  useEffect(() => {
    if (values?.item_type) {
      setItemType(
        itemTypes.find((i) => i.value === values?.item_type?.toString())
          ?.name || ""
      );
    }
    return () => {
      setItemType("");
    };
  }, [values?.item_type]);

  useEffect(() => {
    if (
      values?.quantity !== "" &&
      values?.unit_cost !== "" &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
      setMainTotal(Number(values?.markup_amount) + Number(values?.total));
    } else {
      setFieldValue("total", "");
      setMainTotal("");
    }
    if (values?.total && values?.markup !== "" && values?.markup) {
      const _markup =
        values?.markup == "-" || !values?.markup ? 0 : values.markup;

      if (markup === "markup_percent") {
        const markup = (Number(values?.total) * Number(_markup)) / 100;
        setFieldValue("markup_amount", markup);
        setMainTotal(Number(markup) + Number(values?.total));
      } else {
        const markup = Number(_markup);
        const markupPercentage =
          (Number(markup) * 100) / (Number(values?.total) || 1) - 100;
        setFieldValue("markup_amount", markupPercentage.toFixed(2));
        setMainTotal(markup);
      }
    } else {
      setFieldValue("markup_amount", "");
      setMainTotal(Number(values?.total));
    }
  }, [
    values?.quantity,
    values?.unit_cost,
    values?.total,
    values?.markup,
    values?.is_markup_percentage,
    markup,
  ]);

  useMemo(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [isAddItem, formData, values]);

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show =
      isAddItem ||
      (values?.reference_item_id === 0 &&
        values?.reference_module_item_id === 0);

    const disable = !isAddItem && !!values?.item_on_database;

    return { show, disable };
  }, [isAddItem, values]);

  const handleSaveItem = (key: string) => {
    setSubmitAction(key);
    // formik.handleSubmit();
    addOnSubmit();
  };

  const currentItemIndex = viewItemRowIndex;

  const handlePrevItem = () => {
    if (Number(currentItemIndex) && Number(currentItemIndex) > 0) {
      // if (formik.dirty && !isViewOnly) {
      // formik.submitForm();
      // handleSaveItem("save_n_next_prev");
      // formik.setStatus(Number(currentItemIndex) - 1);
      // } else {
      setViewItemRowIndex?.(Number(currentItemIndex) - 1);
      // }
    }
  };

  const handleNextItem = () => {
    if (Number(currentItemIndex) < (items?.length ?? 0) - 1) {
      // if (formik.dirty && !isViewOnly) {
      // formik.submitForm();
      // handleSaveItem("save_n_next_prev");
      // formik.setStatus(Number(currentItemIndex) + 1);
      // } else {
      setViewItemRowIndex?.(Number(currentItemIndex) + 1);
      // }
    }
  };

  const MARKUP_OPTIONS = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {currency_symbol ?? "$"}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  return (
    <>
      <Drawer
        open={itemOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t(`Bid Item Details`)}
              </Header>
              {!isAddItem && (
                <div className="flex items-center sm:gap-2 gap-0 pr-2">
                  {formik.isSubmitting &&
                  submitAction === "save_n_next_prev" ? (
                    <FontAwesomeIcon
                      className="w-3.5 h-3.5 fa-spin"
                      icon="fa-duotone fa-solid fa-spinner-third"
                    />
                  ) : (
                    ""
                  )}
                  {items?.length && items.length > 1 && (
                    <>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Previous")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-chevron-left"
                        className="item-pre-next-button disabled:bg-transparent"
                        onClick={handlePrevItem}
                        disabled={
                          Number(currentItemIndex) <= 0 || formik.isSubmitting
                        }
                      />
                      <ButtonWithTooltip
                        tooltipTitle={_t("Next")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-chevron-right"
                        className="item-pre-next-button disabled:bg-transparent"
                        onClick={handleNextItem}
                        disabled={
                          Number(currentItemIndex) >=
                            (items?.length ?? 0) - 1 || formik.isSubmitting
                        }
                      />
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <Form
          method="post"
          className="py-4"
          onSubmit={formik?.handleSubmit}
          noValidate
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    errorMessage={
                      formik.touched?.subject && !values?.subject
                        ? errors.subject
                        : ""
                    }
                    disabled={isViewOnly}
                    isRequired={true}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value);
                    }}
                    onBlur={formik.handleBlur}
                    autoComplete="off"
                  />
                </div>
                <div
                  className={`grid ${
                    isItemDetailDrawer ? "md:grid-cols-1" : "md:grid-cols-2"
                  } md:gap-5 gap-5`}
                >
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      name="item_type"
                      errorMessage={
                        values?.item_type?.toString() === "163"
                          ? "NOTE: Labor cannot be Lump Sum in Estimate or SOV. Labor Report will not calculate correctly if it is Lump Sum."
                          : formik.touched?.item_type && !values?.item_type
                          ? errors.item_type
                          : ""
                      }
                      disabled={
                        isViewOnly ||
                        !itemTypeAndSaveItemToListField.show ||
                        itemTypeAndSaveItemToListField.disable
                      }
                      value={HTMLEntities.decode(
                        sanitizeString(values?.item_type?.toString())
                      )}
                      onChange={(value) => {
                        setFieldValue("item_type", value);

                        const itemType = itemTypes?.find(
                          (i) => i?.value?.toString() === value?.toString()
                        );

                        if (!isMuPercentFieldChanged) {
                          setFieldValue("markup", itemType?.mark_up || "");
                        }
                        if (isAddItem) {
                          setFieldValue(
                            "unit",
                            value?.toString() === "163" ? "Hrs" : ""
                          );
                        }
                      }}
                      onBlur={formik.handleBlur}
                      options={itemTypes}
                      notFoundContent={
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                        />
                      }
                    />
                  </div>
                  {!isItemDetailDrawer && (
                    <div className="w-full">
                      <ButtonField
                        label={_t("Assigned To")}
                        name="assigned_to"
                        labelPlacement="top"
                        // disabled={isViewOnly}
                        value={HTMLEntities.decode(
                          sanitizeString(
                            Number(values?.assigned_to) !== 0
                              ? values?.assignee_name
                              : ""
                          )
                        )}
                        onClick={() => {
                          // setIsOpenSelectAssignedTo(true);
                        }}
                        avatarProps={{
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(values?.assignee_name)
                            ),
                            image:
                              values?.assigned_to_contact_id == 0
                                ? values?.user_image
                                : "",
                          },
                        }}
                        addonBefore={
                          values?.assigned_to ? (
                            <div className="flex items-center gap-1">
                              <ContactDetailsButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // setContactDetailDialogOpen(true);
                                }}
                              />
                              {/* <DirectoryFieldRedirectionIcon
                                directoryId={
                                  values?.assigned_to?.toString() || ""
                                }
                                directoryTypeKey={values?.type_key || ""}
                              /> */}
                            </div>
                          ) : (
                            <></>
                          )
                        }
                      />
                    </div>
                  )}
                </div>
                <div className={`grid`}>
                  <div className="w-full">
                    <SelectField
                      label={_t("Cost Code")}
                      labelPlacement="top"
                      value={
                        isItemDetailDrawer
                          ? values.cost_code_name
                          : values?.cost_code_id
                          ? costCodeOptions?.filter((item) => {
                              return (
                                values?.cost_code_id?.toString() ===
                                item?.value?.toString()
                              );
                            })
                          : []
                      }
                      onChange={(value) => {
                        setFieldValue("cost_code_id", value);
                      }}
                      showSearch
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      options={costCodeOptions}
                      disabled={isItemDetailDrawer}
                      allowClear={true}
                      onClear={() => {
                        setFieldValue("cost_code_id", "");
                      }}
                    />
                  </div>
                </div>
              </SidebarCardBorder>

              <SidebarCardBorder cardTitle="Pricing">
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("QTY")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName={`!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input`}
                        placeholder={_t("Item Quantity")}
                        onPaste={handlePaste}
                        disabled={isViewOnly}
                        errorMessage={errors.quantity}
                        // min={0}
                        // max={999999}
                        // maxLength={6}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ? "flex items-center justify-end" : ""
                        }
                        defaultValue={
                          isNaN(Number(values?.quantity)) ||
                          typeof values?.quantity === "undefined"
                            ? ""
                            : Number(values?.quantity)
                        }
                        value={
                          isNaN(Number(values?.quantity)) ||
                          typeof values?.quantity === "undefined"
                            ? ""
                            : Number(values?.quantity)
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) =>
                          onKeyDownCurrency(event, {
                            integerDigits: 6,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          })
                        }
                      />
                    </div>
                  </li>
                  <li className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <Typography className="text-13 block text-primary-900 dark:text-white/90">
                        {_t("Unit")}
                      </Typography>
                    </div>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                      >
                        {isViewOnly &&
                          (!isEmpty(values?.unit_cost) &&
                          values?.unit_cost !== 0.0 &&
                          values?.unit_cost !== "0.00" &&
                          !isEmpty(values?.unit) &&
                          !!values?.unit ? (
                            <Typography
                              className={`text-[#008000] font-medium text-13 ${
                                isViewOnly ? "cursor-no-drop" : ""
                              }`}
                            >
                              {values?.unit}
                            </Typography>
                          ) : (
                            <div className="">
                              <InputField
                                className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Unit")}
                                maxLength={15}
                                name="unit"
                                id="unit"
                                disabled={isViewOnly}
                                value={values?.unit}
                                type="text"
                                onChange={() => {}}
                              />
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>

              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    required={false}
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    disabled={formData?.is_added_item !== 1 && isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={formData?.is_added_item !== 1 && isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    onChange={(e) => {
                      setFieldValue("internal_notes", e.target.value);
                    }}
                  />
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
            {formData?.is_added_item === 1 ? (
              <PrimaryButton
                htmlType="submit"
                buttonText={_t("Save & Close")}
                onClick={() => handleSaveItem("save_n_close")}
                isLoading={
                  submitAction === "save_n_close" && formik.isSubmitting
                }
                disabled={
                  !values?.subject || !values?.item_type || isSubmitting
                  // ||isReadOnly
                }
              />
            ) : (
              <PrimaryButton
                htmlType="button"
                buttonText={_t("Close")}
                onClick={() => {
                  handleSaveItem("save_n_close");
                  onClose();
                }}
                disabled={isSubmitting}
              />
            )}
          </div>
        </Form>
      </Drawer>
    </>
  );
};

export default BidItemDetail;
