import { useEffect, useRef, useState } from "react";
import { Outlet, useNavigate, useParams } from "@remix-run/react";
import delay from "lodash/delay";
import type SignatureCanvas from "react-signature-canvas";
import { v4 as uuidv4 } from "uuid";
// FontAwesome File
import { SMDetailRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/safety-meeting/detail/regular";
import { SMDetailSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/safety-meeting/detail/solid";
import { SMDetailLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/safety-meeting/detail/light";

// Hook + remix
import { b64toBlob } from "~/shared/utils/helper/baseToBlob";
import {
  getCommonSidebarCollapse,
  getGConfig,
  getGSettings,
  setCommonSidebarCollapse,
} from "~/zustand";
import { sanitizeString } from "~/helpers/helper";
import { sendMessageKeys } from "~/components/page/$url/data";
import {
  SAFETY_MEETING_SIDE_MENU,
  upadateFieldStatus,
} from "~/modules/people/safetymeetings/utils/constants";
import ManageSafetyMeetingTab from "./$id+/$tab";
import { routes } from "~/route-services/routes";
import { useIframe, useTranslation } from "~/hook";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import SMStoreProvider from "~/modules/people/safetymeetings/redux/SMStoreProvider";
import {
  useAppSMDispatch,
  useAppSMSelector,
} from "~/modules/people/safetymeetings/redux/store";
import {
  fetchSMDetails,
  updateSMStatusApi,
} from "~/modules/people/safetymeetings/redux/action/sMDetailsAction";
import { fetchSafetyTopicData } from "~/modules/people/safetymeetings/redux/action/commonSMAction";
import { updateSMDetail } from "~/modules/people/safetymeetings/redux/slices/sMDetailsSlice";
import {
  getStatusActionForField,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { awsFileUpload } from "~/redux/action/awsFileAction";

// Components
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ModuleSidebar } from "~/shared/components/moduleSidebar";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Button } from "~/shared/components/atoms/button";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import AddSignatureModal from "~/components/modals/add-signature";

// Fort Awesome Library Add icons
SMDetailRegularIconAdd();
SMDetailSolidIconAdd();
SMDetailLightIconAdd();

const ManageSafetyMeetingsCom = () => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const { date_format }: GSettings = getGSettings();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const { module_key, page_is_iframe, module_access }: GConfig = getGConfig();

  const { id: sMId, tab }: RouteParams = useParams();
  const { parentPostMessage } = useIframe();
  const dispatch = useAppSMDispatch();
  const { safetyTopicData }: ISMCommonInitialState = useAppSMSelector(
    (state) => state.sMCommonData
  );
  const { details }: ISMDetailsInitialState = useAppSMSelector(
    (state) => state.safetyMeetingDetails
  );

  const loadingStatusRef = useRef(upadateFieldStatus);
  const signaturePad = useRef<SignatureCanvas | null>(null);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(upadateFieldStatus);
  const [isEmpConfirmOpen, setIsEmpConfirmOpen] = useState(false);
  const [isSignatureOpen, setIsSignatureOpen] = useState(false);
  const [isLoadingSignature, setIsLoadingSignature] = useState(false);
  const [signature, setSignature] = useState<string>("");
  const [isSignatureLoading, setIsSignatureLoading] = useState(false);

  useEffect(() => {
    if (sMId) {
      (async () => {
        const resData = (await dispatch(
          fetchSMDetails({ id: sMId || "", add_event: true })
        ).unwrap()) as ISMDetailsApiRes;

        if (!resData?.success) {
          notification.error({
            description: resData?.message || "No data found.",
          });
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            delay(() => {
              parentPostMessage(sendMessageKeys?.modal_change, {
                open: false,
              });
            }, 1000);
          } else {
            navigate(`${routes.MANAGE_SAFETY_MEETINGS.url}`);
          }
        }
      })();
    }
  }, [sMId]);

  useEffect(() => {
    if (!safetyTopicData.isDataFetched) {
      dispatch(
        fetchSafetyTopicData({
          start: 0,
          limit: 0,
          only_topics: 1,
          status: "0",
          language: "0,1",
        })
      );
    }
  }, [safetyTopicData.isDataFetched]);

  // Complete Meeting

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateStatus = async (
    data: ISMDetailFields,
    otherData?: { signature?: string; isLoad?: boolean }
  ) => {
    const field = Object.keys(data)[0] as keyof ISMDetails;
    setIsSignatureLoading(true);
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newData = { ...data };

    const updateRes = (await updateSMStatusApi({
      id: details?.meeting_id || "",
      ...{
        meeting_date: backendDateFormat(
          details?.meeting_date || "",
          date_format
        ),
      },
      ...data,
      ...(data?.signature && {
        signature: data.signature,
        // signatures: [
        //   {
        //     signature: data.signature,
        //     user_id: details?.leader_id || 0,
        //   },
        // ],
      }),
    })) as ISMDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      if (!!otherData?.signature) {
        dispatch(updateSMDetail({ ...data, signature: otherData?.signature }));
      } else {
        dispatch(updateSMDetail(newData));
      }

      if (updateRes?.data?.meeting_status_name) {
        dispatch(
          updateSMDetail({
            meeting_status_name: updateRes?.data?.meeting_status_name,
          })
        );
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      notification.error({
        description: updateRes?.message,
      });
    }

    clearSignature();
    setIsSignatureLoading(false);
    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );

      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleComplete = () => {
    if (details?.group_safety_meeting == 0 && !details?.signature) {
      setIsEmpConfirmOpen(true);
      return;
    }

    handleUpdateStatus({
      meeting_status: details?.meeting_status == 2 ? 0 : 2,
    });
  };

  // Add Signature

  const handleSaveSignature = async () => {
    try {
      if (!signaturePad?.current?.isEmpty()) {
        setIsLoadingSignature(true);
        const base64 = signaturePad?.current
          ?.getTrimmedCanvas()
          ?.toDataURL("image/png");
        if (base64) {
          const binaryFile: Blob = b64toBlob(base64);
          const filename: string = uuidv4();
          const getSignatureUrl: IAWSFileRes = await awsFileUpload(
            {
              moduleName: module_key,
              fileName: `${filename}.png`,
              fileType: "image/png",
              saveAsNew: 1,
            },
            binaryFile
          );
          if (getSignatureUrl?.success) {
            handleUpdateStatus(
              {
                terms: "1",
                meeting_status: 2,
                signature: getSignatureUrl?.data?.fileUrl,
              },
              { signature: getSignatureUrl?.data?.cdnUrl }
            );
            setIsSignatureOpen(false);
          }
        }
      } else {
        notification.error({
          description: "Please Enter a Signature.",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Some thing went wrong!",
      });
    } finally {
      setIsLoadingSignature(false);
    }
  };

  const clearSignature = () => {
    if (!isLoadingSignature) {
      signaturePad?.current?.clear();
    }
  };
  return (
    <div
      className={`flex overflow-hidden ${
        !page_is_iframe
          ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
          : "h-screen"
      }`}
    >
      {!sidebarCollapse && (
        <div
          className={`xl:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
            sidebarCollapse
              ? "w-0 h-0"
              : window.ENV.PAGE_IS_IFRAME
              ? "w-full h-full"
              : "w-full h-[calc(100dvh-112px)]"
          }`}
          onClick={() => setCommonSidebarCollapse(true)}
        ></div>
      )}
      <ModuleSidebar
        sidebarCollapse={sidebarCollapse}
        onSidebarCollapse={setCommonSidebarCollapse}
        selectOptions={SAFETY_MEETING_SIDE_MENU}
        onSelectedOption={(value: string) => {
          navigate(value);
        }}
        sidebarClassName="pb-[60px]"
        selectedOption={tab ?? "details"}
        SelecteMenuBottom={
          <div
            className={`fixed bg-primary-900 ${
              window.ENV.PAGE_IS_IFRAME ? "bottom-0" : "md:bottom-8 bottom-28"
            } ${
              sidebarCollapse
                ? "md:w-[75px] w-0 md:p-2.5 md:flex hidden "
                : "w-[225px] p-2.5"
            }`}
          >
            {sidebarCollapse ? (
              <Tooltip
                title={_t(
                  details?.meeting_status == 2
                    ? "Mark meeting as Incomplete"
                    : "Mark Meeting as Complete"
                )}
                placement="right"
                overlayClassName="menu-tooltip"
              >
                <Button
                  type="primary"
                  htmlType="button"
                  className={`h-[38px] w-[calc(100%-12px)] mx-auto gap-0 !bg-deep-orange-500 hover:!bg-deep-orange-500 !shadow-none !duration-1000 ease-in-out !border-transparent ${
                    tab == "estimate_finalize"
                      ? "!bg-[linear-gradient(90deg,#34496F_0%,#34496F_100%)] dark:!bg-[linear-gradient(90deg,#2b323b_0%,#131d27_100%)]"
                      : ""
                  }`}
                  onClick={() => handleComplete()}
                  // loading={
                  //   getStatusForField(loadingStatus, "meeting_status") ===
                  //   "loading"
                  // }
                  disabled={
                    module_access === "read_only" ||
                    getStatusForField(loadingStatus, "meeting_status") ===
                      "loading"
                  }
                >
                  <div className="w-[25px] flex justify-center">
                    <FontAwesomeIcon
                      className="w-5 h-5 text-white"
                      icon={
                        details?.meeting_status == 2
                          ? "fa-regular fa-user"
                          : "fa-regular fa-user-check"
                      }
                    />
                  </div>
                </Button>
              </Tooltip>
            ) : (
              <Button
                type="primary"
                htmlType="button"
                className={`w-full h-[38px] gap-0 !bg-deep-orange-500 hover:!bg-deep-orange-500 !shadow-none !duration-1000 ease-in-out !border-transparent ${
                  sidebarCollapse ? "md:flex hidden" : ""
                } ${
                  tab == "estimate_finalize"
                    ? "!bg-[linear-gradient(90deg,#34496F_0%,#34496F_100%)] dark:!bg-[linear-gradient(90deg,#2b323b_0%,#131d27_100%)]"
                    : ""
                }`}
                onClick={() => handleComplete()}
                // loading={
                //   getStatusForField(loadingStatus, "meeting_status") ===
                //   "loading"
                // }
                disabled={
                  module_access === "read_only" ||
                  getStatusForField(loadingStatus, "meeting_status") ===
                    "loading"
                }
              >
                <Typography className="ease-in-out whitespace-nowrap duration-75 text-white normal-case font-normal">
                  {_t(
                    details?.meeting_status == 2
                      ? "Mark meeting as Incomplete"
                      : "Mark Meeting as Complete"
                  )}
                </Typography>
              </Button>
            )}
          </div>
        }
      />

      {tab ? <Outlet /> : <ManageSafetyMeetingTab />}

      {isEmpConfirmOpen && (
        <ConfirmModal
          isOpen={isEmpConfirmOpen}
          modaltitle={_t("Employee Confirmation")}
          description={`${_t("I, ")} ${HTMLEntities.decode(
            sanitizeString(details?.leader_name || "")
          )} ${_t(
            "have read the details of this safety topic in full and understand it completely and how safety is required at all times. If I had any questions, I discussed them with my superiors prior to concluding this training."
          )}`}
          modalIcon={"fa-regular fa-file-check"}
          onAccept={() => {
            setIsSignatureOpen(true);
            setIsEmpConfirmOpen(false);
          }}
          onDecline={() => {
            setIsEmpConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsEmpConfirmOpen(false);
          }}
        />
      )}

      <AddSignatureModal
        open={isSignatureOpen}
        closeModalHandler={() => {
          if (!isLoadingSignature) {
            setIsSignatureOpen(false);
          }
          clearSignature();
        }}
        ref={signaturePad}
        clear={clearSignature}
        save={handleSaveSignature}
        loading={isLoadingSignature}
      />
    </div>
  );
};

const ManageSafetyMeetings = () => {
  return (
    <SMStoreProvider>
      <ManageSafetyMeetingsCom />
    </SMStoreProvider>
  );
};
export default ManageSafetyMeetings;

export { ErrorBoundary };
