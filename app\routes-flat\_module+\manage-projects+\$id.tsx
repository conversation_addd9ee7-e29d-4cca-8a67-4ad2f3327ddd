import { use<PERSON>ara<PERSON> } from "@remix-run/react";
import { Outlet, useNavigate } from "@remix-run/react";
import {
  getCommonSidebarCollapse, // In future this code move in redux, developer change this code
  getGConfig,
  setCommonSidebarCollapse, // In future this code move in redux, developer change this code
} from "~/zustand";
// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
// ModuleSidebar
import { ModuleSidebar } from "~/shared/components/moduleSidebar";
// Other
import ManageProjectTab from "./$id+/$tab";
import {
  SELECT_TAB_OPTIONS,
  USER_PLANE_IDS,
} from "~/modules/projectManagement/pages/project/utils/constants";
import { useAppProDispatch } from "~/modules/projectManagement/pages/project/redux/store";
import { useEffect, useMemo, useRef } from "react";
import { fetchProjectDetailsApi } from "~/modules/projectManagement/pages/project/redux/action/projectDetailsAction";
import { Number } from "~/helpers/helper";
import ProjectStoreProvider from "~/modules/projectManagement/pages/project/redux/projectStoreProvider";

// FontAwesome File
import { ProjectDetailRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/project/detail/regular";
import { ProjectDetailLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/project/detail/light";
import { ProjectDetailSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/project/detail/solid";
import { ProjectDetailDuotoneIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/project/detail/duotone";
import { resetGantt, setProjectId } from "~/redux/slices/schedulerSlice";
import { useModuleAccess } from "~/modules/projectManagement/pages/project/hook/useModuleAcces";
import { resetSummarynActionuItemsStates } from "~/modules/projectManagement/pages/project/redux/slices/proDetailsSlice";

// Fort Awesome Library Add icons
ProjectDetailRegularIconAdd();
ProjectDetailLightIconAdd();
ProjectDetailSolidIconAdd();
ProjectDetailDuotoneIconAdd();

const ManageProjectCom = () => {
  const { tab, id }: RouteParams = useParams();
  const sidebarCollapse = getCommonSidebarCollapse();
  const navigate = useNavigate();
  const { page_is_iframe }: GConfig = getGConfig();
  const isProjectsLoading = useRef<boolean>(false);

  const {
    hasEnoughAccessofProjectFinanceTabModule,
    enoughProcurementAccess,
    enoughGanttModuleAccess,
    enoughClientModuleAccess,
    enoughReportModuleAccess,
    enoughDocumentTabAccess,
    enoughTabFileAccess,
    enoughTabScheduleAccess,
  } = useModuleAccess();

  const dispatch = useAppProDispatch();

  const fetchProDetailApis = () => {
    dispatch(
      fetchProjectDetailsApi({
        project_id: Number(id),
        record_type: "project",
      })
    );
  };

  useEffect(() => {
    if (id && !isProjectsLoading.current) {
      isProjectsLoading.current = true;
      fetchProDetailApis();
      dispatch(resetGantt());
      dispatch(resetSummarynActionuItemsStates());
      dispatch(setProjectId(id));
    }
  }, [id]);

  const clientAccessNotAlloedPlanIds: number[] = useMemo(() => {
    return [
      USER_PLANE_IDS.basic_v5,
      USER_PLANE_IDS.standard_v5,
      USER_PLANE_IDS.plus_v5,
    ];
  }, []);

  const filteredTabOptions = useMemo(() => {
    return SELECT_TAB_OPTIONS?.filter((option) => {
      if (option.value == "procurement") {
        return enoughProcurementAccess;
      }
      if (option.value === "summary") {
        return hasEnoughAccessofProjectFinanceTabModule;
      }

      if (option.value == "schedule") {
        return enoughGanttModuleAccess;
      }

      if (option.value === "client_access") {
        return enoughClientModuleAccess;
      }

      if (option.value === "reports") {
        return enoughReportModuleAccess;
      }
      if (option.value === "financial") {
        return hasEnoughAccessofProjectFinanceTabModule;
      }

      if (option.value === "documents") {
        return enoughDocumentTabAccess;
      }

      if (option.value === "files_photos") {
        return enoughTabFileAccess;
      }

      if (option.value === "schedule_of_values") {
        return enoughTabScheduleAccess;
      }

      return true;
    });
  }, [
    enoughProcurementAccess,
    enoughGanttModuleAccess,
    enoughClientModuleAccess,
    enoughReportModuleAccess,
    hasEnoughAccessofProjectFinanceTabModule,
    enoughDocumentTabAccess,
    enoughTabFileAccess,
    enoughTabScheduleAccess,
  ]);

  return (
    <ProjectStoreProvider>
      <div
        className={`flex overflow-hidden ${
          !page_is_iframe
            ? "md:h-[calc(100vh-143px)] h-[calc(100vh-112px)]"
            : "h-screen"
        }`}
      >
        {!sidebarCollapse && (
          <div
            className={`xl:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
              sidebarCollapse
                ? "w-0 h-0"
                : window.ENV.PAGE_IS_IFRAME
                ? "w-full h-full"
                : "w-full h-[calc(100dvh-112px)]"
            }`}
            onClick={() => setCommonSidebarCollapse(true)}
          ></div>
        )}
        <ModuleSidebar
          sidebarCollapse={sidebarCollapse}
          onSidebarCollapse={setCommonSidebarCollapse}
          selectOptions={filteredTabOptions}
          onSelectedOption={(value: string) => {
            navigate(value);
          }}
          selectedOption={tab ?? "summary"}
        />

        {tab ? (
          <Outlet context={{ fetchProDetailApis }} />
        ) : (
          <ManageProjectTab fetchProDetailApis={fetchProDetailApis} />
        )}
      </div>
    </ProjectStoreProvider>
  );
};

const ManageProject = () => {
  return (
    <ProjectStoreProvider>
      {" "}
      <ManageProjectCom />{" "}
    </ProjectStoreProvider>
  );
};
export default ManageProject;

export { ErrorBoundary };
