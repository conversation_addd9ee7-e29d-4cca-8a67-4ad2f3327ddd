import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "@remix-run/react";
import type { ColDef } from "ag-grid-community";
// Ag grid
import type {
  IRowNode,
  GridOptions,
  ValueGetterParams,
  ValueSetterParams,
} from "ag-grid-community";

// Hook Redux
import { useTranslation } from "~/hook";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import {
  generateCostCodeLabel,
  qtyNumberCheck,
  wholeNumberRegex,
} from "~/shared/utils/helper/common";
import { ICONSFORITEMSTYPE } from "~/shared/constants";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import {
  useAppIVDispatch,
  useAppIVSelector,
} from "~/modules/financials/pages/invoice/redux/store";
import { resetDash } from "~/modules/financials/pages/invoice/redux/slices/dashboardSlice";
import {
  deleteInvoiceItems,
  fetchInvoiceItemsList,
  updateInvoiceItem,
} from "~/modules/financials/pages/invoice/redux/action/InvoiceItemsActions";
import { updateInvoiceItems } from "~/modules/financials/pages/invoice/redux/slices/InvoiceItemsSlice";

import { calMarkupPercentageIv } from "~/modules/financials/pages/invoice/utils/helper";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
interface IInvoiceLumpSumItemsProps {
  isReadOnly: boolean;
  setIsItemTabsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setIsAddInvoiceItem: React.Dispatch<React.SetStateAction<boolean>>;
  setItemToBeUpdate: React.Dispatch<React.SetStateAction<IInvoiceItemData>>;
  setInvoiceRecordList: React.Dispatch<
    React.SetStateAction<IInvoiceItemData[]>
  >;
  setItemTabsType: (data: string) => void;
}

const LumpSumItems = ({
  isReadOnly,
  setIsItemTabsOpen,
  setIsAddInvoiceItem,
  setItemToBeUpdate,
  setInvoiceRecordList,
  setItemTabsType,
}: IInvoiceLumpSumItemsProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { id: iVId }: RouteParams = useParams();
  const dispatch = useAppIVDispatch();
  const invoiceItemsRef = useRef<IInvoiceItemsListData | null>(null);
  const filterRef = useRef<string[] | null>(null);

  const isOriginalScopeLoading = false;
  const { invoiceItems }: IInvoiceItemsInitialState = useAppIVSelector(
    (state) => state.proInvoiceItemList
  );
  const { details }: IIVDetailsInitialState = useAppIVSelector(
    (state) => state.invoiceDetails
  );
  const { codeCostData }: IGetCostCodeList = useAppIVSelector(
    (state) => state.costCode
  );

  const { filter }: IIVCommonInitialState = useAppIVSelector(
    (state) => state.invoiceCommonData
  );

  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isActiveTable, setIsActiveTable] = useState<string[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<number>(0);
  const [selectedParams, setSelectedParams] = useState<{
    params: ValueSetterParams | null;
    fieldName: keyof IUpdateInvoiceItem | null;
  }>({ params: null, fieldName: null });
  const gridRef = useRef<ExtendedAgGridReact<IInvoiceItemData> | null>(null);
  const [selectedData, setSelectedData] = useState<IInvoiceItemData>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  
  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
    }
  }, []);

  const filterCostCodeName = (val: string) => {
    if (val) {
      return val
        .toString()
        .replace(/\(.*?\)/g, "")
        .trim();
    }
    return "";
  };

  const getTotalAmount = useCallback(
    (data: IWorkorderDetailsItem, qty: string, unit_cost: string | number) => {
      const markup_amount = Number(data.markup || 0);
      const is_markup_percentage = Number(data.is_markup_percentage);

      const total: number = Number(qty) * Number(unit_cost);
      let mainTotal: number = 0;

      if (is_markup_percentage === 1) {
        const markup = (total * markup_amount) / 100;
        mainTotal = Number(markup) + Number(total);
      } else {
        mainTotal = Number(qty) > 0 ? markup_amount : total;
      }
      return mainTotal;
    },
    []
  );

  const updateCellValueApi = async (
    params: ValueSetterParams,
    fieldName: keyof IUpdateInvoiceItem,
    isChecked: boolean = false
  ) => {
    const { data, node, oldValue, newValue } = params;
    try {
      const formData: IUpdateInvoiceItemsParams = {
        id: Number(iVId || ""),
        items: [
          {
            item_id: data?.item_id,
            subject: data?.subject,
            quantity: data?.quantity,
            unit_cost: data?.unit_cost,
            markup: data?.markup,
            is_markup_percentage: data?.is_markup_percentage,
            cost_code_id: data?.cost_code_id,
            total: data?.total,
            item_type: data?.item_type,
            unit: data?.unit,
            assigned_to: data?.assigned_to,
            assigned_to_contact_id: data?.assigned_to_contact_id,
            apply_global_tax: data?.apply_global_tax,
            tax_id: data?.tax_id,
            reference_item_id: data?.reference_item_id,
            // invoice_total: data?.invoice_total,
            description: data?.description,
            notes: data?.notes,
            add_item_to_database: data?.add_item_to_database,
          },
        ],
      };

      let newValueFinal = newValue;
      let isFieldValue = false;
      if (fieldName === "unit_cost") {
        newValueFinal = newValue ? Number(newValue) * 100 : "0";
        formData.items[0]["total"] =
          Number(data?.quantity) * Number(newValueFinal);
      }

      if (fieldName === "quantity") {
        newValueFinal = !!newValue ? Number(newValue) : "0";
        formData.items[0]["total"] =
          data.is_markup_percentage?.toString() !== "1"
            ? getTotalAmount(
                params.data,
                newValueFinal,
                params.data?.unit_cost as string | number
              )
            : Number(newValueFinal) * Number(data?.unit_cost);
        isFieldValue = true;
      }
      if (fieldName === "markup") {
        formData.items[0]["markup"] = Number(newValue);
        formData.items[0]["total"] =
          Number(data?.quantity) * Number(data?.unit_cost);
      }

      if (
        fieldName === "quantity" ||
        fieldName === "unit_cost" ||
        fieldName === "markup"
      ) {
        if (data.is_markup_percentage?.toString() === "1") {
          const newTotal = Number(formData.items[0]["total"] || "");
          const markupPerc = Number(formData.items[0]["markup"] || "");
          const updatedTotal = newTotal + (newTotal * markupPerc) / 100;

          formData.items[0]["total"] = updatedTotal;
        } else {
          if (!isFieldValue) {
            formData.items[0]["total"] =
              data?.markup && data?.markup != 0
                ? Number(data?.markup || "")
                : formData.items[0]["total"];
          }
        }

        if (!isFieldValue) {
          const newAllTotal =
            Math.round(
              Number(details?.total || "") +
                Number(formData.items[0]["total"] || "")
            ) - Number(data?.total || "");
          if (newAllTotal < 0 && !isChecked) {
            setSelectedParams({ params, fieldName });
            return;
          }
        }
      }

      if (
        fieldName === "quantity" &&
        newValueFinal != null &&
        newValueFinal.toString().includes(".")
      ) {
        formData.items[0][fieldName] = newValueFinal.toFixed(2);
      } else if (
        fieldName === "unit_cost" &&
        newValueFinal != null &&
        newValueFinal.toString().includes(".")
      ) {
        formData.items[0][fieldName] = newValueFinal.toFixed(2);
      } else {
        formData.items[0][fieldName] =
          newValueFinal == null ? "" : newValueFinal;
      }

      if (fieldName === "cost_code_id") {
        const newValueFlt = filterCostCodeName(newValue);
        const fltCostCode = codeCostData.find(
          (item) => item.cost_code_name?.toString()?.trim() == newValueFlt
        );
        formData.items[0][fieldName] = Number(fltCostCode?.code_id || "");
        formData.items[0]["cost_code_name"] = fltCostCode?.cost_code_name || "";
        formData.items[0]["csi_code"] = fltCostCode?.csi_code || "";
      }
      if (fieldName === "apply_global_tax") {
        formData.items[0][fieldName] = newValueFinal ? 1 : 0;
      }

      if (fieldName === "unit" && window.ENV.ENABLE_UNIT_DROPDOWN) {
        formData.items[0][fieldName] = (
          newValue?.name?.trim() || ""
        )?.toString();
      }

      const updatedItems = {
        ...invoiceItems,
        items: invoiceItems?.items?.map((item) =>
          item?.item_id == formData?.items[0]?.item_id
            ? { ...item, ...formData.items[0] }
            : item
        ),
      };
      dispatch(updateInvoiceItems({ items: updatedItems }));

      const response = (await updateInvoiceItem(
        formData as IUpdateInvoiceItemsParams
      )) as IUpdateInvoiceItemsApiRes;

      if (response?.success) {
        if (fieldName === "unit") {
          params?.api?.stopEditing();
        }
        dispatch(resetDash());
      } else {
        dispatch(
          fetchInvoiceItemsList({
            id: Number(iVId),
            data_for: "all",
            need_section: details?.is_new_tm_invoice == 1 ? 1 : 0,
            is_separate_change_order_sections:
              details.is_new_tm_invoice === 0 ? true : undefined,
            is_separate_estimate_sections: false,
            isHideLoading: true,
          })
        );

        notification.error({
          description: response?.message,
        });
      }
    } catch (e) {
      dispatch(
        fetchInvoiceItemsList({
          id: Number(iVId),
          data_for: "all",
          need_section: details?.is_new_tm_invoice == 1 ? 1 : 0,
          is_separate_change_order_sections:
            details.is_new_tm_invoice === 0 ? true : undefined,
          is_separate_estimate_sections: false,
          isHideLoading: true,
        })
      );

      notification.error({
        description: _t("Error fetching"),
      });
    }
  };

  //? this useEffect is for handleDragAndDrop callback function
  useEffect(() => {
    if (invoiceItems) {
      invoiceItemsRef.current = invoiceItems; // Now this assignment works
    }
  }, [JSON.stringify(invoiceItems)]);
  useEffect(() => {
    filterRef.current = filter;
  }, [filter]);

  const handleDragAndDrop = async (items: IUpdateInvoiceItem[]) => {
    const tempItems: IUpdateInvoiceItem[] = items?.map(
      (row: IUpdateInvoiceItem, index: number) => ({
        ...row,
        invoice_item_no: index + 1,
      })
    );

    const formData: IUpdateInvoiceItemsParams = {
      id: Number(iVId || ""),
      items: tempItems,
    };

    const updatedItems = {
      ...invoiceItemsRef.current,
      items: invoiceItemsRef.current?.items?.map((item: IInvoiceItemData) => {
        const updatedItem = tempItems?.find(
          (formItem) => formItem?.item_id == item?.item_id
        );
        return updatedItem?.item_id ? { ...item, ...updatedItem } : item;
      }),
    };

    dispatch(updateInvoiceItems({ items: updatedItems }));

    const response = (await updateInvoiceItem(
      formData as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response?.success) {
      // TODO: Make below dyaanic
      // dispatch(resetSCItems());
    } else {
      dispatch(
        fetchInvoiceItemsList({
          id: Number(iVId),
          data_for: "all",
          need_section: details?.is_new_tm_invoice == 1 ? 1 : 0,
          is_separate_change_order_sections:
            details.is_new_tm_invoice === 0 ? true : undefined,
          is_separate_estimate_sections: false,
          isHideLoading: true,
        })
      );
      notification.error({
        description: response?.message,
      });
    }
  };

  const gridOptions: GridOptions = {
    onRowDragEnd: function (event) {
      if (isReadOnly) {
        return;
      }
      // ag grid community provide "RowNode" But not working that's why use any
      const { node, overIndex } = event as {
        node: IRowNode;
        overIndex: number;
      };
      if (!gridOptions.api || !node) return;

      const rowData: IUpdateInvoiceItem[] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));

      if (node.rowIndex !== null) {
        // rowData.splice(overIndex, 0, rowData.splice(node.rowIndex, 1)[0]);
        const movedRecord = rowData.splice(node?.rowIndex, 1)[0];
        rowData.splice(overIndex, 0, movedRecord);

        if (
          filterRef.current?.length === 0 ||
          (filterRef.current?.length &&
            invoiceItemsRef.current &&
            invoiceItemsRef.current?.items?.length <= 1)
        ) {
          handleDragAndDrop(rowData);
        } else {
          let nextRecord =
            rowData[node?.rowIndex + 1] || rowData[node?.rowIndex - 1];

          if (
            invoiceItemsRef.current &&
            invoiceItemsRef.current?.items?.length > 1
          ) {
            const movedRecordIndex = invoiceItemsRef?.current?.items?.findIndex(
              (i: IInvoiceItemData) => i.item_id == movedRecord.item_id
            );
            const nextRecordIndex = nextRecord
              ? invoiceItemsRef?.current?.items?.findIndex(
                  (i: IInvoiceItemData) => i.item_id == nextRecord.item_id
                )
              : invoiceItemsRef?.current?.items?.length - 1;

            const tempInvoiceItems = [...invoiceItemsRef?.current?.items]; // Create a copy of serviceItems
            const [removedItem] = tempInvoiceItems.splice(movedRecordIndex, 1); // Remove the moved item
            tempInvoiceItems.splice(nextRecordIndex, 0, removedItem); // Insert it at the new position

            // Step 7: Handle the updated service items
            handleDragAndDrop(tempInvoiceItems as IUpdateInvoiceItem[]);
          }
        }
      }
    },
  };

  const handleDeleteItem = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteInvoiceItems({
        id: Number(iVId || ""),
        itemId: selectedItemId,
      })) as IUpdateInvoiceItemsApiRes;

      if (deleteRes?.success) {
        dispatch(resetDash());
        await dispatch(
          fetchInvoiceItemsList({
            id: Number(iVId),
            data_for: "all",
            need_section: details?.is_new_tm_invoice == 1 ? 1 : 0,
            is_separate_change_order_sections:
              details.is_new_tm_invoice === 0 ? true : undefined,
            is_separate_estimate_sections: false,
            isHideLoading: true,
          })
        );
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setSelectedItemId(0);
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const invoiceItemsList = useMemo(() => {
    //! The same code is used in the file below. If you want to make any changes to this code, apply the same changes to the file below:
    // app\modules\financials\pages\invoice\components\tab\ItemsTab.tsx

    const BILL_MODULE_ID = 78;
    const ESTIMATE_MODULE_ID = 15;
    const is_new_tm_invoice = details?.is_new_tm_invoice;
    const newData = invoiceItems?.items?.filter((obj: IInvoiceItemData) => {
      const estimateId = Number(obj.estimate_id || 0);
      const projectBudgetItemId = Number(obj.project_budget_item_id || 0);
      const changeOrderId = Number(obj.change_order_id || 0);
      const workOrderId = Number(obj.work_order_id || 0);
      const isRetainageItem = obj.is_retainage_item?.toString() === "1";
      const referenceModuleId = Number(obj.reference_module_id || 0);
      const referencePrimaryId = Number(obj.reference_primary_id || 0);
      const referenceModuleItemId = Number(obj.reference_module_item_id || 0);

      if (
        (estimateId > 0 && projectBudgetItemId > 0) ||
        changeOrderId > 0 ||
        workOrderId > 0 ||
        projectBudgetItemId > 0 ||
        isRetainageItem ||
        (referenceModuleId === BILL_MODULE_ID &&
          (!obj.reference_module_item_id || referenceModuleItemId === 0)) ||
        (is_new_tm_invoice == 1 &&
          referenceModuleId === BILL_MODULE_ID &&
          (!!obj.reference_module_item_id || referenceModuleItemId > 0)) ||
        (referencePrimaryId > 0 &&
          referenceModuleId !== ESTIMATE_MODULE_ID &&
          referenceModuleId !== BILL_MODULE_ID) ||
        isRetainageItem
      ) {
        return false; // DON'T RENDER
      }
      return true; // Include this item
    });

    setInvoiceRecordList(newData);
    return newData;
  }, [JSON.stringify(invoiceItems?.items), details?.is_new_tm_invoice]);

  const lumpSumItemListFilter = useMemo(() => {
    if (filter.length === 0) {
      setIsActiveTable([invoiceItemsList?.length > 0 ? "1" : "0"]);
      return invoiceItemsList.sort(
        (a, b) => Number(a?.invoice_item_no) - Number(b.invoice_item_no)
      );
    }

    const returnData = invoiceItemsList
      .filter(
        (item) =>
          item?.item_type_key !== undefined &&
          filter.includes(item?.item_type_key)
      )
      .sort((a, b) => Number(a?.invoice_item_no) - Number(b?.invoice_item_no));

    setIsActiveTable([returnData?.length > 0 ? "1" : "0"]);
    return returnData;
  }, [invoiceItemsList, filter]);

  const totalData = useMemo(() => {
    let total = 0;
    if (lumpSumItemListFilter?.length > 0) {
      lumpSumItemListFilter.forEach((entry) => {
        const amount = entry.total ? parseFloat(entry.total) : 0;
        total += amount;
      });
    }
    return {
      total: total.toFixed(2),
    };
  }, [lumpSumItemListFilter]);

  useEffect(() => {
    if (lumpSumItemListFilter?.length) {
      setInvoiceRecordList(lumpSumItemListFilter);
    }
  }, [lumpSumItemListFilter]);

  const handleColseConfirmModal = () => {
    if (selectedParams?.params?.node && selectedParams?.fieldName) {
      const { data, node, oldValue, newValue } = selectedParams?.params;
      node.setData({
        ...data,
        [selectedParams?.fieldName]:
          selectedParams?.fieldName === "unit_cost" ? oldValue * 100 : oldValue,
      });
    }
    setSelectedParams({ params: null, fieldName: null });
  };

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "",
      field: "move",
      minWidth: 30,
      maxWidth: 30,
      suppressMenu: true,
      rowDrag: true,
      cellClass: "ag-cell-center ag-move-cell custom-move-icon-set",
      cellRenderer: () => {
        return (
          <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
            <FontAwesomeIcon
              className="w-4 h-4 text-[#4b5a76]"
              icon="fa-solid fa-grip-dots"
            />
          </div>
        );
      },
    },
    {
      headerName: _t("Type"),
      maxWidth: 50,
      minWidth: 50,
      field: "item_type_key",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const itemType = data?.item_type_key;
        const itemTypeName = data?.item_type_name;
        const itemTypeIcon = ICONSFORITEMSTYPE[itemType as IconKey];
        return itemTypeIcon ? (
          <Tooltip title={itemTypeName}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              icon={itemTypeIcon}
            />
          </Tooltip>
        ) : (
          <></>
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: 220,
      maxWidth: 220,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",

      cellClass: "ag-cell-left",
      editable:
        !isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      valueGetter: (params: IIVLumpSumTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(params?.data?.subject)),
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          if (nVal == "") {
            notification.error({
              description: "Item Name is required.",
            });
            return false;
          }
          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              subject: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "subject");
            return false;
          }
          const updatedData = {
            ...params.data,
            subject: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "subject");
        }
        return true;
      },
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const subject = HTMLEntities.decode(sanitizeString(data?.subject));
        return subject ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">
              {subject || "-"}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code_name",
      minWidth: 560,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",

      cellEditor: "agRichSelectCellEditor",
      editable:
        !isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      cellEditorParams: {
        values: codeCostData?.map((item: ICostCode) =>
          generateCostCodeLabel({
            name: item?.cost_code_name,
            code: item?.csi_code,
            isArchived: false,
            isAllowCodeWithoutName: true,
          })
        ),
        filterList: true,
        searchType: "matchAny",
        allowTyping: true,
        valueListMaxHeight:
          invoiceItemsList?.length == 1
            ? 60
            : invoiceItemsList?.length == 2
            ? 90
            : invoiceItemsList?.length == 3
            ? 120
            : invoiceItemsList?.length == 4
            ? 150
            : 180,
      },
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? generateCostCodeLabel({
              name: params.data?.cost_code_name,
              code: params.data?.csi_code,
              isArchived: false,
            })
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        const { newValue } = params;
        const updatedData = {
          ...params.data,
          cost_code_name: newValue,
        };

        if (params && params.node) {
          params.node.setData(updatedData);
        }
        updateCellValueApi(params, "cost_code_id");
        return true;
      },
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const costCode = generateCostCodeLabel({
          name: data?.cost_code_name || "",
          code: data?.csi_code,
          isArchived: data?.code_is_deleted == 1,
        });
        return costCode ? (
          <Tooltip title={costCode}>
            <Typography className="table-tooltip-text">
              {costCode || "-"}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "quantity",
      maxWidth: 80,
      minWidth: 80,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      suppressKeyboardEvent,
      cellClass: "ag-cell-right",
      editable:
        !isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      cellEditor: "agNumberCellEditor",
      valueGetter: (params: IIVLumpSumTableCellRenderer) =>
        params?.data?.quantity,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              quantity: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "quantity");
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);
          const integerPartLength = nVal.toString().split(".")[0].length;

          if (integerPartLength > 6 || !checkNum) {
            notification.error({
              description: "Quantity should be less than or equal to 6 digits.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }

          const updatedData = {
            ...params.data,
            quantity: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "quantity");
        }
        return true;
      },
      cellRenderer: (params: IIVLumpSumTableCellRenderer) => {
        const quantityUnit =
          formatter(
            formatAmount(Number(params?.data.quantity), { isQuantity: true })
          ).value || 0;
        return (
          <Tooltip title={quantityUnit}>
            <Typography className="table-tooltip-text">
              {quantityUnit}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Cost"),
      field: "unit_cost",
      maxWidth: 130,
      minWidth: 130,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      suppressKeyboardEvent,
      cellClass: "ag-cell-right",
      editable:
        !isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      cellEditor: "agNumberCellEditor",
      valueGetter: (params: IIVLumpSumTableCellRenderer) =>
        Number(params?.data?.unit_cost) / 100,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              unit_cost: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "unit_cost");
            return false;
          }

          if (
            Number(params.newValue) < 0 &&
            params?.data?.is_discount_item != 1
          ) {
            notification.error({
              description: _t("Negative values are not allowed for Cost."),
            });
            return false;
          }
          // If discount item, allow 11 digits (Becuase of - sign)
          const maxLengthAllow = params?.data?.is_discount_item != 1 ? 10 : 11;
          const checkNum = qtyNumberCheck(nVal);
          const cleanedValue =
            nVal != null ? nVal?.toString().split(".")[0].replace("-", "") : "";
             const fullStr = BigInt(Math.floor(Number(nVal))).toString();
             if (
               cleanedValue &&
               cleanedValue != null &&
               fullStr?.length > maxLengthAllow
             ) {
               notification.error({
                 description:
                   "Unit cost should be less than or equal to 10 digits.",
               });
               return false;
             }
          if (!floatWithNegativeRegex.test(nVal)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(2);
          }

          const updatedData = {
            ...params.data,
            unit_cost: nVal * 100,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "unit_cost");
        }
        return true;
      },
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const nValue = Number(data?.unit_cost) / 100;
        const unitCost = formatter(
          formatAmount(nValue.toFixed(2))
        ).value_with_symbol;
        return unitCost ? (
          <Tooltip title={unitCost}>
            <Typography className="table-tooltip-text">{unitCost}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      maxWidth: 100,
      minWidth: 100,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      editable:
        !isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      ...(window.ENV.ENABLE_UNIT_DROPDOWN && {
        suppressKeyboardEvent: (params) => {
          if (params.event.key === "Enter") {
            params.event.preventDefault();
            return true; // Block Ag-Grid's default behavior
          }
          return false;
        },
        cellEditorParams: {
          values: units,
          onKeyDown: (
            e: React.KeyboardEvent<HTMLInputElement>,
            data: IInvoiceItemData
          ) => {
            if (e.key === "Enter") {
              const value = e?.currentTarget?.value?.trim();
              const newType = onEnterSelectSearchValue(
                e,
                units?.map((unit) => ({
                  label: unit?.name,
                  value: "",
                })) || []
              );
              if (newType) {
                setNewUnitName(newType);
                setSelectedData(data);
              } else if (value) {
                notification.error({
                  description:
                    "Records already exist, no new records were added.",
                });
              }
            }
          },
        },
        cellEditor: UnitCellEditor<IInvoiceItemData>,
      }),
      valueGetter: (params: IIVLumpSumTableCellRenderer) => params?.data?.unit,
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const nVal = window.ENV.ENABLE_UNIT_DROPDOWN
            ? (params?.newValue?.name?.trim() || "")?.toString()
            : (params?.newValue || "")?.toString();

          if (!nVal || nVal == null) {
            const updatedData = {
              ...params.data,
              unit: "",
            };
            params.node.setData(updatedData);
            updateCellValueApi(params, "unit");
            return false;
          }

          if (Number(params.newValue) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Unit."),
            });
            return false;
          }

          if (nVal && nVal.toString().length > 15) {
            notification.error({
              description:
                "Unit should be less than or equal to 15 characters.",
            });
            return false;
          }

          const updatedData = {
            ...params.data,
            unit: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "unit");
        }
        return true;
      },
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const unit = data?.unit;
        return unit ? (
          <Tooltip title={unit}>
            <Typography className="table-tooltip-text">{unit}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("MU%"),
      field: "markup",
      minWidth: 80,
      maxWidth: 80,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellEditor: "agNumberCellEditor",
      suppressMovable: false,
      suppressMenu: true,
      editable: (params: IIVLumpSumTableCellRenderer) =>
        Boolean(!isReadOnly) &&
        Boolean(params?.data?.is_markup_percentage) &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      valueGetter: (params: IIVLumpSumTableCellRenderer) =>
        params.data?.markup?.toString() ? params.data?.markup : "",
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const markupToShow = calMarkupPercentageIv(data);

        if (
          data.markup === null ||
          data.markup === undefined ||
          data.markup === ""
        ) {
          return (
            <Typography className="table-tooltip-text text-center text-gray-400">
              -
            </Typography>
          );
        }

        return (
          <div className="flex gap-1 overflow-hidden w-full justify-end">
            {!data?.is_markup_percentage && Boolean(!isReadOnly) ? (
              <Tooltip
                title={_t(
                  `To edit a ${
                    formatter().currency_symbol
                  } amount View and Edit the Item`
                )}
                placement="top"
              >
                <FontAwesomeIcon
                  className="w-3.5 h-3.5 text-primary-900"
                  icon="fa-regular fa-circle-info"
                />
              </Tooltip>
            ) : null}
            {markupToShow ? (
              <Tooltip title={markupToShow}>
                <Typography className="table-tooltip-text">
                  {markupToShow}
                </Typography>
              </Tooltip>
            ) : (
              "-"
            )}
          </div>
        );
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue?.toString();
          if (!nVal || nVal == null) {
            const _updateData = {
              ...params.data,
              markup: null,
            };
            params.node.setData(_updateData);

            updateCellValueApi(params, "markup");

            return true;
          }
          if (Number(params.newValue) < 0) {
            notification.error({
              description: _t("Negative values are not allowed for Markup."),
            });
            return false;
          }
          const checkNum = qtyNumberCheck(nVal);

          if (
            nVal &&
            nVal.toString().length > 3 &&
            (!nVal.toString().includes(".") || !checkNum)
          ) {
            notification.error({
              description: "Markup should be less than or equal to 3 digits.",
            });
            return false;
          }
          if (!wholeNumberRegex.test(nVal)) {
            notification.error({
              description: _t("Decimal is not allowed in Markup"),
            });
            return false;
          }
          if (nVal.toString().includes(".")) {
            nVal = nVal.toFixed(0);
          }
          const updatedData = {
            ...params.data,
            markup: Number(nVal),
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "markup");
        }
        return true;
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 130,
      maxWidth: 130,
      suppressMovable: false,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
        const nValue = Number(data?.total || "") / 100;
        const total = formatter(
          formatAmount(nValue.toFixed(2))
        ).value_with_symbol;
        return total ? (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Tax"),
      field: "apply_global_tax",
      minWidth: 50,
      maxWidth: 50,
      suppressMovable: false,
      suppressMenu: true,

      headerClass: "ag-header-center",
      cellClass: "ag-cell-center flex justify-center",
      editable:
        !isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold"),
      cellRenderer: "agCheckboxCellRenderer",
      cellEditor: "agCheckboxCellEditor",
      valueGetter: ({ data }: IIVLumpSumTableCellRenderer) => {
        return data?.apply_global_tax == 1;
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;
          const updatedData = {
            ...params.data,
            apply_global_tax: nVal,
          };
          params.node.setData(updatedData);
          updateCellValueApi(params, "apply_global_tax");
        }
        return true;
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: (params: IIVLumpSumTableCellRenderer) => {
        return (
          <div className="flex items-center gap-1.5 justify-end">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                setItemToBeUpdate(params.data as IInvoiceItemData);
                setIsItemTabsOpen(true);
                setIsAddInvoiceItem(false);
                setItemTabsType("lumpSum");
              }}
            />
            <div>
              {!isReadOnly &&
                (details?.approval_type_key === "invoice_open" ||
                  details?.approval_type_key === "invoice_on_hold") && (
                  <ButtonWithTooltip
                    tooltipTitle={_t("Delete")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-trash-can"
                    onClick={() => {
                      setSelectedItemId(Number(params?.data?.item_id) || 0);
                      setIsDeleteConfirmOpen(true);
                    }}
                  />
                )}
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="grid gap-2.5">
        <CollapseSingleTable
          title={_t("Lump Sum Items")}
          totalRecordIcon={true}
          activeKey={isActiveTable}
          onChange={(key: string | string[]) => {
            setIsActiveTable(Array.isArray(key) ? key : [key]);
          }}
          totalRecord={
            formatter(formatAmount((+totalData?.total / 100).toFixed(2)))
              .value_with_symbol
          }
          children={
            <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-6 before:bg-gradient-to-r from-primary-500">
              {isOriginalScopeLoading && lumpSumItemListFilter?.length < 1 ? (
                <Spin className="w-full h-20 flex items-center justify-center" />
              ) : (
                <div className="ag-theme-alpine">
                  <StaticTable
                    ref={gridRef}
                    className="static-table"
                    gridOptions={gridOptions}
                    rowDragManaged={!isReadOnly}
                    columnDefs={columnDefs}
                    stopEditingWhenCellsLoseFocus={true}
                    rowData={lumpSumItemListFilter}
                    noRowsOverlayComponent={() => (
                      <NoRecords
                        image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                      />
                    )}
                  />
                </div>
              )}
            </div>
          }
        />
      </div>

      {/* Confirm negative value allow */}
      {!!selectedParams?.fieldName && (
        <ConfirmModal
          isOpen={!!selectedParams?.fieldName}
          modaltitle={_t("Are you sure?")}
          description={_t(
            "Saving an Invoice with an amount less than 0 is not allowed by most accounting integrations. If you are not using an accounting system, click OK to continue or Cancel to revise the Invoice."
          )}
          modalIcon={"fa-regular fa-file-check"}
          onAccept={() => {
            setSelectedParams({ params: null, fieldName: null });
            updateCellValueApi(
              selectedParams.params as ValueSetterParams,
              selectedParams.fieldName as keyof IUpdateInvoiceItem,
              true
            );
          }}
          onDecline={handleColseConfirmModal}
          onCloseModal={handleColseConfirmModal}
        />
      )}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteItem}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.item_id === selectedData?.item_id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode?.setData({
                      ...currentRowNode.data,
                      unit: newUnitName,
                    });
                    const formData: IUpdateInvoiceItemsParams = {
                      id: Number(iVId || ""),
                      items: [
                        {
                          item_id: oldData?.item_id,
                          subject: oldData?.subject,
                          quantity: oldData?.quantity,
                          unit_cost: oldData?.unit_cost,
                          markup: oldData?.markup,
                          is_markup_percentage: oldData?.is_markup_percentage,
                          cost_code_id: oldData?.cost_code_id,
                          total: oldData?.total,
                          item_type: oldData?.item_type,
                          unit: newUnitName,
                          assigned_to: oldData?.assigned_to,
                          assigned_to_contact_id:
                            oldData?.assigned_to_contact_id,
                          apply_global_tax: oldData?.apply_global_tax,
                          tax_id: oldData?.tax_id,
                          reference_item_id: oldData?.reference_item_id,
                          // invoice_total: oldData?.invoice_total,
                          description: oldData?.description,
                          notes: oldData?.notes,
                          add_item_to_database: oldData?.add_item_to_database,
                        },
                      ],
                    };
                    try {
                      const updatedItems = {
                        ...invoiceItems,
                        import_from_estimate_section:
                          invoiceItems?.import_from_estimate_section?.map(
                            (section) => ({
                              ...section,
                              items: section?.items?.map((item) =>
                                item.item_id == formData.items[0].item_id
                                  ? { ...item, ...formData.items[0] }
                                  : item
                              ),
                            })
                          ),
                      };
                      dispatch(updateInvoiceItems({ items: updatedItems }));

                      const response = (await updateInvoiceItem(
                        formData as IUpdateInvoiceItemsParams
                      )) as IUpdateInvoiceItemsApiRes;

                      if (response?.success) {
                        dispatch(resetDash());
                        const existingColDefs = api.getColumnDefs();
                        if (!existingColDefs) return;

                        const updatedColDefs = existingColDefs.map((col) =>
                          "field" in col && col.field === "unit"
                            ? {
                                ...col,
                                filterParams: {
                                  values:
                                    newUnits.map((unit) => ({
                                      label: unit.name?.toString(),
                                      value: unit.name?.toString(),
                                    })) ?? [],
                                },
                                cellEditorParams: {
                                  ...col.cellEditorParams,
                                  values: newUnits,
                                },
                              }
                            : col
                        );

                        api.setColumnDefs(updatedColDefs);

                        // Ensure the grid header re-renders
                        api.refreshHeader();
                      } else {
                        currentRowNode?.setData(oldData);
                        dispatch(
                          fetchInvoiceItemsList({
                            id: Number(iVId),
                            data_for: "all",
                            need_section:
                              details?.is_new_tm_invoice == 1 ? 1 : 0,
                            is_separate_change_order_sections:
                              details.is_new_tm_invoice === 0
                                ? true
                                : undefined,
                            is_separate_estimate_sections: false,
                            isHideLoading: true,
                          })
                        );
                        notification.error({
                          description: response?.message,
                        });
                      }
                    } catch (error) {
                      notification.error({
                        description: (error as Error)?.message,
                      });
                      dispatch(
                        fetchInvoiceItemsList({
                          id: Number(iVId),
                          data_for: "all",
                          need_section: details?.is_new_tm_invoice == 1 ? 1 : 0,
                          is_separate_change_order_sections:
                            details.is_new_tm_invoice === 0 ? true : undefined,
                          is_separate_estimate_sections: false,
                          isHideLoading: true,
                        })
                      );
                    }
                  }
                }
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
};

export default LumpSumItems;
