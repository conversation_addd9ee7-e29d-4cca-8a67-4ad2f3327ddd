import * as Yup from "yup";
import isEqual from "lodash/isEqual";

// Hook
import { useTranslation } from "~/hook";

// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { IAddProjectItemProps } from "./type";
import { type RadioChangeEvent } from "antd";
import { DefaultProjectItemData } from "../../../../utils/constants";
import isEmpty from "lodash/isEmpty";
import { Form } from "@remix-run/react";
import { useFormik } from "formik";
import { Number, sanitizeString } from "~/helpers/helper";
import { useAppProDispatch, useAppProSelector } from "../../../../redux/store";
import {
  filterOptionBySubstring,
  floatNumberRegex,
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { addUnit } from "~/redux/action/unitActions";
import { onKeyDownNumber } from "~/helpers/key-down.helper";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  addProjectItemTodb,
  updateProjectItemTodb,
} from "../../../../redux/action/projectSovAction";
import { isValidId } from "~/modules/financials/pages/estimates/utils/common";
import {
  updateBudgetItems,
  updateUnitData,
} from "../../../../redux/slices/proSovSlice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { Tooltip } from "~/shared/components/atoms/tooltip";

const AddProjectItem = ({
  addProjectItem,
  setAddProjectItem,
  isReadOnly = false,
  itemToEdit,
  isProjectItemAdd: isAddItem,
  items,
  setItemToEdit,
  callSummaryApi,
  callProjectSOVItemsApi,
}: IAddProjectItemProps) => {
  const { _t } = useTranslation();

  const { itemTypes } = useAppProSelector((state) => state.itemTypes);
  const { details } = useAppProSelector((state) => state.proDetails);
  const { codeCostData }: IGetCostCodeList = useAppProSelector(
    (state) => state.costCode
  );
  const { unitData } = useAppProSelector((state) => state.proSov);

  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const [isProjectItemAdd, setIsProjectItemAdd] = useState<boolean>(
    isAddItem ?? false
  );

  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isReadOnly);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isAddSaveAnotherData, setAddSaveAnotherData] = useState<boolean>(false)
  const [isMuPercentFieldChanged, setIsMuPercentFieldChanged] =
    useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [existingCidbItem, setExistingCidbItem] =
    useState<IAddProjectItemApiRes | null>();
  const [submitAction, setSubmitAction] = useState<
    "save" | "save_n_add_another"
  >();
  const [shouldHideNextPrev, setShouldHideNextPrev] = useState<boolean>(false);

  const [emailSiderBar, setEmailSiderBar] = useState<{
    open: boolean;
    data: Partial<IDirectoryData>;
  }>({
    open: false,
    data: {},
  });

  const [newTypeName, setNewTypeName] = useState<string>("");

  const unitCostContainerRef = useRef<HTMLDivElement>(null);

  const dispatch = useAppProDispatch();

  const PROJECT_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base min-w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {formatter().currency_symbol}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];

  useEffect(() => {
    setIsProjectItemAdd(isAddItem ?? false);
  }, [isAddItem]);

  const handleParagraphClick = () => {
    if (!isReadOnly) {
      setShowUnitInputs(true);
    }
  };

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };

  const initialValues = useMemo(() => {
    if (isAddSaveAnotherData) {
      return DefaultProjectItemData;
    } else {
      return {
        ...itemToEdit,
        unit_cost: (Number(itemToEdit?.unit_cost || 0) / 100)?.toFixed(2),
        markup: itemToEdit?.markup?.toString()
          ? itemToEdit?.is_markup_percentage === 0
            ? (Number(itemToEdit?.markup) / 100)?.toString()
            : itemToEdit?.markup?.toString()
          : "",
      };
    }
  }, [isAddSaveAnotherData, itemToEdit]);

  const validationSchema = useMemo(
    () =>
      Yup.object().shape({
        subject: Yup.string().required("This field is required."),
        item_type: Yup.string().required("This field is required."),
      }),
    []
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      setSubmitting(true);
      const payload = {
        directory_id: details.estimator, // should change in Future
        subject: values.subject,
        quantity: Number(values.quantity) || "",
        unit: values.unit || "",
        unit_cost: Number(values.unit_cost) * 100 || "",
        cost_code_id: values.cost_code_id || "",
        markup: values?.markup?.toString()
          ? values?.is_markup_percentage === 0
            ? (Number(values?.markup) * 100)?.toString()
            : values?.markup?.toString()
          : null,
        is_markup_percentage: values.is_markup_percentage,
        apply_global_tax: values.apply_global_tax,
        item_type: Number(values.item_type) || "",
        reference_item_type_id: Number(values?.reference_item_type_id) || "",
        assignee_name: values.assignee_name ?? "",
        item_assigned_to: Number(values.item_assigned_to) || "",
        assigned_to_image: values.assigned_to_image ?? "",
        item_assigned_to_contact_id:
          Number(values.item_assigned_to_contact_id) || "",
        description: values.description || "",
        internal_notes: values.internal_notes || "",
        add_item_to_database: values.item_on_database,
        is_single_item: 1,
        total: Number(grandTotal) * 100 || "",
      };

      const itemData = (
        isAddSaveAnotherData ? getValuableObj(payload) : payload
      ) as Partial<ISOVBudgetItemsData>;

      let itemRes: IAddProjectItemApiRes | null = null;

      if (isAddSaveAnotherData) {
        itemRes = (await addProjectItemTodb(
          Number(details.id),
          itemData
        )) as IAddProjectItemApiRes;
      } else {
        const payload = {
          items: [{ ...itemToEdit, ...itemData }],
        };

        itemRes = (await updateProjectItemTodb(
          Number(details.id),
          payload
        )) as IAddProjectItemApiRes;
      }

      if (itemRes?.success) {
        if (!isAddSaveAnotherData) {
          const oldVal = {
            quantity: Number(initialValues?.quantity) || null,
            unit_cost: Number(initialValues?.unit_cost) || null,
            item_type: Number(initialValues?.item_type) || null,
            markup: Number(initialValues?.markup) || null,
          };

          const newVal = {
            quantity: Number(values?.quantity) || null,
            unit_cost: Number(values?.unit_cost) || null,
            item_type: Number(values?.item_type) || null,
            markup: Number(values?.markup) || null,
          };

          const isQtyORPriceChanged = !isEqual(oldVal, newVal);

          // If Qunatity OR Unit Cost is changed, then and then we'll call summary API, otherwise not.
          if (isQtyORPriceChanged) {
            await callSummaryApi();
          }

          dispatch(
            updateBudgetItems({
              item_id: itemToEdit?.item_id,
              updatedItem: { ...itemToEdit, ...itemData },
            })
          );
        } else {
          await Promise.all([callSummaryApi(), callProjectSOVItemsApi?.()]);
        }

        if (submitAction === "save") {
          closeItemdrawer();
          if (shouldHideNextPrev) {
            setShouldHideNextPrev(false);
          }
        } else if (submitAction === "save_n_add_another") {
          setShouldHideNextPrev(true);
          setIsProjectItemAdd(true);
          setShowUnitInputs(true);
          resetForm({ values: DefaultProjectItemData });
        }
      } else {
        if (isValidId(itemRes?.data?.reference_item_id)) {
          setExistingCidbItem(itemRes);
        } else {
          notification.error({
            description: itemRes?.message || "Faield to add item",
          });
        }
      }
      setSubmitting(false);
    },
  });

  const {
    handleSubmit,
    handleBlur,
    setFieldValue,
    values,
    touched,
    errors,
    resetForm,
    isSubmitting,
    submitForm,
  } = formik;

  useEffect(() => {
    resetForm({ values: initialValues });
    setShouldHideNextPrev(false);
  }, [initialValues]);

  useEffect(() => {
    if (!isAddSaveAnotherData) {
      changeUitInputState(values?.unit_cost || "", values.unit || "");
    }
  }, []);

  useEffect(() => {
    setAddSaveAnotherData(!!isProjectItemAdd)
  }, [isProjectItemAdd])

  const isMarkupPercentage = useMemo(() => {
    return Number(values.is_markup_percentage) === 1;
  }, [values.is_markup_percentage]);

  const itemTypeOptions = useMemo(
    () =>
      itemTypes.length
        ? itemTypes
          .filter((item: IItemTypes) => item.type_id)
          .map((item: IItemTypes) => {
            return {
              label: (
                <div className="flex items-center gap-1.5">
                  <FontAwesomeIcon
                    icon={getItemTypeIcon({
                      type: item?.type_id?.toString() || "",
                    })}
                  />
                  {item?.name}
                </div>
              ),
              value: item.type_id?.toString() || "",
            };
          })
        : [],
    [itemTypes]
  );

  const costCodeOptions = useMemo(() => {
    let costCodeOpts = codeCostData.map((item: ICostCode) => {
      return {
        label: `${item.cost_code_name}${item.csi_code ? ` (${item.csi_code})` : ""
          }${item.is_deleted === 1 ? ` (Archived)` : ""}`,
        value: item.code_id,
      };
    });

    if (!isAddSaveAnotherData && itemToEdit?.is_deleted === 1) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          label: `${itemToEdit?.cost_code_name}${itemToEdit.cost_code ? ` (${itemToEdit.cost_code})` : ""
            } (Archived)`,
          value: itemToEdit.cost_code_id?.toString() || "",
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData]);

  const selectedAssignedTo = useMemo(() => {
    if (Number(values?.item_assigned_to) !== 0) {
      const assigned_to = [
        {
          display_name: values?.assignee_name,
          user_id: Number(values?.item_assigned_to),
          contact_id: Number(values?.item_assigned_to_contact_id) ?? 0,
          image:
            Number(values?.item_assigned_to_contact_id) === 0
              ? values?.assigned_to_image
              : "",
          type: values?.assignee_type,
          type_key: getDirectaryKeyById(
            Number(values?.assignee_type),
            undefined
          ),
        },
      ];
      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [
    values?.item_assigned_to,
    values.assignee_name,
    values.assignee_type,
    values.item_assigned_to_contact_id,
    values.assigned_to_image,
  ]);

  var changeUitInputState = (unit_cost: string | number, unit: string) => {
    if (Number(unit_cost) && !isEmpty(unit)) {
      setShowUnitInputs(false);
    }
  };

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      changeUitInputState(values?.unit_cost || "", values.unit || "");
    }
  };

  const handleChangeAssignedTo = (data: TselectedContactSendMail[]) => {
    if (data.length) {
      setFieldValue("item_assigned_to", data[0].user_id);
      setFieldValue("item_assigned_to_contact_id", data[0].contact_id);
      setFieldValue("assignee_name", data[0].display_name);
      setFieldValue(
        "assignee_type",
        data[0].type ||
        getDirectaryIdByKey(data[0].type_key as CustomerTabs, undefined)
      );
      setFieldValue("assigned_to_image", data[0].image);
    } else {
      setFieldValue("item_assigned_to", 0);
      setFieldValue("item_assigned_to_contact_id", 0);
      setFieldValue("assignee_name", "");
      setFieldValue("assigned_to_image", "");
    }
  };

  const closeEmailSidebar = () => {
    setEmailSiderBar({
      open: false,
      data: {},
    });
  };

  const closeItemdrawer = () => {
    resetForm();
    setAddProjectItem(false);
    setShowUnitInputs(true);
  };

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show =
      isAddSaveAnotherData || Number(initialValues.reference_item_id) === 0;
    const disable =
      !isAddSaveAnotherData && Number(initialValues.item_on_database) === 1;
    return { show, disable };
  }, [isAddSaveAnotherData, values]);

  const saveItemKey = useMemo(() => {
    const itemType = itemTypes?.find(
      (i: IItemTypes) => i.type_id?.toString() === values?.item_type?.toString()
    );

    return itemType?.name;
  }, [itemTypes, values?.item_type]);

  const totalWithoutMU = useMemo(() => {
    const unitCost = Number(values.unit_cost || 0);
    const qty = Number(values.quantity || 0);
    const total = qty * unitCost;
    return total;
  }, [values.unit_cost, values.quantity]);

  var grandTotal = useMemo(() => {
    const markupToConsider = Number(values.markup || 0);

    if (isMarkupPercentage) {
      const markupOnTotal = (totalWithoutMU * markupToConsider) / 100;
      const totalWithMU = totalWithoutMU + markupOnTotal;
      return totalWithMU;
    } else {
      return markupToConsider ? markupToConsider : totalWithoutMU;
    }
  }, [values.unit_cost, values.quantity, isMarkupPercentage, values.markup]);

  const markUpAmount = useMemo(() => {
    const markupToConsider = Number(values.markup || 0);

    if (isMarkupPercentage) {
      const amount = (totalWithoutMU * markupToConsider) / 100;
      return amount;
    } else {
      const percent =
        totalWithoutMU === 0
          ? 0
          : (markupToConsider * 100) / totalWithoutMU - 100;
      return markupToConsider ? percent : 0;
    }
  }, [values.unit_cost, values.quantity, isMarkupPercentage, values.markup]);

  const addNewUnit = useCallback(async () => {
    if (!isAddingCustomData && newTypeName) {
      setIsAddingCustomData(true);
      const response = (await addUnit({
        name: newTypeName,
      })) as IUnitAddResponse;

      if (response.success && response.data) {
        dispatch(updateUnitData([{ name: newTypeName }, ...unitData]));
        setFieldValue("unit", newTypeName);
        setNewTypeName("");
      } else {
        notification.error({
          description: response.message,
        });
      }
      setIsAddingCustomData(false);
    }
  }, [isAddingCustomData, newTypeName, unitData]);

  const getItem = useCallback((item: Partial<ISOVBudgetItemsData>) => {
    return {
      subject: item?.subject || null,
      item_type: Number(item?.item_type) || null,
      item_assigned_to: Number(item?.item_assigned_to) || null,
      cost_code_id: Number(item?.cost_code_id) || null,
      quantity: Number(item?.quantity) || null,
      unit_cost: Number(item?.unit_cost) || null,
      unit: item?.unit || null,
      is_markup_percentage: Number(item?.is_markup_percentage) || null,
      markup: item?.markup,
      description: item?.description || null,
      internal_notes: item?.internal_notes || null,
      item_on_database: Number(item?.item_on_database) || null,
      apply_global_tax: Number(item?.apply_global_tax) || null,
    };
  }, []);

  const isItemChanged = useMemo(() => {
    const initItem = getItem(initialValues ?? {});
    const latestItem = getItem(values);

    const isItemSame = isEqual(initItem, latestItem);

    if (!isItemSame) {
      return true;
    }
    return false;
  }, [itemToEdit, values]);

  const currenItemIndex = useMemo(() => {
    return (
      items?.findIndex(
        (i) => i.item_id?.toString() === itemToEdit?.item_id?.toString()
      ) || 0
    );
  }, [items, itemToEdit]);

  const validateFormBeforeNavigation = async () => {
    const errors = await formik.validateForm();
    return Object.keys(errors).length === 0;
  };

  const onClickNext = async () => {
    if (items?.length) {
      const isFormValid = await validateFormBeforeNavigation();
      if (currenItemIndex !== items?.length - 1 && isFormValid) {
        if (isItemChanged) {
          await submitForm();
        }
        const nextItemToEdit = items?.[currenItemIndex + 1];
        setItemToEdit?.(nextItemToEdit);
      }
    }
  };

  const onClickPrevious = async () => {
    const isFormValid = await validateFormBeforeNavigation();
    if (currenItemIndex !== 0 && isFormValid) {
      if (isItemChanged) {
        await submitForm();
      }
      const prevItemToEdit = items?.[currenItemIndex - 1];
      setItemToEdit?.(prevItemToEdit);
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };

  return (
    <Drawer
      open={addProjectItem}
      rootClassName="drawer-open"
      width={718}
      push={false}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-clipboard-list"
            />
          </div>
          <div className="flex justify-between w-full">
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                (!isAddSaveAnotherData ? "" : "Add ") +
                HTMLEntities.decode(
                  sanitizeString(currentMenuModule?.singular_name)
                ) +
                " Item"
              )}
            </Header>
            {!isProjectItemAdd && !shouldHideNextPrev && (
              <div className="flex items-center sm:gap-2 gap-0 pr-2">
                <ButtonWithTooltip
                  tooltipTitle={_t("Previous")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-chevron-left"
                  className="item-pre-next-button disabled:bg-transparent"
                  onClick={onClickPrevious}
                  disabled={currenItemIndex === 0 || isSubmitting}
                />
                <ButtonWithTooltip
                  tooltipTitle={_t("Next")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-chevron-right"
                  className="item-pre-next-button disabled:bg-transparent"
                  onClick={onClickNext}
                  disabled={
                    (items && currenItemIndex === items?.length - 1) ||
                    isSubmitting
                  }
                />
              </div>
            )}
          </div>
        </div>
      }
      closeIcon={<CloseButton onClick={closeItemdrawer} />}
    >
      <Form className="py-4" method="post" onSubmit={handleSubmit} noValidate>
        <div className="sidebar-body overflow-y-auto sm:h-[calc(100vh-132px)] h-[calc(100vh-173px)] px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="grid gap-3.5">
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="subject"
                    id="subject"
                    value={_t(
                      HTMLEntities.decode(
                        sanitizeString(values.subject?.toString() || "")
                      )
                    )}
                    errorMessage={
                      touched?.subject && !String(values.subject || "").trim()
                        ? errors.subject
                        : ""
                    }
                    onChange={(e) => {
                      setFieldValue(
                        "subject",
                        e.target.value.trimStart()
                      );
                    }}
                    onBlur={handleBlur}
                    autoComplete="off"
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      name="item_type"
                      errorMessage={
                        touched?.item_type && !values.item_type
                          ? errors.item_type
                          : ""
                      }
                      disabled={
                        isReadOnly ||
                        !itemTypeAndSaveItemToListField.show ||
                        itemTypeAndSaveItemToListField.disable
                      }
                      value={
                        Number(values?.item_type) !== 0
                          ? HTMLEntities.decode(
                            sanitizeString(values?.item_type?.toString())
                          )
                          : "" // Show nothing (blank) if item_type is "0"
                      }
                      onChange={(value) => {
                        setFieldValue("item_type", value);

                        if (!isMuPercentFieldChanged) {
                          const itemType = itemTypes?.find(
                            (i: IItemTypes) =>
                              i.type_id?.toString() === value?.toString()
                          );

                          setFieldValue("markup", itemType?.mark_up || "");
                        }
                      }}
                      onBlur={handleBlur}
                      options={itemTypeOptions}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Assigned To")}
                      name="item_assigned_to"
                      labelPlacement="top"
                      disabled={isReadOnly}
                      value={HTMLEntities.decode(
                        sanitizeString(
                          values?.item_assigned_to !== 0
                            ? values?.assignee_name
                            : ""
                        )
                      )}
                      onClick={() => {
                        setIsOpenSelectAssignedTo(true);
                      }}
                      addonBefore={
                        values?.item_assigned_to ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setContactDetailDialogOpen(true);
                              }}
                            />
                          </div>
                        ) : (
                          <></>
                        )
                      }
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values?.assignee_name)
                          ),
                          image: !Number(values?.item_assigned_to_contact_id)
                            ? values?.assigned_to_image
                            : "",
                        },
                      }}
                    />
                  </div>
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    value={
                      values?.cost_code_id
                        ? costCodeOptions.filter((item) => {
                          return (
                            values?.cost_code_id?.toString() ===
                            item?.value?.toString()
                          );
                        })
                        : []
                    }
                    onChange={(value) => {
                      setFieldValue("cost_code_id", value);
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    options={costCodeOptions}
                    disabled={isReadOnly}
                    allowClear={true}
                    onClear={() => {
                      setFieldValue("cost_code_id", "");
                    }}
                  />
                </div>
              </div>
            </SidebarCardBorder>
            <SidebarCardBorder cardTitle="Pricing">
              <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Qty")}
                  </Typography>
                  <div className="sm:w-40 w-28">
                    <InputNumberField
                      name="quantity"
                      id="quantity"
                      rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                      placeholder={_t("Item Quantity")}
                      disabled={isReadOnly}
                      onPaste={handlePaste}
                      errorMessage={errors.quantity}
                      labelPlacement="left"
                      formInputClassName={
                        isReadOnly ? "flex items-center justify-end" : ""
                      }
                      defaultValue={
                        Number(values.quantity) !== 0 ? values.quantity : ""
                      }
                      value={
                        Number(values.quantity) !== 0
                          ? values.quantity?.toString()
                          : ""
                      }
                      formatter={(value) => {
                        return inputFormatter(value?.toString()).value;
                      }}
                      onChange={(value) => {
                        setFieldValue("quantity", value?.toString());
                      }}
                      parser={(value) => {
                        const inputValue = value
                          ? unformatted(value.toString())
                          : "";
                        return inputValue;
                      }}
                      onKeyDown={(event) =>
                        onKeyDownCurrency(event, {
                          integerDigits: 6,
                          decimalDigits: 2,
                          unformatted,
                          allowNegative: true,
                          decimalSeparator: inputFormatter().decimal_separator,
                        })
                      }
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                </li>
                <li>
                  <ul className="py-0.5 relative">
                    <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                      <FontAwesomeIcon
                        className="w-3 h-3 text-primary-900 dark:text-white"
                        icon="fa-regular fa-xmark"
                      />
                    </li>
                  </ul>
                </li>
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Unit Cost/Unit")}
                  </Typography>
                  <div className="sm:w-[260px] w-28 h-[22px]">
                    <div
                      ref={unitCostContainerRef}
                      className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                    >
                      {!isReadOnly && (
                        <>
                          {showUnitInputs ? (
                            <div className="flex gap-2">
                              <div className="w-[calc(100%-52px)]">
                                <InputNumberField
                                  name="unit_cost"
                                  id="unit_cost"
                                  rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                  placeholder={_t("Item Unit Cost")}
                                  disabled={isReadOnly}
                                  labelPlacement="left"
                                  errorMessage={errors.unit_cost}
                                  onPaste={handlePaste}
                                  autoFocus={Boolean(
                                    Number(values?.unit_cost) &&
                                    !isEmpty(values.unit)
                                  )}
                                  defaultValue={
                                    Number(values.unit_cost) !== 0
                                      ? values.unit_cost
                                      : ""
                                  }
                                  value={
                                    Number(values.unit_cost) !== 0
                                      ? values.unit_cost
                                      : ""
                                  }
                                  onChange={(value) => {
                                    let formattedValue = value
                                      ?.toString()
                                      .trim();
                                    if (formattedValue?.startsWith("-")) {
                                      formattedValue = formattedValue?.replace(
                                        "-",
                                        ""
                                      );
                                    }
                                    setFieldValue("unit_cost", formattedValue);
                                  }}
                                  formatter={(value, info) => {
                                    return inputFormatter(value?.toString())
                                      .value;
                                  }}
                                  parser={(value) => {
                                    const inputValue = value
                                      ? unformatted(value.toString())
                                      : "";
                                    return inputValue;
                                  }}
                                  onKeyDown={(event) => {
                                    if (
                                      (event.ctrlKey || event.metaKey) &&
                                      (event.key.toLowerCase() === "c" ||
                                        event.key.toLowerCase() === "v")
                                    ) {
                                      return;
                                    }
                                    if (
                                      event.key === "-" ||
                                      event.key === "Subtract"
                                    ) {
                                      event.preventDefault();
                                      return;
                                    }
                                    onKeyDownCurrency(event, {
                                      integerDigits: 10,
                                      decimalDigits: 2,
                                      unformatted,
                                      allowNegative: false,
                                      decimalSeparator:
                                        inputFormatter().decimal_separator,
                                    });
                                  }}
                                  onBlur={handleFocusOut}
                                />
                              </div>
                              <div className="w-[62px]">
                                <SelectField
                                  className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                  placeholder="Unit"
                                  name="unit"
                                  disabled={isReadOnly}
                                  labelPlacement="left"
                                  maxLength={15}
                                  value={values?.unit || null}
                                  iconView={true}
                                  popupClassName="!w-[260px]"
                                  showSearch
                                  options={
                                    unitData.map((type) => ({
                                      label: type.name.toString(),
                                      value: type.name.toString(),
                                    })) ?? []
                                  }
                                  allowClear
                                  filterOption={(input, option) =>
                                    filterOptionBySubstring(
                                      input,
                                      option?.label as string
                                    )
                                  }
                                  onChange={(value) => {
                                    setFieldValue("unit", value);
                                  }}
                                  onBlur={handleFocusOut}
                                  addItem={{
                                    text: "Add Unit: Type Unit & Press Enter",
                                    icon: "fa-regular fa-plus",
                                  }}
                                  onInputKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                      const value =
                                        e?.currentTarget?.value?.trim();
                                      const newType = onEnterSelectSearchValue(
                                        e,
                                        unitData?.map((unit) => ({
                                          label: unit?.name,
                                          value: "",
                                        })) || []
                                      );
                                      if (newType) {
                                        setNewTypeName(newType);
                                      } else if (value) {
                                        notification.error({
                                          description:
                                            "Records already exist, no new records were added.",
                                        });
                                      }
                                    }
                                  }}
                                  onClear={() => {
                                    setFieldValue("unit", "");
                                  }}
                                  errorMessage={errors.unit}
                                />
                              </div>
                            </div>
                          ) : (
                            <Typography
                              className="text-[#008000] cursor-pointer text-13 font-medium"
                              onClick={handleParagraphClick}
                              disabled={isReadOnly}
                            >
                              {
                                formatter(Number(values?.unit_cost).toFixed(2))
                                  .value_with_symbol
                              }
                              /
                              {_t(
                                HTMLEntities.decode(
                                  sanitizeString(values?.unit ?? "")
                                )
                              )}
                            </Typography>
                          )}
                        </>
                      )}

                      {isReadOnly &&
                        (!isEmpty(values?.unit_cost) &&
                          Number(values?.unit_cost) !== 0 &&
                          !isEmpty(values?.unit) ? (
                          <Typography
                            className={`text-[#008000] font-medium text-13 ${isReadOnly ? "cursor-no-drop" : ""
                              }`}
                          >
                            {
                              formatter(values?.unit_cost?.toString())
                                .value_with_symbol
                            }
                            /
                            {_t(
                              HTMLEntities.decode(
                                sanitizeString(values?.unit ?? "")
                              )
                            )}
                          </Typography>
                        ) : (
                          <div className="flex gap-2">
                            <div className="w-[calc(100%-52px)]">
                              <InputField
                                className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Item Unit Cost")}
                                type="number"
                                name="unit_cost"
                                id="unit_cost"
                                maxLength={10}
                                disabled={isReadOnly}
                                value={values?.unit_cost}
                                onChange={() => { }}
                                onPaste={handlePaste}
                                onPressEnter={handleEnterKeyPress}
                              />
                            </div>
                            <div className="w-11">
                              <InputField
                                className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Unit")}
                                maxLength={15}
                                name="unit"
                                id="unit"
                                disabled={isReadOnly}
                                value={_t(
                                  HTMLEntities.decode(
                                    sanitizeString(values?.unit ?? "")
                                  )
                                )}
                                type="text"
                                onChange={() => { }}
                                onPaste={handlePaste}
                                onPressEnter={handleEnterKeyPress}
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </li>
                <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                  <Typography className="text-13 block text-primary-900 font-semibold">
                    {_t("Total Cost")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-red-600 text-13 font-semibold"
                      disabled={true}
                    >
                      {formatter(totalWithoutMU.toFixed(2)).value_with_symbol}
                    </Typography>
                  </div>
                </li>
              </ul>
              <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-plus"
                  />
                </li>
              </ul>
              <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex items-center justify-between">
                  <div className="flex items-center gap-1.5">
                    <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                      <ListTabButton
                        value={
                          Number(values.is_markup_percentage) === 1
                            ? "markup_percent"
                            : "markup_dolar"
                        }
                        options={PROJECT_ITEMS_LIST_TAB}
                        className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                        activeclassName="active:bg-[#ffffff]"
                        onChange={(e: RadioChangeEvent) => {
                          setFieldValue("markup", "");
                          setIsMuPercentFieldChanged(true);
                          if (e.target.value === "markup_percent") {
                            setFieldValue("is_markup_percentage", 1);
                          } else {
                            setFieldValue("is_markup_percentage", 0);
                          }
                        }}
                      />
                    </div>
                    <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                      <Tooltip
                        title={_t(
                          `% -- Add the % amount that the item should be marked up. ${formatter().currency_symbol
                          } -- Add the ${formatter().currency_symbol
                          } amount that should be charged for the item.`
                        )}
                        rootClassName="!max-w-[265px]"
                      >
                        <FontAwesomeIcon
                          className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                          icon="fa-regular fa-circle-info"
                        />
                      </Tooltip>
                    </div>
                  </div>
                  <div className="sm:w-40 w-28">
                    <InputField
                      className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                      placeholder={
                        isMarkupPercentage
                          ? _t("Item Markup") + " %"
                          : _t("Total Sales Price")
                      }
                      onPaste={handlePaste}
                      onChange={(e) => {
                        if (!floatNumberRegex.test(e.target.value)) {
                          return;
                        }
                        setFieldValue(
                          "markup",
                          isMarkupPercentage
                            ? Number(e.target.value) < 0 ||
                              isNaN(Number(e.target.value))
                              ? 0 // Show 0 for negative or invalid values
                              : Number(e.target.value) // Valid positive value
                            : e.target.value
                        );
                      }}
                      value={Number(values?.markup) === 0 ? "" : values.markup}
                      labelPlacement="left"
                      type="text"
                      disabled={isReadOnly}
                      onKeyDown={(
                        event: React.KeyboardEvent<HTMLInputElement>
                      ) => {
                        if (event.key === "Enter") {
                          if (Number(event?.currentTarget?.value) === 0) {
                            setFieldValue("markup", "");
                          } else {
                            setFieldValue(
                              "markup",
                              event?.currentTarget?.value
                            );
                          }
                        } else {
                          return onKeyDownNumber(event, {
                            decimalDigits: 2,
                            integerDigits: isMarkupPercentage ? 3 : 8,
                            allowNegative: false,
                          });
                        }
                      }}
                    />
                  </div>
                </li>
                <li className="flex items-center justify-between">
                  <Typography className="text-13 block text-primary-900">
                    {_t("Markup")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-[#008000] text-13 font-medium"
                      disabled={true}
                    >
                      {isMarkupPercentage
                        ? formatter(markUpAmount.toFixed(2)).value_with_symbol
                        : `${markUpAmount.toFixed(2)}%`}
                    </Typography>
                  </div>
                </li>
              </ul>
              <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-equals"
                  />
                </li>
              </ul>
              <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex items-center justify-between">
                  <Typography className="text-13 block text-primary-900 font-semibold">
                    {_t("Total Revenue")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-red-600 text-13 font-semibold"
                      disabled={true}
                    >
                      {
                        formatter(Number(grandTotal || 0).toFixed(2))
                          .value_with_symbol
                      }
                    </Typography>
                  </div>
                </li>
              </ul>
            </SidebarCardBorder>
            <SidebarCardBorder addGap={true}>
              <div className="w-full">
                <TextAreaField
                  required={false}
                  label={_t("Description")}
                  placeholder={_t(
                    "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                  )}
                  labelPlacement="top"
                  disabled={isReadOnly}
                  value={HTMLEntities.decode(
                    sanitizeString(values?.description)
                  )}
                  onChange={(e) => {
                    setFieldValue("description", e.target.value);
                  }}
                />
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Internal Notes")}
                  labelPlacement="top"
                  placeholder={_t(
                    "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                  )}
                  disabled={isReadOnly}
                  value={HTMLEntities.decode(
                    sanitizeString(values?.internal_notes)
                  )}
                  onChange={(e) => {
                    setFieldValue("internal_notes", e.target.value);
                  }}
                />
              </div>
              {values.item_type ? (
                <CheckBox
                  className="gap-1.5 text-primary-900 w-fit"
                  checked={!!values?.item_on_database}
                  onChange={(event) => {
                    const valueToSet: number = event.target.checked ? 1 : 0;
                    setFieldValue("item_on_database", valueToSet);
                  }}
                  disabled={
                    isReadOnly ||
                    !itemTypeAndSaveItemToListField.show ||
                    itemTypeAndSaveItemToListField.disable
                  }
                >
                  {_t(`Save this item into my ${saveItemKey} Items list?`)}
                </CheckBox>
              ) : (
                ""
              )}
              <CheckBox
                className="gap-1.5 w-fit"
                disabled={isReadOnly}
                checked={!!values.apply_global_tax}
                onChange={(e) => {
                  setFieldValue("apply_global_tax", e.target.checked ? 1 : 0);
                }}
              >
                {_t("Collect Tax on this Item?")}
              </CheckBox>
            </SidebarCardBorder>
          </div>
        </div>
        <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
          <PrimaryButton
            className="w-full justify-center primary-btn"
            htmlType="submit"
            buttonText={_t("Save & Close")}
            disabled={isReadOnly || isSubmitting}
            onClick={() => setSubmitAction("save")}
          />
          <PrimaryButton
            className="w-full justify-center primary-btn"
            htmlType="submit"
            buttonText={_t("Save & Add Another Item")}
            disabled={isReadOnly || isSubmitting}
            onClick={() => setSubmitAction("save_n_add_another")}
          />
        </div>
      </Form>

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={handleChangeAssignedTo}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.misc_contact_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={selectedAssignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={Number(details?.project_id)}
          activeTab={itemTypeAndSaveItemToListField?.disable ? CFConfig.employee_key : CFConfig.contractor_key}
        />
      )}
      {contactDetailDialogOpen && (
        <ContactDetails
          isOpenContact={contactDetailDialogOpen}
          contactId={values?.item_assigned_to || ""}
          onCloseModal={() => setContactDetailDialogOpen(false)}
          onEmailClick={(data) => {
            setEmailSiderBar({
              open: true,
              data,
            });
          }}
          additional_contact_id={values?.item_assigned_to_contact_id}
        />
      )}
      <DirSendEmail
        isOpen={emailSiderBar.open}
        options={[
          CFConfig.employee_key,
          "my_crew",
          CFConfig.customer_key,
          CFConfig.lead_key,
          CFConfig.contractor_key,
          CFConfig.vendor_key,
          CFConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={closeEmailSidebar}
        onClose={closeEmailSidebar}
        groupCheckBox={true}
        selectedCustomer={
          emailSiderBar.data?.user_id
            ? ([emailSiderBar.data] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />

      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={addNewUnit}
          onDecline={() => setNewTypeName("")}
        />
      )}

      {isValidId(existingCidbItem?.data?.reference_item_id) && (
        <ConfirmModal
          isOpen={isValidId(existingCidbItem?.data?.reference_item_id)}
          modaltitle={_t("This item already exists")}
          description={
            existingCidbItem?.message ??
            `There is already an item associated with this name in your CIDB. Would you like to rename the current item or import the existing item from your CIDB?`
          }
          onAccept={() => {
            setFieldValue(
              "reference_item_type_id",
              existingCidbItem?.data?.reference_item_id
            );
            setFieldValue("add_item_to_database", 0);
            setExistingCidbItem(null);
            handleSubmit();
          }}
          yesButtonLabel="Use Existing"
          noButtonLabel="Rename"
          onDecline={() => setExistingCidbItem(null)}
          onCloseModal={() => setExistingCidbItem(null)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-trash-can"
        />
      )}
    </Drawer>
  );
};

export default AddProjectItem;