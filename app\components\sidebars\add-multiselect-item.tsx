import { Form } from "@remix-run/react";
import { CFButton } from "~/components/third-party/ant-design/cf-button";
import { CFCheckBox } from "~/components/third-party/ant-design/cf-checkbox";
import {
  CFDrawer,
  SidebarHeader,
} from "~/components/third-party/ant-design/cf-drawer";
import { CFInput } from "~/components/third-party/ant-design/cf-input";
import { CFSelect } from "~/components/third-party/ant-design/cf-select";
import { CFTextarea } from "~/components/third-party/ant-design/cf-textarea";
import { useTranslation } from "~/hook";
import CommonCardWithBorder from "~/components/common/common-card-with-border";
import { useEffect, useState } from "react";
import isEmpty from "lodash/isEmpty";
import { Float, Int, sanitizeString } from "~/helpers/helper";
import { onKeyDownNumber } from "~/helpers/key-down.helper";
import { setCostItemsDatabaseItemAdd } from "~/zustand/pages/manage-bills/costItemDatabase/actions";
import { getLumpSumTotal } from "~/zustand/pages/manage-bills/addBillDropDown/store";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { defaultConfig } from "~/data";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";

declare global {
  interface AddMultiselectItemSideProps {
    addMultiselectItemOpen: boolean;
    setAddMultiselectItemOpen: React.Dispatch<React.SetStateAction<boolean>>;
    selectOption?: CustomerSelectOption<string>;
    onClickItem: (data: Partial<ItemSideData>) => void;
  }
  interface CostCodeData {
    code_id: string;
    original_code_id: string;
    csi_code: string;
    parent_id: string;
    csi_name: string;
    parent_code_id: string;
    cost_code_name: string;
    is_deleted: string;
    is_managed_level: string;
    has_no_child: string;
  }
  interface FormInitialState {
    name: string;
    unitCost: string;
    unit: string;
    markup: string | number;
    hiddenMU: string;
    costCode: string;
    description: string;
    notes: string;
    addCheckbox: boolean;
    total: string | number;
    quantity: string;
    add_list_checkbox: boolean;
  }
  interface IError {
    name: string;
    unitCost: string;
    unit: string;
  }
}
const AddMultiselectItemSide = ({
  addMultiselectItemOpen,
  setAddMultiselectItemOpen,
  selectOption,
  onClickItem,
}: AddMultiselectItemSideProps) => {
  const { _t } = useTranslation();
  const costCodeData: CostCodeData[] = getLumpSumTotal();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const {
    default_equipment_markup_percent = "",
    default_labor_markup_percent = "",
    default_material_markup_percent = "",
    default_other_item_markup_percent = "",
    default_sub_contractor_markup_percent = "",
    default_undefined_markup_percent = "",
  } = appSettings || {};
  let markupDefaultValue = "";
  if (selectOption?.value === "material") {
    markupDefaultValue = default_material_markup_percent;
  } else if (selectOption?.value === "equipment") {
    markupDefaultValue = default_equipment_markup_percent;
  } else if (selectOption?.value === "labor") {
    markupDefaultValue = default_labor_markup_percent;
  } else if (selectOption?.value === "subcontractor") {
    markupDefaultValue = default_sub_contractor_markup_percent;
  } else if (selectOption?.value === "other_items") {
    markupDefaultValue = default_other_item_markup_percent;
  } else if (default_undefined_markup_percent) {
    markupDefaultValue = default_undefined_markup_percent;
  }
  const [formData, setFormData] = useState<Partial<FormInitialState>>({
    name: "",
    unitCost: "",
    unit: "",
    markup: Number(markupDefaultValue),
    hiddenMU: "",
    costCode: "",
    description: "",
    notes: "",
    addCheckbox: true,
  });
  useEffect(() => {
    setFormData({
      name: "",
      unitCost: "",
      unit: "",
      markup: Number(markupDefaultValue),
      hiddenMU: "",
      costCode: "",
      description: "",
      notes: "",
      addCheckbox: true,
    });
  }, [selectOption]);
  const [errors, setErrors] = useState<IError>({
    name: "",
    unitCost: "",
    unit: "",
  });
  const [hasErrors, setHasErrors] = useState<boolean>(false);

  const updateFormData = (key: string, value: string | number | boolean) => {
    setFormData((prevFormData: Partial<FormInitialState>) => ({
      ...prevFormData,
      [key]: value,
    }));
  };
  const resetForm = () => {
    setFormData({
      name: "",
      unitCost: "",
      unit: "",
      markup: "",
      hiddenMU: "",
      costCode: "",
      description: "",
      notes: "",
      addCheckbox: true,
    });
    setErrors({
      name: "",
      unitCost: "",
      unit: "",
    });
  };

  const handleHiddenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("hiddenMU", Float(inputValue));
  };
  const handleNameOnchange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("name", inputValue);
    setErrors((prev: IError) => {
      if (!isEmpty(inputValue)) {
        return { ...prev, name: "" };
      }
      return prev;
    });
  };
  const handleUnit = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("unit", inputValue);
    setErrors((prev: IError) => {
      if (!isEmpty(e?.target?.value)) {
        return { ...prev, unit: "" };
      }
      return prev;
    });
  };
  const handleCost = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("unitCost", inputValue);
    setErrors((prev: IError) => {
      if (!isEmpty(e?.target?.value)) {
        return { ...prev, unitCost: "" };
      }
      return prev;
    });
  };
  const handleMarkep = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e?.target?.value;
    updateFormData("markup", Float(inputValue));
  };
  const handleTaxCheckbox = (e: CheckboxChangeEvent) => {
    const value = e?.target?.checked;
    updateFormData("addCheckbox", value);
  };

  const handleDescription = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e?.target?.value;
    updateFormData("description", value);
  };

  const handleCostCode = (
    selected: string,
    options: any // change this in future ( temporary resolve type issue )
  ) => {
    const selectedValue = options?.value;
    updateFormData("costCode", selectedValue);
  };
  const handleNotes = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e?.target?.value;
    updateFormData("notes", value);
  };
  const handleAddFormData = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    try {
      const tempErrors = {
        unitCost:
          selectOption?.value !== defaultConfig.equipment_key
            ? !isEmpty(formData?.unitCost?.toString())
              ? ""
              : "This field is required."
            : "",
        name: !isEmpty(formData?.name) ? "" : "This field is required.",
        unit:
          selectOption?.value !== defaultConfig.equipment_key
            ? !isEmpty(formData?.unit?.toString())
              ? ""
              : "This field is required."
            : "",
      };
      if (
        Object?.values(tempErrors)?.some(
          (errorMessage) => !isEmpty(errorMessage)
        )
      ) {
        setHasErrors(true);
        setErrors(tempErrors);
        return;
      } else {
        setHasErrors(false);
      }

      const items: Partial<FormInitialState> = {
        unitCost: (Int(formData?.unitCost) * 100).toString(),
        costCode: formData?.costCode,
        description: formData?.description,
        name: formData?.name,
        unit: formData?.unit,
        notes: formData?.notes,
        hiddenMU: formData?.hiddenMU,
        markup: formData?.markup,
        total: (Int(formData?.unitCost) * 100).toString(),
        add_list_checkbox: formData?.addCheckbox,
      };
      setCostItemsDatabaseItemAdd(
        selectOption?.value ?? "",
        () => {},
        items,
        onClickItem
      );
      setAddMultiselectItemOpen(false);
      handleCloseBill();
    } catch (error) {
      notification.error({
        description: "something went wrong!",
      });
    }
  };

  const handleAddFormAnotherData = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    try {
      const tempErrors = {
        unitCost:
          selectOption?.value !== defaultConfig.equipment_key
            ? !isEmpty(formData?.unitCost?.toString())
              ? ""
              : "This field is required."
            : "",
        name: !isEmpty(formData?.name) ? "" : "This field is required.",
        unit:
          selectOption?.value !== defaultConfig.equipment_key
            ? !isEmpty(formData?.unit?.toString())
              ? ""
              : "This field is required."
            : "",
      };
      if (
        Object?.values(tempErrors)?.some(
          (errorMessage) => !isEmpty(errorMessage)
        )
      ) {
        setHasErrors(true);
        setErrors(tempErrors);
        return;
      } else {
        setHasErrors(false);
      }

      const items: Partial<FormInitialState> = {
        unitCost: (Int(formData?.unitCost) * 100).toString(),
        costCode: formData?.costCode,
        description: formData?.description,
        name: formData?.name,
        unit: formData?.unit,
        notes: formData?.notes,
        hiddenMU: formData?.hiddenMU,
        markup: formData?.markup,
        total: (Int(formData?.unitCost) * 100).toString(),
        quantity: "",
        add_list_checkbox: formData?.addCheckbox,
      };
      setCostItemsDatabaseItemAdd(
        selectOption?.value ?? "",
        () => {},
        items,
        onClickItem
      );
      handleClose();
    } catch (error) {
      notification.error({
        description: "something went wrong!",
      });
    }
  };

  const handleClose = () => {
    resetForm();
    setHasErrors(false);
  };

  const handleCloseBill = () => {
    resetForm();
    setAddMultiselectItemOpen(!addMultiselectItemOpen);
    setHasErrors(false);
  };

  return (
    <CFDrawer
      open={addMultiselectItemOpen}
      drawerBody="!h-[calc(100vh-52px)]"
      size={700}
      header={
        <SidebarHeader
          title={`Add ${selectOption?.name} Item`}
          icon={selectOption?.displayIcon}
          closeDrawer={handleCloseBill}
        />
      }
    >
      <Form method="post" noValidate className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          <CommonCardWithBorder>
            <div className="w-full">
              <CFInput
                label="Name"
                type="text"
                size="middle"
                name="name"
                labelClass="dark:text-white/90"
                required
                onChange={handleNameOnchange}
                errorMessage={errors?.name}
                value={formData?.name}
              />
            </div>
            <div className="grid md:grid-cols-2 gap-5">
              <div className="w-full">
                <CFInput
                  label="Unit Cost"
                  type="text"
                  size="middle"
                  name="unit_cost"
                  labelClass="dark:text-white/90"
                  required={selectOption?.value !== defaultConfig.equipment_key}
                  min={0}
                  onChange={handleCost}
                  onKeyDown={(event) => {
                    return onKeyDownNumber(event, {
                      decimalDigits: 2,
                      integerDigits: 10,
                    });
                  }}
                  errorMessage={errors?.unitCost}
                  value={formData?.unitCost}
                />
              </div>
              <div className="w-full">
                <CFInput
                  label="Unit"
                  type="text"
                  size="middle"
                  name="unit"
                  labelClass="dark:text-white/90"
                  required={selectOption?.value !== defaultConfig.equipment_key}
                  onChange={handleUnit}
                  maxLength={15}
                  onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                    if (e.key === '"') {
                      e.preventDefault();
                    }
                  }}
                  errorMessage={errors?.unit}
                  value={formData?.unit}
                />
              </div>
            </div>
            <div className="grid md:grid-cols-2 gap-5">
              <div className="w-full">
                <CFInput
                  label="Markup"
                  type="number"
                  size="middle"
                  name="markup"
                  labelClass="dark:text-white/90"
                  onChange={handleMarkep}
                  min={0}
                  onKeyDown={(event) => {
                    return onKeyDownNumber(event, {
                      decimalDigits: 0,
                      integerDigits: 3,
                    });
                  }}
                  value={formData?.markup}
                />
              </div>
              <div className="w-full">
                <CFInput
                  label="Hidden MU (%)"
                  type="number"
                  size="middle"
                  name="hidden_mu"
                  min={0}
                  labelClass="dark:text-white/90"
                  onChange={handleHiddenChange}
                  onKeyDown={(event) => {
                    return onKeyDownNumber(event, {
                      decimalDigits: 0,
                      integerDigits: 3,
                    });
                  }}
                  value={formData?.hiddenMU}
                />
              </div>
            </div>
            <div className="w-full">
              <CFSelect
                name="cost_code"
                label="Cost Code"
                options={
                  costCodeData && costCodeData?.length > 0
                    ? costCodeData?.map((item: IOCostCode) => ({
                        label:
                          `${HTMLEntities.decode(
                            sanitizeString(item?.cost_code_name)
                          )}` +
                          `${
                            item?.csi_code &&
                            ` (${HTMLEntities.decode(
                              sanitizeString(item?.csi_code)
                            )})`
                          }`,
                        value: item?.code_id,
                      }))
                    : []
                }
                className="text-sm"
                onChange={handleCostCode}
                value={formData?.costCode}
                // errorMessage={formData?.costCode}
              />
            </div>
            <div className="w-full">
              <CFTextarea
                label="Description"
                name="description"
                onChange={handleDescription}
                value={formData?.description}
              />
            </div>
            <div className="w-full">
              <CFTextarea
                label="Internal Notes"
                name="internal_note"
                onChange={handleNotes}
                value={formData?.notes}
              />
            </div>
            <CFCheckBox
              className="gap-1.5 text-primary-900 w-fit"
              checked={formData?.addCheckbox}
              label="Add this item to my current list upon Saving."
              onChange={handleTaxCheckbox}
              value={formData?.addCheckbox}
            />
          </CommonCardWithBorder>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full sm:gap-5 gap-3.5 px-4 pt-4">
          <CFButton
            variant="primary"
            className="w-full justify-center"
            htmlType="submit"
            onClick={handleAddFormData}
          >
            {_t("Save")}
          </CFButton>
          <CFButton
            variant="primary"
            className="w-full justify-center"
            htmlType="submit"
            onClick={handleAddFormAnotherData}
          >
            {_t("Save & Add Another Item")}
          </CFButton>
        </div>
      </Form>
    </CFDrawer>
  );
};

export default AddMultiselectItemSide;
