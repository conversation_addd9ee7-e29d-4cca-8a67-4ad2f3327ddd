import { useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";

// Hooks and Helper
import { useTranslation } from "~/hook";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useAppDLSelector } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  filterAttachmentFiles,
  filterOptionBySubstring,
  floatNumberRegex,
  generateCostCodeLabel,
  onKeyDownCurrency,
  wholeNumberRegex,
} from "~/shared/utils/helper/common";
import { removeDuplicatesFile } from "~/shared/utils/helper/removeDuplicatesFile";
import { addMtlEquItems } from "~/modules/projectManagement/pages/dailyLogs/redux/action/commonDLAction";
// atoms
import { Header } from "~/shared/components/atoms/header";
import { Drawer } from "~/shared/components/atoms/drawer";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { defaultConfig } from "~/data";
import DLSendEmail from "../../../DLSendEmail";
import { addUnit, getUnitList } from "~/redux/action/unitActions";

const AddMyList = ({
  addMyList,
  setAddMyList,
  type,
  selecedData,
  onSaved,
}: IAddMyListProps) => {
  const { _t } = useTranslation();
  const { inputFormatter, unformatted } = useCurrencyFormatter();

  const gSettings: GSettings = getGSettings();
  const { module_id, module_access }: GConfig = getGConfig();
  const {
    date_format,
    image_resolution,
    default_material_markup_percent,
    default_equipment_markup_percent,
  } = gSettings;
  const { checkModuleAccessByKey } = useGModules();

  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const { codeCostData }: IGetCostCodeList = useAppDLSelector(
    (state) => state.costCode
  );

  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [selectedContractor, setSelectedContractor] = useState<
    Partial<IDirectoryData>
  >({});
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([]);
  const [isFormLoaing, setIsFormLoaing] = useState<boolean>(false);
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [newTypeName, setNewTypeName] = useState<string>("");
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units || []);
  };
  useEffect(() => {
    getUnit();
  }, []);

  const codeCostOptions = codeCostData.map((item) => ({
    label: generateCostCodeLabel({
      name: item?.cost_code_name,
      code: item?.csi_code,
      isArchived: item?.is_deleted == 1,
      isAllowCodeWithoutName: true,
    }),
    value: item.code_id,
  }));

  const initialValues: IAddMyListInitVal = useMemo(
    () => ({
      costCodeKey: "",
      name: HTMLEntities.decode(sanitizeString(selecedData?.item_name)) || "",
      itemVariations: "",
      sku: "",
      costCodeId: "",
      unitCost: "",
      hiddenMarkup: "",
      unit: "",
      markup:
        type === "material"
          ? default_material_markup_percent || ""
          : default_equipment_markup_percent || "",
      supplierId: "",
      notes: "",
      internalNotes: "",
    }),
    [type, selecedData]
  );

  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().required("This field is required."),
    unit: Yup.string()
      .trim()
      .when("costCodeKey", {
        is: (costCodeKey: string) => costCodeKey == "item_material",
        then: () => Yup.string().required("This field is required."),
        otherwise: () => Yup.string().notRequired(),
      }),
    unitCost: Yup.string()
      .trim()
      .when("costCodeKey", {
        is: (costCodeKey: string) => costCodeKey == "item_material",
        then: () => Yup.string().required("This field is required."),
        otherwise: () => Yup.string().notRequired(),
      }),
  });

  // Submit
  const handleSubmitForm = async (values: IAddMyListInitVal) => {
    const formSetData = {
      // costCodeKey: type === "material" ? "item_material" : "item_equipment",
      ...values,
      name: !!values.name ? escapeHtmlEntities(values.name?.trim()) : "",
      unitCost: values?.unitCost
        ? (Number(values.unitCost) * 100).toString()
        : "",
    };
    if (selectedFiles.length) {
      const { attachImage, awsFilesUrl }: IFilterAttachmentFiles =
        filterAttachmentFiles(selectedFiles);
      let filteredAttachImages = attachImage.length ? attachImage : undefined;
      formSetData.attachImage = filteredAttachImages?.length
        ? filteredAttachImages
        : undefined;
      formSetData.awsFilesUrl = awsFilesUrl.length ? awsFilesUrl : undefined;
    }

    const formData = getValuableObj(formSetData);
    setIsFormLoaing(true);
    const resApi = (await addMtlEquItems(formData)) as IAddCostCodeDataApiRes;
    if (resApi.success) {
      onSaved(resApi?.data?.id);
      setAddMyList(false);
    } else {
      notification.error({
        description: resApi.message,
      });
    }
    setIsFormLoaing(false);
  };

  //Formik
  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: handleSubmitForm,
  });

  const { handleSubmit, handleChange, setFieldValue, values, touched, errors } =
    formik;

  useEffect(() => {
    setFieldValue(
      "costCodeKey",
      type === "material" ? "item_material" : "item_equipment"
    );
  }, [type]);

  const handleSelectCustomer = (data: Partial<IDirectoryData>) => {
    const userIds = data.user_id;
    setSelectedContractor(data);
    setFieldValue("supplierId", userIds ? userIds : "");
    setIsOpenSelectCustomer(false);
  };

  return (
    <>
      <Drawer
        open={addMyList}
        rootClassName="drawer-open"
        width={722}
        push={false}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {type == "material"
                ? _t("Add to My Material List")
                : _t("Add to My Equipment List")}
            </Header>
          </div>
        }
        closeIcon={<CloseButton onClick={() => setAddMyList(false)} />}
      >
        <form method="post" noValidate className="py-4" onSubmit={handleSubmit}>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="name"
                    value={values?.name}
                    onChange={handleChange}
                    errorMessage={touched?.name ? errors?.name : ""}
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("SKU")}
                    labelPlacement="top"
                    name="sku"
                    value={values?.sku}
                    onChange={handleChange}
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    mode="tags"
                    label={_t("Variations")}
                    labelPlacement="top"
                    formInputClassName="overflow-visible"
                    containerClassName="overflow-visible"
                    className="remove-down-arrow multi-selected-tags-padding"
                    name="itemVariations"
                    open={false}
                    maxTagValue="responsive"
                    value={
                      !!values?.itemVariations
                        ? values?.itemVariations?.split(",")
                        : []
                    }
                    onChange={(value) => {
                      if (typeof value === "object") {
                        setFieldValue("itemVariations", value?.join(","));
                      } else {
                        setFieldValue("itemVariations", "");
                      }
                    }}
                    showSearch={false}
                    allowClear={false}
                    onCloseTag={(data) => {
                      const valArr = !!values?.itemVariations
                        ? values?.itemVariations?.split(",")
                        : [];
                      const filtVal = valArr?.filter((item) => item != data);
                      setFieldValue("itemVariations", filtVal?.join(","));
                    }}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    name="costCodeId"
                    options={codeCostOptions}
                    onChange={(value) => {
                      setFieldValue("costCodeId", value || "");
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    allowClear={true}
                    onClear={() => {
                      setFieldValue("costCodeId", "");
                    }}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputNumberField
                      label={_t("Unit Cost")}
                      isRequired={type === "material"}
                      name="unitCost"
                      labelClass="dark:text-white/90"
                      placeholder={inputFormatter("0.00").value}
                      labelPlacement="top"
                      value={values.unitCost}
                      formatter={(_, info) =>
                        inputFormatter(unformatted(info.input)).value
                      }
                      parser={(value) => {
                        const inputValue = value
                          ? unformatted(value.toString())
                          : "";
                        setFieldValue("unitCost", inputValue || "");
                        return inputValue;
                      }}
                      onKeyDown={(event) => {
                        onKeyDownCurrency(event, {
                          integerDigits: 10,
                          decimalDigits: 2,
                          unformatted,
                          decimalSeparator: inputFormatter().decimal_separator,
                        });
                      }}
                      prefix={inputFormatter().currency_symbol}
                      errorMessage={touched?.unitCost ? errors?.unitCost : ""}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Hidden Markup") + " (%)"}
                      labelPlacement="top"
                      name="hiddenMarkup"
                      value={values?.hiddenMarkup}
                      onChange={(e) => {
                        if (!floatNumberRegex.test(e.target.value)) {
                          return;
                        }
                        if (e.target.value.length > 3) {
                          return;
                        }
                        if (!wholeNumberRegex.test(e.target.value)) {
                          return;
                        }
                        const cleanedInput = e.target.value
                          .split(".")[0]
                          .replace("-", "");
                        if (cleanedInput.length > 10) {
                          return;
                        }
                        setFieldValue("hiddenMarkup", e.target.value);
                      }}
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                    <SelectField
                      label={_t("Unit")}
                      placeholder="Unit"
                      name="unit"
                      labelPlacement="top"
                      maxLength={15}
                      value={values?.unit || null}
                      isRequired={type === "material"}
                      popupClassName="!w-[260px]"
                      showSearch
                      options={
                        unitData.map((type) => ({
                          label: type.name?.toString(),
                          value: type.name?.toString(),
                        })) ?? []
                      }
                      allowClear
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      onChange={(value) => {
                        setFieldValue("unit", value);
                      }}
                      addItem={{
                        text: "Add Unit: Type Unit & Press Enter",
                        icon: "fa-regular fa-plus",
                      }}
                      onInputKeyDown={(e) => {
                        if (e.key === "Enter") {
                          const value = e?.currentTarget?.value?.trim();
                          const newType = onEnterSelectSearchValue(
                            e,
                            unitData?.map((unit) => ({
                              label: unit?.name,
                              value: "",
                            })) || []
                          );
                          if (newType) {
                            setNewTypeName(newType);
                          } else if (value) {
                            notification.error({
                              description:
                                "Records already exist, no new records were added.",
                            });
                          }
                        }
                      }}
                      onClear={() => {
                        setFieldValue("unit", "");
                      }}
                      errorMessage={touched?.unit ? errors?.unit : ""}
                    />
                  ) : (
                    <InputField
                      label={_t("Unit")}
                      labelPlacement="top"
                      name="unit"
                      type="text"
                      maxLength={15}
                      isRequired={type === "material"}
                      value={values?.unit}
                      onChange={handleChange}
                      errorMessage={touched?.unit ? errors?.unit : ""}
                    />
                  )}
                  <div className="w-full">
                    <InputField
                      label={_t("Markup") + " (%)"}
                      name="markup"
                      labelPlacement="top"
                      value={values?.markup}
                      onChange={(e) => {
                        if (!floatNumberRegex.test(e.target.value)) {
                          return;
                        }
                        if (e.target.value.length > 3) {
                          return;
                        }
                        if (!wholeNumberRegex.test(e.target.value)) {
                          return;
                        }
                        const cleanedInput = e.target.value
                          .split(".")[0]
                          .replace("-", "");
                        if (cleanedInput.length > 10) {
                          return;
                        }
                        setFieldValue("markup", e.target.value);
                      }}
                    />
                  </div>
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Supplier")}
                    labelPlacement="top"
                    name="supplierId"
                    value={HTMLEntities.decode(
                      sanitizeString(selectedContractor.display_name)
                    )}
                    avatarProps={{
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(selectedContractor?.display_name)
                        ),
                        image: selectedContractor?.image,
                      },
                    }}
                    addonBefore={
                      <div className="flex items-center gap-1">
                        {selectedContractor?.user_id &&
                        selectedContractor?.display_name ? (
                          <ContactDetailsButton
                            onClick={(e: React.MouseEvent) => {
                              e.stopPropagation();
                              setIsOpenContactDetails(true);
                            }}
                          />
                        ) : (
                          <></>
                        )}
                        <DirectoryFieldRedirectionIcon
                          className="!w-5 !h-5"
                          directoryId={
                            selectedContractor?.user_id?.toString() || ""
                          }
                          directoryTypeKey={
                            (!selectedContractor?.type_key ||
                              selectedContractor?.type_key == "user" ||
                              selectedContractor?.type_key == "contact") &&
                            selectedContractor?.user_id &&
                            selectedContractor?.user_id != 0
                              ? "employee"
                              : selectedContractor?.type_key?.toString() || ""
                          }
                        />
                      </div>
                    }
                    onClick={() => setIsOpenSelectCustomer(true)}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    name="notes"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    value={values?.notes}
                    onChange={handleChange}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    name="internalNotes"
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    value={values?.internalNotes}
                    onChange={handleChange}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle={_t("Files")}>
                <AttachmentCard
                  isAddAllow={true}
                  isReadOnly={false}
                  files={selectedFiles}
                  validationParams={{
                    date_format,
                    file_support_module_access: checkModuleAccessByKey(
                      defaultConfig.file_support_key
                    ),
                    image_resolution,
                    module_key: "database_items",
                    module_id,
                    module_access,
                  }}
                  onAddAttachment={(data) => {
                    setSelectedFiles((prevData) => {
                      const newData = [
                        ...prevData,
                        ...(data as IAddFileRequestBodyChild[]),
                      ];
                      return removeDuplicatesFile(newData);
                    });
                  }}
                  isShowDeleteMenu={true}
                  editView={true}
                  setUpdatedData={(data) => {
                    const newData = data as Partial<IgetUpdatedFileRes>;
                    const newFile = newData?.data;
                    if (!newFile) {
                      return;
                    }
                    setSelectedFiles((prevSelectedFiles) => {
                      const fileExists = prevSelectedFiles.some(
                        (file) => file.image_id === newFile?.image_id
                      );
                      if (fileExists) {
                        return prevSelectedFiles.map((file) =>
                          file.image_id === newFile?.image_id ? newFile : file
                        );
                      } else {
                        return [...prevSelectedFiles, newFile];
                      }
                    });
                  }}
                  attachmentsFromEmailDrawer={true}
                  onDeleteFile={(data: Partial<IFile>) => {
                    const key = Object.keys(data)[0] as keyof IFile;
                    const value = data[key] as string;
                    setSelectedFiles((prevFiles) => {
                      return prevFiles.filter((item) => {
                        if (key in item) {
                          return item[key] !== value;
                        }
                        return true;
                      });
                    });
                  }}
                />
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              disabled={isFormLoaing}
              isLoading={isFormLoaing}
            />
          </div>
        </form>
      </Drawer>

      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={true}
          options={[
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            // "my_project",
          ]}
          setCustomer={(data) => {
            handleSelectCustomer(
              data.length ? (data[0] as Partial<IDirectoryData>) : {}
            );
          }}
          // projectId={Number(details?.projectId || "")}
          selectedCustomer={
            selectedContractor?.user_id && selectedContractor?.user_id != 0
              ? ([selectedContractor] as unknown as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
        />
      )}
      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={selectedContractor?.user_id || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={module_access == "read_only"}
          additional_contact_id={selectedContractor?.contact_id}
        />
      )}

      <DLSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
          "my_project",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        projectId={Number(details?.projectId || "")}
        app_access={false}
      />
      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}
    </>
  );
};

export default AddMyList;
