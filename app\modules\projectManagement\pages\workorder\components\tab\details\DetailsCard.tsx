import { useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Hook
import { useDateFormatter, useTranslation } from "~/hook";
// Other
import {
  useWoAppSelector,
  useWoAppDispatch,
} from "~/modules/projectManagement/pages/workorder/redux/store";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import {
  workorderDetailsField,
  workorderfieldStatus,
} from "../../../utils/constasnts";
import { defaultConfig } from "~/data";
import {
  getDifferenceBetweenDate,
  getFormat,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import dayjs, { Dayjs } from "dayjs";
import { getStatusForField } from "~/shared/utils/helper/common";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
import {
  updateWorkOrderDetailApi,
  fetchWorkorderDetails,
} from "../../../redux/action/workorderDetailsAction";
import { useParams } from "@remix-run/react";
import { updateWorkorderDetail } from "../../../redux/slices/workorderDetailsSlice";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { getCOModuleName } from "../../../utils/helper";

const WorkorderDetails = () => {
  const { _t } = useTranslation();
  const { module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const [inputValues, setInputValues] = useState<IWorkorderDetails>(
    workorderDetailsField
  );
  const gConfig: GConfig = getGConfig();
  const { date_format }: GSettings = getGSettings();
  const params = useParams();

  const { details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const dateFormat = useDateFormatter();

  const [isOpenSelectIssuedBy, setIsOpenSelectIssuedBy] =
    useState<boolean>(false);
  const [isOpenSelectApprovedBy, setIsOpenSelectApprovedBy] =
    useState<boolean>(false);
  const [isOpenSelectBillTo, setIsOpenSelectBillTo] = useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [assignedTo, setAssignedTo] = useState<TselectedContactSendMail[]>([]);
  const [isDatePVisible, setIsDatePVisible] = useState<boolean>(false);
  const [dateBeingChanged, setDateBeingChanged] = useState<boolean>(false);
  const [isEndDatePickerOpened, setIsEndDatePickerOpened] =
    useState<boolean>(false);

  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<boolean>(false);
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(workorderfieldStatus);
  const [additionalContact, setAdditionContact] = useState<number>(0);
  const [fixedDirType, setFixedDirType] = useState<number | null>(null);

  const dispatch = useWoAppDispatch();
  const dtDivRef = useRef<HTMLLIElement>(null);
  const dateTimeSelectRef = useRef<HTMLLIElement>(null);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const handleDateFieldOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;
    const targetElement = document.querySelector(".ant-picker-dropdown");

    if (targetElement && targetElement.contains(clickedElement)) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg" &&
      !isEndDatePickerOpened
    ) {
      setIsDatePVisible(false);
      return;
    }

    if (
      dtDivRef.current &&
      !dtDivRef.current.contains(event.target as Node) &&
      !dateBeingChanged
    ) {
      setIsDatePVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleDateFieldOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleDateFieldOutsideClick);
    };
  }, []);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleIssuedBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (data?.user_id != inputValues.issued_by) {
        setInputValues({
          ...inputValues,
          issued_by: data?.user_id,
          issued_by_name: data?.display_name,
          issued_by_image: data?.image,
          issued_by_type:
            data?.orig_type?.toString() ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          issued_by: data.user_id,
          issued_by_contact: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "issued_by",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          issued_by: details.issued_by,
          issued_by_name: details.issued_by_name,
          issued_by_image: details.issued_by_image,
        });
      }
    } else {
      handleUpdateField({ issued_by: "" });
      setInputValues({
        ...inputValues,
        issued_by: "",
        issued_by_name: "",
      });
    }
  };

  const handleBilledTo = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (
        data?.user_id?.toString() != details.billed_to ||
        data?.contact_id?.toString() !== details.billed_to_contact
      ) {
        setInputValues({
          ...inputValues,
          billed_to: data?.user_id,
          billed_to_name:
            !!data.contact_id &&
            data.company_name &&
            !data.display_name?.includes(data.company_name)
              ? `${data.display_name} (${data.company_name})`
              : data?.display_name,
          billed_to_contact: data?.contact_id,
          billed_to_image: data?.image,
          billed_to_dir_type:
            data?.orig_type ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          billed_to: data?.user_id?.toString(),
          billed_to_contact: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "billed_to",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          billed_to: details.billed_to,
          billed_to_image: details.image,
          billed_to_name: details.billed_to_name,
          billed_to_contact: details.billed_to_contact,
        });
      }
    } else {
      handleUpdateField({
        billed_to: "",
        billed_to_contact: 0,
      });
      setInputValues({
        ...inputValues,
        billed_to: "",
        billed_to_name: "",
      });
    }
  };

  const handleApprovedBy = (data: Partial<TselectedContactSendMail>) => {
    if (data?.user_id !== undefined) {
      if (
        data?.user_id?.toString() != details.approved_by ||
        data?.contact_id?.toString() !== details.approved_by_contact_id
      ) {
        setInputValues({
          ...inputValues,
          approved_by: data?.user_id,
          approved_by_name:
            !!data.contact_id &&
            data.company_name &&
            !data.display_name?.includes(data.company_name)
              ? `${data.display_name} (${data.company_name})`
              : data?.display_name,
          approved_by_contact_id: data?.contact_id,
          approved_by_dir_type:
            data?.orig_type ||
            getDirectaryIdByKey(data.type_key as CustomerTabs, gConfig),
        });
        handleUpdateField({
          approved_by: data?.user_id?.toString(),
          approved_by_contact_id: data?.contact_id?.toString(),
        });
      } else {
        handleChangeFieldStatus({
          field: "approved_by",
          status: "button",
          action: "BLUR",
        });
        setInputValues({
          ...inputValues,
          approved_by: details.approved_by,
          approved_by_name: details.approved_by_name,
          approved_by_contact_id: details.approved_by_contact_id,
        });
      }
    } else {
      handleUpdateField({
        approved_by: "",
        approved_by_contact_id: 0,
      });
      setInputValues({
        ...inputValues,
        approved_by: "",
        approved_by_name: "",
      });
    }
  };

  const handleAssignedTo = (data: TselectedContactSendMail[]) => {
    const isPreviouslyEmpty = inputValues.assigned_to_arr?.length === 0;
    if (data?.length !== 0) {
      const assignedToArray = data.map((user) => {
        return {
          dir_type:
            user?.orig_type?.toString() ||
            getDirectaryIdByKey(user.type_key as CustomerTabs, gConfig),
          dir_type_name: user.type_name,
          directory_id:
            user.contact_id?.toString() === "0"
              ? user.user_id
              : user.contact_id,
          display_name: user.display_name,
          user_id: user.user_id,
          image: user.image,
        };
      });
      setInputValues({
        ...inputValues,
        assigned_to_arr: assignedToArray,
        assigned_to: data?.map((user) => user.user_id).join(","),
      });
      handleUpdateField({
        assigned_to: data?.map((user) => user.user_id).join(","),
      });
    } else {
      if (!isPreviouslyEmpty) {
        handleUpdateField({
          assigned_to: "",
        });
        handleChangeFieldStatus({
          field: "assigned_to",
          status: "button",
          action: "BLUR",
        });
      }
      setInputValues({
        ...inputValues,
        assigned_to: "",
        assigned_to_arr: [],
      });
    }
  };

  const handleInpOnChange = ({
    target: { name, value },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleUpdateField = async (data: IWorkorderDetails) => {
    const field = Object.keys(data)[0] as keyof IWorkorderDetails;

    if (field === "order_date" || field === "end_date") {
      handleChangeFieldStatus({
        field: "order_end_date",
        status: "loading",
        action: "API",
      });
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateWorkOrderDetailApi({
      id: params.id,
      ...data,
      ...(field === "order_date" && data.order_date
        ? {
            order_date: backendDateFormat(data.order_date, date_format),
          }
        : {}),
      ...(field === "end_date" && data.end_date
        ? {
            end_date: backendDateFormat(data.end_date, date_format),
          }
        : {}),
    })) as ApiCallResponse;

    if (updateRes?.success) {
      if (field === "order_date" || field === "end_date") {
        handleChangeFieldStatus({
          field: "order_end_date",
          status: "success",
          action: "API",
        });
      }
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateWorkorderDetail(data));
      dispatch(fetchWorkorderDetails({ id: params.id, shouldLoading: false }));
    } else {
      if (field === "order_date" || field === "end_date") {
        handleChangeFieldStatus({
          field: "order_end_date",
          status: "error",
          action: "API",
        });
      }
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
    }
    delay(() => {
      if (field === "order_date" || field === "end_date") {
        handleChangeFieldStatus({
          field: "order_end_date",
          status: "button",
          action: "API",
        });
      }
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 1000);
  };

  const handleClearDatesAndSendFullPayload = () => {
    const payload = {
      order_date: null,
      end_time: null,
    };
    setInputValues((prev) => ({
      ...prev,
      order_date: null,
      end_time: null,
    }));
    handleUpdateField(payload, "");
  };

  useEffect(() => {
    const dirType = Number(inputValues?.assigned_to_arr[0]?.dir_type);
    if (dirType === 1 || (fixedDirType === 2 && dirType === 0)) {
      setFixedDirType(2);
    } else {
      setFixedDirType(dirType);
    }
  }, [inputValues?.assigned_to_arr[0]?.dir_type]);

  useEffect(() => {
    const woDetails = {
      ...details,
      response_name:
        details.response_name === "Accept"
          ? "Accepted"
          : details.response_name === "Reject"
          ? "Rejected"
          : details.response_name,
    };
    setInputValues(woDetails);
  }, [details.billed_to_name, details.work_order_id]);

  useEffect(() => {
    if (inputValues?.assigned_to_arr?.length !== 0) {
      const assigned_to = inputValues?.assigned_to_arr?.map((assignee) => {
        const supplierKey: string = getDirectaryKeyById(
          Number(assignee?.dir_type == 1 ? 2 : assignee.dir_type),
          gConfig
        );
        return {
          display_name:
            HTMLEntities.decode(
              sanitizeString(assignee?.display_name?.trim())
            ) ?? "",
          type: Number(assignee.dir_type),
          type_key: supplierKey,
          user_id: Number(assignee.user_id),
          image: assignee.image,
        };
      });
      setAssignedTo(assigned_to as TselectedContactSendMail[]);
    }
  }, [inputValues?.assigned_to_arr]);

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return dateFormat({
        date,
        dateFormat: getFormat(date_format),
        format: "yyyy-MM-dd",
      });
    }
    return undefined;
  };

  const onOrderDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const EndDate = getDate(inputValues?.end_date) as string;
      if (!!date) {
        if (inputValues.end_date !== "") {
          const startDateFormatted = date.format("YYYY-MM-DD");
          const difference = getDifferenceBetweenDate(
            EndDate,
            startDateFormatted,
            "days"
          );
          if (difference < 0) {
            const errorMessage =
              "Service date must be less than or equal to End Date.";
            notification.error({
              description: errorMessage,
            });
            setInputValues({
              ...inputValues,
              order_date: inputValues.end_date,
            });
          } else {
            const orderDate = !!date ? date?.format(date_format) : "";
            setInputValues({
              ...inputValues,
              order_date: orderDate,
            });
            handleUpdateField({
              order_date: orderDate,
            });
          }
        } else {
          const orderDate = !!date ? date?.format(date_format) : "";
          setInputValues({
            ...inputValues,
            order_date: orderDate,
          });
          handleUpdateField({
            order_date: orderDate,
          });
        }
      } else {
        handleChangeFieldStatus({
          field: "order_date",
          status: "button",
          action: "BLUR",
        });
        handleUpdateField({
          order_date: "",
        });
        setInputValues({
          ...inputValues,
          order_date: "",
        });
      }
    }
  };

  const onEndDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const orderDate = getDate(inputValues?.order_date) as string;

      if (!!date) {
        if (inputValues.order_date !== "") {
          const endDateFormatted = date.format("YYYY-MM-DD");
          const difference = getDifferenceBetweenDate(
            endDateFormatted,
            orderDate,
            "days"
          );
          if (difference < 0) {
            const errorMessage =
              "End date must be greater than or equal to Service Date.";
            notification.error({
              description: errorMessage,
            });
            setInputValues({
              ...inputValues,
              end_date: inputValues.order_date,
            });
          } else {
            const endDate = !!date ? date?.format(date_format) : "";
            setInputValues({
              ...inputValues,
              end_date: endDate,
            });
            handleUpdateField({
              end_date: endDate,
            });
          }
        } else {
          const endDate = !!date ? date?.format(date_format) : "";
          setInputValues({
            ...inputValues,
            end_date: endDate,
          });
          handleUpdateField({
            end_date: endDate,
          });
        }
      } else {
        handleChangeFieldStatus({
          field: "end_date",
          status: "button",
          action: "BLUR",
        });
        handleUpdateField({
          end_date: "",
        });
        setInputValues({
          ...inputValues,
          end_date: "",
        });
      }
    }
  };
  const handleCountContractCheck = async (value: number) => {
    setIsLoadingCheckBox(true);
    handleUpdateField({
      count_in_contract_amount: value,
    });
    setIsLoadingCheckBox(false);
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li
                className={`overflow-hidden flex ${
                  !isDatePVisible ? "hidden" : ""
                }`}
                ref={dtDivRef}
              >
                <InlineField
                  label={_t("Service Start/End Date")}
                  labelPlacement="left"
                  field={
                    <div className="grid grid-cols-2 gap-1 items-center w-full">
                      <DatePickerField
                        label=""
                        labelPlacement="left"
                        placeholder={_t("Select Start Date")}
                        name="order_date"
                        editInline={true}
                        iconView={true}
                        id="order_date"
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "order_date"
                        )}
                        value={displayDateFormat(
                          inputValues.order_date?.toString().trim(),
                          date_format
                        )}
                        onChange={(date) => {
                          setDateBeingChanged(true);
                          onOrderDateChange(date);
                          setIsDatePVisible(true);
                        }}
                        format={date_format}
                        disabled={isReadOnly}
                        onMouseLeave={() => {
                          handleChangeFieldStatus({
                            field: "order_date",
                            status: "button",
                            action: "ML",
                          });
                        }}
                      />
                      <DatePickerField
                        labelPlacement="left"
                        label=""
                        placeholder={_t("Select End Date")}
                        labelClass="!hidden"
                        name="end_date"
                        id="end_date"
                        editInline={true}
                        iconView={true}
                        readOnly={isReadOnly}
                        value={displayDateFormat(
                          inputValues.end_date?.toString().trim(),
                          date_format
                        )}
                        fixStatus={getStatusForField(loadingStatus, "end_date")}
                        onChange={(date) => {
                          onEndDateChange(date);
                          setIsEndDatePickerOpened(false);
                        }}
                        format={date_format}
                        onMouseLeave={() => {
                          handleChangeFieldStatus({
                            field: "end_date",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onBlur={() => {
                          setIsEndDatePickerOpened(false);
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li
                className={`overflow-hidden ${isDatePVisible ? "hidden" : ""}`}
                ref={dateTimeSelectRef}
              >
                <InlineField
                  label={_t("Service Start/End Date")}
                  labelPlacement="left"
                  field={
                    <div className="relative w-full group/edit">
                      <InputField
                        labelPlacement="left"
                        placeholder={_t("Select Date")}
                        required={false}
                        editInline={true}
                        iconView={true}
                        readOnly={isReadOnly}
                        readOnlyClassName="!h-[34px]"
                        value={
                          !!inputValues.order_date && !!inputValues.end_date
                            ? dayjs(inputValues.order_date, date_format).format(
                                date_format
                              ) +
                              " - " +
                              dayjs(inputValues.end_date, date_format).format(
                                date_format
                              )
                            : !!inputValues?.order_date
                            ? dayjs(inputValues.order_date, date_format).format(
                                date_format
                              )
                            : !!inputValues?.end_date
                            ? dayjs(inputValues.end_date, date_format).format(
                                date_format
                              )
                            : ""
                        }
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "order_end_date"
                        )}
                        onChange={() => {}}
                        onFocus={() => {
                          setIsDatePVisible(true);
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "order_end_date",
                            status: "button",
                            action: "ML",
                          });
                        }}
                      />
                      {(!!inputValues?.order_date || !!inputValues.end_date) &&
                      !isReadOnly &&
                      !["loading", "success", "error"].includes(
                        getStatusForField(loadingStatus, "order_end_date")
                      ) ? (
                        <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                          <FontAwesomeIcon
                            icon="fa-solid fa-circle-xmark"
                            className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                            onClick={() => {
                              handleClearDatesAndSendFullPayload();
                            }}
                          />
                        </div>
                      ) : null}
                    </div>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Issued By")}
                  name="issued_by"
                  placeholder={_t("Select Employee")}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.issued_by_name)
                  )}
                  labelPlacement="left"
                  className="justify-between"
                  readOnlyClassName="sm:block flex"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "issued_by"),
                  }}
                  onClick={() => {
                    setIsOpenSelectApprovedBy(false);
                    setIsOpenSelectAssignedTo(false);
                    setIsOpenSelectBillTo(false);
                    setIsOpenSelectApprovedBy(false);
                    setIsOpenSelectAssignedTo(false);
                    setIsOpenSelectBillTo(false);
                    setIsOpenSelectIssuedBy(true);
                  }}
                  avatarProps={
                    inputValues?.issued_by_name
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(inputValues?.issued_by_name)
                            ),
                            image: inputValues?.issued_by_image,
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    <>
                      {inputValues.issued_by &&
                      inputValues.issued_by !== "" &&
                      !!inputValues.issued_by_name ? (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={async () => {
                              await setAdditionContact(Number(0));
                              setIsContactDetails(true);
                              setcontactId(inputValues?.issued_by as number);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={inputValues?.issued_by.toString()}
                            directoryTypeKey={
                              inputValues.issued_by_type !== "" &&
                              inputValues.issued_by_type
                                ? getDirectaryKeyById(
                                    Number(inputValues.issued_by_type) === 1
                                      ? 2
                                      : Number(inputValues.issued_by_type),
                                    gConfig
                                  )
                                : ""
                            }
                          />
                        </div>
                      ) : (
                        <></>
                      )}
                    </>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Invoiced To")}
                  name="billed_to"
                  placeholder={_t("If Not Customer")}
                  className="justify-between"
                  readOnlyClassName="sm:block flex"
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.billed_to_name)
                  )}
                  labelPlacement="left"
                  editInline={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  iconView={true}
                  onClick={() => {
                    setIsOpenSelectIssuedBy(false);
                    setIsOpenSelectApprovedBy(false);
                    setIsOpenSelectAssignedTo(false);
                    setIsOpenSelectBillTo(true);
                  }}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "billed_to"),
                  }}
                  avatarProps={{
                    user: {
                      name: HTMLEntities.decode(
                        sanitizeString(inputValues?.billed_to_name)
                      ),
                      image:
                        inputValues?.billed_to_contact === 0 ||
                        inputValues?.billed_to_contact === null ||
                        !inputValues.billed_to_contact
                          ? inputValues?.billed_to_image
                          : "",
                    },
                  }}
                  rightIcon={
                    <>
                      {inputValues.billed_to &&
                      inputValues.billed_to !== "" &&
                      !!details?.billed_to_name ? (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={async () => {
                              await setAdditionContact(
                                Number(details?.billed_to_contact)
                              );
                              setIsContactDetails(true);
                              setcontactId(inputValues?.billed_to as number);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={inputValues?.billed_to.toString()}
                            directoryTypeKey={
                              inputValues.billed_to_dir_type !== "" &&
                              inputValues.billed_to_dir_type
                                ? getDirectaryKeyById(
                                    Number(inputValues.billed_to_dir_type) === 1
                                      ? 2
                                      : Number(inputValues.billed_to_dir_type),
                                    gConfig
                                  )
                                : ""
                            }
                          />
                        </div>
                      ) : (
                        <></>
                      )}
                    </>
                  }
                />
              </li>
              <li>
                <InputField
                  label={_t("Customer Contract") + " #"}
                  placeholder={_t("Customer Contract") + " #"}
                  name="customer_contract"
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues.customer_contract)
                  )}
                  maxLength={21}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  onChange={handleInpOnChange}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "customer_contract"
                  )}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "customer_contract",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "customer_contract",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "customer_contract",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.customer_contract) {
                      handleUpdateField({ customer_contract: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "customer_contract",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        customer_contract: details.customer_contract,
                      });
                    }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Assigned To")}
                  name="assigned_to"
                  placeholder={_t("Select Assignee")}
                  className="justify-between"
                  readOnlyClassName="sm:block flex"
                  labelPlacement="left"
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  editInline={true}
                  value={
                    inputValues?.assigned_to_arr?.length &&
                    inputValues?.assigned_to_arr?.length !== 0 &&
                    inputValues?.assigned_to_arr.length > 1
                      ? `${inputValues.assigned_to_arr?.length} Selected`
                      : HTMLEntities.decode(
                          sanitizeString(
                            inputValues?.assigned_to_arr
                              ?.map((assignee) => assignee.display_name)
                              .join(", ")
                          )
                        )
                  }
                  iconView={true}
                  onClick={() => {
                    setIsOpenSelectIssuedBy(false);
                    setIsOpenSelectBillTo(false);
                    setIsOpenSelectApprovedBy(false);
                    setIsOpenSelectAssignedTo(true);
                  }}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "assigned_to"),
                  }}
                  avatarProps={
                    inputValues?.assigned_to_arr?.length == 1
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(
                                inputValues?.assigned_to_arr[0]?.display_name
                              )
                            ),
                            image: inputValues?.assigned_to_arr[0]?.image,
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    <>
                      {inputValues.assigned_to_arr &&
                        inputValues.assigned_to_arr.length === 1 && (
                          <div className="flex gap-1 items-center">
                            <ContactDetailsButton
                              onClick={async () => {
                                await setAdditionContact(0);
                                setIsContactDetails(true);
                                setcontactId(
                                  inputValues?.assigned_to_arr?.[0]
                                    ?.user_id as number
                                );
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                inputValues?.assigned_to_arr[0]?.user_id?.toString() as string
                              }
                              directoryTypeKey={
                                fixedDirType
                                  ? getDirectaryKeyById(fixedDirType, gConfig)
                                  : ""
                              }
                            />
                          </div>
                        )}

                      {inputValues?.assigned_to_arr &&
                        inputValues?.assigned_to_arr?.length > 1 && (
                          <AvatarIconPopover
                            placement="bottom"
                            redirectionIcon={true}
                            assignedTo={
                              inputValues?.assigned_to_arr as IAssignedToUsers[]
                            }
                            setSelectedUserId={(data) => {
                              setcontactId(data.id);
                              setAdditionContact(data?.contactId || 0);
                            }}
                            setIsOpenContactDetails={setIsContactDetails}
                          />
                        )}
                    </>
                  }
                />
              </li>
            </ul>
          </div>
        }
        headerRightButton={
          <div className="flex md:flex-row flex-col md:items-center items-end gap-2.5">
            <div className="flex gap-2">
              <div>
                <CustomCheckBox
                  className="gap-1.5 w-fit ml-auto font-medium"
                  defaultChecked={!!details.count_in_contract_amount}
                  loadingProps={{
                    isLoading: isLoadingCheckBox,
                    className: "bg-[#ffffff]",
                  }}
                  disabled={isReadOnly}
                  onChange={(e) => {
                    handleCountContractCheck(e.target.checked ? 1 : 0);
                  }}
                >
                  {_t(
                    "No Cost " + getCOModuleName({ gConfig, isRequest: false })
                  )}
                </CustomCheckBox>
              </div>
            </div>
          </div>
        }
      />

      {isOpenSelectIssuedBy && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectIssuedBy}
          closeDrawer={() => {
            setIsOpenSelectIssuedBy(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleIssuedBy(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          selectedCustomer={
            inputValues.issued_by &&
            inputValues?.issued_by !== "" &&
            !!inputValues?.issued_by_name
              ? ([
                  {
                    display_name: inputValues?.issued_by_name,
                    image: inputValues?.issued_by_image,
                    user_id: inputValues?.issued_by,
                    type: inputValues.issued_by_type,
                    type_key: getDirectaryKeyById(
                      Number(inputValues.issued_by_type) === 1
                        ? 2
                        : Number(inputValues.issued_by_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
          additionalContactDetails={0}
        />
      )}

      {isOpenSelectBillTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectBillTo}
          closeDrawer={() => {
            setIsOpenSelectBillTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleBilledTo(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          additionalContactDetails={1}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.billed_to &&
            inputValues?.billed_to !== "" &&
            !!inputValues?.billed_to_name
              ? ([
                  {
                    display_name: inputValues?.billed_to_name,
                    user_id: inputValues?.billed_to,
                    contact_id: inputValues?.billed_to_contact ?? 0,
                    type: inputValues.billed_to_dir_type,
                    image:
                      inputValues?.billed_to_contact === 0 ||
                      inputValues?.billed_to_contact === null ||
                      !inputValues.billed_to_contact
                        ? inputValues?.billed_to_image
                        : "",
                    type_key: getDirectaryKeyById(
                      Number(inputValues.billed_to_dir_type) === 1
                        ? 2
                        : Number(inputValues.billed_to_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
        />
      )}

      {isOpenSelectApprovedBy && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectApprovedBy}
          closeDrawer={() => {
            setIsOpenSelectApprovedBy(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleApprovedBy(
              data.length ? (data[0] as Partial<TselectedContactSendMail>) : {}
            );
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.approved_by && inputValues?.approved_by !== ""
              ? ([
                  {
                    display_name: inputValues?.approved_by_name,
                    user_id: inputValues?.approved_by,
                    contact_id: inputValues?.approved_by_contact_id,
                    type: inputValues.approved_by_dir_type,
                    type_key: getDirectaryKeyById(
                      Number(inputValues.approved_by_dir_type) === 1
                        ? 2
                        : Number(inputValues.approved_by_dir_type),
                      gConfig
                    ),
                  },
                ] as TselectedContactSendMail[])
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
        />
      )}

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={false}
          setCustomer={(data) => {
            handleAssignedTo(data);
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.contractor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={
            inputValues.assigned_to_arr &&
            inputValues.assigned_to_arr.length !== 0
              ? assignedTo
              : []
          }
          groupCheckBox={true}
          projectId={details?.project_id as number}
          additionalContactDetails={0}
        />
      )}

      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => {
            setcontactId(0);
            setIsContactDetails(false);
          }}
          contactId={contactId}
          readOnly={isReadOnly}
          additional_contact_id={additionalContact}
        />
      )}
    </>
  );
};

export default WorkorderDetails;
