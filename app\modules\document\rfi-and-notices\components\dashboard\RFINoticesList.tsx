import type { GridReadyEvent, SortChangedEvent } from "ag-grid-community";
// helper, hook, utils, redux,
import {
  escapeHtmlEntities,
  getDefaultStatuscolor,
  sanitizeString,
} from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { routes } from "~/route-services/routes";
import isEmpty from "lodash/isEmpty";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
// Organisms
import { useEffect, useMemo, useRef, useState } from "react";
import { getGConfig, getGModuleFilters, getGProject } from "~/zustand";
import { getrfiAndNoticesListApi } from "../../redux/action/dashboardAction";
import isEqual from "lodash/isEqual";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useAppRFIDispatch, useAppRFISelector } from "../../redux/store";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { STATUS_CODE } from "~/shared/constants";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import RFIListAction from "../RFIListAction";
import { fetchDashData } from "~/modules/document/rfi-and-notices/redux/action/dashboardAction";
import AddScheduleNotice from "~/modules/document/rfi-and-notices/components/sidebar/addScheduleNotice/AddScheduleNotice";
import { AddComplianceNotice } from "~/modules/document/rfi-and-notices/components/sidebar/addComplianceNotice";
import { AddRequestInformation } from "~/modules/document/rfi-and-notices/components/sidebar/addRequestInformation";

let timeout: NodeJS.Timeout;

const RFINoticesList = ({
  setIsRFILoading = () => {},
  search,
}: RFITableProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppRFIDispatch();
  const { module_access }: GConfig = getGConfig();

  const { datasource, gridRowParams } = useTableGridData();

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const [addScheduleNoticeOpen, setAddScheduleNoticeOpen] =
    useState<boolean>(false);
  const [addComplianceNoticeOpen, setAddComplianceNoticeOpen] =
    useState<boolean>(false);
  const [addRequestInformationOpen, setAddRequestInformationOpen] =
    useState<boolean>(false);

  const filterSrv = getGModuleFilters() as Partial<RFIModuleFilter> | undefined;

  const filter = useMemo(() => {
    return {
      project: filterSrv?.project || "",
      status: filterSrv?.status !== "2" ? filterSrv?.status : "",
      type: filterSrv?.type == "" ? undefined : filterSrv?.type,
      tab_status: filterSrv?.tab_status || "all",
    };
  }, [JSON.stringify(filterSrv)]);

  const fetchRfiList = async () => {
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const { start, order_by_name, order_by_dir } = changeGridParams || {};
    const limit = changeGridParams?.length ?? 30;

    const filterObj: Partial<RFIModuleFilter> | undefined = !isEmpty(filter)
      ? getValuableObj(filter)
      : undefined;

    const length = changeGridParams?.length ?? limit;
    const startPagination = !!start ? Math.floor(start) : 0;

    let dataParams = {
      limit: length,
      start: startPagination,
      ignore_filter: 1,
      filter: filterObj,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
      search: !!search ? escapeHtmlEntities(search || "") : undefined,
    };

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_name;
    }

    try {
      if (dataParams?.start > 0) {
        setIsRFILoading(false);
      }
      gridParams?.api.hideOverlay();

      const resData = (await getrfiAndNoticesListApi(
        getValuableObj(dataParams)
      )) as IRFIApiRes;
      setIsRFILoading(false);

      const rfiListArr = resData?.data?.correspondenceData || [];
      // Check if we got less data than requested - indicates last page
      const isLastPage = rfiListArr.length < length;

      // Calculate total based on current page and data length
      const currentTotal = isLastPage
        ? (start ? start : 0) + rfiListArr.length
        : (start ? start : 0) + length + 1; // +1 indicates there might be more

      // Send response to grid
      gridParams?.success({
        rowData: rfiListArr,
        rowCount: currentTotal,
      });

      if (
        (!resData?.success || rfiListArr.length <= 0) &&
        startPagination === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && rfiListArr.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      setIsRFILoading(false);
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const previousValues = useRef({
    filter: JSON.stringify(filterSrv),
    search,
  });

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "prefix_company_correspondence_id",
      minWidth: 130,
      maxWidth: 160,
      flex: 1,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IRFITableCellRenderer) => {
        const { data } = params;
        const Id = HTMLEntities.decode(
          sanitizeString(data?.prefix_company_correspondence_id)
        );
        return (
          <>
            {Id ? (
              <Tooltip title={Id}>
                <Typography className="table-tooltip-text">{Id}</Typography>
              </Tooltip>
            ) : (
              <>-</>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Title"),
      field: "title",
      minWidth: 130,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IRFITableCellRenderer) => {
        const { data } = params;
        const rfi_title = HTMLEntities.decode(sanitizeString(data?.rfi_title));
        return rfi_title ? (
          <Tooltip title={rfi_title}>
            <Typography className="table-tooltip-text">{rfi_title}</Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "correspondence_type",
      minWidth: 75,
      maxWidth: 75,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellClass: "ag-cell-center",
      headerClass: "ag-header-center",
      cellRenderer: (params: IRFITableCellRenderer) => {
        const { data } = params;
        const correspondenceType = HTMLEntities.decode(
          sanitizeString(data?.correspondence_type)
        );
        if (data) {
          let typeIcon:
            | { name: string; icon: IFontAwesomeIconProps["icon"] }
            | undefined;
          if (correspondenceType === "Schedule Notice") {
            typeIcon = {
              name: _t("Schedule Notice"),
              icon: "fa-solid fa-calendar-lines-pen",
            };
          } else if (correspondenceType === "Compliance Notice") {
            typeIcon = {
              name: _t("Compliance Notice"),
              icon: "fa-solid fa-sensor-triangle-exclamation",
            };
          } else if (correspondenceType === "Request for Information") {
            typeIcon = {
              name: _t("Request for Information"),
              icon: "fa-solid fa-messages-question",
            };
          } else return <div>-</div>;
          return (
            typeIcon && (
              <ButtonWithTooltip
                icon={typeIcon?.icon}
                tooltipTitle={typeIcon?.name}
                tooltipPlacement="top"
                className="mx-auto"
                onClick={() => {}}
              />
            )
          );
        } else {
          return;
        }
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IRFITableCellRenderer) => {
        const { data } = params;
        const projectName = HTMLEntities.decode(
          sanitizeString(data?.project_name)
        );
        return projectName ? (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("From"),
      field: "from_username",
      minWidth: 130,
      maxWidth: 250,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IRFITableCellRenderer) => {
        const { data } = params;
        const fromUserNames = HTMLEntities.decode(
          sanitizeString(data?.from_username)
        );
        const avatarSrc = data?.from_username_image || "";
        const companyName = data?.company_name || "";

        return fromUserNames || companyName ? (
          <Tooltip title={fromUserNames}>
            <div className="flex gap-2 justify-start items-start max-w-full w-fit">
              <AvatarProfile
                user={{
                  name: HTMLEntities.decode(sanitizeString(fromUserNames)),
                  image: avatarSrc,
                }}
                className="flex justify-start items-start"
                iconClassName="text-[11px] font-semibold"
              />
              <Typography className="table-tooltip-text justify-start items-start !max-w-[calc(100%-32px)]">
                {fromUserNames || companyName}
              </Typography>
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("To"),
      field: "user_to_names",
      sortable: true,
      minWidth: 130,
      maxWidth: 200,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IRFITableCellRenderer) => {
        const { user_to_names, user_to, user_image } = data;
        let toNamesList: string[] = [];
        let toNamesImages: string[] = [];
        let toNamesIds: string[] = [];
        const to_names = user_to_names || [];
        const formattedToNames = user_to_names?.[0]?.replace(/,+/g, "")?.trim();
        const to_user_image = user_image?.split(",") || [];
        const to_ids = user_to?.split(",") || [];
        to_names?.forEach((name: string, index: number) => {
          if (name?.trim() !== "") {
            toNamesList.push(name.trim());
            toNamesIds.push(to_ids[index]?.trim() || "");
            toNamesImages.push(to_user_image[index]?.trim() || "");
          }
        });

        const cleanFirstName = to_names?.[0]
          ?.replace(/[^a-zA-Z\s]/g, "")
          ?.trim();
        const companyName = data?.company_name || "";

        return (
          <>
            {user_to_names !== null &&
            formattedToNames !== "" &&
            user_to_names !== undefined ? (
              <div className="flex items-center gap-2 overflow-hidden max-w-full w-fit">
                {user_to_names.length > 1 ? (
                  <>
                    <AvatarGroup
                      max={{
                        count: 1,
                        style: {
                          color: "#223558",
                          backgroundColor: "#ECF1F9",
                        },
                      }}
                      size={24}
                      className="flex justify-start"
                      prefixCls="multi-avatar-scroll"
                    >
                      {toNamesList?.map((item: string, index: number) => {
                        const dName = HTMLEntities.decode(sanitizeString(item));
                        const userImg = toNamesImages[index];
                        return (
                          <div
                            key={index}
                            className={`flex items-center ${
                              index === 0 ? "" : "gap-2 py-0.5 px-1"
                            }`}
                          >
                            <Tooltip title={dName} placement="top">
                              <div
                                className={`flex items-center gap-1 w-full ${
                                  index === 0 ? "" : "p-1"
                                }`}
                              >
                                <AvatarProfile
                                  user={{
                                    name: item,
                                    image:
                                      !userImg ||
                                      userImg.trim().replaceAll("'", "") === ""
                                        ? undefined
                                        : userImg,
                                  }}
                                  iconClassName="text-[11px] font-semibold"
                                />
                                {index > 0 && (
                                  <Typography className="">
                                    {_t(dName)}
                                  </Typography>
                                )}
                              </div>
                            </Tooltip>
                          </div>
                        );
                      })}
                    </AvatarGroup>
                    <Typography className="table-tooltip-text">
                      {cleanFirstName || companyName || "-"}
                    </Typography>
                  </>
                ) : (
                  <Tooltip
                    title={HTMLEntities.decode(
                      sanitizeString(formattedToNames)
                    )}
                  >
                    <div className="flex items-center gap-2 overflow-hidden max-w-full w-fit">
                      <AvatarProfile
                        user={{
                          name: HTMLEntities.decode(
                            sanitizeString(formattedToNames)
                          ),
                          image: to_user_image[0] || "",
                        }}
                        iconClassName="text-[11px] font-semibold"
                      />
                      <Typography className="table-tooltip-text !max-w-[calc(100%-32px)]">
                        {HTMLEntities.decode(
                          sanitizeString(formattedToNames)
                        ) || "-"}
                      </Typography>
                    </div>
                  </Tooltip>
                )}
              </div>
            ) : (
              "-"
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "correspondence_date",
      minWidth: 230,
      maxWidth: 230,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IRFITableCellRenderer) => {
        const { data } = params;
        const Date = data?.correspondence_date;
        const Time = data?.correspondence_time;
        return Date || Time ? (
          <DateTimeCard format="datetime" date={Date} time={Time} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "rfi_status",
      minWidth: 110,
      maxWidth: 110,
      cellClass: "ag-cell-center",
      headerClass: "ag-header-center",
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IRFITableCellRenderer) => {
        const rfiStatus = params?.data?.rfi_status_name?.toString() ?? "";
        const { color, textColor } = getDefaultStatuscolor(
          params?.data?.status_color || ""
        );
        return rfiStatus ? (
          <Tooltip title={rfiStatus}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                className="mx-auto text-13 type-badge common-tag"
                style={{
                  color: textColor,
                }}
              >
                {rfiStatus}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellClass: "!cursor-auto",
      cellRenderer: ({ data }: IRFITableCellRenderer) => {
        return (
          !isReadOnly && (
            <div className="flex items-center gap-2 justify-center">
              <RFIListAction
                isKanbanDropDown={false}
                isDetailDropDown={false}
                paramsData={data as unknown as IRFIDetails}
                onActionComplete={() => {
                  refreshAgGrid();
                  dispatch(fetchDashData());
                }}
              />
            </div>
          )
        );
      },
    },
  ];

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      setIsRFILoading(true);
      timeout = setTimeout(() => {
        if (!isEmpty(filterSrv)) {
          fetchRfiList();
        }
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filterSrv),
      search,
    };

    if (
      !isEqual(previousValues.current, currentValues) &&
      !isEmpty(filterSrv)
    ) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [JSON.stringify(filterSrv), search]);

  const ADD_RFI_NOTOCE_OPTION = [
    {
      label: "RFI (Request for Information)",
      icon: "fa-regular fa-plus",
      value: "add_rfi",
      onClick: () => {
        setAddRequestInformationOpen(true);
      },
    },
    {
      label: "Schedule Notice",
      icon: "fa-regular fa-plus",
      value: "add_schedule_notice",
      onClick: () => {
        setAddScheduleNoticeOpen(true);
      },
    },
    {
      label: "Compliance Notice",
      icon: "fa-regular fa-plus",
      value: "add_compliance_notice",
      onClick: () => {
        setAddComplianceNoticeOpen(true);
      },
    },
  ];

  return (
    <>
      <div
        className={`list-view-table ag-grid-cell-pointer ag-theme-alpine ${
          module_access === "read_only"
            ? "h-[calc(100dvh-294px)]"
            : "h-[calc(100dvh-270px)]"
        }`}
      >
        <DynamicTable
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div className="flex items-center gap-1">
                    <DropdownMenu
                      contentClassName="w-[250px] add-items-drop-down"
                      options={ADD_RFI_NOTOCE_OPTION}
                      buttonClass="w-fit h-[26px] m-0 !bg-transparent p-0 add-select-dropdown rounded-r-sm"
                      children={
                        <Typography className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer">
                          {_t("Click here")}
                        </Typography>
                      }
                    />
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={true}
          generateOpenInNewTabUrl={(data: { correspondence_id?: number }) =>
            `${routes.MANAGE_RFI_NOTICES.url}/${data?.correspondence_id}`
          }
        />
      </div>
      {addScheduleNoticeOpen && (
        <AddScheduleNotice
          addScheduleNoticeOpen={addScheduleNoticeOpen}
          setAddScheduleNoticeOpen={setAddScheduleNoticeOpen}
        />
      )}
      {addComplianceNoticeOpen && (
        <AddComplianceNotice
          addComplianceNoticeOpen={addComplianceNoticeOpen}
          setAddComplianceNoticeOpen={setAddComplianceNoticeOpen}
        />
      )}
      {addRequestInformationOpen && (
        <AddRequestInformation
          addRequestInformationOpen={addRequestInformationOpen}
          setAddRequestInformationOpen={setAddRequestInformationOpen}
        />
      )}
    </>
  );
};

export default RFINoticesList;
