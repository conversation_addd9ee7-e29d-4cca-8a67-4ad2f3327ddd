import { useEffect, useState } from "react";

//  Hook, redux
import { useTranslation } from "~/hook";
import { fetchDashData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { equiStaticColor } from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";

// atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";

const EquipmentDeliveries = () => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();

  const {
    isDashLoading,
    equipment_delivery,
    equipment_delivery_last_refresh_time,
  }: IDailyLogIntlState = useAppDLSelector((state) => state.dashboard);
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IEquipDelivery[]>([]);
  useEffect(() => {
    if (!isCashLoading && equipment_delivery) {
      setRowData(equipment_delivery);
    }
  }, [equipment_delivery, isCashLoading]);
  const columnDefs = [
    {
      headerName: _t("Item Name"),
      field: "equipment_name",
      minWidth: 130,
      maxWidth: 250,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      flex: 2,
      cellRenderer: ({ data }: IDLEquipmentTableCellRenderer) => {
        const equipmentName = data?.equipment_name;
        return equipmentName ? (
          <Tooltip title={equipmentName}>
            <Typography className="table-tooltip-text">
              {equipmentName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Supplier "),
      field: "vendor_name",
      minWidth: 130,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      flex: 2,
      cellRenderer: ({ data }: IDLEquipmentTableCellRenderer) => {
        const vendorName = data?.vendor_name;
        return vendorName ? (
          <Tooltip title={vendorName}>
            <Typography className="table-tooltip-text">{vendorName}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "status",
      minWidth: 100,
      maxWidth: 100,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellRenderer: (params: IDLEquipmentTableCellRenderer) => {
        const { return_status } = params.data;
        // ! This lable value will not send confirm with backend developer
        let satusColor: string = "";
        let satusBg: string = "";

        if (return_status === "In Use") {
          satusColor = equiStaticColor.inUse.color;
          satusBg = equiStaticColor.inUse.bg;
        } else {
          satusColor = equiStaticColor.returned.color;
          satusBg = equiStaticColor.returned.bg;
        }
        return return_status ? (
          <Tooltip title={return_status}>
            <div className="text-center overflow-hidden">
              {return_status && (
                <Tag
                  color={satusBg}
                  style={{
                    color: satusColor,
                  }}
                  className={`mx-auto text-13 type-badge common-tag`}
                >
                  {return_status}
                </Tag>
              )}
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const handleRefreshWidget = async () => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refreshType: "equipment_delivery" }));
    setIsCashLoading(false);
  };
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-equipment-items-delivered.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={_t("Equipment Deliveries")}
        showRefreshIcon={true}
        refreshIconTooltip={equipment_delivery_last_refresh_time}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isCashLoading}
        leftContent={
          <Tooltip
            title={_t(
              "Recently delivered equipment item selected from the Equipment tab > Equipment Items delivered."
            )}
            placement="top"
          >
            <FontAwesomeIcon
              className="h-[15px] w-[15px] text-primary-900 mr-auto"
              icon="fa-regular fa-circle-info"
            />
          </Tooltip>
        }
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            key={isDashLoading ? "loading" : "loaded"}
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            noRowsOverlayComponent={
              isDashLoading || isCashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};

export default EquipmentDeliveries;
