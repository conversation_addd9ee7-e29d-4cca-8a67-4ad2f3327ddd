import { useState } from "react";
import { useParams } from "@remix-run/react";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { getDLNotesAction } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { resetProjectNotesAct } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLNotesSlice";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { AddButton } from "~/shared/components/molecules/addButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// Other
import { useTranslation } from "~/hook";
import { ProjectNotes } from "../sidebar/projectNotes";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import Iframe from "~/components/page/$url/iframe";

const ProjectNotesCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { id: DLId }: RouteParams = useParams();
  const dispatch = useAppDLDispatch();

  const { isNoteTabLoading, noteData }: IDLNotesInitialState = useAppDLSelector(
    (state) => state.dailyLogNotes
  );
  const [isProjectNotesOpen, setIsProjectNotesOpen] = useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<number>(0);
  const [isProjectNotesLoding, setIsProjectNotesLoding] =
    useState<boolean>(false);

  const handleGetProjectNotes = async () => {
    setIsProjectNotesLoding(true);
    const resApi = (await getDLNotesAction({
      id: DLId || "",
    })) as IDLNotesApiRes;
    setIsProjectNotesLoding(false);
    if (resApi.success) {
      dispatch(resetProjectNotesAct(resApi.data));
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Project Notes")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-file-invoice",
          containerClassName:
            "bg-[linear-gradient(180deg,#42dd9b1a_0%,#3cb9b31a_100%)]",
          id: "project_notes_file_invoice",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        headerRightButton={
          <AddButton
            disabled={isReadOnly || isNoteTabLoading}
            onClick={() => {
              if (isReadOnly) {
                return;
              }
              setIsProjectNotesOpen(true);
            }}
          >
            {_t("Note")}
          </AddButton>
        }
        children={
          <div className="pt-2">
            {isNoteTabLoading || isProjectNotesLoding ? (
              <Spin className="w-full h-10 flex items-center justify-center" />
            ) : (
              <div className="ag-theme-alpine">
                <StaticTable
                  className="static-table"
                  columnDefs={[
                    {
                      headerName: _t("Added By"),
                      field: "prepared_by",
                      minWidth: 150,
                      flex: 1,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLProjectNotesTablCellRenderer) => {
                        const preparedBy = HTMLEntities.decode(
                          sanitizeString(data?.prepared_by?.toString())
                        );
                        return preparedBy ? (
                          <Tooltip title={preparedBy}>
                            <Typography className="table-tooltip-text">
                              {preparedBy}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Title"),
                      field: "title",
                      minWidth: 530,
                      maxWidth: 530,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLProjectNotesTablCellRenderer) => {
                        const subject = replaceDOMParams(
                          sanitizeString(data?.subject)
                        );
                        return subject ? (
                          <Tooltip title={subject}>
                            <Typography className="table-tooltip-text">
                              {subject}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      maxWidth: 80,
                      minWidth: 80,
                      suppressMenu: true,
                      cellRenderer: ({
                        data,
                      }: IDLProjectNotesTablCellRenderer) => {
                        return (
                          <div className="flex items-center gap-2 justify-center">
                            <ButtonWithTooltip
                              tooltipTitle={_t("View")}
                              tooltipPlacement="top"
                              icon="fa-solid fa-eye"
                              onClick={() => {
                                setSelectedId(data.note_id);
                              }}
                            />
                          </div>
                        );
                      },
                    },
                  ]}
                  rowData={noteData?.projectNotes || []}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-project-notes.svg`}
                    />
                  )}
                />
              </div>
            )}
          </div>
        }
      />
      {isProjectNotesOpen && (
        <ProjectNotes
          isOpen={isProjectNotesOpen}
          isViewOnly={isReadOnly}
          onClose={setIsProjectNotesOpen}
          onAddedRec={(data) => {
            setIsProjectNotesOpen(false);
          }}
        />
      )}
      {Boolean(selectedId) && (
        <CommonModal
          isOpen={Boolean(selectedId)}
          widthSize="98%"
          draggable={false}
          onCloseModal={() => {
            setSelectedId(0);
          }}
          modalBodyClass="p-0"
          header={{
            title: "",
            closeIcon: true,
          }}
        >
          <div className="p-5">
            {selectedId ? (
              <Iframe
                path="/manage_notes"
                otherParams={`iframecall=1&id=${selectedId}`}
                onIframeClose={() => {
                  handleGetProjectNotes();
                  setSelectedId(0);
                }}
              />
            ) : (
              "Something went wrong!"
            )}
          </div>
        </CommonModal>
      )}
    </>
  );
};

export default ProjectNotesCard;
