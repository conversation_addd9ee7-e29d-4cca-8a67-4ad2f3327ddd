interface IBillListApiResponseDataRecord {
  bill_id: number | string;
  project_id: number;
  subject: string;
  order_date?: string;
  due_date: string;
  term_id?: string;
  term_key?: string;
  notes?: string;
  tax_id: number;
  signature: string | null;
  total: string;
  cost: string;
  user_id: number;
  company_id: number;
  date_added: string;
  date_modified: string;
  demo_data: number;
  parent_bill_id: number;
  total_tax_rate?: string | null;
  company_bill_id: string;
  supplier_id: number;
  is_deleted: number;
  supplier_contact_id: number;
  qb_date?: string;
  quickbook_bill_id: number;
  qbc_id: string;
  is_updated: number;
  custom_bill_id: string;
  amount?: string;
  reference_purchase_order_id: number;
  reference_invoice_id: number;
  need_prefix_project: number;
  is_notes_convert: number;
  ref_po: string;
  reference_module_id: number;
  reference_primary_id: number;
  reference_sub_contract_id: number;
  is_shared: number;
  reversible_tax_amount: string;
  sc_multi_bill: number;
  origin: number;
  is_billable: number;
  project_name?: string;
  quickbook_classprojecttype_id?: string | null;
  project_type_name?: string | null;
  project_type?: string;
  term_name?: string;
  emp_username: string;
  supplier_name?: string;
  email_subject: string;
  supplier_company_name?: string;
  quickbook_contractor_id?: number;
  tpar?: number;
  purchase_order: string;
  due_balance: string;
  time_added: string;
  qb_date_added?: string;
  qb_time_added?: string;
  bill_payment: string;
  quickbook_term_id?: string;
  number_of_items: string;
  custom_form_data?: Object[];
}

interface IBillListObject {
  statusCode: number;
  message: string;
  success: boolean;
  responseTime: string;
  data: IBillListApiResponseDataRecord[];
}

interface IBillListApiResponse extends Omit<IApiCallResponse, "data"> {
  data: IBillListApiResponseDataRecord[];
  responseTime: string;
  message: string;
  statusCode: number;
  success: boolean;
}

interface IBillTableDropDownItemsProps {
  confirmArchiveDialogOpen: boolean;
  isCopyBillConfirmOpen?: boolean;
  confirmDialogOpen: boolean;
  callApiAgain?: () => void;
  onCopySuccess?: () => void;
  isDeleted?: string;
  billId?: string;
  onClose: (value: string) => void;
  confirmArchiveDialogOpen: boolean;
  confirmDialogOpen: boolean;
  callApiAgain?: () => void;
  isDeleted?: string;
  primary_key: string | number;
  onClose: (value: string) => void;
  isViewEmailModalOpen: boolean;
  data?: IBillListApiResponseDataRecord;
  setBillData: (data: IBillListApiResponseDataRecord) => void;
  setIsViewEmailModalOpen: (isViewEmailModalOpen: boolean) => void;
  isShareOpen: boolean;
  setIsSendEmailSidebarOpen;
  shareLink: string;
  setShareLink: (shareLink: string) => void;
  setIsShareOpen: (isShareOpen: boolean) => void;
  refreshAgGrid?: () => void;
}

interface IPurchaseOrderItems {
  item_id: number;
  purchase_order_id: number;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string;
  markup: string;
  tax_id: string;
  total_tax: string;
  cost_code_id: number;
  total: string;
  description: string;
  purchase_order_item_no: number;
  item_type: number;
  reference_item_id: number;
  assigned_to: number;
  company_id: number;
  directory_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  parent_item_id: number;
  demo_data: number;
  quickbook_poitem_id: number;
  quickbook_item_id: number;
  qbc_id: string;
  estimate_id: number;
  change_order_id: number;
  work_order_id: number;
  reference_module_item_id: number;
  project_budget_item_id: number;
  user_id: number;
  apply_global_tax: number | undefined;
  unit_cost_back: string;
  is_markup_percentage: number;
  markup_amount: string;
  item_on_database: number;
  is_discount_item: number;
  is_tax_item: number;
  is_freight_charge_item: number;
  sku: string;
  assigned_to_contact_id: number;
  variation_id: number;
  billed_quantity: number;
  delivered_quantity: number;
  internal_notes: string;
  is_project_template: number;
  project_template_id: string;
  origin: number;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  assignee_type: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  company_order_id: string;
  order_item_no: string;
  company_estimate_id: string;
  estimate_item_no: string;
  work_order: string;
  budget_item_no: string;
  budget_item_id: string;
  tax_rate: string;
  cost_code_name: string;
  cost_code: string;
  code_id: number;
  quickbook_costcode_id: number;
  bill_item_id: string;
  origin_date_modified: string;
  variation_name: string;
  isChecked?: boolean;
  account_id?: number;
}
interface IPoList {
  purchase_order_id: number;
  company_purchase_order_id: string;
  prefix_company_purchase_order_id: string;
  subject: string;
  supplier_details: [];
  multiple_supplier_names: string;
  multiple_supplier_company_names: string;
}
interface IBillDownloadDocumentWithAction extends IDownloadDocumentBase {
  action: string;
  op: string;
  bill_id: number | string;
}

interface BillsTableProps {
  moduleAccess: string;
  setIsBillLoding: (val: boolean) => void;
  setAddBillSidebarOpen: (val: boolean) => void;
  isBillLoding: boolean;
  addBill: () => void;
  customRef: React.MutableRefObject<{
    refreshAgGrid: () => void;
  }>;
}

interface IBillListObject {
  statusCode: number;
  message: string;
  success: boolean;
  responseTime: string;
  data: IBillListApiResponseDataRecord[];
}

interface IGridParamsBillList {
  changeGridParams?: IBillListParmas;
  gridParams?: IServerSideGetRowsParams;
}

interface ITempFil {
  start_date?: string;
  end_date?: string;
  project?: string;
  directory?: string;
  expense_by?: string;
  vendor?: string;
  status?: string;
  agency?: string;
  expire_start_date?: string;
  expire_end_date?: string;
  permit_type?: string;
  list_for?: string | number;
  sort_by_order?: string;
  directory_names?: string;
  filter_my_list?: string;
  project_names?: string;
  response_status?: string;
  sort_by_field?: number;
  tab?: string;
}

interface IBillTableDropDownItemsProps {
  confirmArchiveDialogOpen: boolean;
  confirmDialogOpen: boolean;
  callApiAgain?: () => void;
  isDeleted?: string;
  onClose: (value: string) => void;
  confirmArchiveDialogOpen: boolean;
  confirmDialogOpen: boolean;
  callApiAgain?: () => void;
  isDeleted?: string;
  primary_key: string | number;
  onClose: (value: string) => void;
  isViewEmailModalOpen: boolean;
  data: any; //it will be fixed once update with new module
  setBillData: (data: any) => void; //it will be fixed once update with new module
  setIsViewEmailModalOpen: (isViewEmailModalOpen: boolean) => void;
  isShareOpen: boolean;
  setIsSendEmailSidebarOpen;
  shareLink: string;
  setShareLink: (shareLink: string) => void;
  setIsShareOpen: (isShareOpen: boolean) => void;
  refreshAgGrid: () => void;
}

interface IBillDownloadDocumentWithAction extends IDownloadDocumentBase {
  action: string;
  op: string;
  bill_id: number | string;
}

interface IUpdateBillDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  message: string;
  success: boolean;
}

interface IDeleteBillPaymentItem extends Omit<IDefaultAPIRes, "data"> {
  message: string;
  success: boolean;
}

interface ICopyBillDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  message: string;
  success: boolean;
}

interface IBillDetailAPI extends Omit<IDefaultAPIRes, "data"> {
  message: string;
  success: boolean;
  data: IBillDetailData[];
}
interface IBillRetainageDetailAPIRes extends Omit<IDefaultAPIRes, "data"> {
  message: string;
  success: boolean;
  data: IBillRetainageInfo;
}
interface IBillItemTableCellRenderer {
  data: Partial<IBillDetailsItem>;
}

interface IPostPayment {
  amount: number;
  bill_credit_account_id: number;
  bill_id: number;
  company_id: number;
  come_from: string;
  payment_date: string;
  payment_notes: string;
  user_id: number;
}

interface IBillTermType {
  item_id: string;
  is_custom_item: string;
  name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  date_added: string;
  date_modified: string;
  status_name: string;
  is_deleted: string;
  status_color: string;
  sort_order: string;
  term_id: string;
}
interface BillItemCostCardProps {
  handleUpdateField: (data: IBillDetailsFields) => Promise<void>;
  setTotalAmountForHeader: React.Dispatch<React.SetStateAction<number>>;
}
interface IBillDetailsTabProps {
  totalAmountForHeader?: number;
  loadingStatus?: IFieldStatus[];
  handleUpdateField?: (data: IBillDetailsFields) => Promise<void>;
  inputValues?: IBillDetailData | undefined;
  setInputValues?: (value: IBillDetailData) => void;
  handleChangeFieldStatus?: (data: IExpenseFieldStatus) => void;
  isReadOnly?: boolean;
}

interface IBillAddResponse {
  bill_id: number;
}

interface IPurchaseOrderItemApiRes {
  success: boolean;
  statusCode: number;
  message?: string;
  data?: IPurchaseOrderItems[];
}
interface IBillItemDeleteApiRes extends Omit<IDefaultAPIRes, "data"> {
  success: boolean;
  statusCode: number;
  message?: string;
}
interface IBillItemCreateHandler {
  (
    items: IBillDetailsItem[],
    options: {
      skipClose?: boolean;
      addFromResponse?: boolean;
      is_single_item?: boolean;
    }
  ): Promise<void>;
}
interface submitProps {
  items?: IBillDetailsItem[];
  skipClose?;
  addFromResponse?;
  is_single_item?: IBillItemCreateHandler;
}
interface ILumSumProps {
  isOpen: boolean;
  onClose: React.Dispatch<React.SetStateAction<boolean>>;
  isViewOnly: boolean;
  handleBillDetails: (data: IBillAddItesmApiRes) => Promise<void>;
}
interface IImportItemsPurchaseProps {
  importItemsPurchase: boolean;
  setImportItemsPurchase: React.Dispatch<React.SetStateAction<boolean>>;
  handleBillDetails: (data: IBillAddItesmApiRes) => Promise<void>;
}
interface IImportSubContractProps {
  importFromSubContract: boolean;
  setImportFromSubContract: React.Dispatch<React.SetStateAction<boolean>>;
  handleBillDetails: (data: IBillAddItesmApiRes) => Promise<void>;
}
interface IImportItemsCreditProps {
  importItemsCredit?: boolean;
  setImportItemsCredit?: React.Dispatch<React.SetStateAction<boolean>>;
  handleBillDetails: (data: IBillAddItesmApiRes) => Promise<void>;
}

interface importCreditFormData {
  unit_cost: number | string;
  cost_code_id: string;
  description: string;
  cost_code_name: string;
}
interface IImportItemsExpenseProps {
  expenseSingularItem: boolean;
  setExpenseSingularItem: React.Dispatch<React.SetStateAction<boolean>>;
  handleBillDetails: (data: IBillAddItesmApiRes) => Promise<void>;
}

interface IImportBillDiscountProps {
  addDiscountItem: boolean;
  setAddDiscountItem: React.Dispatch<React.SetStateAction<boolean>>;
  onSubmit: (
    items?: Partial<IBillDetailsItem>[],
    addFromResponse: IBillItemCreateHandler
  ) => Promise<void>;
}
interface IBillFrieghtChargeItemsProps {
  freightchargeItem: boolean;
  setfreightchargeItem: React.Dispatch<React.SetStateAction<boolean>>;
  onSubmit: (
    items?: Partial<IBillDetailsItem>[],
    addFromResponse: IBillItemCreateHandler
  ) => Promise<void>;
}
interface IBillRetainageItemProps {
  addRetainagePayment: boolean;
  setaddRetainagePayment: React.Dispatch<React.SetStateAction<boolean>>;
  onSubmit: (
    items?: Partial<IBillDetailsItem>[],
    addFromResponse: IBillItemCreateHandler
  ) => Promise<void>;
}

interface IBillDashboardApiResponseDataCountBillPaymentValue {
  amount: string;
}

interface IBillDashboardApiResponseDataUnPaidBillsValue {
  this_month: string;
  previous_month: string;
  this_year: string;
  previous_year: string;
}

interface IBillDashboardApiResponseDataBalanceStatus {
  bill_due_today: IBillDashboardApiResponseDataDueToday[];
  bill_due_1_30: IBillDashboardApiResponseDataDueToday[];
  bill_due_31_60: IBillDashboardApiResponseDataDueToday[];
  bill_due_61: IBillDashboardApiResponseDataDueToday[];
  [key: string]: IBillDashboardApiResponseDataDueToday[];
}

interface IBillDashboardApiResponseDataBillComingDue {
  bill_id: number;
  due_date: string;
  total: string;
  due_balance: string;
  amount: string;
  bill_total: string;
  supplier_name: string;
  supplier_name_only: string;
  bill: string;
  supplier_company_name: string;
}

interface IBillDashboardApiResponseDataBillPastDue {
  bill_id: number;
  due_date: string;
  total: string;
  due_balance: string;
  bill_total: string;
  supplier_name: string;
  supplier_name_only: string;
  bill: string;
  supplier_company_name: string;
}
interface IBillDashboardApiResponseDataSalesByMonth {
  data: string[];
  this_year_payment: string[];
  this_year_bill: string[];
  previous_year_payment: string[];
  previous_year_bill: string[];
}
interface IBillDashboardApiResponseData {
  countBillPaymentValue: IBillDashboardApiResponseDataCountBillPaymentValue;
  unPaidBillsValue: IBillDashboardApiResponseDataUnPaidBillsValue;
  balanceStatusData: IBillDashboardApiResponseDataBalanceStatus;
  billComingDue: IBillDashboardApiResponseDataBillComingDue[];
  billPastDue: IBillDashboardApiResponseDataBillPastDue[];
  salesByMonth: IBillDashboardApiResponseDataSalesByMonth;
  countBillPaymentValueLastRefreshTime: string;
  unPaidBillsValueLastRefreshTime: string;
  billComingDueLastRefreshTime: string;
  billPastDueLastRefreshTime: string;
  balanceStatusDataLastRefreshTime: string;
  salesByMonthLastRefreshTime: string;
}

interface IBillDashboardApiResponse extends Omit<IApiCallResponse, "data"> {
  data?: IBillDashboardApiResponseData;
  responseTime: string;
  refresh_type?: string;
}

interface IDownloadBillPdf {
  bill_id: number | undefined;
  remove_associated_data?: number;
  action?: string;
  t_id?: string;
}

interface ITaxAmountApiParams {
  start?: number;
  limit?: number;
}

interface IDownloadBillRes extends IBillAPIRes {
  data: ObjType;
}

interface IBillAPIRes {
  statusCode: number;
  message: string;
  success: boolean;
  base64_encode_pdf: string | undefined;
  base64_encode_pdfUrl: string | undefined;
}

interface IBillSendEmailForm extends ISendEmailFormDataWithApiDefaultNew {
  t_id?: string;
  bill_id?: number;
  action: string;
  custom_approve_message?: string;
  req_from?: string;
}

interface IAddLumSumItem {
  subject: string;
  item_type: string | number;
  total?: string;
  description?: string;
  internal_notes?: string;
  apply_global_tax?: boolean;
  account?: string;
  submitAction?: string;
  quantity?: number;
  unit_cost?: string;
}

interface IBillTerms {
  term_id: string;
  name: string;
}

interface IBillTermsInitialState {
  billTermsList: IBillTerms[];
  billTermsLoading: boolean;
  defaultTerm: string;
}

interface IBillTermsRes extends Omit<IDefaultAPIRes, "data"> {
  data: IBillTerms[];
  default_term: string;
}

interface IBillDataFrm {
  name?: string;
  quickbookTermId?: number;
  status?: number;
  qbcId?: number;
}
