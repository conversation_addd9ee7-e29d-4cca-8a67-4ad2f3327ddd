// Atoms

import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// Hook
import { useTranslation } from "~/hook";
import { useInAppDispatch, useInAppSelector } from "../../../redux/store";
import { useCallback, useEffect, useRef, useState } from "react";
import { CHECKLIST_DATA } from "../../../utils/constants";
import { updateInspectionDetail } from "../../../redux/slices/inspectionDetailsSlice";
import Sortable from "sortablejs";
import { addUpdateInspectionCheckList } from "../../../redux/action/inspectionDetailsAction";
import { Number } from "~/helpers/helper";
import { useParams } from "@remix-run/react";
import Checklist from "./Checklist";

const ChecklistCard = () => {
  const { _t } = useTranslation();
  const { id } = useParams();
  const dispatch = useInAppDispatch();
  const taskListRef = useRef<HTMLUListElement | null>(null);
  const checklistContainerRef = useRef<HTMLDivElement>(null);

  const [
    isLoadingTaskDragAndDropInSection,
    setIsLoadingTaskDragAndDropInSection,
  ] = useState<Record<number, boolean>>({});
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<
    Record<number, boolean>
  >({});

  const { details, loadingStatus } = useInAppSelector(
    (state) => state.inspectionDetails
  );

  useEffect(() => {
    if (
      details &&
      details.checklist[details.checklist.length - 1]?.item_id !== 0
    ) {
      const detailsWithDummyCheckList = {
        ...details,
        checklist: [...details.checklist, { ...CHECKLIST_DATA }],
      };
      dispatch(updateInspectionDetail(detailsWithDummyCheckList));
    }
  }, [details.checklist]);

  const initializeSortable = useCallback(() => {
    if (taskListRef.current) {
      const sortable = Sortable.create(taskListRef.current, {
        group: "shared",
        animation: 150,
        handle: ".drag-handle",
        filter: ".undraggable",
        onEnd: (evt) => handleTaskDrop(evt),
      });

      return () => sortable.destroy();
    }
  }, [JSON.stringify(details.checklist)]);

  useEffect(() => {
    const cleanupSortable = initializeSortable();
    return cleanupSortable;
  }, [initializeSortable]);

  const handleTaskDrop = (evt: Sortable.SortableEvent) => {
    const { oldIndex, newIndex, from, to } = evt;
    const nextTask = details.checklist[newIndex ?? 0];
    if (nextTask?.item_id === 0) {
      evt.from.insertBefore(evt.item, evt.from.children[oldIndex ?? 0]);
      return;
    }

    moveTask(oldIndex, newIndex);
  };

  const moveTask = async (oldIndex: number, newIndex: number) => {
    const movedTaskId = details.checklist[oldIndex ?? 0]?.item_id;
    setIsLoadingTaskDragAndDropInSection((prev) => ({
      ...prev,
      [movedTaskId]: true,
    }));
    const filteredCheckList = details.checklist.filter(
      (task) => task.item_id !== 0
    );
    const taskRemove = filteredCheckList.find(
      (data) => data.item_id == movedTaskId
    );

    const taskFilter = filteredCheckList.filter(
      (data) => data.item_id != movedTaskId
    );

    if (taskRemove) taskFilter.splice(newIndex ?? 0, 0, taskRemove);

    const task_sorting: IInspectionDetailsCheckList[] = taskFilter.map(
      (data, index) => ({
        ...data,
        sort_order: index + 1,
      })
    );

    const checkListToUpdate = task_sorting.map((task) => {
      return {
        item_id: task.item_id,
        status: task.status,
        task: task.task,
        sort_item_order_no: task.sort_order,
      };
    });

    try {
      const updateRes = (await addUpdateInspectionCheckList({
        id: Number(id),
        checklist: checkListToUpdate,
      })) as IAddUpdateInspectionCheckListApiRes;

      if (updateRes.success) {
        const sortedCheckList = updateRes.data.sort(
          (a, b) => a.sort_order - b.sort_order
        );

        const updatedDetails = {
          ...details,
          checklist: [...sortedCheckList, { ...CHECKLIST_DATA }],
        };
        dispatch(updateInspectionDetail(updatedDetails));
        setIsLoadingTaskDragAndDropInSection((prev) => ({
          ...prev,
          [movedTaskId]: false,
        }));
      } else {
        notification.error({
          description: updateRes.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong !1",
      });
    } finally {
      setIsLoadingTaskDragAndDropInSection((prev) => ({
        ...prev,
        [movedTaskId]: false,
      }));
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Checklist")}
        iconProps={{
          icon: "fa-solid fa-clipboard-list-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#4776E61a_0%,#8E54E91a_100%)]",
          id: "Checklist_icon",
          colors: ["#4776E6", "#8E54E9"],
        }}
        children={
          <div
            ref={checklistContainerRef}
            className="mt-2 relative p-2 bg-[#F8F8F8] rounded "
          >
            <Typography className="text-13 text-primary-900/50 mb-1.5 block">
              {_t("Check Item Once it is Completed")}
            </Typography>
            <ul
              ref={taskListRef}
              key={`checklit-section`}
              className="w-full flex flex-col gap-1"
            >
              {details.checklist.map((task, index) => {
                return (
                  <Checklist
                    task={task}
                    loadingProps={{
                      isLoadingCheckBox,
                      setIsLoadingCheckBox,
                    }}
                    isLoadingTaskDragAndDropInSection={
                      isLoadingTaskDragAndDropInSection
                    }
                    taskIndex={index}
                    checklistContainerRef={checklistContainerRef}
                  />
                );
              })}
            </ul>
          </div>
        }
      />
    </>
  );
};

export default ChecklistCard;
