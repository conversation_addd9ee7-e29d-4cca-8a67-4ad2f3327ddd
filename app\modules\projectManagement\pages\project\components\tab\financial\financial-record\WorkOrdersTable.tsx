import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { WorkOrderslFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/workOrderslFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProDispatch, useAppProSelector } from "../../../../redux/store";
import {
  getDefaultStatuscolor,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { useProjectDetail } from "../../../../hook/useProjectsDetails";
import { toBoolean } from "~/modules/financials/pages/estimates/utils/common";
import { routes } from "~/route-services/routes";
import { useNavigate, useParams } from "@remix-run/react";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { fetchProjectDetailsApi } from "../../../../redux/action/projectDetailsAction";
import { getGConfig } from "~/zustand";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { sendMessageKeys } from "~/components/page/$url/data";

const WorkOrdersTable = (props: TableProps) => {
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { authorization }: GConfig = getGConfig();
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const WorkOrderModule = getGlobalModuleByKey(CFConfig.work_order_module);
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const workOrders = financialData?.work_orders ?? [];
  const { formatter } = useCurrencyFormatter();
  const { handleUpdateField, loadingStatus } = useProjectDetail();
  const { details } = useAppProSelector((state) => state.proDetails);
  const navigate = useNavigate();
  const { id } = useParams();
  const dispatch = useAppProDispatch();
  const [selectedId, setSelectedId] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [allWorkOrders, setAllWorkOrders] = useState<IProjectWorkOrdersData[]>(
    []
  );
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const purchaseSubWorkOrders = isShowingMore
    ? allWorkOrders
    : allWorkOrders?.slice(0, dataLimit);
  const totalCount = Number(
    financialData?.work_orders_count?.[0]?.number_of_work_order ?? 0
  );
  const totalAmount = Number(financialData?.work_orders_count?.[0]?.total ?? 0);
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  const [collapse, setCollapse] = useState<string[]>([]);

  useEffect(() => {
    if (isInitialLoad) {
      setAllWorkOrders(workOrders);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(
      workOrders?.map((wo) => [wo?.work_order_id, wo])
    );

    const mergedWorkOrders = allWorkOrders?.map((existing) => {
      const updated = updatedMap?.get(existing?.work_order_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(allWorkOrders?.map((wo) => wo?.work_order_id));

    const newWorkOrders = workOrders?.filter(
      (wo) => !existingIds?.has(wo?.work_order_id)
    );

    const nextAll = [...mergedWorkOrders, ...newWorkOrders];

    const hasChanged =
      nextAll?.length !== allWorkOrders?.length ||
      nextAll?.some(
        (wo, i) => JSON.stringify(wo) !== JSON.stringify(allWorkOrders[i])
      );

    if (hasChanged) {
      setAllWorkOrders(nextAll);
    }
  }, [workOrders, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "work_orders") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allWorkOrders.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["work_orders"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "company_order_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const woId = `WO #${HTMLEntities?.decode(
          sanitizeString(String(value))
        )}`;
        return value ? (
          <Tooltip title={woId}>
            <Typography className="table-tooltip-text">{woId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Service Date"),
      field: "order_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: "Subject",
      field: "work_order",
      minWidth: 320,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const work_order = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={work_order}>
            <Typography className="table-tooltip-text">{work_order}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "status_name",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: WorkOrders }) => {
        const status = data.status_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.status_color || ""
        );
        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <div className="table-tooltip-text text-center">-</div>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formattedValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "work_order_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: WorkOrders }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_WORKORDER.url +
                    "/" +
                    (data?.work_order_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <WorkOrderslFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              workOrdersId={data.work_order_id}
            />
          </div>
        );
      },
    },
  ];

  async function changeCheckboxHanlder(e: CheckboxChangeEvent) {
    setLoading(true);
    await handleUpdateField({
      data_to_send_in_api: {
        add_wo_total_to_revised_contract_amount: Number(e.target.checked),
      },
      data_to_update_in_store: {
        add_wo_total_to_revised_contract_amount: Number(e.target.checked),
      },
    });
    dispatch(
      fetchProjectDetailsApi({
        project_id: Number(id),
        record_type: "project",
        skipLoading: true,
      })
    );
    setLoading(false);
  }

  return (
    <>
      <CollapseSingleTable
        title={_t(WorkOrderModule?.plural_name ?? "Work-Orders")}
        defaultActiveKey={totalCount > 0 ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(WorkOrderModule?.module_name ?? "Work-Order")}
        rightContentClass="flex-wrap"
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_WORKORDER.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_WORKORDER.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        beforeContant={
          <CustomCheckBox
            className="flex items-center gap-2 ml-auto"
            checked={toBoolean(
              details?.add_wo_total_to_revised_contract_amount
            )}
            onChange={changeCheckboxHanlder}
            loadingProps={{
              isLoading: loading,
              className: "bg-[#ffffff]",
            }}
          >
            {_t("Add WO Total to Revised Contract Amount?")}
          </CustomCheckBox>
        }
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={purchaseSubWorkOrders}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-work-order.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["work_orders", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, ["work_orders", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default WorkOrdersTable;
