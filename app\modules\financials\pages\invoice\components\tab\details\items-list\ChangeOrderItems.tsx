// atoms
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { useAppIVDispatch, useAppIVSelector } from "../../../../redux/store";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "@remix-run/react";
import {
  deleteInvoiceItems,
  fetchSOVItemsList,
  getInvoiceItemsList,
  updateInvoiceItem,
} from "../../../../redux/action/InvoiceItemsActions";
import {
  deleteInvoiceItem,
  updateInvoiceItems,
} from "../../../../redux/slices/InvoiceItemsSlice";
import { getGConfig, useGModules } from "~/zustand";
import { ValueSetterParams } from "ag-grid-community";
import { defaultConfig } from "~/data";
import { ColDef } from "ag-grid-community";

interface IInvoiceChangeOrderSOVItemsProps {
  isReadOnly: boolean;
}

const ChangeOrderSOVItems = ({
  isReadOnly,
}: IInvoiceChangeOrderSOVItemsProps) => {
  const { _t } = useTranslation();
  const params = useParams();
  const dispatch = useAppIVDispatch();
  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );
  const invoiceDetails = useAppIVSelector(
    (state) => state.invoiceDetails.details
  );
  const { getGModuleByKey } = useGModules();
  const [selectedItemToDelete, setSelectedItemToDelete] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const { formatter } = useCurrencyFormatter();
  const { module_access, module_id }: GConfig = getGConfig();

  const readOnlyMode = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        item_equipment: "fa-regular fa-screwdriver-wrench",
        item_material: "fa-regular fa-block-brick",
        item_labour: "fa-regular fa-user-helmet-safety",
        item_sub_contractor: "fa-regular fa-file-signature",
        item_other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  const getUpdatedBillingRatio = useCallback(
    (original_item_total: number, newValue: number) => {
      const mainTotal = (newValue * 100) / original_item_total;
      return mainTotal;
    },
    []
  );

  const getUpdatedBillingTotal = useCallback(
    (original_item_total: number, newValue: number) => {
      const mainTotal = (original_item_total * newValue) / 100;
      return mainTotal;
    },
    []
  );

  const getTotalAmount = useCallback(
    (data: IInvoiceItemData, qty: string, unit_cost: string | number) => {
      const markup_amount = Number(data.markup || 0);
      const is_markup_percentage = Number(data.is_markup_percentage);

      const total: number = Number(qty) * Number(unit_cost);
      let mainTotal: number = 0;

      if (is_markup_percentage === 1) {
        const markup = (total * markup_amount) / 100;
        mainTotal = Number(markup) + Number(total);
      } else {
        mainTotal = markup_amount / 100;
      }
      return mainTotal;
    },
    []
  );

  const handleInLineEditing = async (newValue: IUpdateInvoiceItem) => {
    const itemToBeUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: [
        {
          ...newValue,
        },
      ],
    };

    const response = (await updateInvoiceItem(
      itemToBeUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    let itemsNewValue = {};
    if (newValue.billing_option === "percentage") {
      itemsNewValue = {
        total: newValue.total,
        paid_bill: newValue.billing_ratio,
        billing_option: newValue.billing_option,
        apply_global_tax: newValue.apply_global_tax,
        item_type: newValue.item_type,
      };
    } else {
      itemsNewValue = {
        billing_option: newValue.billing_option,
        paid_unit: newValue.billing_unit,
        apply_global_tax: newValue.apply_global_tax,
        total: newValue.total,
        item_type: newValue.item_type,
      };
    }
    if (response.success) {
      const updatedItems = {
        ...invoiceItems,
        change_order_sections: invoiceItems.change_order_sections.map(
          (section) => {
            return {
              ...section,
              items: section.items.map((item) => {
                if (item.item_id === newValue.item_id) {
                  if (newValue.billing_option === "percentage")
                    return {
                      ...item,
                      ...itemsNewValue,
                      total_bill:
                        Number(item.total_bill || 0) -
                        Number(item.paid_bill || 0) +
                        Number(newValue.billing_ratio),
                    };
                  else {
                    return {
                      ...item,
                      ...itemsNewValue,
                      total_bill:
                        Number(item.total_bill || 0) -
                        Number(item.paid_unit || 0) +
                        Number(newValue.billing_unit),
                    };
                  }
                } else {
                  return item;
                }
              }),
            };
          }
        ),
        items: invoiceItems.items.map((item) => {
          if (item.item_id === newValue.item_id) {
            if (newValue.billing_option === "percentage")
              return {
                ...item,
                ...itemsNewValue,
                total_bill:
                  Number(item.total_bill || 0) -
                  Number(item.paid_bill || 0) +
                  Number(newValue.billing_ratio),
              };
            else {
              return {
                ...item,
                ...itemsNewValue,
                total_bill:
                  Number(item.total_bill || 0) -
                  Number(item.paid_unit || 0) +
                  Number(newValue.billing_unit),
              };
            }
          } else {
            return item;
          }
        }),
      };

      dispatch(updateInvoiceItems({ items: updatedItems }));
    }
  };

  const handleInvoiceItemDelete = async (itemId: number) => {
    setIsDeleting(true);
    const response = (await deleteInvoiceItems({
      id: Number(params.id),
      itemId: itemId,
    })) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        change_order_sections: invoiceItems.change_order_sections.map(
          (section) => {
            return {
              ...section,
              items: section.items.filter(
                (item: IInvoiceItemData) => item.item_id !== itemId
              ),
            };
          }
        ),
        items: invoiceItems.items.filter((item) => item.item_id !== itemId),
      };
      const filtereNewItems: IInvoiceItemsListData = {
        ...newItems,
        change_order_sections: newItems.change_order_sections.filter(
          (section) => section.items.length > 0
        ),
      };
      dispatch(deleteInvoiceItem({ items: filtereNewItems }));
      if (invoiceDetails.project_id) {
        dispatch(
          fetchSOVItemsList({
            project_id: invoiceDetails.project_id,
            module_id: module_id,
            need_section: 1,
          })
        );
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    } else {
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
      notification.error({
        description: response.message || "Something went wrong!",
      });
    }
  };

  const handleApplyGlobalTaxUpdate = async (
    invoiceItem: IInvoiceItemData,
    applyGlobalTax: number
  ) => {
    const itemToBeUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: [
        {
          item_id: invoiceItem.item_id,
          subject: invoiceItem.subject,
          item_type: invoiceItem.item_type,
          apply_global_tax: applyGlobalTax,
        },
      ],
    };

    const response = (await updateInvoiceItem(
      itemToBeUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        change_order_sections: invoiceItems.change_order_sections.map(
          (section) => {
            return {
              ...section,
              items: section.items.map((item) => {
                if (item.item_id === invoiceItem.item_id) {
                  return {
                    ...item,
                    apply_global_tax: applyGlobalTax,
                  };
                } else {
                  return { ...item };
                }
              }),
            };
          }
        ),
        items: invoiceItems.items.map((item) => {
          if (item.item_id === invoiceItem.item_id) {
            return {
              ...item,
              apply_global_tax: applyGlobalTax,
            };
          } else {
            return { ...item };
          }
        }),
      };
      dispatch(updateInvoiceItems({ items: newItems }));
    }

    if (!response.success) {
      notification.error({
        description: response.message || "Something went wrong",
      });
    }
  };

  const handlePercentageClear = async (items: IInvoiceItemData[]) => {
    const dataFotUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: items.map((item) => {
        if (item.billing_option === "percentage") {
          return {
            item_id: item.item_id,
            billing_option: item.billing_option,
            billing_ratio: "0",
            apply_global_tax: item.apply_global_tax,
            total: 0 * 100,
            item_type: item.item_type,
          };
        } else {
          return {
            item_id: item.item_id,
            billing_option: item.billing_option,
            billing_unit: "0",
            apply_global_tax: item.apply_global_tax,
            total: 0 * 100,
            item_type: item.item_type,
          };
        }
      }),
    };

    const response = (await updateInvoiceItem(
      dataFotUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const getInvoiceItemsResponse = (await getInvoiceItemsList({
        id: Number(params.id),
        data_for: "all",
        need_section: invoiceDetails.is_new_tm_invoice === 1 ? 1 : 0,
        is_separate_change_order_sections:
          invoiceDetails.is_new_tm_invoice === 0 ? true : undefined,
        is_separate_estimate_sections: false,
      })) as IGetInvoiceItemsApiRes;

      if (getInvoiceItemsResponse.success) {
        dispatch(updateInvoiceItems({ items: getInvoiceItemsResponse.data }));
      }
    }
  };

  const CustomHeader = (props: any) => {
    return (
      <div className="text-right w-full">
        <div className="text-[#181d1f] font-semibold dark:text-white/90 w-fit ml-auto">
          {props.displayName}
          <br />
          <div
            className={`text-[#181d1f] font-normal dark:text-white/90 underline underline-offset-1 italic w-fit ml-auto pr-0.5 ${
              !isReadOnly ? "cursor-pointer" : "cursor-not-allowed"
            }`}
            onClick={() => {
              if (!isReadOnly) {
                handlePercentageClear(props.items as IInvoiceItemData[]);
              }
            }}
          >
            {props.clearText}
          </div>
        </div>
      </div>
    );
  };

  const tabToNextCell = (params: any) => {
    const { previousCellPosition } = params;

    if (previousCellPosition) {
      return {
        rowIndex: previousCellPosition.rowIndex,
        column: previousCellPosition.column,
        floating: previousCellPosition.floating,
      };
    }

    return params.nextCellPosition;
  };

  function roundUp(value: number, decimals: number) {
    const multiplier = Math.pow(10, decimals);
    return Math.round(value * multiplier);
  }

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  return (
    <>
      {invoiceItems.change_order_sections.map((section) => {
        let sectionTotal = 0;
        section.items.map((item) => {
          sectionTotal += Number(item.total);
        });

        if (section.items.length !== 0 && section.items[0].is_tm_item === 0) {
          const sectionTitle = `${
            getGModuleByKey(defaultConfig.change_order_module)?.plural_name ||
            "Change Orders"
          } ${
            section.items[0].change_order_item_section_id
              ? "#" + section.items[0].change_order_item_section_id
              : ""
          }${
            section.items[0].change_order_subject_name &&
            section.items[0].change_order_subject_name !== null
              ? ": " + section.items[0].change_order_subject_name
              : ""
          }`;

          const showPercentageColumn = section.items.some((item) => {
            return !(item.billing_option === "percentage");
          });

          const showUnitColumn = section.items.some((item) => {
            return !(item.billing_option === "unit");
          });
          const data = section.items;
          const sortedData = [...data].sort(
            (a, b) => Number(a?.invoice_item_no) - Number(b.invoice_item_no)
          );
          return (
            <CollapseSingleTable
              title={_t(HTMLEntities.decode(sanitizeString(sectionTitle)))}
              defaultActiveKey={section.items.length !== 0 ? 1 : 0}
              totalRecord={`${
                formatter(
                  formatAmount(((Number(sectionTotal) || 0) / 100).toFixed(2))
                ).value_with_symbol
              }`}
              totalRecordIcon={true}
              children={
                <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-8 before:bg-gradient-to-r from-primary-500">
                  <div className="ag-theme-alpine double-line-header">
                    <StaticTable
                      className="static-table"
                      stopEditingWhenCellsLoseFocus={true}
                      columnDefs={[
                        {
                          headerName: "",
                          field: "move",
                          minWidth: 30,
                          maxWidth: 30,
                          suppressMenu: true,
                        },
                        {
                          headerName: _t("Type"),
                          field: "type",
                          maxWidth: 50,
                          minWidth: 50,
                          suppressMenu: true,
                          headerClass: "ag-header-center",
                          cellClass: "ag-cell-center",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <Tooltip title={_t(data.item_type_name)}>
                                <FontAwesomeIcon
                                  className="w-4 h-4 text-primary-900 mx-auto"
                                  icon={iconByItemTypeName[data.item_type_key]}
                                />
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Item Name"),
                          field: "item",
                          minWidth: 220,
                          maxWidth: 220,
                          suppressMenu: true,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return (
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(data.subject)
                                )}
                              >
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(data.subject)
                                  )}
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Cost Code"),
                          field: "cost_code",
                          minWidth: 150,
                          flex: 2,
                          resizable: true,
                          suppressMenu: true,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            const costCode = `${data?.cost_code_name ?? ""}${
                              data?.csi_code ? ` (${data?.csi_code})` : ""
                            }${
                              data?.code_is_deleted === 1 ? ` (Archived)` : ""
                            }`;

                            return (
                              <>
                                {costCode ? (
                                  <Tooltip
                                    title={HTMLEntities.decode(
                                      sanitizeString(costCode)
                                    )}
                                  >
                                    <Typography className="table-tooltip-text">
                                      {HTMLEntities.decode(
                                        sanitizeString(costCode || "-")
                                      )}
                                    </Typography>
                                  </Tooltip>
                                ) : (
                                  "-"
                                )}
                              </>
                            );
                          },
                        },
                        {
                          headerName: _t("Total"),
                          field: "total",
                          maxWidth: 120,
                          minWidth: 120,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          headerComponentParams: {
                            template: `
                      <div class="text-right w-full">
                        <div class="text-[#181d1f] font-semibold dark:text-white/90">
                          ${_t("Total")} <br />
                          <div class="text-[#181d1f] font-normal dark:text-white/90">
                            ${_t("(Original)")}
                          </div>
                        </div>
                      </div>
                    `,
                          },
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      (
                                        (Number(data.original_item_total) ||
                                          0) / 100
                                      ).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(data.original_item_total) ||
                                            0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Invoiced"),
                          field: "billed",
                          maxWidth: 120,
                          minWidth: 120,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          headerComponentParams: {
                            template: `
                      <div class="text-right w-full">
                        <div class="text-[#181d1f] font-semibold dark:text-white/90">
                          ${
                            _t("Invoiced") +
                            `${showUnitColumn ? " (%)" : " (#)"}`
                          } <br />
                          <div class="text-[#181d1f] font-normal dark:text-white/90">
                            ${
                              _t("Remain") +
                              `${showUnitColumn ? " (%)" : " (#)"}`
                            }
                          </div>
                        </div>
                      </div>
                    `,
                          },
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const billed = `${Number(
                              Number(data.total_bill).toFixed(3)
                            )}/${
                              invoiceDetails.billing_option === "percentage"
                                ? Number(
                                    (
                                      100 - Number(data.total_bill || 0)
                                    ).toFixed(3)
                                  )
                                : Number(
                                    Number(data.quantity || 0) -
                                      Number(data.total_bill || 0)
                                  ) < 0 &&
                                  Number(invoiceDetails.allow_overbilling) === 1
                                ? "&#8734;"
                                : Number(
                                    Number(data.quantity || 0) -
                                      Number(data.total_bill || 0)
                                  )
                            }`;
                            return (
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(billed)
                                )}
                              >
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(sanitizeString(billed))}
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Invoice") + " (%)",
                          field: "to_bill",
                          maxWidth: 170,
                          minWidth: 170,
                          hide: showUnitColumn,
                          suppressMenu: true,
                          suppressKeyboardEvent,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellEditor: "agNumberCellEditor",
                          headerComponent: CustomHeader,
                          headerComponentParams: {
                            displayName:
                              _t("Invoice") +
                              `${showUnitColumn ? " (%)" : " (#)"}`,
                            clearText:
                              _t("(Clear") + `${showUnitColumn ? "%)" : "#)"}`,
                            items: section.items,
                          },
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return (
                              <Tooltip title={data.paid_unit}>
                                <Typography className="table-tooltip-text">
                                  {data.paid_unit}
                                </Typography>
                              </Tooltip>
                            );
                          },
                          editable: () => (isReadOnly ? false : true),
                          valueGetter: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return data.paid_unit;
                          },
                          valueSetter: (params: ValueSetterParams) => {
                            let newValue = params.newValue
                              ? params.newValue.toString()
                              : "0";
                            const [integerPart, decimalPart] =
                              newValue.split(".");
                            if (decimalPart && decimalPart.length > 3) {
                              notification.error({
                                description: _t(
                                  "Decimal digit should be less than or equal to 3 digits"
                                ),
                              });
                              return false;
                            }
                            const paidUnit = params.data.paid_unit;

                            if (newValue !== "") {
                              if (Number(newValue) === Number(paidUnit)) {
                                return false;
                              }
                              let remaining: number =
                                Number(params.data.quantity || 0) -
                                Number(params.data.total_bill || 0) +
                                Number(params.data.paid_unit || 0);
                              if (
                                params.data.allow_overbilling === 0 &&
                                Number(newValue) > Number(remaining)
                              ) {
                                if (Number(remaining) <= 0) {
                                  notification.error({
                                    description: _t(
                                      "This item has already been billed 100%."
                                    ),
                                  });
                                } else {
                                  notification.error({
                                    description: _t(
                                      `Enter amount between 0 and ${Number(
                                        remaining
                                      )}`
                                    ),
                                  });
                                }
                                return false;
                              }

                              const updatedTotal: number = getTotalAmount(
                                params.data,
                                Number(newValue).toString() as string,
                                (Number(params.data.unit_cost) / 100) as
                                  | string
                                  | number
                              );

                              const valuesToBeUpdate: IUpdateInvoiceItem = {
                                item_id: params.data.item_id,
                                billing_option: params.data.billing_option,
                                billing_unit: newValue,
                                apply_global_tax: params.data.apply_global_tax,
                                total: updatedTotal * 100,
                                item_type: params.data.item_type,
                                change_order_id: params.data.change_order_id,
                              };
                              let updatedData = {};
                              if (
                                valuesToBeUpdate.billing_option === "percentage"
                              ) {
                                updatedData = {
                                  ...params.data,
                                  total: valuesToBeUpdate.total,
                                  paid_bill: valuesToBeUpdate.billing_ratio,
                                  billing_option:
                                    valuesToBeUpdate.billing_option,
                                  apply_global_tax:
                                    valuesToBeUpdate.apply_global_tax,
                                  item_type: valuesToBeUpdate.item_type,
                                  total_bill:
                                    Number(params.data.total_bill || 0) -
                                    Number(params.data.paid_bill || 0) +
                                    Number(valuesToBeUpdate.billing_ratio),
                                };
                              } else {
                                updatedData = {
                                  ...params.data,
                                  billing_option:
                                    valuesToBeUpdate.billing_option,
                                  paid_unit: valuesToBeUpdate.billing_unit,
                                  apply_global_tax:
                                    valuesToBeUpdate.apply_global_tax,
                                  total: valuesToBeUpdate.total,
                                  item_type: valuesToBeUpdate.item_type,
                                  total_bill:
                                    Number(params.data.total_bill || 0) -
                                    Number(params.data.paid_unit || 0) +
                                    Number(valuesToBeUpdate.billing_unit),
                                };
                              }
                              params.node?.setData(updatedData);

                              handleInLineEditing(valuesToBeUpdate);

                              return true;
                            }

                            return false;
                          },
                        },
                        {
                          headerName: _t("Invoice") + " (%)",
                          field: "to_bill",
                          maxWidth: 115,
                          minWidth: 115,
                          hide: showPercentageColumn,
                          suppressMenu: true,
                          headerClass: "ag-header-right to-bill-header-cell",
                          cellClass: "ag-cell-right ad-call-pr-0",
                          cellEditor: "agNumberCellEditor",
                          suppressKeyboardEvent,
                          headerComponent: CustomHeader,
                          headerComponentParams: {
                            displayName:
                              _t("Invoice") +
                              `${showUnitColumn ? " (%)" : " (#)"}`,
                            clearText:
                              _t("(Clear") + `${showUnitColumn ? "%)" : "#)"}`,
                            items: section.items,
                          },
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      Number(Number(data.total) / 100).toFixed(
                                        2
                                      )
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        Number(
                                          Number(data.total) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            );
                          },
                          editable: () => (isReadOnly ? false : true),
                          valueGetter: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const toBill = (Number(data.total) / 100).toFixed(
                              2
                            );
                            return toBill;
                          },
                          valueSetter: (params: ValueSetterParams) => {
                            let newValue = params.newValue
                              ? params.newValue.toString()
                              : "0";
                            const [integerPart, decimalPart] =
                              newValue.split(".");
                            if (decimalPart && decimalPart.length > 2) {
                              notification.error({
                                description: _t(
                                  "Decimal digit should be less than or equal to 2 digits"
                                ),
                              });
                              return false;
                            }
                            const toBill = (
                              (Number(params.data.original_item_total) *
                                Number(params.data.paid_bill)) /
                              10000
                            ).toFixed(2);

                            if (newValue !== "") {
                              if (Number(newValue) === Number(toBill)) {
                                return false;
                              }

                              let remaining: number =
                                Number(params.data.total_bill || 0) -
                                Number(params.data.paid_bill || 0);

                              let remainingValue = (
                                (Number(params.data.original_item_total) *
                                  Number(remaining)) /
                                10000
                              ).toFixed(2);
                              if (
                                params.data.allow_overbilling === 0 &&
                                Number(newValue) + Number(remainingValue) >
                                  Number(params.data.original_item_total / 100)
                              ) {
                                if (
                                  Number(
                                    Number(
                                      params.data.original_item_total / 100
                                    ) - Number(remainingValue)
                                  ) <= 0
                                ) {
                                  notification.error({
                                    description: _t(
                                      "This item has already been billed 100%."
                                    ),
                                  });
                                } else {
                                  notification.error({
                                    description: _t(
                                      `Enter amount between 0 and ${Number(
                                        Number(
                                          params.data.original_item_total / 100
                                        ) - Number(remainingValue)
                                      )}`
                                    ),
                                  });
                                }
                                return false;
                              }

                              const updatedBillingRatio: number =
                                getUpdatedBillingRatio(
                                  Number(params.data.original_item_total) / 100,
                                  Number(params.newValue)
                                );

                              const updatedBillingTotal: number =
                                getUpdatedBillingTotal(
                                  Number(params.data.original_item_total) / 100,
                                  updatedBillingRatio
                                );

                              const valuesToBeUpdate: IUpdateInvoiceItem = {
                                item_id: params.data.item_id,
                                billing_option: params.data.billing_option,
                                billing_ratio: updatedBillingRatio.toFixed(3),
                                apply_global_tax: params.data.apply_global_tax,
                                total: updatedBillingTotal * 100,
                                item_type: params.data.item_type,
                                change_order_id: params.data.change_order_id,
                              };
                              let updatedData = {};
                              if (
                                valuesToBeUpdate.billing_option === "percentage"
                              ) {
                                updatedData = {
                                  ...params.data,
                                  total: valuesToBeUpdate.total,
                                  paid_bill: valuesToBeUpdate.billing_ratio,
                                  billing_option:
                                    valuesToBeUpdate.billing_option,
                                  apply_global_tax:
                                    valuesToBeUpdate.apply_global_tax,
                                  item_type: valuesToBeUpdate.item_type,
                                  total_bill:
                                    Number(params.data.total_bill || 0) -
                                    Number(params.data.paid_bill || 0) +
                                    Number(valuesToBeUpdate.billing_ratio),
                                };
                              } else {
                                updatedData = {
                                  ...params.data,
                                  billing_option:
                                    valuesToBeUpdate.billing_option,
                                  paid_unit: valuesToBeUpdate.billing_unit,
                                  apply_global_tax:
                                    valuesToBeUpdate.apply_global_tax,
                                  total: valuesToBeUpdate.total,
                                  item_type: valuesToBeUpdate.item_type,
                                  total_bill: valuesToBeUpdate.billing_unit,
                                };
                              }
                              params.node?.setData(updatedData);

                              handleInLineEditing(valuesToBeUpdate);
                              return true;
                            }

                            return false;
                          },
                        },
                        {
                          headerName: "",
                          field: "to_bill",
                          maxWidth: 55,
                          minWidth: 55,
                          hide: showPercentageColumn,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right ad-call-pl-0",
                          cellEditor: "agNumberCellEditor",
                          suppressKeyboardEvent,
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <Tooltip title={Number(data.paid_bill)}>
                                <Typography className="table-tooltip-text">
                                  ({Number(data.paid_bill)}%)
                                </Typography>
                              </Tooltip>
                            );
                          },
                          editable: () => (isReadOnly ? false : true),
                          valueGetter: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return Number(data.paid_bill);
                          },
                          valueSetter: (params: ValueSetterParams) => {
                            let newValue = params.newValue
                              ? params.newValue.toString()
                              : "0";
                            const [integerPart, decimalPart] =
                              newValue.split(".");
                            if (decimalPart && decimalPart.length > 3) {
                              notification.error({
                                description: _t(
                                  "Decimal digit should be less than or equal to 3 digits"
                                ),
                              });
                              return false;
                            }

                            if (newValue !== "") {
                              if (
                                Number(newValue) ===
                                Number(params.data.paid_bill)
                              ) {
                                return false;
                              }

                              let remaining: number =
                                Number(params.data.total_bill || 0) -
                                Number(params.data.paid_bill || 0);
                              if (
                                params.data.allow_overbilling === 0 &&
                                Number(newValue) + Number(remaining) > 100
                              ) {
                                if (Number(100 - remaining) <= 0) {
                                  notification.error({
                                    description: _t(
                                      "This item has already been billed 100%."
                                    ),
                                  });
                                } else {
                                  notification.error({
                                    description: _t(
                                      `Enter amount between 0 and ${Number(
                                        100 - remaining
                                      )}`
                                    ),
                                  });
                                }
                                return false;
                              }

                              const updatedBillingTotal: number =
                                getUpdatedBillingTotal(
                                  Number(params.data.original_item_total) / 100,
                                  Number(params.newValue)
                                );

                              const valuesToBeUpdate: IUpdateInvoiceItem = {
                                item_id: params.data.item_id,
                                billing_option: params.data.billing_option,
                                billing_ratio: Number(newValue).toFixed(3),
                                apply_global_tax: params.data.apply_global_tax,
                                total: updatedBillingTotal * 100,
                                item_type: params.data.item_type,
                                change_order_id: params.data.change_order_id,
                              };
                              let updatedData = {};
                              if (
                                valuesToBeUpdate.billing_option === "percentage"
                              ) {
                                updatedData = {
                                  ...params.data,
                                  total: valuesToBeUpdate.total,
                                  paid_bill: valuesToBeUpdate.billing_ratio,
                                  billing_option:
                                    valuesToBeUpdate.billing_option,
                                  apply_global_tax:
                                    valuesToBeUpdate.apply_global_tax,
                                  item_type: valuesToBeUpdate.item_type,
                                  total_bill:
                                    Number(params.data.total_bill || 0) -
                                    Number(params.data.paid_bill || 0) +
                                    Number(valuesToBeUpdate.billing_ratio),
                                };
                              } else {
                                updatedData = {
                                  ...params.data,
                                  billing_option:
                                    valuesToBeUpdate.billing_option,
                                  paid_unit: valuesToBeUpdate.billing_unit,
                                  apply_global_tax:
                                    valuesToBeUpdate.apply_global_tax,
                                  total: valuesToBeUpdate.total,
                                  item_type: valuesToBeUpdate.item_type,
                                  total_bill: valuesToBeUpdate.billing_unit,
                                };
                              }
                              params.node?.setData(updatedData);

                              handleInLineEditing(valuesToBeUpdate);
                              return true;
                            }

                            return false;
                          },
                        },
                        {
                          headerName: _t("QTY"),
                          field: "qty",
                          minWidth: 80,
                          maxWidth: 80,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                             const quantity =
                               formatter(
                                 formatAmount(Number(data?.quantity || 0), {
                                   isQuantity: true,
                                 })
                               ).value || 0;
                             return (
                               <Tooltip title={quantity}>
                                 <Typography className="table-tooltip-text">
                                   {quantity}
                                 </Typography>
                               </Tooltip>
                             );
                          },
                        },
                        {
                          headerName: _t("Cost"),
                          field: "cost",
                          minWidth: 130,
                          maxWidth: 130,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      (
                                        (Number(data.unit_cost) || 0) / 100
                                      ).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(data.unit_cost) || 0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Unit"),
                          field: "unit",
                          maxWidth: 100,
                          minWidth: 100,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          suppressMenu: true,
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return (
                              <Tooltip title={data.unit}>
                                <Typography className="table-tooltip-text">
                                  {data.unit || "-"}
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: "MU%",
                          field: "mu",
                          maxWidth: 80,
                          minWidth: 80,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const is_markup_percentage =
                              data?.is_markup_percentage ?? "";
                            const total =
                              Number(data?.quantity ?? 0) *
                              (Number(data?.unit_cost ?? 0) / 100);
                            const markup = Number(data?.markup);

                            let markupToShow = formatter("0.00").value;

                            if (is_markup_percentage?.toString() === "1") {
                              markupToShow = formatter(
                                markup?.toString() ?? "0"
                              ).value;
                            } else {
                              if (total != 0) {
                                const markupPercentage = markup / total - 100;
                                markupToShow = markupPercentage.toFixed(2);
                                markupToShow = formatter(
                                  Number(markupToShow || 0).toFixed(2)
                                )?.value;
                              }
                            }
                            return data.markup ? (
                              <Tooltip title={markupToShow}>
                                <Typography className="table-tooltip-text">
                                  {markupToShow}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <>-</>
                            );
                          },
                        },
                        {
                          headerName: _t("Total"),
                          field: "total",
                          maxWidth: 130,
                          minWidth: 130,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          headerComponentParams: {
                            template: `
                      <div class="text-right w-full">
                        <div class="text-[#181d1f] font-semibold dark:text-white/90">
                          ${_t("Total")} <br />
                          <div class="text-[#181d1f] font-normal dark:text-white/90">
                            ${_t("(This Inv.)")}
                          </div>
                        </div>
                      </div>
                    `,
                          },
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      (
                                        (Number(
                                          roundUp(Number(data.total) / 100, 2)
                                        ) || 0) / 100
                                      ).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(
                                            roundUp(Number(data.total) / 100, 2)
                                          ) || 0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            );
                          },
                        },
                        {
                          headerName: _t("Tax"),
                          field: "apply_global_tax",
                          minWidth: 50,
                          maxWidth: 50,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-center",
                          cellClass: "ag-cell-center flex justify-center",

                          editable: !isReadOnly,
                          cellRenderer: "agCheckboxCellRenderer",
                          cellEditor: "agCheckboxCellEditor",
                          valueGetter: ({ data }: IIvItemsCellRenderer) => {
                            return data?.apply_global_tax == 1;
                          },
                          valueSetter: (params: ValueSetterParams) => {
                            if (params && params.node) {
                              let nVal = params.newValue;
                              const updatedData = {
                                ...params.data,
                                apply_global_tax: nVal,
                              };
                              params.node.setData(updatedData);
                              handleApplyGlobalTaxUpdate(
                                params.data,
                                nVal === true ? 1 : 0
                              );
                            }
                            return true;
                          },
                        },
                        {
                          headerName: "",
                          field: "",
                          maxWidth: 80,
                          minWidth: 80,
                          suppressMenu: true,
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <div className="flex items-center gap-1.5 justify-end">
                                {!isReadOnly &&
                                  (invoiceDetails?.approval_type_key ===
                                    "invoice_open" ||
                                    invoiceDetails?.approval_type_key ===
                                      "invoice_on_hold") && (
                                    <ButtonWithTooltip
                                      tooltipTitle={_t("Delete")}
                                      tooltipPlacement="top"
                                      icon="fa-regular fa-trash-can"
                                      onClick={() => {
                                        setSelectedItemToDelete(
                                          Number(data.item_id)
                                        );
                                        setIsDeleteConfirmOpen(true);
                                      }}
                                    />
                                  )}
                              </div>
                            );
                          },
                        },
                      ]}
                      rowData={sortedData}
                      noRowsOverlayComponent={() => (
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-invoice-change-order.svg`}
                        />
                      )}
                      tabToNextCell={tabToNextCell}
                    />
                  </div>
                </div>
              }
            />
          );
        } else {
          return <></>;
        }
      })}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={() => {
            if (selectedItemToDelete) {
              handleInvoiceItemDelete(selectedItemToDelete);
            }
          }}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default ChangeOrderSOVItems;
