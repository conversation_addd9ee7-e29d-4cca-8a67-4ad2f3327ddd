import * as Yup from "yup";
import isEmpty from "lodash/isEmpty";
// Hook
import { useIframe, useTranslation } from "~/hook";
// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
// Other
import { useFormik } from "formik";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { useEffect, useMemo, useRef, useState } from "react";
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import { Form, useNavigate, useSearchParams } from "@remix-run/react";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { getValuableObj, Number, sanitizeString } from "~/helpers/helper";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { defaultConfig } from "~/data";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addInspection } from "../../redux/action/inspectionDashAction";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { routes } from "~/route-services/routes";
import { sendMessageKeys } from "~/components/page/$url/data";

const AddInspection = ({
  setAddInspectionOpen,
  addInspectionOpen,
}: IAddInspectionProps) => {
  const { _t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const isCalledProApiRef = useRef<string | null>(null);
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};

  const gConfig = getGConfig();
  const navigate = useNavigate();
  const { parentPostMessage } = useIframe();
  const { project_id, project_name }: GProject = getGProject();
  const { date_format, is_custom_inspection_id }: GSettings = getGSettings();
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();

  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isIframe, setIsIframe] = useState<boolean>(true);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [submittingFrm, setSubmittingFrm] = useState<boolean>(false);

  useEffect(() => {
    if (window && window?.location?.href) {
      const currentUrl = window.location.href;
      setIsIframe(currentUrl.includes("iframecall=1"));
    }
  }, []);

  useEffect(() => {
    //Call the auto number api only when drawer open and
    if (Number(is_custom_inspection_id) == 2 && addInspectionOpen) {
      setModuleAutoIncrementId(module_id, module_key);
    }
  }, [is_custom_inspection_id, addInspectionOpen]);

  const { componentList, loadingCustomField, setLoadingCustomFields } =
    useSideBarCustomField(
      { directory, directoryKeyValue } as IDirectoryFormCustomField,
      {
        moduleId: module_id,
      } as IRequestCustomFieldForSidebar
    );

  const initValues: IAddInspectionData = {
    project_id: null,
    assigned_to: 0,
    contact_id: 0,
    inspection_type: "",
    custom_inspection_id: "",
  };

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const baseValidationSchema = {
    project_id: Yup.number().required("This field is required."),
    custom_inspection_id:
      is_custom_inspection_id === 0
        ? Yup.string()
        : Yup.string().required("This field is required."),
    inspection_type: Yup.string().required("This field is required."),
  };

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...baseValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...baseValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    validateOnChange: false,
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      await submitInspection(values);
      setSubmitting(false);
    },
  });

  const submitInspection = async (values: IAddInspectionData) => {
    const formData = {
      project_id: values.project_id,
      inspection_type: HTMLEntities.encode(
        values?.inspection_type?.trim() ?? ""
      ),
      custom_inspection_id: HTMLEntities.encode(
        values.custom_inspection_id ?? ""
      ),
      assigned_to: values.assigned_to,
      contact_id: values.contact_id,
      custom_fields:
        formik.values.custom_fields && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
      access_to_custom_fields:
        componentList.length && !isNoAccessCustomField ? 1 : 0,
    };

    setSubmittingFrm(true);
    const addInsResData = (await addInspection(
      getValuableObj(formData)
    )) as Partial<IResponse<IAddInspectionApiRes>>;

    if (addInsResData.success) {
      EventLogger.log(
        EVENT_LOGGER_NAME.inspections + EVENT_LOGGER_ACTION.added,
        1
      );
      if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
        parentPostMessage(sendMessageKeys?.modal_change, {
          open: false,
        });
        setSubmittingFrm(false);
      } else {
        navigate(
          `${routes.MANAGE_INSPECTION.url}/${addInsResData?.data?.inspection_id}`
        );
      }
    } else {
      setSubmittingFrm(false);
      notification.error({
        description:
          addInsResData?.message ||
          "Failed to add insepction, try after some time!",
      });
    }
  };

  const { handleSubmit, setFieldValue, values, errors, touched, setValues } =
    formik;

  useEffect(() => {
    if (
      Number(is_custom_inspection_id) === 2 &&
      need_to_increment &&
      last_primary_id
    ) {
      formik.setValues({
        ...formik.values,
        custom_inspection_id: (
          Number(need_to_increment) + Number(last_primary_id)
        ).toString(),
      });
    } else {
      formik.setValues({
        ...formik.values,
        custom_inspection_id: "",
      });
    }
  }, [
    is_custom_inspection_id,
    need_to_increment,
    last_primary_id,
    addInspectionOpen,
  ]);

  useEffect(() => {
    if (
      searchParams.get("action")?.trim() === "new" &&
      searchParams.get("project")
    ) {
      if (isCalledProApiRef.current !== searchParams.get("project")) {
        isCalledProApiRef.current = searchParams.get("project");

        (async () => {
          try {
            const proResApi = (await getProjectDetails({
              start: 0,
              limit: 1,
              projects: searchParams.get("project") || "",
              need_all_projects: 0,
              global_call: true,
              is_completed: true,
              filter: { status: "0" },
            })) as IProjectDetailsRes;

            const queryPro = proResApi?.data?.projects[0];
            setFieldValue("project_id", queryPro?.id);
            setSelectedProject([
              {
                id: Number(queryPro?.id),
                project_name: queryPro?.project_name,
              },
            ]);
          } catch (e) {}
        })();
      }
    } else {
      if (
        project_id &&
        project_id !== "0" &&
        isEmpty(searchParams.get("project"))
      ) {
        setFieldValue("project_id", Number(project_id));
        setSelectedProject([
          {
            id: Number(project_id),
            project_name: project_name,
          },
        ]);
      } else {
        setFieldValue("project_id", null);
        setSelectedProject([]);
      }
    }
  }, [
    addInspectionOpen,
    project_id,
    project_name,
    isCalledProApiRef,
    searchParams.get("action"),
    searchParams.get("project"),
  ]);

  const selectedAssignedTo = useMemo(() => {
    if (
      values?.assigned_to !== 0 &&
      values?.assigned_to?.toString() !== "" &&
      values?.assigned_to
    ) {
      const assigned_to = [
        {
          display_name: HTMLEntities.decode(
            sanitizeString(values?.assigned_to_name)
          ),
          user_id: Number(values?.assigned_to),
          contact_id: Number(values?.assigned_to_contact_id) ?? 0,
          type: values?.assigned_to_type,
          type_key: getDirectaryKeyById(
            Number(values?.assigned_to_type) === 1
              ? 2
              : Number(values?.assigned_to_type),
            gConfig
          ),
          image: values?.assigned_image,
        },
      ];
      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [values?.assigned_to]);

  const onChangeProject = (projects: IProject[]) => {
    setSelectedProject(projects);
    if (projects.length) {
      setFieldValue("project_id", projects[0].id);
    } else {
      setFieldValue("project_id", null);
    }
  };

  const handleSelectAssignedTo = (data: TselectedContactSendMail[]) => {
    if (data.length) {
      const singleData = data[0];
      setValues({
        ...values,
        assigned_to_contact_id: singleData.contact_id?.toString(),
        assigned_to: singleData.user_id,
        contact_id: Number(singleData.contact_id),
        assigned_to_name: singleData.display_name,
        assigned_to_type:
          singleData.type ||
          getDirectaryIdByKey(singleData.type_key as CustomerTabs, gConfig),
        assigned_image: singleData?.image,
      });
    } else {
      setValues({
        ...values,
        assigned_to: 0,
        assigned_to_name: "",
      });
    }
  };

  const removeQueryParams = () => {
    if (window && window.history) {
      window.history.replaceState({}, document.title, window.location.pathname);
    }

    setSearchParams({}, { replace: true });
  };

  const handleCloseDrawer = () => {
    setAddInspectionOpen(false);
    setIsSubmit(false);
    setSelectedProject([]);
    formik.resetForm();
    formik.setErrors({});
    removeQueryParams();
  };

  return (
    <>
      <Drawer
        open={addInspectionOpen}
        rootClassName="drawer-open"
        width={718}
        maskClosable={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-neuter"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Add ${
                  HTMLEntities.decode(sanitizeString(module_singular_name)) ??
                  "Inspection"
                }`
              )}
            </Header>
          </div>
        }
        closeIcon={!isIframe && <CloseButton onClick={handleCloseDrawer} />}
      >
        <Form method="post" onSubmit={handleSubmit} noValidate>
          <div className="py-4">
            <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
              <div className="flex flex-col gap-5">
                <SidebarCardBorder addGap={true}>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Project")}
                      name="project_id"
                      id="project_id"
                      key="project_id"
                      labelPlacement="top"
                      onClick={() => setIsSelectProjectOpen(true)}
                      required={true}
                      value={
                        selectedProject.length
                          ? HTMLEntities.decode(
                              sanitizeString(selectedProject[0]?.project_name)
                            )
                          : ""
                      }
                      errorMessage={
                        touched?.project_id && !values.project_id
                          ? errors.project_id
                          : ""
                      }
                      addonBefore={
                        selectedProject.length ? (
                          <ProjectFieldRedirectionIcon
                            projectId={`${selectedProject[0]?.id}`}
                          />
                        ) : null
                      }
                    />
                  </div>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="w-full">
                      <InputField
                        disabled={is_custom_inspection_id == 0}
                        isRequired={is_custom_inspection_id !== 0}
                        label={_t("Insp.") + " #"}
                        name="custom_inspection_id"
                        id="custom_inspection_id"
                        value={
                          is_custom_inspection_id == 0
                            ? "Save To View"
                            : values?.custom_inspection_id
                        }
                        maxLength={20}
                        labelPlacement="top"
                        errorMessage={
                          touched?.custom_inspection_id &&
                          !values.custom_inspection_id
                            ? errors.custom_inspection_id
                            : ""
                        }
                        autoComplete="off"
                        onChange={(e) => {
                          setFieldValue("custom_inspection_id", e.target.value);
                        }}
                        onBlur={(e) => {
                          setFieldValue(
                            "custom_inspection_id",
                            e.target.value.trim()
                          );
                        }}
                      />
                    </div>
                    <div className="w-full">
                      <InputField
                        isRequired={true}
                        label={_t("Type")}
                        name="inspection_type"
                        id="inspection_type"
                        value={values?.inspection_type}
                        labelPlacement="top"
                        errorMessage={
                          touched?.inspection_type && !values.inspection_type
                            ? errors.inspection_type
                            : ""
                        }
                        autoComplete="off"
                        onChange={(e) => {
                          setFieldValue("inspection_type", e.target.value);
                        }}
                        onBlur={(e) => {
                          setFieldValue(
                            "inspection_type",
                            e.target.value.trim()
                          );
                        }}
                      />
                    </div>
                  </div>
                  <div className="w-full overflow-hidden">
                    <ButtonField
                      label={_t("Assigned To")}
                      name="assigned_to"
                      labelPlacement="top"
                      value={HTMLEntities.decode(
                        sanitizeString(
                          `${values.assigned_to_name?.toString() || ""}`
                        )
                      )}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values.assigned_to_name || "")
                          ),
                          image: values?.assigned_image || "",
                        },
                      }}
                      onClick={() => {
                        setIsOpenSelectAssignedTo(true);
                      }}
                      addonBefore={
                        values?.assigned_to ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setContactDetailDialogOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={values?.assigned_to.toString()}
                              directoryTypeKey={
                                Number(values?.assigned_to_type)
                                  ? getDirectaryKeyById(
                                      Number(values?.assigned_to_type) === 1
                                        ? 2
                                        : Number(values?.assigned_to_type),
                                      gConfig
                                    )
                                  : ""
                              }
                            />
                          </div>
                        ) : (
                          <></>
                        )
                      }
                    />
                  </div>
                </SidebarCardBorder>
                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                />
              </div>
            </div>
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                onClick={() => setIsSubmit(true)}
                buttonText={_t(
                  `Create ${
                    HTMLEntities.decode(sanitizeString(module_singular_name)) ??
                    "Inspection"
                  }`
                )}
                isLoading={submittingFrm}
                disabled={submittingFrm || loadingCustomField}
              />
            </div>
          </div>
        </Form>
      </Drawer>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={false}
          module_key={module_key}
        />
      )}

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleSelectAssignedTo(data);
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={selectedAssignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={values?.project_id as number}
          activeTab={defaultConfig.employee_key}
          additionalContactDetails={1}
        />
      )}

      {contactDetailDialogOpen && (
        <ContactDetails
          isOpenContact={contactDetailDialogOpen}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          additional_contact_id={values?.assigned_to_contact_id}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default AddInspection;
