// react
import { useMemo } from "react";
import { useParams } from "@remix-run/react";

// component
import ConditionsCard from "./details/ConditionsCard";
import DetailsCard from "./details/DetailsCard";
import IncidentsAccidents from "./details/IncidentsAccidents";
import NotesCard from "./details/NotesCard";
import WeatherCard from "./details/WeatherCard";

// Atoms
import { Spin } from "~/shared/components/atoms/spin";

// Organisms
import { CustomFieldForm } from "~/shared/components/organisms/customField";

// redux
import { useAppDLSelector } from "../../redux/store";

// zustand
import { getGConfig, useGModules } from "~/zustand";
import { defaultConfig } from "~/data";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";

const DetailsTab = ({ isClickOnReload }: { isClickOnReload: number }) => {
  const { id }: RouteParams = useParams();
  const { checkModuleAccessByKey } = useGModules();
  const { module_id, module_key }: GConfig = getGConfig();

  const { isReadOnlyCustomField, isNoAccessCustomField }: ICustomFieldAccess =
    getCustomFieldAccess();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const isAccessInc = useMemo(
    () => checkModuleAccessByKey(defaultConfig.incident_module),
    [defaultConfig.incident_module]
  );

  const { isDetailLoading }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const requestBody = useMemo(() => {
    return {
      moduleId: module_id,
      recordId: id,
    };
  }, [module_id, id]);
  return (
    <div className="grid lg:grid-cols-2 gap-2.5">
      <div className="py-3 px-[15px] common-card min-h-[221px]">
        <DetailsCard isReadOnly={isReadOnly} />
      </div>
      <div className="py-3 px-[15px] common-card min-h-[221px]">
        <WeatherCard isReadOnly={isReadOnly} />
      </div>
      {!isNoAccessCustomField && (
        <div className="py-3 px-[15px] common-card h-fit">
          {isDetailLoading && (
            <Spin className="w-full h-[150px] flex items-center justify-center" />
          )}
          {!isDetailLoading && (
            <CustomFieldForm
              requestBody={requestBody}
              isReadOnly={isReadOnly || isReadOnlyCustomField}
            />
          )}
        </div>
      )}
      <div className="flex flex-col gap-2.5">
        <div className="py-3 px-[15px] common-card min-h-[149px]">
          <NotesCard isReadOnly={isReadOnly} />
        </div>
        <div className="py-3 px-[15px] common-card min-h-[145px]">
          <ConditionsCard isReadOnly={isReadOnly} />
        </div>
      </div>
      {isAccessInc !== "no_access" ? (
        <div className="py-3 px-[15px] common-card lg:col-span-2">
          <IncidentsAccidents
            isClickOnReload={isClickOnReload}
            isReadOnly={isReadOnly || isAccessInc === "read_only"}
          />
        </div>
      ) : null}
    </div>
  );
};

export default DetailsTab;
