import { getApiDefaultParams } from "~/helpers/helper";
import { getApiData } from "~/helpers/axios-api-helper";
import { useGModuleFiltertore } from "./store";
import { apiRoutes } from "~/route-services/routes";
import { defaultConfig } from "~/data";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { modulesRoutes } from "~/route-services/module.routes";
import { initialPermitData } from "~/modules/projectManagement/pages/permits/zustand/constants";
import { initialChangeOrderData } from "~/modules/financials/pages/changeOrder/zustand/constant";
import { initialSTData } from "~/modules/projectManagement/pages/servicetickets/zustand/constants";

const { setState } = useGModuleFiltertore;

export const defaultModuleFilter = {
  project_permit: initialPermitData.filter,
  estimate_module: {
    // sort_by_field: 0,
    sort_by_order: "desc",
    filter_my_list: "0",
    expire_start_date: "",
    expire_end_date: "",
    start_date: "",
    end_date: "",
    status: "0",
    permit_type: "",
    project_names: "",
    project: "",
    agency: "",
    agency_names: "",
    permit_type_names: "",
    customer: "",
    customer_names: "",
    project_contact: "",
    project_contact_names: "",
    approval_type_names: "",
    approval_type: "",
    estimate_project_type: "",
    estimate_project_type_names: "",
    estimate_template_names: "",
    estimate_template_only: "0",
  },
  bill_module: {
    sort_by_field: 0,
    sort_by_order: "desc",
    filter_my_list: "0",
    start_date: "",
    end_date: "",
    status: "0",
    project_names: "",
    project: "",
    directory: "",
    directory_names: "",
    response_status: "",
    tab: "",
  },
  project_module: {
    status: "0",
    start_date: "",
    end_date: "",
    project_status: "",
    project_type: "",
    customer: "",
    project_manager: "",
    project_manager_names: "",
    sales_rep: "",
    sales_rep_names: "",
    project_contacts: "",
    customer_names: "",
    project_contacts_names: "",
    project_status_names: "",
    project_type_names: "",
    project_status_kanban: "",
    project_status_kanban_names: "",
    tab: "all",
  },
  equipment_logs: {
    project: "",
    equipments: "",
    project_names: "",
    equipment_names: "",
    status: "0",
    start_date: "",
    end_date: "",
  },
  directories: {
    assignee: "",
    directory_type: "",
    directory_type_names: "",
    group: "",
    group_names: "",
    project: "",
    project_names: "",
    project_type: "",
    quality: "",
    referral_source: "",
    service: "",
    service_names: "",
    stage: "",
    status: "0",
    tags: "",
    tags_names: "",
  },
  daily_log_module: {
    sort_by_field: 0,
    sort_by_order: "desc",
    filter_my_list: "0",
    start_date: "",
    end_date: "",
    status: "0",
    project_names: "",
    project: "",
    directory: "",
    directory_names: "",
    response_status: "",
    tab: "",
  },
  expense_module: {
    project: "",
    directory: "",
    status: "0",
    vendor: "",
    expense_by: "",
    vendor_names: "",
    expense_by_names: "",
    directory_names: "",
    end_date: "",
    start_date: "",
    project_names: "",
  },
  incidents_module: {
    project: "",
    directory: "",
    start_date: "",
    end_date: "",
    project_names: "",
    list_for: "",
    key: "",
    value: "",
    directory_names: "",
  },
  forms_checklists_module: {
    project: "",
    start_date: "",
    end_date: "",
    status: "",
    status_names: "",
  },
  inspections_module: {
    project: "",
    start_date: "",
    end_date: "",
    status: "",
    status_names: "",
    inspection_status: "",
    inspection_status_kanban: "",
  },
  invoice_module: {
    start_date: "",
    end_date: "",
    project: "",
    project_names: "",
    customer: "",
    customer_names: "",
    billing_status: "",
    billing_status_names: "",
    billing_status_kanban: "",
    billing_status_kanban_names: "",
    status: "0",
    tab: "all",
    key: "",
    value: "",
    is_aging: 0,
  },
  payment_module: {
    customer: "",
    customer_names: "",
    contact_id: "",
    customer_contact_id: "",
    project: "",
    project_name: "",
    project_names: "",
    status: "0",
    tab: "all",
  },
  punchlist_module: {
    project: "",
    status: "",
    completed_status: 0,
    tab: "",
    project_names: "",
    open: "",
  },
  purchase_orders_module: {
    billing_status: "",
    billing_status_kanban: "",
    billing_status_kanban_names: "",
    billing_status_names: "",
    directory: "",
    directory_names: "",
    project: "",
    project_names: "",
    status: "0",
    tab: "all",
    is_multiple_suppliers: "2",
  },
  leads_module: {
    sort_by_field: "0",
    sort_by_order: "desc",
    filter_my_list: "0",
    created_start_date: "",
    created_end_date: "",
    status: "0",
    stage: "",
    quality: "",
    sales_rep: "",
    sales_rep_name: "",
    est_sales_start_date: "",
    est_sales_end_date: "",
    project_type: "",
    value_filter_start: "",
    value_filter_end: "",
    referral_source: "",
    last_contacted_start_date: "",
    last_contacted_end_date: "",
    city: "",
    stage_names: "",
    quality_names: "",
    project_type_names: "",
    referral_source_names: "",
    project: 0,
    project_names: "",
  },
  safety_meetings_module: {
    project: "",
    project_names: "",
    employee: "",
    employee_names: "",
    topic_type: "",
    topic_type_names: "",
    status: "0",
    status_names: "",
    leader_id: "",
    topic: "",
    topic_names: "",
  },
  change_order_module: initialChangeOrderData.filter,
  todo_module: {
    assignee: "",
    assignee_names: "",
    is_my_todo: 0,
    project: "",
    project_names: "",
    sort_due_date: 0,
    status: "",
    status_names: "",
    tab: "",
  },
  submittal_module: {
    is_my_submittals: 0,
    project: "",
    project_names: "",
    status: "0",
    submittal_status: "",
    tab: "",
  },
  service_ticket_module: initialSTData.filter,
  work_order_module: {
    start_date: "",
    end_date: "",
    module_status: "0",
    module_status_names: "",
    status: "",
    status_names: "",
    project: "",
    project_names: "",
  },
  sub_contrats_module: {
    directory: "",
    directory_names: "",
    status: "0",
    status_names: "",
    project: "",
    project_names: "",
    sub_contract_status: "",
    start_date: "",
    end_date: "",
  },
  vehicle_module: {
    vehicle: "",
    status: "0",
    status_names: "",
    project: "",
    project_names: "",
    employee: "",
    employee_names: "",
    vehicle_names: "",
  },
  rfi_module: {
    sort_by_field: 0,
    sort_by_order: "desc",
    filter_my_list: "0",
    status: "0",
    status_names: "",
    project_names: "",
    project: "",
    type: "",
    type_names: "",
    response_status: "",
    tab: "",
  },
};

export const setGNewModuleFilter = (
  moduleFilter: Partial<ModuleFilter>,
  moduleId: number,
  moduleKey: string
) => {
  const getFilter = () => {
    let filter = {};
    switch (moduleKey) {
      case defaultConfig.project_permit_module:
        filter = defaultModuleFilter.project_permit;
        break;
      case defaultConfig.estimate_module:
        filter = defaultModuleFilter.estimate_module;
        break;
      case defaultConfig.bill_module:
        filter = defaultModuleFilter.bill_module;
        break;
      case defaultConfig.change_order_module:
        filter = defaultModuleFilter.change_order_module;
        break;
      case defaultConfig.equipment_log_module:
        filter = defaultModuleFilter.equipment_logs;
        break;
      case defaultConfig.directory_module:
        filter = defaultModuleFilter.directories;
        break;
      case defaultConfig.todo_module:
        filter = defaultModuleFilter.todo_module;
        break;
      case defaultConfig.submittal_module:
        filter = defaultModuleFilter.submittal_module;
        break;
      case defaultConfig.service_ticket_module:
        filter = defaultModuleFilter.service_ticket_module;
        break;
      case defaultConfig.work_order_module:
        filter = defaultModuleFilter.work_order_module;
        break;
      case defaultConfig.vehicle_module:
        filter = defaultModuleFilter.vehicle_module;
        break;
      case defaultConfig.invoice_merge_module_key:
        filter = defaultModuleFilter.invoice_module;
        break;
      case defaultConfig.purchase_order_module:
        filter = defaultModuleFilter.purchase_orders_module;
        break;
      case defaultConfig.payment_module:
        filter = defaultModuleFilter.payment_module;
        break;
      case defaultConfig.punch_list_module:
        filter = defaultModuleFilter.punchlist_module;
        break;
      case defaultConfig.daily_log_module:
        filter = defaultModuleFilter.daily_log_module;
        break;
      case defaultConfig.sub_contractor_module:
        filter = defaultModuleFilter.sub_contrats_module;
        break;
      case defaultConfig.leads_module:
        filter = defaultModuleFilter.leads_module;
        break;
      case defaultConfig.safety_meeting_module:
        filter = defaultModuleFilter.safety_meetings_module;
        break;
      case defaultConfig.rfi_module:
        filter = defaultModuleFilter.rfi_module;
        break;
      case defaultConfig.inspection_module:
        filter = defaultModuleFilter.inspections_module;
        break;
    }
    filter = {
      ...filter,
      ...moduleFilter,
    };

    return filter;
  };

  setState((prev: IModuleFilterInitialState) => ({
    ...prev,
    data: {
      ...(moduleFilter ?? {}),
      filter: getFilter(),
      module_id: moduleId,
    },
  }));
};

export const setGModuleFilter = (
  filter: any,
  user: IInitialGlobalData["user"],
  module_id: number | undefined,
  callComplete: () => void
) => {
  return setState((prev: IModuleFilterInitialState) => {
    let tempFilter = {
      ...(prev?.data?.filter ?? {}),
      ...(filter ?? {}),
    };
    getApiData({
      url: apiRoutes.SERVICE.url,
      data: getApiDefaultParams({
        op: "update_module_filter",
        user,
        otherParams: {
          module_id: module_id,
          filter: JSON.stringify(tempFilter),
        },
      }),
      method: "post",
      success: () => {},
      error: (description) => {
        notification.error({
          description,
        });
      },
      callComplete: callComplete,
    });

    return {
      ...(prev ?? {}),
      data: {
        ...(prev?.data ?? {}),
        filter: tempFilter,
      },
    };
  });
};

export const setIsFilterUpdated = (newVal: boolean) => {
  setState({
    isFilterUpdated: newVal,
  });
};

export const setIsFilterBeingApplied = (newVal: boolean) => {
  setState({
    isFilterBeingApplied: newVal,
  });
};

export const setModuleFilterNewData = async ({
  oldData,
  newData,
  moduleId,
  onError,
}: {
  oldData: IModuleFilterInitialState["data"]["filter"];
  newData: IModuleFilterInitialState["data"]["filter"];
  moduleId: string;
  onError: (response: IApiCallResponse) => void;
}) => {
  const workerParams = await getWebWorkerApiParams({
    otherParams: {
      filter: JSON.stringify(newData),
    },
  });

  const result = (await webWorkerApi({
    url: modulesRoutes.FILTER.update(moduleId),
    method: "post",
    data: workerParams,
  })) as IApiCallResponse;

  if (!result.success) {
    setState((prev: IModuleFilterInitialState) => ({
      ...(prev ?? {}),
      data: {
        ...(prev?.data ?? {}),
        filter: oldData,
      },
    }));

    onError(result);
  } else {
    setState((prev: IModuleFilterInitialState) => ({
      ...(prev ?? {}),
      data: {
        ...(prev?.data ?? {}),
        filter: newData,
      },
    }));
  }
  // setIsFilterUpdated(true); // only mutate this over here, don't set isFilterBeingApplied to false over here, as it will be set to false after the kanban list api called
};

export const updateModuleFilter = async ({
  filter,
  moduleId,
  onError,
}: IUpdateModuleFilterParams) => {
  if (Number(moduleId)) {
    setState((prev: IModuleFilterInitialState) => {
      let tempFilter = {
        ...(prev?.data?.filter ?? {}),
        ...(filter ?? {}),
      };

      setModuleFilterNewData({
        oldData: prev?.data?.filter ?? {},
        newData: tempFilter,
        moduleId,
        onError,
      });

      return {
        ...(prev ?? {}),
        data: {
          ...(prev?.data ?? {}),
          filter: tempFilter,
        },
        // isFilterUpdated: false,
        // isFilterBeingApplied: true,
      };
    });
  }
};

export const removeGModuleFilter = () => {
  setState({
    data: {},
  });
};
