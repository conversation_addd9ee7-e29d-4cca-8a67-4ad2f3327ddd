import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { BillFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/billFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { routes } from "~/route-services/routes";
import { Number, sanitizeString } from "~/helpers/helper";
import { useParams } from "@remix-run/react";
import { sendMessageKeys } from "~/components/page/$url/data";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";

const BillsTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const billModule = getGlobalModuleByKey(CFConfig.bill_module);
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const { formatter } = useCurrencyFormatter();
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const bills = financialData?.bills ?? [];
  const [allBills, setAllBills] = useState(bills);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const displayedBills = isShowingMore
    ? allBills
    : allBills.slice(0, dataLimit);

  const totalCount = Number(
    financialData?.bills_count?.[0]?.number_of_bill ?? 0
  );
  const totalAmount = Number(financialData?.bills_count?.[0]?.total ?? 0);
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad) {
      setAllBills(bills);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(bills?.map((b) => [b?.bill_id, b]));

    const mergedBills = allBills?.map((existingBill) => {
      const updated = updatedMap?.get(existingBill?.bill_id);
      return updated ? updated : existingBill;
    });

    const existingIds = new Set(allBills?.map((b) => b?.bill_id));
    const newBills = bills?.filter((b) => !existingIds?.has(b?.bill_id));

    const nextAllBills = [...mergedBills, ...newBills];

    const hasChanged =
      nextAllBills?.length !== allBills?.length ||
      nextAllBills?.some(
        (bill, i) => JSON.stringify(bill) !== JSON.stringify(allBills[i])
      );

    if (hasChanged) {
      setAllBills(nextAllBills);
    }
  }, [bills, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "bills") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allBills.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["bills"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "company_bill_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const billId = `#${HTMLEntities.decode(sanitizeString(value))}`;
        return value ? (
          <Tooltip title={billId}>
            <Typography className="table-tooltip-text">{billId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "order_date",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: _t("Due"),
      field: "due_date",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: "Vendor",
      field: "supplier_company_name",
      minWidth: 170,
      maxWidth: 170,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const vendor = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={vendor}>
            <Typography className="table-tooltip-text">{vendor}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "Description",
      field: "notes",
      minWidth: 135,
      flex: 1,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) => {
        const description = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={description}>
            <Typography className="table-tooltip-text">
              {description}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formateValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formateValue ? (
          <Tooltip title={formateValue}>
            <Typography className="table-tooltip-text">
              {formateValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Balance"),
      field: "payments",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: { data: BillData }) => {
        const balance = (parseFloat(data.total) - parseFloat(data.payments))
          .toFixed(2)
          ?.toString();

        const formattedBalance = formatter(
          Number(balance) !== 0 ? (Number(balance) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedBalance ? (
          <Tooltip title={formattedBalance}>
            <Typography className="table-tooltip-text">
              {formattedBalance}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "bill_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: number }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_BILL.url + "/" + (value?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <BillFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              billId={value}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(billModule?.plural_name ?? "Bills")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(billModule?.module_name ?? "Bill")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_BILL.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_BILL.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedBills}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-bills.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />
      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["bills", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, ["bills", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default BillsTable;
