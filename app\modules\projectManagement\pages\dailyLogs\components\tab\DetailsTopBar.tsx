import { useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
import { useNavigate, useSearchParams } from "@remix-run/react";

// Hooks And Redux
import { sendMessageKeys } from "~/components/page/$url/data";
import {
  getGConfig,
  useGModules,
  setCommonSidebarCollapse,
  getGSettings, // In future this code move in redux, developer change this code
} from "~/zustand";
import { useIframe, useTranslation } from "~/hook";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  archiveDailyLogApi,
  copyDailyLogApi,
  deleteDailyLogApi,
  downloadDLPdfApi,
  updateDLDetailApi,
} from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  updateDLDetail,
  updateDLFullWeatherDetail,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLDetailsSlice";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { defaultConfig } from "~/data";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { resetDash } from "../../redux/slices/dashboardSlice";

// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";

// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";

// Other
import { fieldStatus } from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";
import {
  filterOptionBySubstring,
  getStatusForField,
  removeFirstSlash,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { routes } from "~/route-services/routes";
import DLSendEmail from "~/modules/projectManagement/pages/dailyLogs/components/DLSendEmail";
import { sanitizeString } from "~/helpers/helper";
import { getGlobalUser } from "~/zustand/global/user/slice";

const DetailsTopBar = ({
  onReloadDetails,
  sidebarCollapse,
}: IDLDetailsTopBarProps) => {
  const { _t } = useTranslation();
  const {
    module_id,
    module_key,
    module_singular_name,
    page_is_iframe,
  }: GConfig = getGConfig();

  const { date_format }: GSettings = getGSettings();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const { checkModuleAccessByKey } = useGModules();
  const navigate = useNavigate();
  const { parentPostMessage } = useIframe();

  const dispatch = useAppDLDispatch();

  const { isDetailLoading, details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const { jobStatus }: IStatusListDataInitialState = useAppDLSelector(
    (state) => state.statusListData
  );

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const loadingStatusRef = useRef(fieldStatus);

  const [selectedData, setSelectedData] = useState<Partial<IDLUpDetails>>({});
  const [viewEmailPdf, setViewEmailPdf] = useState<Partial<IDLUpDetails>>({});

  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [shareLink, setShareLink] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isCopyLoading, setIsCopyLoading] = useState<boolean>(false);
  const [intJobStatus, setIntJobStatus] = useState<string>("");
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);

  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject | null>({
    project_name: "",
    project_id: 0,
    id: 0,
  });
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const [searchParams] = useSearchParams();

  const iFrame = searchParams?.get("iframecall")?.trim();
  const pageOpenForm = searchParams?.get("pageOpenfrom")?.trim();
  const authToken = searchParams?.get("authorize_token")?.trim();

  const DAILYLOG_OPTIONS: IDropdownMenuOption[] = [
    {
      label: "View/Email PDF",
      icon: "fa-regular fa-file-pdf",
      key: "view_email",
    },
    {
      label: `Copy ${module_singular_name}`,
      icon: "fa-regular fa-clone",
      key: "copy",
    },
    {
      content: "Share Internal Link",
      icon: "fa-regular fa-share-nodes",
      key: "share",
      onlyIconView: true,
    },
    {
      content: (
        <>
          Status: Archived <br />
          Click to Activate the item
        </>
      ),
      key: "active",
      // icon: "fa-regular fa-circle-check",
      icon: "fa-regular fa-regular-active",
      onlyIconView: true,
    },
    {
      content: (
        <>
          Status: Active <br />
          Click to Archive the item
        </>
      ),
      icon: "fa-regular fa-box-archive",
      key: "archive",
      onlyIconView: true,
    },
    {
      content: "Delete",
      icon: "fa-regular fa-trash-can",
      key: "delete",
      onlyIconView: true,
    },
  ];

  const dLDetailsOptions: IDropdownMenuOption[] = useMemo(() => {
    // Start with the filtered options
    let actionMenuOptListArr = DAILYLOG_OPTIONS.filter((item) => {
      if (details?.isDeleted == "1") return item.key != "archive";
      return item.key != "active";
    });

    // Modify options to disable delete if conditions are met
    actionMenuOptListArr = actionMenuOptListArr.map((option) => ({
      ...option,
      disabled:
        option.key == "delete" &&
        (allow_delete_module_items == "0" || page_is_iframe),
    }));

    return actionMenuOptListArr;
  }, [details?.isDeleted, allow_delete_module_items, page_is_iframe]);

  const jobStatusList: IDirTypeOption[] = useMemo(
    () =>
      jobStatus.map((item) => ({
        label: item.display_name || item.name,
        value: item.type_id.toString(),
      })),
    [jobStatus]
  );

  useEffect(() => {
    setIntJobStatus(details?.jobStatus?.toString() || "");
  }, [details?.jobStatus]);

  useEffect(() => {
    setSelectedProject({
      project_name: details.projectName || "",
      // project_id: details.projectId,
      id: details?.projectId || 0,
    });
  }, [details.projectId]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleDLDelArch = async () => {
    if (!isDeleting && selectedData?.logId) {
      setIsDeleting(true);
      let deleteRes = { success: false, message: "Something went wrong" };
      if (delArchConfirmOpen === "archive") {
        deleteRes = (await archiveDailyLogApi({
          formData: {
            moduleKey: module_key,
            status: 1,
            moduleId: module_id,
          },
          paramsData: {
            logId: selectedData.logId?.toString() || "",
          },
        })) as IDeleteDailyLogRes;
      } else {
        deleteRes = (await deleteDailyLogApi({
          formData: {
            remove_associated_data: 0,
            module_key: module_key,
          },
          paramsData: {
            logId: selectedData.logId?.toString() || "",
          },
        })) as IDeleteDailyLogRes;
      }

      if (deleteRes?.success) {
        if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
        } else {
          dispatch(resetDash());
          navigate(`${routes.MANAGE_DAILYLOG.url}`);
        }
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setSelectedData({});
      setDelArchConfirmOpen("");
      setIsDeleting(false);
    }
  };
  const handleDLActive = async () => {
    if (!isDeleting && selectedData?.logId) {
      setIsDeleting(true);
      const deleteRes = (await archiveDailyLogApi({
        formData: {
          moduleKey: module_key,
          status: 0,
          moduleId: module_id,
        },
        paramsData: {
          logId: selectedData.logId?.toString() || "",
        },
      })) as IDeleteDailyLogRes;

      if (deleteRes?.success) {
        if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
        } else {
          dispatch(resetDash());
          navigate(`${routes.MANAGE_DAILYLOG.url}`);
        }
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
    }
  };

  const handleDLCopy = async () => {
    if (!isCopyLoading && selectedData?.logId) {
      setIsCopyLoading(true);
      const copyRes = (await copyDailyLogApi({
        paramsData: {
          dailyLogId: selectedData?.logId?.toString() || "",
        },
      })) as ICopyDailyLogRes;

      if (copyRes?.success) {
        if (window && window.ENV && window.ENV.PANEL_URL) {
          const currentUrl = window.location.href;
          const isIframeCall = currentUrl.includes("iframecall=1");

          if (window.ENV.PAGE_IS_IFRAME) {
            navigate(
              `${routes.MANAGE_DAILYLOG.url}/${copyRes?.data?.id}?iframecall=${iFrame}&pageOpenfrom=${pageOpenForm}&authorize_token=${authToken}`
            );
          } else {
            navigate(`${routes.MANAGE_DAILYLOG.url}/${copyRes?.data?.id}`);
          }
        } else {
          notification.error({
            description: "Something went wrong!",
          });
        }
      } else {
        notification.error({
          description: copyRes?.message,
        });
      }
      setIsCopyLoading(false);
      setIsConfirmDialogOpen(false);
    }
  };

  const onCloseDelModal = () => {
    setSelectedData({});
    setDelArchConfirmOpen("");
  };

  const handleUpdateField = async (
    data: IDLDetailFields,
    otherData?: { projectName: string }
  ) => {
    const field = Object.keys(data)[0] as keyof IDLUpDetails;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    let newData = { ...data };
    if (field === "projectId") {
      const newADate = details?.arrivalDate
        ? backendDateFormat(details?.arrivalDate.toString(), date_format)
        : "";
      newData = {
        ...data,
        arrivalDate: newADate,
      };
    }
    const updateRes = (await updateDLDetailApi({
      logId: details?.logId || "",
      ...newData,
    })) as IDLDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (field === "projectId") {
        dispatch(updateDLDetail({ projectName: otherData?.projectName }));
        const weatherTemp =
          details?.temperatureScale == "0"
            ? updateRes?.data?.weather_temp_c || ""
            : updateRes?.data?.weather_temp_f || "";
        const weatherJson =
          details?.temperatureScale == "0"
            ? updateRes?.data?.weather_json_c || []
            : updateRes?.data?.weather_json_f || [];

        dispatch(updateDLFullWeatherDetail({ weatherTemp, weatherJson }));
        dispatch(updateDLDetail({ zipCode: updateRes?.data?.zip_code }));
        dispatch(
          updateDLDetail({
            showClientAccess: updateRes?.data?.show_client_access,
          })
        );
      }
      dispatch(updateDLDetail(data));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      if (field === "jobStatus") {
        setIntJobStatus(details?.jobStatus?.toString() || "");
      }
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const optionsViewPdf: CustomerEmailTab[] = [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    // Remove by shail patel (Lead not show in mobile app)
    // defaultConfig.lead_key,
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IDLSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id: Number(viewEmailPdf.logId),
      module_id: module_id,
      module_key: module_key,
      t_id: pdfTempId,
      log_id: viewEmailPdf.logId,
      action: "send",
      op: "pdf_daily_log",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleDownloadPdf = async (tId: string) => {
    const res = (await downloadDLPdfApi({
      log_id: viewEmailPdf.logId,
      action: "download",
      t_id: tId,
    })) as IDownloadExpenseRes;
    if (res) {
      if (res.success) {
        const fileName = res?.data?.pdf_name;
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download = fileName
          ? fileName?.toString()
          : res.base64_encode_pdfUrl || "";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  const intJobStatusValue =
    Number(intJobStatus) != 0 ? intJobStatus : undefined;

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] sm:px-3.5 px-2.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex flex-col-reverse md:flex-row items-center justify-between sm:gap-2 gap-1.5">
            {isDetailLoading ? (
              <TopBarSkeleton num={3} />
            ) : (
              <>
                <div className="flex items-center md:w-[calc(100%-150px)] w-full">
                  <div className="w-11 h-11 flex items-center justify-center bg-primary-900 rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white">
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon="fa-light fa-calendar-check"
                    />
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 max-w-[calc(100%-44px)] ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <ButtonField
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="projectId"
                      labelPlacement="left"
                      value={HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}
                      headerTooltip={`Project: ${HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}`}
                      editInline={true}
                      iconView={true}
                      required={true}
                      placeholder="Select Project"
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-base h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-base font-medium"
                      readOnly={isReadOnly}
                      isDisabled={details?.canUpdateProject === "0"}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "projectId"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-4 !h-4",
                        },
                      }}
                      rightIcon={
                        !isEmpty(selectedProject?.project_name) && (
                          <ProjectFieldRedirectionIcon
                            projectId={details?.projectId?.toString() || ""}
                          />
                        )
                      }
                      onClick={() => {
                        setIsSelectProOpen(true);
                      }}
                    />
                    <div
                      className={`flex items-center gap-2 mt-[3px] ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <SelectField
                        labelPlacement="left"
                        placeholder={_t("Select Log Status")}
                        formInputClassName={`w-fit overflow-visible ${
                          isReadOnly ? "bg-primary-900/10 rounded" : ""
                        }`}
                        containerClassName="overflow-visible"
                        fieldClassName="before:hidden w-fit"
                        className={`h-[21px] directory-stage header-select-status-dropdown !rounded !text-primary-900 bg-[#ECF1F9] hover:bg-[#ECF1F9] ${
                          !intJobStatusValue ? "header-select-placeholder" : ""
                        }`}
                        popupClassName="min-w-[160px]"
                        readOnly={isReadOnly}
                        title=""
                        options={jobStatusList || []}
                        allowClear
                        filterOption={(input, option) =>
                          filterOptionBySubstring(
                            input,
                            option?.label as string
                          )
                        }
                        onClear={() => {
                          handleChangeFieldStatus({
                            field: "jobStatus",
                            status: "loading",
                            action: "API",
                          });
                          handleUpdateField({
                            jobStatus: "",
                          });
                        }}
                        value={intJobStatusValue}
                        disabled={
                          getStatusForField(loadingStatus, "jobStatus") ===
                          "loading"
                        }
                        onChange={(val) => {
                          if (typeof val === "string") {
                            setIntJobStatus(val || "");
                            handleUpdateField({ jobStatus: val });
                          }
                        }}
                      />
                      <FieldStatus
                        className="flex items-center"
                        iconProps={{
                          className: "!w-[15px] !h-[15px]",
                        }}
                        status={getStatusForField(loadingStatus, "jobStatus")}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex justify-between md:w-fit w-full">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <div>
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>
                  </div>
                  <ul className="flex items-center justify-end gap-2.5 sm:w-fit w-full">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName={`!text-primary-900 ${
                          isDetailLoading
                            ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                            : "group-hover/buttonHover:!text-deep-orange-500"
                        }`}
                        className={`!w-[34px] !h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 ${
                          isDetailLoading
                            ? "hover:bg-transparent"
                            : "hover:!bg-deep-orange-500/5"
                        }`}
                        disabled={isDetailLoading}
                        onClick={() => {
                          onReloadDetails();
                        }}
                      />
                    </li>
                    {!isReadOnly && !isDetailLoading && (
                      <li>
                        <DropdownMenu
                          options={
                            dLDetailsOptions?.length > 0
                              ? dLDetailsOptions.map((option) => ({
                                  ...option,
                                  onClick: () => {
                                    setSelectedData(details);
                                    if (option.key === "delete") {
                                      setDelArchConfirmOpen("delete");
                                    } else if (option.key === "archive") {
                                      setDelArchConfirmOpen("archive");
                                    } else if (option.key === "active") {
                                      setDelArchConfirmOpen("active");
                                    } else if (option.key === "share") {
                                      setIsShareOpen(true);
                                    } else if (option.key === "copy") {
                                      setIsConfirmDialogOpen(true);
                                    } else if (option.key === "view_email") {
                                      setViewEmailPdf(details);
                                    }
                                  },
                                }))
                              : []
                          }
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          icon="fa-regular fa-ellipsis-vertical"
                          tooltipcontent={_t("More")}
                          {...(allow_delete_module_items == "0" && {
                            footerText: _t(
                              "Some actions might be unavailable depending on your privilege."
                            ),
                          })}
                        />
                      </li>
                    )}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {isSelectProOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProOpen}
          module_key={module_key}
          setOpen={setIsSelectProOpen}
          selectedProjects={selectedProject ? [selectedProject] : []}
          onProjectSelected={(data) => {
            if (data.length > 0) {
              setSelectedProject(data.length ? data[0] : null);
              handleUpdateField(
                {
                  projectId: Number(data[0].id),
                },
                { projectName: data[0].project_name || "" }
              );
            } else {
              notification.error({
                description: _t("Project field is required."),
              });
            }
          }}
        />
      )}

      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id: Number(selectedData?.logId),
            module_key: module_key,
            module_page: removeFirstSlash(routes.MANAGE_DAILYLOG.url || ""),
          }}
          onEmailLinkClick={(data) => {
            setIsSendEmailSidebarOpen(true);
            setShareLink(data);
            setIsShareOpen(false);
          }}
          onCloseModal={() => {
            setSelectedData({});
            setIsShareOpen(false);
            setShareLink("");
          }}
        />
      )}

      {delArchConfirmOpen !== "" && (
        <ConfirmModal
          isOpen={delArchConfirmOpen !== ""}
          modaltitle={_t(
            delArchConfirmOpen === "delete"
              ? "Delete"
              : delArchConfirmOpen === "archive"
              ? "Archive"
              : "Active"
          )}
          description={_t(
            delArchConfirmOpen === "delete"
              ? "Are you sure you want to delete this Item?"
              : delArchConfirmOpen === "archive"
              ? "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
              : "Are you sure you want to Activate this data?"
          )}
          withConfirmText={delArchConfirmOpen === "delete"}
          modalIcon={
            delArchConfirmOpen === "delete"
              ? "fa-regular fa-trash-can"
              : delArchConfirmOpen === "archive"
              ? "fa-regular fa-box-archive"
              : "fa-regular fa-regular-active"
          }
          isLoading={isDeleting}
          onAccept={() => {
            if (delArchConfirmOpen === "active") {
              handleDLActive();
            } else {
              handleDLDelArch();
            }
          }}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}

      <DLSendEmail
        isOpen={isSendEmailSidebarOpen}
        appUsers={true}
        canWrite={false}
        emailData={{
          subject: "Shared Link",
          body: `A link to a record within Contractor Foreman has been shared with you. <a href="${shareLink}">View Details.</a>`,
        }}
        onSendResponse={() => {}}
        isViewAttachment={false}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setShareLink("");
        }}
        app_access={false}
        contactId={0}
      />

      {viewEmailPdf.logId && (
        <PDFFilePreview
          projectId={details.projectId}
          isOpen={Boolean(viewEmailPdf.logId)}
          onCloseModal={() => {
            setViewEmailPdf({});
          }}
          moduleId={module_id}
          op="pdf_daily_log"
          idName="log_id"
          isLoading={false}
          id={viewEmailPdf.logId.toString()}
          options={optionsViewPdf}
          emailSubject={viewEmailPdf.emailSubject}
          handleEmailApiCall={handleEmailApiCall}
          handleDownload={handleDownloadPdf}
          isViewAttachment={false}
          moduleName={HTMLEntities.decode(sanitizeString(module_singular_name))}
          setPdfTempId={setPdfTempId}
          isAddUserId={true}
        />
      )}

      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modaltitle={_t("Copy")}
          description={_t(
            `Are you sure you want to generate a copy of this ${module_singular_name}?`
          )}
          modalIcon="fa-regular fa-clone"
          isLoading={isCopyLoading}
          onAccept={handleDLCopy}
          onDecline={() => {
            setIsConfirmDialogOpen(false);
          }}
          onCloseModal={() => {
            setIsConfirmDialogOpen(false);
          }}
        />
      )}
    </>
  );
};

export default DetailsTopBar;
