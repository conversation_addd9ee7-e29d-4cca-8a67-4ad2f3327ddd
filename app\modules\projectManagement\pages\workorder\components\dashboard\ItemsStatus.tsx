import { type RadioChangeEvent } from "antd";
import { useRef, useState, useMemo } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { ApexChart } from "~/shared/components/atoms/chart";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import BarChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarChart.skeleton";
// Other
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
import { useApexCharts } from "~/shared/hooks/useApexCharts";
import { WORK_ORDER_TAB } from "../../utils/constasnts";
import { useWoAppDispatch, useWoAppSelector } from "../../redux/store";
import { fetchWorkOrderDashboardApi } from "../../redux/action/workorderDashAction";

const ItemsStatus = () => {
  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const dispatch = useWoAppDispatch();
  const { iteam_by_status, isInitialLoad, resItemByStatusLastRefreshTime } =
    useWoAppSelector((state) => state.workorderDashboard);
  const gridApiRef = useRef<GridApi | null>(null);
  const [value, setValue] = useState<string>("chart");
  const { _t } = useTranslation();

  const handleClickRefresh = async () => {
    setisCashLoading(true);
    await dispatch(
      fetchWorkOrderDashboardApi({
        refreshType: "item_by_status",
      })
    );
    setisCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Status"),
      field: "display_name",
      minWidth: 150,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: ({ data }: IOpenItemByStatusTableCellRenderer) => {
        const priority = data.display_name;
        return priority ? (
          <Tooltip title={priority}>
            <Typography className="table-tooltip-text text-center">
              {priority}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "# " + _t("of Work Orders"),
      field: "no_of_count",
      minWidth: 130,
      maxWidth: 130,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMenu: true,
    },
  ];

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  const SeriesDataarea = [
    {
      name: _t("Work Orders"),
      data: iteam_by_status?.data?.map(
        (item: IIteamByStatusData) => item.no_of_count
      ),
    },
  ];

  const optionsLine = useApexCharts({ type: "bar" });
  const options = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      stroke: {
        show: false,
        curve: "smooth",
        colors: undefined,
        width: 0,
        dashArray: 0,
      },
      markers: {
        size: [4, 7],
      },

      title: {
        text: undefined,
      },
      subtitle: {
        text: undefined,
      },
      plotOptions: {
        bar: {
          columnWidth: "40",
          distributed: true,
        },
      },
      grid: {
        strokeDashArray: 5,
      },
      yaxis: {
        show: true,
      },
      xaxis: {
        categories: iteam_by_status?.data?.map(
          (item: IIteamByStatusData) => item.display_name
        ),
        labels: {
          show: true,
          trim: true,
          rotate: 0,
          rotateAlways: false,
          hideOverlappingLabels: false,
          style: {
            fontSize: "12px",
            fontWeight: 600,
          },
        },
      },
      colors: iteam_by_status?.data?.map(
        (item: IIteamByStatusData) => item.status_color
      ),
      legend: {
        show: false,
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
    };
  }, [iteam_by_status]);

  return (
    <>
      <DashboardCardHeader
        title={_t("Work Order by Status")}
        rightContent={
          <div className="flex items-center ">
            <ListTabButton
              value={value}
              options={WORK_ORDER_TAB}
              onChange={(e: RadioChangeEvent) => {
                setValue(e.target.value);
              }}
              className="first:border-r-0"
              activeclassName="!bg-[#EAE8E8] "
            />
          </div>
        }
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={resItemByStatusLastRefreshTime}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        {value === "chart" ? (
          <>
            {isInitialLoad || isCashLoading ? (
              <BarChartSkeleton sizeClassName="h-[200px] py-3.5" count={6} />
            ) : (
              iteam_by_status && (
                <ApexChart
                  key={JSON.stringify(iteam_by_status)}
                  series={SeriesDataarea}
                  options={options}
                  type={"bar"}
                  height={200}
                />
              )
            )}
          </>
        ) : value === "table" ? (
          <div className="ag-theme-alpine h-[209px]">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              rowData={
                !(isInitialLoad || isCashLoading)
                  ? iteam_by_status?.data
                  : undefined
              }
              noRowsOverlayComponent={() =>
                isInitialLoad || isCashLoading ? (
                  <StaticTableRowLoading columnDefs={columnDefs} />
                ) : (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-st-status.svg`}
                  />
                )
              }
            />
          </div>
        ) : (
          <></>
        )}
      </div>
    </>
  );
};

export default ItemsStatus;
