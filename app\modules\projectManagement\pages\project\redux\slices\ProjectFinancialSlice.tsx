import { createSlice } from "@reduxjs/toolkit";
import { fetchProjectFinancialModules } from "../action/ProjectFinancialAction";

const initialState: IProFinancialInitialState = {
  financialData: {
    bills: [],
    bills_count: [],
    change_orders: [],
    change_orders_count: [],
    estimates: [],
    estimates_count: [],
    expenses: [],
    expenses_count: [],
    invoices: [],
    invoices_count: [],
    payments: [],
    payments_count: [],
    purchase_orders: [],
    purchase_orders_count: [],
    sub_contracts: [],
    sub_contracts_count: [],
    work_orders: [],
    work_orders_count: [],
  },
  projectTypes: [],
  isInitialLoad: false,
  isloading: false,
  isfirsttime: true,
};

export const proDashFinancialSlice = createSlice({
  name: "proFinancial",
  initialState,
  reducers: {
    setIsInitialLoad: (state, action) => {
      state.isInitialLoad = action.payload;
    },
    addCustomProjectType: (state, { payload }) => {
      const data = payload;
      data.item_type = data.item_type.toString();
      data.item_id = data.item_id.toString();
      state.projectTypes = [data, ...state.projectTypes];
    },
    setFinancialData: (state, { payload }) => {
      state.financialData = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchProjectFinancialModules.pending, (state, action) => {
      // state.financialData = [];
      state.isInitialLoad = true;
      if (state.isfirsttime) {
        state.isloading = true;
      }
    });
    builder.addCase(
      fetchProjectFinancialModules.fulfilled,
      (state, { payload, meta }) => {
        const { success, data } = payload as IProjectFinancialApiRes;
        state.financialData = {
          ...state.financialData,
          ...data,
        };
        state.isInitialLoad = false;
        state.isloading = false;
        state.isfirsttime = false;
      }
    );
    builder.addCase(fetchProjectFinancialModules.rejected, (state) => {
      state.financialData = initialState.financialData;
      state.isInitialLoad = true;
      state.isloading = false;
      state.isfirsttime = false;
    });
  },
});

// function getValidEntries(obj: Record<string, any>): Record<string, any> {
//   return Object.fromEntries(
//     Object?.entries(obj).filter(
//       ([_, value]) => value?.length > 0 && value !== null && value !== undefined
//     )
//   );
// }

export const { setIsInitialLoad, addCustomProjectType, setFinancialData } =
  proDashFinancialSlice.actions;
export default proDashFinancialSlice.reducer;
