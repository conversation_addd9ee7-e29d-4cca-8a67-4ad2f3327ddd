import { useParams } from "@remix-run/react";
import { useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CalcList } from "~/shared/components/molecules/calcList";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import SelectTaxRate from "~/shared/components/molecules/selectTaxRate/SelectTaxRate";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { sanitizeString } from "~/helpers/helper";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useAppDispatch, useAppSelector } from "../../../redux/store";
import {
  IChangeOrderDetailsApiRes,
  IChangeOrderDetailState,
  IChangeOrderItem,
  IChangeOrderItemsState,
  ICoCalculation,
  ISection,
} from "../../../redux/types";
import { updateCOTaxes } from "../../../redux/slices/changeOrderItemsSlice";
import { useCOContext } from "~/context/COContext";
import { canModifyItem } from "../../../utils/helpers";
import { updateChangeOrderDetails } from "../../../redux/slices/changeOrderDetailsSlice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";

interface TaxRecord {
  label: string;
  value: string;
  taxRate: number;
}
const ChangeOrderCalculation = ({
  allTotals = {
    totalEstimatedCost: 0,
    totalSubtotal: 0,
    totalTaxableSubtotal: 0,
    totalMarkup: 0,
    totalTax: 0,
    totalGrandTotal: 0,
    totalHours: 0,
  },
  onCOUpdate,
}: ICoCalculation) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { isReadOnly } = useCOContext();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { tax_format } = appSettings || {};

  const tax_formatName = TaxFormattedName?.[tax_format] ?? tax_format;
  const dispatch = useAppDispatch();

  const { details }: IChangeOrderDetailState = useAppSelector(
    (state) => state.changeOrderDetails
  );
  const { taxes }: IChangeOrderItemsState = useAppSelector(
    (state) => state.changeOrderItems
  );

  const [selectedTaxRates, setSelectedTaxRates] = useState<TaxRecord[]>(
    details?.taxes?.map((tax) => {
      return {
        label: `${tax.tax_name} (${Math.round(Number(tax.tax_rate) || 0)}%)`,
        value: tax.tax_id.toString(),
        taxRate: Number(tax.tax_rate) || 0,
      };
    }) || []
  );
  const selectedTaxId = useMemo(() => {
    return selectedTaxRates.filter((item) => item).map((tax) => tax.value)[0];
  }, [selectedTaxRates]);
  const { sections } = useAppSelector((state) => state.changeOrderItems);

  const taxDetails = useMemo(() => {
    if (!details?.taxes) return [];

    return details?.taxes?.map((tax) => {
      const isReversible =
        typeof tax.is_reversible === "boolean"
          ? tax.is_reversible
          : tax.is_reversible?.toString() === "1";
      return {
        taxIsReversible: isReversible,
        taxRate: parseFloat(tax?.tax_rate ?? "0") || 0,
        taxname: tax?.tax_name,
        tax_id: tax.tax_id,
      };
    });
  }, [details?.taxes?.[0]?.tax_id]);
  const {
    taxIsReversible,
    taxRate = 0,
    taxname,
    tax_id,
  } = taxDetails?.[0] || {};

  const checkIfSectionHasItems = useMemo(() => {
    return sections?.some((section: ISection) => {
      return section?.items?.length > 0;
    });
  }, [sections]);
  const getDetails = async () => {
    const response = (await webWorkerApi<IChangeOrderDetailsApiRes>({
      url: apiRoutes.CHANGE_ORDERS.get_details,
      method: "post",
      data: { change_order_id: details?.change_order_id },
    })) as IChangeOrderDetailsApiRes;
    if (response?.success) {
      dispatch(updateChangeOrderDetails(response.data));
    } else {
      dispatch(updateChangeOrderDetails({}));
    }
  };
  const handleTaxUpdate = (updatedTaxes: TaxRecord[]) => {
    const taxes = updatedTaxes.filter((item) => item);
    setSelectedTaxRates(taxes);
    if (taxes.length > 0) {
      const taxId = taxes.map((tax) => tax.value)[0];
      onCOUpdate("tax_id", taxId).then(() => {
        dispatch(updateCOTaxes(JSON.parse(JSON.stringify(taxes))));
        getDetails();
      });
    } else {
      onCOUpdate("tax_id", null).then(() => {
        dispatch(updateCOTaxes([]));
      });
    }
  };
  const disableModification = useMemo(
    () => (details ? !canModifyItem(details) : true) || isReadOnly,
    [details, isReadOnly]
  );

  const {
    totalEstimatedCost,
    totalSubtotal,
    totalTaxableSubtotal,
    totalMarkup,
    totalGrandTotal,
    totalHours,
  } = allTotals;

  if (
    sections &&
    Array.isArray(sections) &&
    sections?.length &&
    checkIfSectionHasItems
  )
    return (
      <div className="md:grid md:grid-cols-2 flex flex-col-reverse gap-2.5">
        <div
          className={`flex flex-col gap-[15px] py-[15px] px-5 ml-auto common-card w-full ${
            checkIfSectionHasItems ? "" : "order-2"
          }`}
        >
          <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
            <CalcList
              label={_t("Estimated Cost")}
              value={
                formatter(totalEstimatedCost?.toFixed(2))?.value_with_symbol
              }
            />
            <li>
              <ul className="md:py-0 py-0.5 relative">
                <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-plus"
                  />
                </li>
              </ul>
            </li>
            <CalcList
              label={_t("Profit Margin")}
              valueClassName="text-[#008000]"
              value={`${
                formatter(totalMarkup?.toFixed(2))?.value_with_symbol
              } (${Math.round(
                calculateProfitMarginPercentage(totalSubtotal, totalMarkup)
              )}%)`}
            />
          </ul>

          <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
            <CalcList
              label={_t("Sub Total")}
              labelClassName="font-semibold"
              value={formatter(totalSubtotal?.toFixed(2))?.value_with_symbol}
            />
            <li>
              <ul className="md:py-0 py-0.5 relative">
                <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                  <FontAwesomeIcon
                    className="w-3 h-3 text-primary-900 dark:text-white"
                    icon="fa-regular fa-plus"
                  />
                </li>
              </ul>
            </li>
            <CalcList
              label={_t(`${tax_formatName}`)}
              valueClassName="text-[#008000]"
              value={
                formatter(
                  (
                    (!isNaN(totalTaxableSubtotal) && !taxIsReversible
                      ? totalTaxableSubtotal * taxRate
                      : 0) / 100
                  )?.toFixed(2)
                )?.value_with_symbol
              }
            />
          </ul>

          <ul className="border-t border-dashed border-[#ddd] relative">
            <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
              <FontAwesomeIcon
                className="w-3 h-3 text-primary-900 dark:text-white"
                icon="fa-regular fa-equals"
              />
            </li>
          </ul>
          <ul className="sm:py-[5px] py-2 px-[15px] rounded relative bg-primary-100 dark:bg-dark-900 w-full">
            <CalcList
              label={_t("Grand Total")}
              labelClassName="font-semibold"
              value={formatter(totalGrandTotal?.toFixed(2))?.value_with_symbol}
            />
          </ul>

          <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
            <CalcList
              label={_t("Hours")}
              value={`${formatter(totalHours?.toString())?.value}`}
            />
            {/* {flags?.isMarkupHidden ? (
              ""
            ) : ( */}
            <CalcList
              label={_t("Markup")}
              value={`${Math.round(
                calculateProfitMarginPercentage(totalEstimatedCost, totalMarkup)
              )}%`}
            />
            {/* )} */}
          </ul>
        </div>
        <div className="flex flex-col gap-[15px] py-[15px] px-5 ml-auto common-card w-full">
          <div className="">
            <FieldLabel labelClass="pb-2.5 !w-full block !text-[13px]">
              {_t(`${tax_formatName}/Amount`)}
            </FieldLabel>
            <ul className="grid gap-2.5">
              <li className="w-full">
                <div className="w-full">
                  <SelectTaxRate
                    value={selectedTaxId}
                    placeholder={`${_t(`Select ${tax_formatName}`)}`}
                    readOnly={isReadOnly}
                    disabled={disableModification}
                    editInline={true}
                    labelPlacement="left"
                    formInputClassName="bg-[#F8F8F8] tax-select-filed"
                    className="!text-[13px]"
                    allowClear
                    onChange={async (_value, options) => {
                      const selectedTaxes = Array.isArray(options)
                        ? options
                        : [options];
                      handleTaxUpdate(selectedTaxes);
                    }}
                  />
                </div>
              </li>
            </ul>
          </div>
          <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
            <CalcList
              label={_t("Sub Total")}
              valueClassName="text-[#008000]"
              value={formatter(totalSubtotal?.toFixed(2))?.value_with_symbol}
            />

            <>
              <li>
                <ul className="md:py-0 py-0.5 relative">
                  <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
              </li>
              <CalcList
                label={_t(
                  `${tax_formatName}${
                    tax_id?.toString() == "0" || !tax_id
                      ? ""
                      : ": " +
                        `${HTMLEntities.decode(
                          sanitizeString(taxname ?? "")
                        )} (${taxRate}%)`
                  }`
                )}
                valueClassName="text-[#008000]"
                value={
                  formatter(
                    (!isNaN(totalTaxableSubtotal)
                      ? (totalTaxableSubtotal * taxRate) / 100
                      : 0
                    )?.toFixed(2)
                  )?.value_with_symbol
                }
              />
            </>
          </ul>
          {taxIsReversible && tax_id && (
            <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
              <CalcList
                label={_t(
                  `Reversal ${tax_formatName}: ${
                    tax_id?.toString() == "0" || !tax_id
                      ? ""
                      : `${HTMLEntities.decode(
                          sanitizeString(taxname ?? "")
                        )} (${taxRate}%)`
                  }`
                )}
                valueClassName="text-[#E25A32]"
                value={
                  formatter(
                    (!isNaN(totalTaxableSubtotal) && taxIsReversible
                      ? (totalTaxableSubtotal * taxRate) / 100
                      : 0
                    )?.toFixed(2)
                  )?.value_with_symbol
                }
              />
              <div className="h-5 w-5 rounded-full bg-white absolute shadow-[0px_5px_10px_rgba(0,_0,_0,_0.15)] leading-5 text-center bottom-auto -top-[14px] left-0 right-0 mx-auto">
                <FontAwesomeIcon
                  className="w-3 h-3 text-primary-900"
                  icon="fa-regular fa-minus"
                />
              </div>
            </ul>
          )}
          <ul className="sm:py-[5px] py-2 px-[15px] rounded relative bg-primary-100 dark:bg-dark-900 w-full">
            <CalcList
              label={_t("Total")}
              labelClassName="font-semibold"
              value={formatter(totalGrandTotal?.toFixed(2))?.value_with_symbol}
            />
          </ul>
        </div>
      </div>
    );

  return "";
};

export default ChangeOrderCalculation;

export const itemTotalCalculator = (
  item: Partial<IChangeOrderItem>,
  includeMarkup?: boolean
): number => {
  const { unit_cost, markup, quantity, is_markup_percentage } = item;

  const baseTotal = parseFloat(
    (Number(unit_cost) * Number(quantity)).toFixed(2)
  );

  if (!includeMarkup) {
    return baseTotal;
  }

  let TotalAmount;

  if (is_markup_percentage) {
    // If markup is a percentage, calculate the total based on the baseTotal and markup percentage
    TotalAmount = parseFloat(
      (baseTotal + Number(baseTotal) * (Number(markup) / 100)).toFixed(2)
    );
  } else {
    // If markup is not a percentage, handle based on other conditions
    const productCost = Number(unit_cost) * Number(quantity);

    if (productCost) {
      TotalAmount =
        Number(item?.markup) === 0
          ? baseTotal // If markup is zero, return baseTotal
          : Number(markup); // Otherwise, return the markup as total
    } else {
      TotalAmount = 0; // If no valid product cost, return 0
    }
  }

  return parseFloat(Number(TotalAmount).toFixed(2));
};

export const calculateProfitMarginPercentage = (
  estimatedCost: number,
  profit: number
): number => {
  if (isNaN(estimatedCost) || isNaN(profit) || estimatedCost <= 0) {
    //
    return 0;
  }

  const profitMargin = (profit / estimatedCost) * 100;
  return parseFloat(profitMargin.toFixed(0));
};

export const labourHoursUnits: Record<string, boolean> = {
  Hours: true,
  HOURS: true,
  hours: true,
  Hrs: true,
  HRS: true,
  hrs: true,
  "P/H": true,
  "P/h": true,
  "Per Hour": true,
  "per hour": true,
  "PER HOUR": true,
  Hr: true,
};

export const TaxFormattedName: Record<string, string> = {
  tax: "Tax",
  gst: "GST",
};
