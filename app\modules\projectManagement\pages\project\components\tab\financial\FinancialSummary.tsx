import { useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";

// Atoms
import { Image } from "~/shared/components/atoms/image";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { ApexChart } from "~/shared/components/atoms/chart";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { type RadioChangeEvent } from "antd";
import { COST_COMPARISON_TAB } from "../../../utils/constants";
import { useApexCharts } from "~/shared/hooks/useApexCharts";

import React from "react";
import { useAppProSelector } from "../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { roundUpTwoDecimal } from "../../utils/common-util";
React.useLayoutEffect = React.useEffect;

const FinancialSummary = () => {
  const { _t } = useTranslation();
  const { formatter: currencyFormatter, formatter } = useCurrencyFormatter();
  const [value, setValue] = useState<string>("chart");
  const { getGlobalModuleByKey } = useGlobalModule();
  const ChangeOrderModule = getGlobalModuleByKey(CFConfig.change_order_module);
  const WorkOrderModule = getGlobalModuleByKey(CFConfig.work_order_module);
  const ServiceTicketModule = getGlobalModuleByKey(
    CFConfig.service_ticket_module
  );
  const { details, isSummaryDetailsLoading } = useAppProSelector((state) => {
    return state.proDetails;
  });
  // BEGIN: DONUT FIRST CHART
  const seriesData = [
    details?.finance_summary?.sov?.est_revenue || 0,
    details?.finance_summary?.sov?.est_cost || 0,
    details?.finance_summary?.sov?.est_profit || 0,
  ];
  const optionsLine = useApexCharts({ type: "donut" });
  const options = useMemo(() => {
    return {
      ...optionsLine,
      labels: [
        "Original Contract Amount",
        "Estimated Cost",
        "Estimated Profit",
      ],
      legend: {
        position: "bottom",
        horizontalAlign: "left",
        offsetX: 0,
        offsetY: 0,
        fontSize: "12px",
        onItemClick: {
          toggleDataSeries: false,
        },
        itemMargin: {
          horizontal: 0,
          vertical: 0,
        },
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            size: "65%",
          },
        },
      },
      colors: ["#f26c39", "#eb9f82", "#b35f3e"],
      dataLabels: {
        enabled: false,
      },
      tooltip: {
        enabled: true,
        y: {
          formatter: function (val: number) {
            const percentage = (val / seriesData.reduce((a, b) => a + b)) * 100;
            return `${percentage.toFixed(0)}% (${
              currencyFormatter((val / 100)?.toFixed(2)).value_with_symbol
            })`;
          },
        },
      },
      title: {
        text: undefined,
      },
      states: {
        normal: {
          filter: {
            type: "none",
          },
        },
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
            value: 0.35,
          },
        },
      },
    };
  }, [details?.finance_summary?.sov]);
  // END: FIRST DONUT CHART

  // BEGIN: SECOND DONUT CHART
  const projectSeriesData = [
    details?.finance_summary?.total?.est_revenue || 0,
    details?.finance_summary?.total?.est_cost || 0,
    details?.finance_summary?.total?.est_profit || 0,
  ];
  const projectOptionsLine = useApexCharts({ type: "donut" });
  const projectOptions = useMemo(() => {
    return {
      ...projectOptionsLine,
      labels: ["Total Project Amount", "Estimated Cost", "Estimated Profit"],
      legend: {
        position: "bottom",
        horizontalAlign: "left",
        offsetX: 0,
        offsetY: 0,
        fontSize: "12px",
        onItemClick: {
          toggleDataSeries: false,
        },
        itemMargin: {
          horizontal: 0,
          vertical: 0,
        },
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            size: "65%",
          },
        },
      },
      colors: ["#528bfc", "#99bbff", "#3061de"],
      dataLabels: {
        enabled: false,
      },
      tooltip: {
        enabled: true,
        y: {
          formatter: function (val: number) {
            const total = projectSeriesData.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? (val / total) * 100 : 0;
            return `${percentage.toFixed(0)}% (${
              currencyFormatter((val / 100)?.toFixed(2)).value_with_symbol
            })`;
          },
        },
      },
      title: {
        text: undefined,
      },
      states: {
        normal: {
          filter: {
            type: "none",
          },
        },
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
            value: 0.35,
          },
        },
      },
    };
  }, [details?.finance_summary?.total]);
  // END: SECOND DONUT CHART

  // BEGIN: THIRD DONUT CHART
  const amountSeriesData = [
    details?.finance_summary?.invoices?.amount_invoiced || 0,
    details?.finance_summary?.invoices?.amount_paid || 0,
    details?.finance_summary?.invoices?.amount_unpaid || 0,
  ];
  const amountOptionsLine = useApexCharts({ type: "donut" });
  const amountOptions = useMemo(() => {
    return {
      ...amountOptionsLine,
      labels: ["Amount Invoiced", "Amount Paid", "Amount Unpaid"],
      legend: {
        position: "bottom",
        horizontalAlign: "left",
        offsetX: 0,
        offsetY: 0,
        fontSize: "12px",
        onItemClick: {
          toggleDataSeries: false,
        },
        itemMargin: {
          horizontal: 0,
          vertical: 0,
        },
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            size: "65%",
          },
        },
      },
      colors: ["#ac4cf5", "#c899f7", "#7717cd"],
      dataLabels: {
        enabled: false,
      },
      tooltip: {
        enabled: true,
        y: {
          formatter: function (val: number) {
            const total = amountSeriesData.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? (val / total) * 100 : 0;
            return `${percentage.toFixed(0)}% (${
              currencyFormatter((val / 100)?.toFixed(2)).value_with_symbol
            })`;
          },
        },
      },
      title: {
        text: undefined,
      },
      states: {
        normal: {
          filter: {
            type: "none",
          },
        },
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
            value: 0.35,
          },
        },
      },
    };
  }, []);
  // END: THIRD DONUT CHART

  const CostComparisionHeader = (props: any) => {
    return (
      <div className="text-right w-full">
        <span className="text-[#181d1f] font-semibold">
          {props.displayName}
          <br />
          {props.displayBottomName}
        </span>
        <Tooltip title={props.tooltipText} placement="top">
          <div className="inline-block relative z-10">
            <FontAwesomeIcon
              className="w-[13px] h-[13px] text-gray-500 ml-1"
              icon="fa-regular fa-circle-info"
            />
          </div>
        </Tooltip>
      </div>
    );
  };

  const columnDefs: IStaticListTableProps["columnDefs"] = [
    {
      headerName: "",
      field: "name",
      sortable: false,
      minWidth: 194,
      maxWidth: 194,
      pinned: "left",
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: { data: IProjectFinanceSummaryCell }) => {
        const name = data?.name;
        return name ? (
          <Tooltip title={name}>
            <Typography className="table-tooltip-text !text-xs">
              {name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Estimated Revenue"),
      field: "estimated_revenue",
      sortable: false,
      minWidth: 100,
      flex: 1,
      suppressMenu: true,
      headerComponent: CostComparisionHeader,
      headerComponentParams: {
        displayName: _t("Estimated"),
        displayBottomName: _t("Revenue"),
        tooltipText: _t("Items total with markup."),
      },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: { data: IProjectFinanceSummaryCell }) => {
        let val = Number(data?.est_revenue || 0) / 100;
        if (!isNaN(data?.sov_tax)) {
          val = Number(data?.sov_tax || 0) / 100;
        } else if (!isNaN(data?.project_amount_w_tax)) {
          val = Number(data?.project_amount_w_tax || 0) / 100;
        } else if (!isNaN(data?.amount_invoiced)) {
          val = Number(data?.amount_invoiced || 0) / 100;
        } else if (!isNaN(data?.amount_paid)) {
          val = Number(data?.amount_paid || 0) / 100;
        } else if (!isNaN(data?.amount_unpaid)) {
          val = Number(data?.amount_unpaid || 0) / 100;
        }

        const formateValue = formatter(
          Number(val)?.toFixed(2)
        ).value_with_symbol;

        return (
          <Tooltip title={formateValue}>
            <Typography className="table-tooltip-text !text-xs text-end block ml-auto">
              {currencyFormatter(Math.round(val)?.toString()).value}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Estimated Cost"),
      field: "estimated_cost",
      sortable: false,
      minWidth: 115,
      flex: 1,
      suppressMenu: true,
      headerComponent: CostComparisionHeader,
      headerComponentParams: {
        displayName: _t("Estimated"),
        displayBottomName: _t("Cost"),
        tooltipText: _t("Items total without markup."),
      },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: { data: IProjectFinanceSummaryCell }) => {
        const val = Math.round(Number(data?.est_cost || 0) / 100)?.toString();
        let isAmountInvoiced = !isNaN(data?.amount_invoiced);
        const isAmountUnpaid = !isNaN(data?.amount_unpaid);
        const isAmountPaid = !isNaN(data?.amount_paid);
        const isTax = !isNaN(data?.sov_tax);
        const isProjectAmountWtax = !isNaN(data?.project_amount_w_tax);

        const formateValue = formatter(
          Number(data?.est_cost)?.toFixed(2)
        ).value_with_symbol;

        const Content = (
          <Typography className="table-tooltip-text !text-xs text-end block ml-auto">
            {isAmountInvoiced && "Retention Held"}
            {isAmountUnpaid && "Due in 30 days"}
            {!isAmountInvoiced &&
              !isAmountUnpaid &&
              currencyFormatter(val).value}
          </Typography>
        );

        return isTax || isProjectAmountWtax || isAmountPaid ? (
          <></>
        ) : !isAmountInvoiced && !isAmountUnpaid ? (
          <Tooltip
            title={
              currencyFormatter((+data?.est_cost / 100)?.toFixed(2))
                .value_with_symbol
            }
          >
            {Content}
          </Tooltip>
        ) : (
          Content
        );
      },
    },
    {
      headerName: _t("Estimated Profit"),
      field: "estimated_profit",
      sortable: false,
      minWidth: 120,
      flex: 1,
      suppressMenu: true,
      headerComponent: CostComparisionHeader,
      headerComponentParams: {
        displayName: _t("Estimated"),
        displayBottomName: _t("Profit"),
        tooltipText: _t(
          "Total markup of items (Estimated Revenue - Estimated Cost)."
        ),
      },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: { data: IProjectFinanceSummaryCell }) => {
        let val = Number(data?.est_profit || 0) / 100;
        if (!isNaN(data?.retention_held)) {
          val = Number(data?.retention_held || 0) / 100;
        } else if (!isNaN(data?.due_30_days)) {
          val = Number(data?.due_30_days || 0) / 100;
        }

        const isAmountPaid = !isNaN(data?.amount_paid);
        const isTax = !isNaN(data?.sov_tax);
        const isProjectAmountWtax = !isNaN(data?.project_amount_w_tax);

        const formateValue = formatter(
          Number(val)?.toFixed(2)
        ).value_with_symbol;

        return isTax || isProjectAmountWtax || isAmountPaid ? (
          <></>
        ) : (
          <Tooltip title={formateValue}>
            <Typography className="table-tooltip-text !text-xs text-end block ml-auto">
              {currencyFormatter(Math.round(val)?.toString()).value}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Estimated Markup"),
      field: "estimated_markup",
      sortable: false,
      minWidth: 120,
      flex: 1,
      suppressMenu: true,
      headerComponent: CostComparisionHeader,
      headerComponentParams: {
        displayName: _t("Estimated"),
        displayBottomName: _t("Markup"),
        tooltipText: _t("(Estimated Profit * 100) / Estimated Cost."),
      },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: { data: IProjectFinanceSummaryCell }) => {
        const val = Math.round(Number(data?.est_markup || 0))?.toString();
        const isRemainingToInvoice = !isNaN(data?.remaining_to_invoice);
        const isPastDue = !isNaN(data?.past_due);
        const isAmountPaid = !isNaN(data?.amount_paid);
        const isTax = !isNaN(data?.sov_tax);
        const isProjectAmountWtax = !isNaN(data?.project_amount_w_tax);
        const isAmountInvoiced = !isNaN(data?.amount_invoiced);
        const isAmountUnpaid = !isNaN(data?.amount_unpaid);

        const Content = (
          <Typography className="table-tooltip-text !text-xs text-end block ml-auto">
            {isRemainingToInvoice && "Remaining to Inv."}
            {isPastDue && "Past Due"}
            {!isRemainingToInvoice &&
              !isPastDue &&
              currencyFormatter(val).value}
            {!isRemainingToInvoice && !isPastDue ? "%" : ""}
          </Typography>
        );

        const formateValue = formatter(
          Number(data?.est_markup)?.toFixed(2)
        ).value_with_symbol;

        return isTax || isProjectAmountWtax || isAmountPaid ? (
          <></>
        ) : !isAmountInvoiced && !isAmountUnpaid ? (
          <Tooltip title={`${formateValue}%`}>{Content}</Tooltip>
        ) : (
          Content
        );
      },
    },
    {
      headerName: _t("Estimated Margin"),
      field: "estimated_margin",
      sortable: false,
      minWidth: 120,
      flex: 1,
      suppressMenu: true,
      headerComponent: CostComparisionHeader,
      headerComponentParams: {
        displayName: _t("Estimated"),
        displayBottomName: _t("Margin"),
        tooltipText: _t("(Estimated Profit * 100) / Estimated Revenue"),
      },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: { data: IProjectFinanceSummaryCell }) => {
        let val = Number(data?.est_margin || 0);
        const isRemainingToInvoice = !isNaN(data?.remaining_to_invoice);
        const isPastDue = !isNaN(data?.past_due);
        if (isRemainingToInvoice) {
          val = Number(data?.remaining_to_invoice || 0) / 100;
        } else if (isPastDue) {
          val = Number(data?.past_due || 0) / 100;
        }

        const isAmountPaid = !isNaN(data?.amount_paid);
        const isTax = !isNaN(data?.sov_tax);
        const isProjectAmountWtax = !isNaN(data?.project_amount_w_tax);

        const formateValue = formatter(
          Number(val)?.toFixed(2)
        ).value_with_symbol;

        return isTax || isProjectAmountWtax || isAmountPaid ? (
          <></>
        ) : (
          <Tooltip
            title={`${
              !isRemainingToInvoice && !isPastDue
                ? `${formateValue}%`
                : currencyFormatter(val?.toFixed(2)).value_with_symbol
            }`}
          >
            <Typography className="table-tooltip-text !text-xs text-end block ml-auto">
              {currencyFormatter(Math.round(val)?.toString()).value}
              {!isRemainingToInvoice && !isPastDue ? "%" : ""}
            </Typography>
          </Tooltip>
        );
      },
    },
  ];
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Financial Summary")}
        iconProps={{
          icon: "fa-solid fa-box-dollar",
          id: "financial_summary_icon",
          containerClassName:
            "bg-[linear-gradient(180deg,#FFB2981a_0%,#FA6D3D1a_100%)]",
          colors: ["#FFB298", "#FA6D3D"],
        }}
        headerRightButton={
          <div className="flex items-center">
            <ListTabButton
              value={value}
              options={COST_COMPARISON_TAB}
              onChange={(e: RadioChangeEvent) => {
                setValue(e.target.value);
              }}
              className="first:border-r-0"
              activeclassName="!bg-[#EAE8E8]"
            />
          </div>
        }
        children={
          <>
            <div className="pt-2 financial-summary-widget">
              {value === "chart" ? (
                <ul className="grid xl:grid-cols-3 lg:grid-cols-2 md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-3">
                  <li className="flex items-center justify-center">
                    {seriesData?.some((el) => Number(el) > 0) ? (
                      <ApexChart
                        className="origin-amt-chart"
                        series={seriesData}
                        options={options}
                        type={"donut"}
                        height={180}
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center gap-3 h-[180px]">
                        <Image
                          src={`${window.ENV.CDN_URL}assets/images/no-data-pie-chart.svg`}
                          preview={false}
                          className="min-w-[107px] max-w-[107px]"
                        />
                        <ul>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#f26c39]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Original Contract Amount")}
                            </Typography>
                          </li>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#eb9f82]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Estimated Cost")}
                            </Typography>
                          </li>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#b35f3e]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Estimated Profit")}
                            </Typography>
                          </li>
                        </ul>
                      </div>
                    )}
                  </li>
                  <li className="flex items-center justify-center">
                    {projectSeriesData?.some((el) => Number(el) > 0) ? (
                      <ApexChart
                        className="total-prj-amt-chart"
                        series={projectSeriesData}
                        options={projectOptions}
                        type={"donut"}
                        height={180}
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center gap-3 h-[180px]">
                        <Image
                          src={`${window.ENV.CDN_URL}assets/images/no-data-pie-chart.svg`}
                          preview={false}
                          className="min-w-[107px] max-w-[107px]"
                        />
                        <ul>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#528bfc]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Total Project Amount")}
                            </Typography>
                          </li>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#99bbff]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Estimated Cost")}
                            </Typography>
                          </li>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#3061de]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Estimated Profit")}
                            </Typography>
                          </li>
                        </ul>
                      </div>
                    )}
                  </li>
                  <li className="flex items-center justify-center lg:col-span-2 xl:col-span-1 md:col-span-1 sm:col-span-2 col-span-1">
                    {amountSeriesData?.some((el) => Number(el) > 0) ? (
                      <ApexChart
                        className="amt-invoice-chart"
                        series={amountSeriesData}
                        options={amountOptions}
                        type={"donut"}
                        height={180}
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center gap-3 h-[180px]">
                        <Image
                          src={`${window.ENV.CDN_URL}assets/images/no-data-pie-chart.svg`}
                          preview={false}
                          className="min-w-[107px] max-w-[107px]"
                        />
                        <ul>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#ac4cf5]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Amount Invoiced")}
                            </Typography>
                          </li>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#c899f7]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Amount Paid")}
                            </Typography>
                          </li>
                          <li className="flex gap-0.5 items-center">
                            <Typography className="w-[13px] h-[13px] rounded-full inline-block bg-[#7717cd]"></Typography>
                            <Typography className="text-xs text-[#373d3f] inline-block whitespace-nowrap">
                              {_t("Amount Unpaid")}
                            </Typography>
                          </li>
                        </ul>
                      </div>
                    )}
                  </li>
                </ul>
              ) : value === "table" ? (
                <div className="ag-theme-alpine">
                  <StaticTable
                    className="static-table"
                    columnDefs={columnDefs}
                    headerHeight={50}
                    rowHeight={31}
                    rowData={[
                      {
                        ...(details?.finance_summary?.sov || {}),
                        name: "Original Contract Amount",
                      },
                      {
                        ...(details?.finance_summary?.change_orders || {}),
                        name: _t(
                          ChangeOrderModule?.plural_name ?? "Change Orders"
                        ),
                      },
                      {
                        ...(details?.finance_summary?.work_orders || {}),
                        name: _t(WorkOrderModule?.plural_name ?? "Work Orders"),
                      },
                      {
                        ...(details?.finance_summary?.service_tickets || {}),
                        name: _t(
                          ServiceTicketModule?.plural_name ?? "Service Tickets"
                        ),
                      },
                      {
                        ...(details?.finance_summary?.total || {}),
                        name: "Total Project Amount",
                      },
                      {
                        sov_tax: details?.finance_summary?.sov_tax,
                        name: "Tax",
                      },
                      {
                        project_amount_w_tax:
                          details?.finance_summary?.project_amount_w_tax,
                        name: "Total Project Amount (w/Tax)",
                      },
                      {
                        remaining_to_invoice:
                          details?.finance_summary?.invoices
                            ?.remaining_to_invoice,
                        amount_invoiced:
                          details?.finance_summary?.invoices?.amount_invoiced,
                        retention_held:
                          details?.finance_summary?.invoices?.retention_held,
                        name: "Amount Invoiced",
                      },
                      {
                        amount_paid:
                          details?.finance_summary?.invoices?.amount_paid,
                        name: "Amount Paid",
                      },
                      {
                        past_due: details?.finance_summary?.invoices?.past_due,
                        due_30_days:
                          details?.finance_summary?.invoices?.due_30_days,
                        amount_unpaid:
                          details?.finance_summary?.invoices?.amount_unpaid,
                        name: "Amount Unpaid",
                      },
                    ]}
                  />
                </div>
              ) : (
                <></>
              )}
            </div>
          </>
        }
      />
    </>
  );
};

export default FinancialSummary;
