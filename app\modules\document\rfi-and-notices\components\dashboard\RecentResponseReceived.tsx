// React + ag-grid
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "@remix-run/react";
// Hook + redux + helper
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { <PERSON>rid<PERSON><PERSON>, GridReadyEvent } from "ag-grid-community";
import {
  useAppRFIDispatch,
  useAppRFISelector,
} from "~/modules/document/rfi-and-notices/redux/store";
import { fetchDashData } from "~/modules/document/rfi-and-notices/redux/action/dashboardAction";
import { routes } from "~/route-services/routes";
import { Tag } from "~/shared/components/atoms/tag";

const RecentResponseReceived = () => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  // States
  const [isRefreshLoading, setIsRefreshLoading] = useState<boolean>(false);

  const gridApiRef = useRef<GridApi | null>(null);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  const dispatch = useAppRFIDispatch();
  const {
    isDashLoading,
    recentlyRespondedRfisData,
    recentlyRespondedRfisDataLastRefreshTime,
  } = useAppRFISelector((state) => state.dashboard);

  const [rowData, setRowData] = useState<IRecentlyRespondedRfis[]>([]);

  useEffect(() => {
    if (!isRefreshLoading && recentlyRespondedRfisData) {
      setRowData(recentlyRespondedRfisData);
    }
  }, [recentlyRespondedRfisData, isRefreshLoading]);

  const handleRefreshWidget = async () => {
    setIsRefreshLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refresh_type: "recently_responded_rfis" }));
    setIsRefreshLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: any) => {
        const { project_name } = data;
        const projectName = HTMLEntities.decode(sanitizeString(project_name));
        return projectName ? (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("RFI") + " #",
      field: "compliance",
      minWidth: 100,
      maxWidth: 100,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: any) => {
        const { prefix_company_correspondence_id } = data;
        return prefix_company_correspondence_id ? (
          <Tooltip title={prefix_company_correspondence_id}>
            <Typography className="table-tooltip-text text-center">
              {prefix_company_correspondence_id}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("From"),
      field: "from",
      minWidth: 80,
      maxWidth: 80,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellStyle: { textAlign: "center" },
      cellRenderer: (params: any) => {
        const { data } = params;
        const fromUserNames = HTMLEntities.decode(
          sanitizeString(data?.from_username)
        );
        const avatarSrc = data?.from_user_image || "";

        return fromUserNames ? (
          <Tooltip title={fromUserNames}>
            <div className="w-fit mx-auto overflow-hidden">
              <AvatarProfile
                user={{
                  name: HTMLEntities.decode(sanitizeString(fromUserNames)),
                  image: avatarSrc,
                }}
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Response Date"),
      field: "date_added",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const { response_due } = data;
        return response_due ? (
          <DateTimeCard format="date" date={response_due} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "rfi_status_name",
      minWidth: 110,
      maxWidth: 110,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellStyle: { textAlign: "center" },
      cellRenderer: ({ data }: any) => {
        const { rfi_status_name, rfi_status_default_color } = data;
        const bgColor = rfi_status_default_color + "1d";
        return rfi_status_name ? (
          <Tooltip title={rfi_status_name}>
            <div className="text-center overflow-hidden">
              <Tag
                className="mx-auto text-13 type-badge common-tag !border-0"
                style={{
                  color: rfi_status_default_color,
                  backgroundColor: bgColor,
                }}
              >
                {rfi_status_name}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
  ];
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-recently-responded_rfis.svg`}
    />
  );

  return (
    <>
      <DashboardCardHeader
        title={_t("Recently Responded RFIs")}
        showRefreshIcon={true}
        isRefreshing={isRefreshLoading}
        refreshIconTooltip={recentlyRespondedRfisDataLastRefreshTime}
        onClickRefresh={handleRefreshWidget}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine ag-grid-cell-pointer h-[175px]">
          <StaticTable
            key={isDashLoading ? "loading" : "loaded"}
            className="static-table"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            noRowsOverlayComponent={
              isDashLoading || isRefreshLoading ? noRowsOverlay : noData
            }
            onCellClicked={(params: any) => {
              if (!!params?.data?.correspondence_id) {
                navigate(
                  `${routes.MANAGE_RFI_NOTICES.url}/${params.data.correspondence_id}`
                );
              }
            }}
          />
        </div>
      </div>
    </>
  );
};
export default RecentResponseReceived;
