import { useEffect, useState } from "react";
import { useParams } from "@remix-run/react";

// ag-grid
import { GridReadyEvent, SortChangedEvent } from "ag-grid-community";

// hooks, helper and constants
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
import { dirTypeIds } from "~/modules/people/directory/utils/constasnts";
import useTableGridData from "~/shared/hooks/useTableGridData";

// redux action
import { getDLTimeCardListApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action";

// common
import DLNoteModal from "~/modules/projectManagement/pages/dailyLogs/components/common/DLNoteModal";

// atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

const EmployeeTimeCards = ({
  isClickOnReload,
}: {
  isClickOnReload: number;
}) => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();
  const { datasource, gridRowParams } = useTableGridData();
  const [noteData, setNoteData] = useState<ICommonNote[]>([]);

  const fetchDirList = async () => {
    let gridData: { rowCount?: number; rowData: IDLPeopleTimeCardData[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const limit = changeGridParams?.length ?? 0;

    let dataParams: IDLListParmas = {
      limit,
      page: changeGridParams?.start
        ? Math.floor(changeGridParams.start / limit)
        : 0,
      dailyLogId: id,
      userType: dirTypeIds.employees,
    };

    if (changeGridParams?.order_by_name) {
      dataParams.order_by_name = changeGridParams.order_by_name;
    }
    if (changeGridParams?.order_by_dir) {
      dataParams.order_by_dir = changeGridParams.order_by_dir;
    }

    try {
      if (gridParams) {
        gridParams.api.hideOverlay();
      }
      const resData = (await getDLTimeCardListApi(
        dataParams
      )) as IDLTimeCardApiRes;
      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.timeCardData?.length < limit) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData.data.timeCardData.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data?.timeCardData ?? [] };
      gridParams?.success(gridData);

      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.page === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  useEffect(() => {
    let timeout: NodeJS.Timeout | null = null;
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      if (gridRowParams) {
        fetchDirList();
      }
    }, 400);
    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [gridRowParams]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    if (isClickOnReload) {
      refreshAgGrid();
    }
  }, [isClickOnReload]);

  const columnDefs = [
    {
      headerName: _t("Employee"),
      field: "employee",
      minWidth: 200,
      maxWidth: 250,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IDLTimeCardTableCellRenderer) => {
        const fullName = HTMLEntities.decode(
          sanitizeString(params?.data?.employee)
        );
        const profileImage = params?.data?.image;
        return fullName || profileImage ? (
          <Tooltip title={fullName}>
            <div className="flex items-center gap-2 overflow-hidden max-w-full w-fit">
              <AvatarProfile
                user={{
                  name: fullName,
                  image: profileImage,
                }}
                iconClassName="text-[11px] font-semibold"
              />
              <Typography className="table-tooltip-text !max-w-[calc(100%-32px)]">
                {fullName}
              </Typography>
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code_name",
      minWidth: 230,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: IDLTimeCardTableCellRenderer) => {
        const costCode = params?.data?.cost_code_name;
        return costCode ? (
          <Tooltip title={costCode}>
            <Typography className="table-tooltip-text">{costCode}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Hours Worked"),
      field: "hours_worked",
      minWidth: 150,
      maxWidth: 150,
      suppressMovable: false,
      suppressMenu: true,
      cellStyle: { textAlign: "right" },
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: IDLTimeCardTableCellRenderer) => {
        return params?.data?.hours_worked ? (
          <Typography className="table-tooltip-text">
            {params?.data?.hours_worked}
          </Typography>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "note",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: (params: IDLTimeCardTableCellRenderer) => {
        const notesData = params?.data?.notes_data;
        return notesData && notesData?.length > 0 ? (
          <div className="flex items-center gap-2 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("Notes")}
              tooltipPlacement="top"
              icon="fa-regular fa-memo"
              onClick={() => {
                setNoteData(notesData);
              }}
            />
          </div>
        ) : (
          <></>
        );
      },
    },
  ];

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Employee Time Cards")}
        iconProps={{
          icon: "fa-solid fa-user-clock",
          containerClassName:
            "bg-[linear-gradient(180deg,#ff8ae51a_0%,#fd3ed31a_100%)]",
          id: "employee_timeuser_clock",
          colors: ["#FF8AE5", "#FD3ED3"],
        }}
        children={
          <div className="pt-2">
            <div className={"ag-theme-alpine h-234px]"}>
              <DynamicTable
                columnDefs={columnDefs}
                onGridReady={onGridReady}
                onSortChanged={onSortChanged}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-time-cards.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {noteData?.length > 0 && (
        <DLNoteModal
          isOpen={noteData?.length > 0}
          notesData={noteData}
          onCloseModal={() => {
            setNoteData([]);
          }}
        />
      )}
    </>
  );
};

export default EmployeeTimeCards;
