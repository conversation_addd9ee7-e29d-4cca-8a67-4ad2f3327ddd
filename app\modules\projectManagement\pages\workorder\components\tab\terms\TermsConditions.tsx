import { useEffect, useMemo, useRef, useState } from "react";
import Froala from "react-froala-wysiwyg";
import parse from "html-react-parser";
import { useParams } from "@remix-run/react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { SelectField } from "~/shared/components/molecules/selectField";
import { FroalaEditor } from "~/shared/components/molecules/froalaEditor";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// store
import { useWoAppSelector } from "../../../redux/store";
import { getGConfig, useGModules } from "~/zustand";
import { updateWorkOrderTermsApi } from "../../../redux/action/workorderDetailsAction";
import { TERMS_DROPDOWN } from "../../../utils/constasnts";
import { useAppDispatch } from "~/redux/store";
import { updateWorkorderDetail } from "../../../redux/slices/workorderDetailsSlice";
import { useTranslation } from "~/hook";

const TermsConditions = () => {
  const { _t } = useTranslation();
  const { module_key }: GConfig = getGConfig();
  const gConfig: GConfig = getGConfig();
  const params = useParams();
  const dispatch = useAppDispatch();

  const { checkModuleAccessByKey } = useGModules();
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const { details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const editorDefaultRef = useRef<Froala | null>(null);
  const editorCustomRef = useRef<Froala | null>(null);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  // States for terms and selection
  const [woDefaultTerms, setWoDefaultTerms] = useState<string>("");
  const [woTerms, setWoTerms] = useState<string>("");
  const [termsSelection, setTermsSelection] = useState(""); // "default", "custom", "both"
  useEffect(() => {
    // Update state when details change
    if (details) {
      setWoDefaultTerms(details?.wo_default_terms?.replace(/\\\"/g, '"') || "");
      setWoTerms(details?.wo_terms || "");
      setTermsSelection(details?.term_type || "default");
    }
  }, [details]);

  const handleUpdateField = async (data: IWorkorderDetails) => {
    setIsLoading(true);

    try {
      const updateRes = (await updateWorkOrderTermsApi({
        workorder_id: params?.id || "",
        module_id: gConfig?.module_id,
        ...data,
      })) as ApiCallResponse;

      if (updateRes?.success) {
        dispatch(updateWorkorderDetail(data));
      } else {
        notification.error({
          description: updateRes?.message || "Error while updating terms.",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Error while updating terms.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditorBlur = (
    editorContent: string,
    field: "wo_default_terms" | "wo_terms" | "term_type"
  ) => {
    if (field === "wo_default_terms" && editorContent !== woDefaultTerms) {
      handleUpdateField({ wo_default_terms: editorContent });
      setWoDefaultTerms(editorContent);
    } else if (field === "wo_terms" && editorContent !== woTerms) {
      handleUpdateField({ wo_terms: editorContent });
      setWoTerms(editorContent);
    } else if (field === "term_type") {
      if (editorContent !== termsSelection) {
        handleUpdateField({ term_type: editorContent });
      }
      setTermsSelection(editorContent);
    }
  };

  useEffect(() => {
    if (woDefaultTerms) {
      let text = woDefaultTerms;
      const tempDiv: HTMLDivElement = document.createElement("div");
      tempDiv.innerHTML = text.trim(); // Trim initial input to avoid leading/trailing spaces

      const cleanNodes = (node: Node): void => {
        node.childNodes.forEach((child: ChildNode) => {
          if (child.nodeType === Node.TEXT_NODE) {
            let cleanedText: string = (child.nodeValue ?? "")
              .replace(/[\u200B\u200C\u00A0]/g, "") // Remove invisible characters
              .trim(); // Ensure no leading/trailing spaces

            child.nodeValue = cleanedText || ""; // Ensure empty text remains empty
          } else {
            cleanNodes(child); // Recursively clean child elements
          }
        });
      };

      cleanNodes(tempDiv);
      let cleanedHtml: string = tempDiv.innerHTML.trim(); // Final cleanup

      setWoDefaultTerms(cleanedHtml); // Set cleaned HTML safely
    }

    if (woTerms) {
      let text = woTerms;
      // Create a temporary DOM element
      let tempDiv = document.createElement("div");
      tempDiv.innerHTML = text; // Set the HTML content

      // Function to clean only text nodes, keeping styles intact
      function cleanTextNodes(node: any) {
        node.childNodes.forEach((child: any) => {
          if (child.nodeType === Node.TEXT_NODE) {
            // Trim unwanted spaces or invisible characters at the start and end
            child.nodeValue = child.nodeValue.replace(
              /^[\u200B\u200C\u00A0\s]+|[\u200B\u200C\u00A0\s]+$/g,
              ""
            );
          } else {
            cleanTextNodes(child); // Recursively clean child elements
          }
        });
      }

      cleanTextNodes(tempDiv);
      let cleanedHtml = tempDiv.innerHTML; // Extract cleaned HTML

      setWoTerms(cleanedHtml);
    }
  }, [woDefaultTerms, woTerms]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Terms and Conditions")}
        iconProps={{
          icon: "fa-solid fa-ballot-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#63759A1a_0%,#63759A1a_100%)]",
          id: "terms_condition_icon",
          colors: ["#63759A", "#63759A"],
        }}
        headerRightButton={
          <div className="max-w-fit relative bg-blue-50 shadow-[0px_3px_20px] shadow-black/5 rounded">
            <SelectField
              isRequired
              labelPlacement="left"
              name="filter_tems_header"
              className="text-sm !h-7 pl-2 edit-block-select-view"
              value={termsSelection}
              fieldClassName="before:hidden"
              options={TERMS_DROPDOWN}
              disabled={isReadOnly}
              popupClassName="!w-[100px]"
              placement="bottomRight"
              onChange={
                (value) =>
                  typeof value === "string"
                    ? handleEditorBlur(value, "term_type")
                    : {} // No need any operation
              }
            />
            <FontAwesomeIcon
              className="w-3 h-3 text-primary-900 absolute right-2 top-1/2 -translate-y-1/2"
              icon="fa-regular fa-chevron-down"
            />
          </div>
        }
        children={
          <div className="pt-2 froala-editor">
            {isReadOnly ? (
              termsSelection === "default" && woDefaultTerms.trim() ? (
                parse(woDefaultTerms)
              ) : termsSelection === "custom" && woTerms.trim() ? (
                parse(woTerms)
              ) : termsSelection === "both" ? (
                <>
                  {woDefaultTerms.trim() && parse(woDefaultTerms)}
                  {woTerms.trim() && parse(woTerms)}
                </>
              ) : (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-froala-editor.svg`}
                />
              )
            ) : (
              <div
                className={`flex xl:flex-row flex-col gap-[15px] ${
                  termsSelection === "both"
                    ? "justify-between "
                    : "justify-start"
                }`}
              >
                {(termsSelection === "default" ||
                  termsSelection === "both") && (
                  <div
                    className={`w-full rounded-md shadow-[0_0_10px] shadow-black/15 p-1.5 ${
                      termsSelection === "both" ? "w-1/2" : "w-full"
                    } pr-2`}
                  >
                    <FieldLabel
                      labelClass="!px-3 py-2.5 rounded-t-md bg-[#F8F8F8] block"
                      children={_t("Default")}
                    />
                    <div className="p-3.5">
                      <FroalaEditor
                        ref={editorDefaultRef}
                        value={woDefaultTerms.trimStart()}
                        config={{
                          htmlAllowedTags: [".*"],
                          htmlAllowedAttrs: [".*"],
                        }}
                        additionalConfigs={{ hideCursorInitially: true }}
                        onBlurEnt={() => {
                          const editorInstance =
                            editorDefaultRef.current?.getEditor();
                          if (editorInstance) {
                            // If in code view, manually sync code textarea content
                            if (editorInstance?.codeView?.isActive()) {
                              const codeTextarea = document.querySelector(
                                ".fr-wrapper textarea"
                              ) as HTMLTextAreaElement;
                              if (codeTextarea) {
                                const rawHtml = codeTextarea?.value;
                                // Set it into Froala
                                editorInstance.html.set(rawHtml);
                                // Trigger internal change
                                editorInstance.events.trigger("contentChanged");
                              }
                            }
                            // Now get the actual updated HTML
                            const editorContent = editorInstance.html.get();
                            handleEditorBlur(editorContent, "wo_default_terms");
                          }
                        }}
                      />
                    </div>
                  </div>
                )}
                {(termsSelection === "custom" || termsSelection === "both") && (
                  <div
                    className={`w-full rounded-md shadow-[0_0_10px] shadow-black/15 p-1.5 ${
                      termsSelection === "both" ? "w-1/2" : "w-full"
                    } pl-2`}
                  >
                    <FieldLabel
                      labelClass="!px-3 py-2.5 rounded-t-md bg-[#F8F8F8] block"
                      children={_t("Custom")}
                    />
                    <div className="p-3.5">
                      <FroalaEditor
                        ref={editorCustomRef}
                        value={woTerms}
                        onBlurEnt={() => {
                          const editorInstance =
                            editorCustomRef.current?.getEditor();
                          if (editorInstance) {
                            // If in code view, manually sync code textarea content
                            if (editorInstance?.codeView?.isActive()) {
                              const codeTextarea = document.querySelector(
                                ".fr-wrapper textarea"
                              ) as HTMLTextAreaElement;
                              if (codeTextarea) {
                                const rawHtml = codeTextarea?.value;
                                // Set it into Froala
                                editorInstance.html.set(rawHtml);
                                // Trigger internal change
                                editorInstance.events.trigger("contentChanged");
                              }
                            }
                            // Now get the actual updated HTML
                            const editorContent = editorInstance.html.get();
                            handleEditorBlur(editorContent, "wo_terms");
                          }
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        }
      />
    </>
  );
};

export default TermsConditions;
