import { useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";

// common
import WeatherCardList from "./WeatherCardList";

// helper
import {
  allowDigitSlash,
  filterOptionBySubstring,
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";

import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";

// constants
import { DLWeatherDetailsField, fieldStatus } from "../../../utils/constasnts";

// redux store, action, and slice
import { useAppDLDispatch, useAppDLSelector } from "../../../redux/store";
import { updateDLDetailApi } from "../../../redux/action";
import { updateDLWeatherDetail } from "../../../redux/slices/dLDetailsSlice";
import { sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";

const WeatherCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const decInpRef = useRef<HTMLInputElement>(null);

  const dispatch = useAppDLDispatch();
  const { weatherDetails, isDetailLoading, details }: IDLDetailsInitialState =
    useAppDLSelector((state) => state.dailyLogDetails);
  const { weatherCondition }: IStatusListDataInitialState = useAppDLSelector(
    (state) => state.statusListData
  );

  const loadingStatusRef = useRef(fieldStatus);
  const [inputValues, setInputValues] = useState<IDLWeatherUpDetails>(
    DLWeatherDetailsField
  );
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);

  // Make dynamic hooks in future
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  useEffect(() => {
    if (checkStatusLoading) {
      setInputValues(weatherDetails);
    }
  }, [weatherDetails, checkStatusLoading]);

  const weatherConditionList = useMemo(
    () =>
      weatherCondition.map((item: IWeatherConditionSL) => ({
        label: HTMLEntities.decode(
          sanitizeString(item.display_name || item.name)
        ),
        value: item.type_id.toString(),
      })),
    [weatherCondition]
  );

  useEffect(() => {
    if (
      loadingStatus.length > 0 &&
      loadingStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingStatus]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: IDLDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDLWeatherUpDetails;
    if (field !== "weatherTemp") {
      setInputValues({ ...inputValues, ...data });
    }
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newData = { ...data };
    if (field === "weatherTemp") {
      const newADate = details?.arrivalDate
        ? backendDateFormat(
            details?.arrivalDate.toString(),
            CFConfig.day_js_date_format
          )
        : "";
      newData = {
        ...data,
        projectId: details?.projectId ? details.projectId.toString() : "",
        arrivalDate: newADate,
      };
    }
    const updateRes = (await updateDLDetailApi({
      logId: weatherDetails?.logId || "",
      ...newData,
    })) as IDLDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (field === "weatherTemp") {
        const weatherTemp =
          details?.temperatureScale == "0"
            ? updateRes?.data?.weather_temp_c || ""
            : updateRes?.data?.weather_temp_f || "";

        dispatch(updateDLWeatherDetail({ weatherTemp }));
      } else {
        dispatch(updateDLWeatherDetail(data));
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: weatherDetails[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <CrudCommonCard
      headerTitle={_t("Weather")}
      iconProps={{
        icon: "fa-solid fa-cloud-sun",
        containerClassName:
          "bg-[linear-gradient(180deg,#7fa3ff1a_0%,#3387fd1a_100%)]",
        id: "weather_cloud_sun",
        colors: ["#7FA3FF", "#3387FD"],
      }}
      children={
        <div className="pt-2">
          {isDetailLoading ? (
            <Spin className="w-full h-[200px] flex items-center justify-center" />
          ) : (
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <InputField
                  label={_t("Temp")}
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  value={inputValues?.weatherTemp}
                  name="weatherTemp"
                  placeholder={_t("Temperature")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "weatherTemp")}
                  onChange={handleInpOnChange}
                  onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
                    if (event.key === "Enter") {
                      setInputValues({
                        ...inputValues,
                        weatherTemp: event?.currentTarget?.value,
                      });
                    }
                    return allowDigitSlash(event, {
                      integerDigits: 10,
                    });
                  }}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "weatherTemp",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "weatherTemp",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "weatherTemp",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value;
                    if (value !== weatherDetails?.weatherTemp) {
                      setInputValues({
                        ...inputValues,
                        weatherTemp: value,
                      });
                      const newVal = value?.replace(/[°CFcf]/g, "")?.trim();
                      handleUpdateField({ weatherTemp: newVal });
                    } else {
                      handleChangeFieldStatus({
                        field: "weatherTemp",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                  }}
                />
              </li>
              <li
                className={
                  details.zipCode == "" &&
                  (weatherDetails?.weatherTemp == null ||
                    weatherDetails?.weatherTemp == "")
                    ? "block"
                    : "hidden"
                }
              >
                <Typography
                  className={`text-[#D2322D] text-xs block sm:pl-[150px]`}
                >
                  {_t(
                    "Add a Zip Code to the Project details to retrieve weather information."
                  )}
                </Typography>
              </li>
              <li>
                <SelectField
                  label={_t("Condition")}
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  placeholder={_t("Select Condition")}
                  value={
                    inputValues?.weatherCondition
                      ? inputValues.weatherCondition
                      : undefined
                  }
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  showSearch
                  options={weatherConditionList}
                  allowClear
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "weatherCondition"
                  )}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    const newValue =
                      typeof value === "string" ? value : value[0];
                    setInputValues({
                      ...inputValues,
                      weatherCondition: newValue,
                    });
                    handleUpdateField({
                      weatherCondition: newValue,
                    });
                  }}
                  onClear={() => {
                    setInputValues({
                      ...inputValues,
                      weatherCondition: "",
                    });
                    handleUpdateField({
                      weatherCondition: "",
                    });
                  }}
                />
              </li>
              <li>
                <InlineField
                  label={_t("Weather Notes")}
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  labelPlacement="left"
                  field={
                    <div className="flex flex-col gap-2 w-full">
                      <TextAreaField
                        ref={decInpRef}
                        placeholder={_t("Weather Notes")}
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        readOnly={isReadOnly}
                        value={inputValues?.weatherNotes}
                        name="weatherNotes"
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "weatherNotes"
                        )}
                        onChange={handleInpOnChange}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "weatherNotes",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "weatherNotes",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onFocus={() =>
                          handleChangeFieldStatus({
                            field: "weatherNotes",
                            status: "save",
                            action: "FOCUS",
                          })
                        }
                        onBlur={(e) => {
                          const value = e?.target?.value.trim();
                          if (value !== weatherDetails?.weatherNotes) {
                            handleUpdateField({ weatherNotes: value });
                          } else {
                            handleChangeFieldStatus({
                              field: "weatherNotes",
                              status: "button",
                              action: "BLUR",
                            });
                            setInputValues({
                              ...inputValues,
                              weatherNotes: weatherDetails.weatherNotes,
                            });
                          }
                        }}
                        onClickStsIcon={() => {
                          if (
                            getStatusForField(loadingStatus, "weatherNotes") ===
                            "edit"
                          ) {
                            decInpRef.current?.focus();
                          }
                        }}
                      />
                      <ul className="flex items-center justify-between">
                        {!!weatherDetails?.weatherJson &&
                          weatherDetails.weatherJson?.length > 0 &&
                          weatherDetails.weatherJson?.map(
                            (items: IDLDetailWeather, ind) => (
                              <WeatherCardList
                                key={ind}
                                image={items?.icon || ""}
                                time={items?.hours || ""}
                                temprecher={
                                  items?.temp
                                    ? items?.temp?.replace("&deg;", "°")
                                    : ""
                                }
                              />
                            )
                          )}
                      </ul>
                    </div>
                  }
                />
              </li>
            </ul>
          )}
        </div>
      }
    />
  );
};

export default WeatherCard;
