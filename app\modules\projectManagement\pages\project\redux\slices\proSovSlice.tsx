// This is the demo
import { createSlice } from "@reduxjs/toolkit";
import { ProjectSovSummary } from "../../utils/constants";
import {
  fetchProjectSOVItemsApi,
  fetchUnitsApi,
  fetchSovSummaryApi,
} from "../action/projectSovAction";
import { Number } from "~/helpers/helper";
import { RootState } from "../store";

const initialState: ISovSummaryInitialState = {
  sov_summary: ProjectSovSummary,
  taxable_total: 0,
  isSovInitialLoading: true,
  noSovAvailable: false,
  current_filter_by: "all",
  isSOVItemsLoading: true,
  projectSOVItems: {
    budget_items: [],
  },
  noSOVItemsAvailable: false,
  unitData: [],
};

export const projectDetailsSlice = createSlice({
  name: "proSov",
  initialState,
  reducers: {
    updateSov: (state, { payload }) => {
      state.sov_summary = { ...state.sov_summary, ...payload };
    },
    setNoSovAvail: (state, { payload }) => {
      state.noSovAvailable = payload;
    },
    updateSOVItems: (state, { payload }) => {
      state.projectSOVItems.budget_items = payload.items;
    },
    setCurrentFilter: (state, { payload }) => {
      state.current_filter_by = payload;
    },
    updateBudgetItems: (state, { payload }) => {
      const { item_id, updatedItem } = payload as {
        item_id: string;
        updatedItem: Partial<ISOVBudgetItemsData>;
      };

      state.projectSOVItems.budget_items = !updatedItem
        ? state.projectSOVItems.budget_items.filter(
            (itm) => Number(itm.item_id) !== Number(item_id)
          )
        : state.projectSOVItems.budget_items.map((itm) => {
            if (Number(itm.item_id) === Number(item_id)) {
              return { ...itm, ...updatedItem };
            }
            return itm;
          });
    },
    updateWholeBudgetItems: (state, { payload }) => {
      state.projectSOVItems.budget_items = payload;
    },
    setIsSovInitialLoading: (state, { payload }) => {
      state.isSovInitialLoading = payload;
    },
    setIsSovItemsLoading: (state, { payload }) => {
      state.isSOVItemsLoading = payload;
    },
    updateUnitData: (state, { payload }) => {
      state.unitData = payload;
    },
    resetSovStates: (state) => {
      state.isSOVItemsLoading = true;
      state.projectSOVItems.budget_items = [];
      state.sov_summary = ProjectSovSummary;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchSovSummaryApi.pending, (state, action) => {
      state.current_filter_by = state.current_filter_by;
    });
    builder.addCase(fetchSovSummaryApi.fulfilled, (state, { payload }) => {
      const { success, data, message } = payload as ISovSummaryApiRes;
      if (success) {
        state.sov_summary = data.sov_summary;
        state.taxable_total = data.taxable_total;
      } else {
        state.noSovAvailable = true;
      }
      state.isSovInitialLoading = false;
    });
    builder.addCase(fetchSovSummaryApi.rejected, (state, { payload }) => {
      state.noSovAvailable = true;
      state.isSovInitialLoading = false;
    });

    builder.addCase(fetchProjectSOVItemsApi.pending, (state, action) => {
      // state.projectSOVItems = initialState.projectSOVItems;
      // state.isSOVItemsLoading = true;
    });
    builder.addCase(fetchProjectSOVItemsApi.fulfilled, (state, { payload }) => {
      const { success, data, message } = payload as IProjectSOVItemsApiRes;
      if (success) {
        state.projectSOVItems = data;
      }
      state.isSOVItemsLoading = false;
    });
    builder.addCase(fetchProjectSOVItemsApi.rejected, (state, { payload }) => {
      state.noSOVItemsAvailable = true;
      state.isSOVItemsLoading = false;
    });

    // Fetch Units thunk
    builder.addCase(fetchUnitsApi.pending, (state, action) => {});
    builder.addCase(fetchUnitsApi.fulfilled, (state, { payload }) => {
      const { success, data } = payload as IUnitListResponse;
      if (success) {
        state.unitData = data.units;
      }
    });
    builder.addCase(fetchUnitsApi.rejected, (state, { payload }) => {});
  },
});

export const {
  setNoSovAvail,
  setCurrentFilter,
  updateBudgetItems,
  updateSOVItems,
  setIsSovInitialLoading,
  setIsSovItemsLoading,
  updateWholeBudgetItems,
  updateUnitData,
  resetSovStates,
} = projectDetailsSlice.actions;

export const getUpdatedSovData = (): ((
  dispatch: unknown,
  getState: () => RootState
) => ISovSummaryInitialState) => {
  return (_, getState) => {
    const state = getState();
    return state.proSov;
  };
};

export default projectDetailsSlice.reducer;
