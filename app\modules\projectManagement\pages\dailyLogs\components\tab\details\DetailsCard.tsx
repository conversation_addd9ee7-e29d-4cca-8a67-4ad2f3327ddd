import { useEffect, useRef, useState } from "react";
import delay from "lodash/delay";
import dayjs from "dayjs";

// atoms
import { Spin } from "~/shared/components/atoms/spin";

// molecules
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";

// constants
import {
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import {
  backendDateFormat,
  backendTimeFormat,
  displayTimeFormat,
  frontEndTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";

// helper
import {
  DLDetailsField,
  fieldStatus,
} from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";

// redeux action, store, and slice
import { updateDLDetail<PERSON><PERSON> } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  updateDLDetail,
  updateDLFullWeatherDetail,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLDetailsSlice";

// Hook
import { useTranslation } from "~/hook";

const DetailsCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const decInpRef = useRef<HTMLInputElement>(null);

  const dispatch = useAppDLDispatch();
  const { details, isDetailLoading }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const loadingStatusRef = useRef(fieldStatus);
  const [inputValues, setInputValues] = useState<IDLUpDetails>(DLDetailsField);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);

  // Make dynamic hooks in future
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  useEffect(() => {
    if (checkStatusLoading) {
      setInputValues(details);
    }
  }, [details, checkStatusLoading]);

  useEffect(() => {
    if (
      loadingStatus.length > 0 &&
      loadingStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingStatus]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: IDLDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDLUpDetails;
    if (
      field !== "arrivalDate" &&
      field !== "arrivalTime" &&
      field !== "departureTime"
    ) {
      setInputValues({ ...inputValues, ...data });
    }
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    let newDataForm = { ...data };
    if (field === "arrivalDate") {
      newDataForm = { ...data, projectId: details?.projectId || "" };
    }

    const updateRes = (await updateDLDetailApi({
      logId: details?.logId || "",
      ...newDataForm,
    })) as IDLDetailsUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      let newData = data;
      if (field === "arrivalTime") {
        newData.arrivalTime = data.arrivalTime
          ? frontEndTimeFormat(data.arrivalTime as string)
          : "";
      }
      if (field === "arrivalDate") {
        newData = {
          arrivalDate: data[field]
            ? dayjs(data[field], "YYYY-MM-DD").format(
                CFConfig.day_js_date_format
              )
            : "",
        };
        const weatherTemp =
          details?.temperatureScale == "0"
            ? updateRes?.data?.weather_temp_c || ""
            : updateRes?.data?.weather_temp_f || "";
        const weatherJson =
          details?.temperatureScale == "0"
            ? updateRes?.data?.weather_json_c || []
            : updateRes?.data?.weather_json_f || [];
        dispatch(updateDLFullWeatherDetail({ weatherTemp, weatherJson }));
      }
      if (field === "departureTime") {
        newData.departureTime = data.departureTime
          ? frontEndTimeFormat(data.departureTime as string)
          : "";
      }
      dispatch(updateDLDetail(newData));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleChangeDate = (val: string, type: string) => {
    const newValue = val ? backendTimeFormat(val) : "";
    if (type === "arrivalTime") {
      setInputValues({
        ...inputValues,
        arrivalTime: val,
      });
      handleUpdateField({
        arrivalTime: newValue,
      });
    } else {
      setInputValues({
        ...inputValues,
        departureTime: val,
      });
      handleUpdateField({
        departureTime: newValue,
      });
    }
  };

  const handleChange = (dateString: string) => {
    const newValue = dateString
      ? backendDateFormat(dateString.toString(), CFConfig.day_js_date_format)
      : "";
    setInputValues({
      ...inputValues,
      arrivalDate: dateString.toString(),
    });
    handleUpdateField({
      arrivalDate: newValue,
    });
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <CrudCommonCard
      headerTitle={_t("Details")}
      iconProps={{
        icon: "fa-solid fa-file-lines",
        containerClassName:
          "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
        id: "details_icon",
        colors: ["#FF868A", "#FD3333"],
      }}
      children={
        <div className="pt-2">
          {isDetailLoading ? (
            <Spin className="w-full h-[200px] flex items-center justify-center" />
          ) : (
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <DatePickerField
                  label={_t("Arrival Date")}
                  name="arrival_date"
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  labelPlacement="left"
                  placeholder={_t("Select Date")}
                  editInline={true}
                  allowClear={false}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "arrivalDate")}
                  disabled={details?.hasAdminRole === "0"}
                  format={CFConfig.day_js_date_format}
                  value={
                    inputValues.arrivalDate
                      ? dayjs(
                          inputValues?.arrivalDate,
                          CFConfig.day_js_date_format
                        )
                      : undefined
                  }
                  disabledDate={(current) =>
                    current && current > dayjs().endOf("day")
                  }
                  onChange={(_, dateString) =>
                    handleChange(dateString as string)
                  }
                />
              </li>
              <li>
                <TimePickerField
                  label={_t("Arrival Time")}
                  format="hh:mm A"
                  use12Hours
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  labelPlacement="left"
                  placeholder={_t("Select Time")}
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  name="arrivalTime"
                  fixStatus={getStatusForField(loadingStatus, "arrivalTime")}
                  value={
                    inputValues.arrivalTime
                      ? displayTimeFormat(inputValues?.arrivalTime)
                      : undefined
                  }
                  allowClear={false}
                  onChange={(_, val) => {
                    if (inputValues?.arrivalTime !== val) {
                      handleChangeDate(val as string, "arrivalTime");
                    }
                  }}
                />
              </li>
              <li>
                <TimePickerField
                  label={_t("Departure Time")}
                  format="hh:mm A"
                  use12Hours
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  labelPlacement="left"
                  placeholder={_t("Select Time")}
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  name="departureTime"
                  fixStatus={getStatusForField(loadingStatus, "departureTime")}
                  value={
                    inputValues?.departureTime
                      ? displayTimeFormat(inputValues.departureTime)
                      : undefined
                  }
                  onChange={(_, val) => {
                    if (inputValues?.departureTime !== val) {
                      handleChangeDate(val as string, "departureTime");
                    }
                  }}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Tasks Performed")}
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  placeholder={_t("Log Tasks Performed")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  ref={decInpRef}
                  value={inputValues?.taskPerformed}
                  name="taskPerformed"
                  fixStatus={getStatusForField(loadingStatus, "taskPerformed")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "taskPerformed",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "taskPerformed",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "taskPerformed",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.taskPerformed) {
                      handleUpdateField({ taskPerformed: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "taskPerformed",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        taskPerformed: details.taskPerformed,
                      });
                    }
                  }}
                  onClickStsIcon={() => {
                    if (
                      getStatusForField(loadingStatus, "taskPerformed") ===
                      "edit"
                    ) {
                      decInpRef.current?.focus();
                    }
                  }}
                />
              </li>
            </ul>
          )}
        </div>
      }
    />
  );
};

export default DetailsCard;
