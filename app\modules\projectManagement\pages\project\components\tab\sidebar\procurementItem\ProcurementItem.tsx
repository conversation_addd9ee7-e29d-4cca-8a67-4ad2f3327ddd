// Hook
import { useTranslation } from "~/hook";
// atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Typography } from "~/shared/components/atoms/typography";
import { Header } from "~/shared/components/atoms/header";
// Molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import SelectCostCode from "~/shared/components/molecules/selectCostCode/SelectCostCode";

import { IProcurementItemProps } from "./type";
import { useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { sanitizeString } from "~/helpers/helper";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { useAppESSelector } from "~/modules/financials/pages/estimates/redux/store";
import { generateCostCodeLabel } from "~/shared/utils/helper/common";
const ProcurementItem = ({
  isprocurItem,
  setIsprocurItem,
  isViewOnly = false,
  formData,
  setIsOpenContactDetails,
  setAdditionContact,
  setcontactId,
}: IProcurementItemProps) => {
  const { _t } = useTranslation();
  const [markup, setMarkup] = useState<string>("markup_percent");
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const { formatter } = useCurrencyFormatter();
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const { codeCostData } = useAppESSelector((state) => state.costCode);

  const costCodeOptions = useMemo(() => {
    const filteredCodeCostData = codeCostData?.filter(
      (item) =>
        (item?.cost_code_name?.toString() !== "" ||
          item?.csi_code?.toString() !== "") &&
        Number(item?.parent_id) > 0
    );
    let costCodeOpts = filteredCodeCostData?.map((item: ICostCode) => {
      return {
        label: `${generateCostCodeLabel({
          name: item?.cost_code_name,
          code: item?.csi_code,
          isArchived: false,
          isAllowCodeWithoutName: true,
        })}`,
        value: item?.code_id,
      };
    });

    if (formData?.is_deleted === 1) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          label: `${generateCostCodeLabel({
            name: formData?.cost_code_name,
            code: formData?.csi_code,
            isArchived: false,
          })}`,
          value: formData?.cost_code_id?.toString() ?? "",
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData]);
  return (
    <Drawer
      open={isprocurItem}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-cart-circle-check"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t("Procurement Item")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setIsprocurItem(false)} />}
    >
      <form className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100vh-132px)]  px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="grid gap-3.5">
                <div className="w-full">
                  <InputField
                    label={_t("Subject")}
                    labelPlacement="top"
                    name="subject"
                    value={formData?.subject}
                    isRequired={true}
                    disabled={true}
                    onChange={() => {}}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      disabled={true}
                      options={[]}
                      value={formData?.item_type_name}
                    />
                  </div>
                  <div className="w-full relative">
                    <div className="w-full">
                      <ButtonField
                        label={_t("Assigned To")}
                        name="assigned_to"
                        labelPlacement="top"
                        // disabled={isViewOnly}
                        avatarProps={{
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(formData?.assignee_name)
                            ),
                            image:
                              formData?.assigned_to_contact_id == 0
                                ? formData?.user_image
                                : "",
                          },
                        }}
                        value={HTMLEntities.decode(
                          sanitizeString(
                            formData?.assigned_to != "0"
                              ? formData?.assignee_name
                              : ""
                          )
                        )}
                        disabled={true}
                        onClick={() => { }}
                        addonBefore={
                          formData?.assigned_to && formData?.assigned_to != "0" ? (
                            <div className="flex items-center gap-1">
                              <ContactDetailsButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setContactDetailDialogOpen(true);
                                }}
                              />
                            </div>
                          ) : (
                            <></>
                          )
                        }
                      />
                    </div>
                    {/* <div className="absolute right-0 bottom-0">
                      <ContactDetailsButton
                        onClick={async (e) => {
                          e.stopPropagation();
                          await setIsOpenContactDetails(true);
                          await setcontactId(formData?.assigned_to as number);
                          await setAdditionContact(
                            Number(formData?.assigned_to_contact_id)
                          );
                        }}
                      />
                    </div> */}
                  </div>
                </div>
                <div className="w-full">
                  <SelectCostCode
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    disabled={true}
                    onChange={() => {}}
                    loadingStatus={[]}
                    value={(() => {
                      const selectedOption = costCodeOptions?.find(
                        (item) =>
                          formData?.cost_code_id?.toString() ===
                          item?.value?.toString()
                      );
                      return selectedOption
                        ? selectedOption
                        : [
                          {
                            label: HTMLEntities.decode(
                              sanitizeString(
                                `${formData?.cost_code_name} (Archived)`
                              )
                            ),
                            id: formData?.cost_code_id,
                          },
                        ];
                    })()}
                  />
                </div>
              </div>
            </SidebarCardBorder>
            <SidebarCardBorder cardTitle="Pricing">
              <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Qty")}
                  </Typography>
                  <div className="sm:w-40 w-28">
                    <InputField
                      className="!p-0 !pl-1.5 text-success !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                      placeholder={_t("Item Quantity")}
                      labelPlacement="left"
                      type="text"
                      onChange={() => {}} // change this in future ( temporary resolve type issue )
                      disabled={true}
                      value={formData?.quantity}
                    />
                  </div>
                </li>
                <li>
                  <ul className="py-0.5 relative">
                    <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                      <FontAwesomeIcon
                        className="w-3 h-3 text-primary-900 dark:text-white"
                        icon="fa-regular fa-xmark"
                      />
                    </li>
                  </ul>
                </li>
                <li className="flex justify-between items-center">
                  <Typography className="text-13 block text-primary-900 dark:text-white/90">
                    {_t("Unit Cost/Unit")}
                  </Typography>
                  <div className="sm:w-[260px] w-28 h-[22px]" ref={costUnitRef}>
                    <div
                      ref={unitCostContainerRef}
                      className="text-right !text-[#008000] leading-[22px] font-semibold text-sm"
                    >
                      {!isViewOnly && (
                        <>
                          <div className="flex gap-2">
                            <div className="w-[calc(100%-52px)]">
                              <InputField
                                className={`!p-0 !pl-1.5 text-success !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                  !showUnitInputs && "!hidden"
                                }`}
                                placeholder={_t("Item Unit Cost")}
                                labelPlacement="left"
                                type="text"
                                onChange={() => {}} // change this in future ( temporary resolve type issue )
                                disabled={true}
                                value={
                                  formatter(
                                    (
                                      Number(formData?.unit_cost) / 100
                                    )?.toFixed(2)
                                  ).value
                                }
                              />
                            </div>
                            <div className="w-11">
                              <InputField
                                className={`!p-0 !pl-1.5 text-success !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                  !showUnitInputs && "!hidden"
                                }`}
                                placeholder={_t("Unit")}
                                labelPlacement="left"
                                maxLength={15}
                                type="text"
                                onChange={() => {}} // change this in future ( temporary resolve type issue )
                                disabled={true}
                                value={formData?.unit}
                              />
                            </div>
                          </div>
                          {!showUnitInputs && (
                            <Typography
                              className="text-[#008000] cursor-pointer font-medium"
                              onClick={() => {}}
                            >
                              28.53/EA
                            </Typography>
                          )}
                        </>
                      )}

                      {isViewOnly &&
                        (!isEmpty(formData?.unit_cost) &&
                        formData?.unit_cost !== 0.0 &&
                        formData?.unit_cost !== "0.00" &&
                        !isEmpty(formData?.unit) &&
                        formData?.unit !== 0.0 ? (
                          <Typography className="text-[#008000] font-medium">
                            28.53/EA
                          </Typography>
                        ) : (
                          <div className="flex gap-2">
                            <div className="w-[calc(100%-52px)]">
                              <InputField
                                className="!p-0 !pl-1.5 text-success !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Item Unit Cost")}
                                type="text"
                                readOnly={true}
                                onChange={() => {}} // change this in future ( temporary resolve type issue )
                                disabled={true}
                              />
                            </div>
                            <div className="w-11">
                              <InputField
                                className="!p-0 !pl-1.5 text-success !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                placeholder={_t("Unit")}
                                readOnly={true}
                                type="text"
                                onChange={() => {}} // change this in future ( temporary resolve type issue )
                                disabled={true}
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </li>
                <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                  <Typography className="text-13 block text-primary-900 font-semibold">
                    {_t("Total Cost")}
                  </Typography>
                  <div className="sm:w-[260px] w-32 flex justify-end items-center">
                    <Typography
                      className="!text-red-600 text-13 font-semibold"
                      disabled={true}
                    >
                      {
                        formatter((Number(formData?.total) / 100)?.toFixed(2))
                          .value_with_symbol
                      }
                    </Typography>
                  </div>
                </li>
              </ul>
            </SidebarCardBorder>
            <SidebarCardBorder addGap={true}>
              <div className="w-full">
                <TextAreaField
                  label={_t("Description")}
                  labelPlacement="top"
                  placeholder={_t(
                    "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                  )}
                  value={formData?.description || "-"}
                  disabled={true}
                />
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Internal Notes")}
                  labelPlacement="top"
                  placeholder={_t(
                    "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                  )}
                  value={formData?.internal_notes || "-"}
                  disabled={true}
                />
              </div>
              <CheckBox className="gap-1.5" disabled={true} checked={formData?.apply_global_tax}>
                {_t("Collect Tax on this Item?")}
              </CheckBox>
            </SidebarCardBorder>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full gap-4 px-4 pt-4">
          <PrimaryButton
            className="w-full justify-center primary-btn"
            buttonText={_t("Cancel")}
            onClick={() => {
              setIsprocurItem(false);
              setcontactId(0);
              setAdditionContact(0);
            }}
          ></PrimaryButton>
        </div>
      </form>
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          CFConfig.employee_key,
          "my_crew",
          CFConfig.customer_key,
          CFConfig.lead_key,
          CFConfig.contractor_key,
          CFConfig.vendor_key,
          CFConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
      {contactDetailDialogOpen && (
        <ContactDetails
          isOpenContact={contactDetailDialogOpen}
          contactId={formData?.assigned_to || ""}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          additional_contact_id={formData?.assigned_to_contact_id}
        />
      )}
    </Drawer>
  );
};

export default ProcurementItem;
