import { useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { ApexChart } from "~/shared/components/atoms/chart";
// Molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import BarChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarChart.skeleton";
// Other
import { useApexCharts } from "~/shared/hooks/useApexCharts";
import { getGConfig } from "~/zustand";
import { SELECT_INSPECTIONS_TYPE } from "../../utils/constants";
import { useInAppDispatch, useInAppSelector } from "../../redux/store";
import { Number } from "~/helpers/helper";
import { fetchInspectionsDashboardApi } from "../../redux/action/inspectionDashAction";

const InspectionsByStatus = () => {
  const { _t } = useTranslation();
  const { module_name }: GConfig = getGConfig();
  const dispatch = useInAppDispatch();
  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const [thisLastYearFilter, setThisLastYearFilter] =
    useState<string>("this_year");
  const {
    this_year_inspection_chart,
    last_year_inspection_chart,
    isInitialLoad,
  } = useInAppSelector((state) => state.inspectionsDashboard);

  const chartData = useMemo(() => {
    let data = this_year_inspection_chart;
    if (thisLastYearFilter === "last_year") {
      data = last_year_inspection_chart;
    }
    return data;
  }, [
    thisLastYearFilter,
    this_year_inspection_chart,
    last_year_inspection_chart,
  ]);

  const SeriesData = [
    {
      name: _t(`${module_name}`),
      data: chartData?.data.map((item) => Number(item.insp_count || 0)),
    },
  ];
  const optionsLine = useApexCharts({ type: "bar" });
  const options = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        stacked: false,
        toolbar: {
          show: false,
        },
        zoom: {
          enabled: false,
        },
      },
      stroke: {
        colors: ["#fff"],
        width: 0,
      },
      markers: {
        size: [4, 7],
      },

      title: {
        text: undefined,
      },
      subtitle: {
        text: undefined,
      },
      plotOptions: {
        bar: {
          columnWidth: "40",
          distributed: true,
        },
      },
      grid: {
        strokeDashArray: 5,
      },
      yaxis: {
        show: true,
      },
      xaxis: {
        categories: chartData?.data.map((item) => item.inspection_status_name),
        labels: {
          show: true,
          trim: true,
          rotate: 0,
          rotateAlways: false,
          hideOverlappingLabels: false,
          style: {
            fontSize: "12px",
            fontWeight: 600,
          },
        },
      },
      colors: chartData?.data.map((item) => item.inspection_status_color),
      legend: {
        show: false,
        position: "top",
        onItemClick: {
          toggleDataSeries: false,
        },
        onItemHover: {
          highlightDataSeries: true,
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
    };
  }, [chartData]);

  const handleClickRefresh = async () => {
    setisCashLoading(true);
    await dispatch(
      fetchInspectionsDashboardApi({
        refresh_type:
          thisLastYearFilter === "last_year"
            ? "last_year_inspection_chart"
            : "this_year_inspection_chart",
      })
    );
    setisCashLoading(false);
  };

  return (
    <>
      <DashboardCardHeader
        title={_t(`${module_name} by Status`)}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={chartData?.last_refres_time}
        onClickRefresh={handleClickRefresh}
        rightContent={
          <SelectField
            labelPlacement="top"
            applyBorder={true}
            value={thisLastYearFilter}
            formInputClassName="max-w-[100px] overflow-visible"
            containerClassName="overflow-visible"
            fieldClassName="before:hidden"
            className="border-select-filed rounded h-7"
            options={SELECT_INSPECTIONS_TYPE}
            showSearch={false}
            onChange={(val) => {
              if (!!val) {
                setThisLastYearFilter(val as string);
              } else {
                setThisLastYearFilter("");
              }
            }}
            popupClassName="popup-select-option-header"
          />
        }
      />
      <div className="py-2 px-2.5">
        {isInitialLoad || isCashLoading ? (
          <BarChartSkeleton sizeClassName="h-[200px] py-3.5" count={4} />
        ) : (
          <ApexChart
            series={SeriesData}
            options={options}
            type={"bar"}
            height={190}
          />
        )}
      </div>
    </>
  );
};

export default InspectionsByStatus;
