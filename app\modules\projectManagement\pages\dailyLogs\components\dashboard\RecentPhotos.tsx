import { useState } from "react";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { fetchDashData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { useTranslation } from "~/hook";

// atoms
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ThumbnailView } from "~/shared/components/molecules/thumbnailView";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";

const RecentPhotos = () => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const {
    isDashLoading,
    recentPhotos,
    recent_photos_last_refresh_time,
  }: IDailyLogIntlState = useAppDLSelector((state) => state.dashboard);
  const [isLoading, setIsLoading] = useState(false); // for type script issue solve
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const numbers = Array.from({ length: 8 }, (_, index) => index + 1);

  const handleRefreshWidget = async () => {
    setIsCashLoading(true);
    await dispatch(fetchDashData({ refreshType: "recent_photos" }));
    setIsCashLoading(false);
  };
  return (
    <>
      <DashboardCardHeader
        title={_t("Recent Photos")}
        showRefreshIcon={true}
        refreshIconTooltip={recent_photos_last_refresh_time}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isCashLoading}
      />
      <div className="py-2 px-2.5">
        {isDashLoading || isCashLoading ? (
          <div className="grid lg:grid-cols-4 md:grid-cols-5 grid-cols-3 md:gap-3 gap-5">
            {numbers.map((number) => (
              <div
                className=" pb-[83%] w-[83%] mx-auto flex items-center justify-center overflow-hidden bg-[#f1f1f1] animate-pulse rounded-lg"
                key={number}
              >
                <div className="w-full h-full  bg-black/10 "></div>
              </div>
            ))}
          </div>
        ) : recentPhotos && recentPhotos?.length > 0 ? (
          <LightGalleryModel
            zoom={true}
            thumbnail={true}
            backdropDuration={150}
            showZoomInOutIcons={true}
            actualSize={false}
            mode="lg-slide"
            alignThumbnails="left"
            className="grid lg:grid-cols-4 md:grid-cols-5 grid-cols-3 md:gap-3 gap-5"
            mousewheel={true}
          >
            {recentPhotos.map((items: IRecentPhotos) => (
              <div
                key={items?.image_id}
                className="relative pb-[83%] w-[83%] mx-auto flex items-center justify-center overflow-hidden bg-[#f1f1f1] rounded-lg cursor-pointer"
              >
                <div className="w-full h-full absolute top-0 left-0 right-0 bottom-0">
                  <a
                    href={items?.file_path}
                    key={items?.image_id}
                    className="lightGalleryModel"
                  >
                    <ThumbnailView
                      file_ext={items?.file_ext ?? ""}
                      file_path={items?.file_path ?? ""}
                      image_id={items?.image_id}
                      file_name={items?.file_name}
                      className="hover:scale-110 delay-100 ease-in duration-300"
                      isLoading={isLoading}
                      setIsLoading={setIsLoading}
                    />
                  </a>
                </div>
              </div>
            ))}
          </LightGalleryModel>
        ) : (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-recent-photos.svg`}
          />
        )}
      </div>
    </>
  );
};

export default RecentPhotos;
