// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
// Other
import { useTranslation } from "~/hook";
import { useEffect, useMemo, useState } from "react";
import { sanitizeString } from "~/helpers/helper";
import { FormikValues, useFormik } from "formik";
import { formEstimateProjectAddSchema } from "./utils";
import * as yup from "yup";
import { AddProjectApi } from "~/modules/projectManagement/pages/project/redux/action/addProjectAction";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import {
  addCustomData,
  generateAutoNumber,
} from "~/redux/action/customDataAction";
import { useAppDispatch } from "~/redux/store";
import delay from "lodash/delay";
import { ConfirmModal } from "../confirmModal";
import { faClipboardListCheck } from "@fortawesome/pro-regular-svg-icons";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { getGTypes } from "~/zustand";
import { getCustomStatusData } from "./actions/projectAction";
import { useGlobalMenuModule } from "~/zustand/global/menuModules/slice";
import { SelectOption } from "../../atoms/selectOption";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { onEnterSelectSearchValue } from "../selectField/units";

const AddNewProject = ({
  addNewProject,
  setAddNewProject,
  customer_id = "",
  onNewProjectAdd,
}: IAddNewProjectProps) => {
  const { _t } = useTranslation();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
  const globalSetting = getGlobalAppSettings();
  const is_custom_project_id = globalSetting?.is_custom_project_id;
  const [selectFieldSearch, setSelectFieldSearch] = useState<string>("");
  const [projectTypeList, setProjectTypeList] = useState<
    ISelectFieldProps["options"]
  >([]);
  const [projectStatusList, setProjectStatusList] = useState<
    ISelectFieldProps["options"]
  >([]);
  const [autoNumber, setAutoNumber] =
    useState<IProjectModuleAutoNumberApiResponseData>({});
  const { getGlobalMenuModuleByKey } = useGlobalMenuModule();

  const itemType: GType[] = getGTypes();

  const keysToFilter = [
    CFConfig.project_type_key,
    CFConfig.project_status_type_key,
  ];
  const projectTypes = itemType.filter((item) =>
    keysToFilter.includes(item.key)
  );
  const projectTypeID = projectTypes.find(
    (item) => item.key === CFConfig.project_type_key
  )?.type_id;
  const projectStatusTypeID = projectTypes.find(
    (item) => item.key === CFConfig.project_status_type_key
  )?.type_id;

  const module = useMemo(
    () => getGlobalMenuModuleByKey(CFConfig.project_module),
    []
  );

  const fetchCustomData = async () => {
    if (module) {
      const customStatusDataRes = await getCustomStatusData({
        is_deleted: 0,
        module_id: [module.module_id],
      });

      if (customStatusDataRes.success && customStatusDataRes.data) {
        const groupedData = customStatusDataRes.data.reduce(
          (acc, item) => {
            if (item.item_type.toString() === projectTypeID?.toString()) {
              acc.projectTypes.push({
                label: item.name,
                // value: item.item_id?.toString(),
                value: item.key?.toString(),
              });
            } else if (
              item.item_type.toString() === projectStatusTypeID?.toString()
            ) {
              acc.projectStatuses.push({
                label: item.name,
                // value: item.key?.toString(),
                value: item.key?.toString(),
              });
            }
            return acc;
          },
          { projectTypes: [] as IOption[], projectStatuses: [] as IOption[] }
        );

        setProjectTypeList(groupedData.projectTypes);
        setProjectStatusList(groupedData.projectStatuses);
      }
    } else {
      notification.error({
        description: "Module not found!",
      });
    }
  };

  const fetchAutoNumber = async () => {
    const mAutoNumber = (await generateAutoNumber({
      module_id: Number(module?.module_id),
      module_key: module?.module_key.toString() || "",
    })) as IProjectModuleAutoNumberApiResponse;
    if (mAutoNumber.success) {
      setAutoNumber(mAutoNumber.data);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsDataLoading(true);
      await Promise.all([fetchCustomData(), fetchAutoNumber()]);
      setIsDataLoading(false);
    };

    fetchData();
  }, []);

  useEffect(() => {
    const { last_primary_id, need_to_increment } = autoNumber;
    if (
      Number(is_custom_project_id) === 2 &&
      need_to_increment &&
      last_primary_id
    ) {
      formik.setValues({
        ...formik.values,
        project_id: last_primary_id.toString(),
      });
    } else if (Number(is_custom_project_id) === 0) {
      formik.setValues({
        ...formik.values,
        project_id: "",
      });
    } else {
      formik.setValues({
        ...formik.values,
        project_id: "",
      });
    }

    let projectId;

    if (last_primary_id) {
      projectId = last_primary_id.toString();
    } else {
      projectId = "";
    }

    formik.setValues((prev) => ({
      ...prev,
      project_id: projectId,
    }));
  }, [autoNumber]);

  const {
    initValues,
    validateArray,
  }: {
    initValues: IEstimateAddNewProjectFormState;
    validateArray: string[];
  } = formEstimateProjectAddSchema();

  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const handleSubmit = async (
    values: IEstimateAddNewProjectFormState,
    { resetForm }: FormikValues
  ) => {
    setIsLoading(true);
    if (
      !formik.values?.project_id?.trim() &&
      Number(is_custom_project_id) !== 0
    ) {
      formik.setFieldError("project_id", "This field is required.");
      setIsLoading(false);
      return;
    }
    const formData = {
      ...formik.values,
    };
    if (is_custom_project_id == 0 && formData) {
      delete formData.project_id;
    }
    if (customer_id) {
      formData.customer_id = String(customer_id);
    }
    try {
      const response = (await AddProjectApi(formData)) as IAddProjectApiRes;
      if (response.success) {
        if (response.data.inserted_id) {
          onNewProjectAdd &&
            onNewProjectAdd(response.data.inserted_id.toString());
        }
        setAddNewProject(false);
      } else {
        notification.error({
          description: response?.message || "Something went wrong!",
        });
      }
      setIsLoading(false);
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
      setIsLoading(false);
    }
  };

  const staticValidationSchema = validateArray.reduce((acc, fieldName) => {
    acc[fieldName] = yup.string().required("This field is required.");

    return acc;
  }, {} as Record<string, yup.StringSchema>);

  const validationSchema = yup.object().shape({
    ...staticValidationSchema,
    project_id:
      is_custom_project_id === 0
        ? yup.string()
        : yup.string().required("This field is required."),
  });
  const initialFormValues = initValues;

  const formik = useFormik({
    initialValues: initialFormValues,
    onSubmit: handleSubmit,
    validationSchema,
  });
  const { errors } = formik;

  // useEffect(() => {
  //   if (addNewProjectAutoNumber) {
  //     formik.setFieldValue("project_id", addNewProjectAutoNumber);
  //   }
  // }, [addNewProjectAutoNumber]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType?: string
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      if (itemType == projectTypeID) {
        const newType = onEnterSelectSearchValue(event, projectTypeList || []);
        if (newType) {
          setCustomDataAdd({
            itemType: projectTypeID,
            name: `${HTMLEntities.decode(sanitizeString(newType))}`,
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      } else {
        const newType = onEnterSelectSearchValue(
          event,
          projectStatusList || []
        );
        if (newType) {
          setCustomDataAdd({
            itemType: projectStatusTypeID,
            name: `${HTMLEntities.decode(sanitizeString(newType))}`,
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      }
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: `${HTMLEntities.encode(customDataAdd?.name)}`,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        if (cDataRes.data) {
          if (customDataAdd?.itemType === projectTypeID?.toString()) {
            setProjectTypeList((prev = []) => {
              prev.push({
                label: cDataRes.data.name,
                value: cDataRes.data.key?.toString(),
                // value: cDataRes.data.item_id?.toString(),
              });
              return prev;
            });
          } else if (
            customDataAdd?.itemType === projectStatusTypeID?.toString()
          ) {
            setProjectStatusList((prev = []) => {
              prev.push({
                label: cDataRes.data.name,
                value: cDataRes.data.key?.toString(),
                // value: cDataRes.data.item_id?.toString(),
              });
              return prev;
            });
          }
        }
        delay(() => {
          if (customDataAdd?.itemType == projectTypeID) {
            formik.setFieldValue("project_type", cDataRes?.data?.key);
            // formik.setFieldValue("project_type", cDataRes?.data?.item_id);
          } else {
            formik.setFieldValue("project_status", cDataRes?.data?.key);
            // formik.setFieldValue("project_status", cDataRes?.data?.item_id);
          }
        }, 500);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  return (
    <>
      <Drawer
        open={addNewProject}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden ",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon className="w-4 h-4" icon="fa-regular fa-house" />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t("Add Project")}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton
            onClick={() => {
              setAddNewProject(false);
            }}
          />
        }
      >
        <form
          noValidate
          method="post"
          className="py-4"
          onSubmit={formik.handleSubmit}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    name="project_id"
                    id="project_id"
                    disabled={is_custom_project_id === 0}
                    isRequired={is_custom_project_id === 0 ? false : true}
                    label={_t("Project") + " #"}
                    labelPlacement="top"
                    onChange={(e) => {
                      formik.setFieldValue(
                        "project_id",
                        e.target.value.trimStart()
                      );
                      if (e.target.value?.trimStart()) {
                        formik.setFieldError("project_id", "");
                      } else {
                        formik.setFieldError(
                          "project_id",
                          _t("This field is required.")
                        );
                      }
                    }}
                    errorMessage={
                      isSubmit &&
                      !formik.values?.project_id?.trim() &&
                      is_custom_project_id !== 0
                        ? _t("This field is required.")
                        : formik.touched.project_id && errors.project_id
                        ? errors.project_id
                        : ""
                    }
                    value={
                      is_custom_project_id == 0
                        ? "Save To View"
                        : formik.values?.project_id
                    }
                    maxLength={20}
                  />
                </div>
                <div className="w-full">
                  <InputField
                    name="project_name"
                    label={_t("Project Name")}
                    labelPlacement="top"
                    isRequired={true}
                    onChange={formik.handleChange}
                    value={HTMLEntities.decode(
                      sanitizeString(formik.values.project_name)
                    )}
                    errorMessage={
                      isSubmit && !formik.values.project_name.trim()
                        ? _t("This field is required.")
                        : formik.touched.project_name && errors.project_name
                        ? errors.project_name
                        : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    name="project_type"
                    label={_t("Project Type")}
                    labelPlacement="top"
                    isRequired={true}
                    iconView={true}
                    showSearch
                    allowClear={true}
                    value={
                      projectTypeList
                        ? projectTypeList.filter(
                            (item) =>
                              "value" in item &&
                              formik.values.project_type?.toString() ===
                                item?.value?.toString()
                          )
                        : []
                    }
                    onChange={(val) => {
                      formik.setFieldValue("project_type", val || "");
                      setSelectFieldSearch("");
                    }}
                    errorMessage={
                      isSubmit && !formik.values.project_type
                        ? _t("This field is required.")
                        : formik.touched.project_type && errors.project_type
                        ? errors.project_type
                        : ""
                    }
                    addItem={addItemObject}
                    onInputKeyDown={(e) => handlekeyDown(e, projectTypeID)}
                    options={projectTypeList}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    name="project_status"
                    label={_t("Status")}
                    labelPlacement="top"
                    iconView={true}
                    showSearch
                    allowClear={false}
                    isRequired={true}
                    value={
                      projectStatusList
                        ? projectStatusList.filter(
                            (item) =>
                              // String(formik.values.project_status) ===
                              // String(item?.label?.toString())
                              "value" in item &&
                              formik.values.project_status?.toString() ===
                                item?.value?.toString()
                          )
                        : []
                    }
                    onChange={(val) => {
                      formik.setFieldValue("project_status", val || "");
                      setSelectFieldSearch("");
                    }}
                    errorMessage={
                      isSubmit && !formik.values.project_status
                        ? _t("This field is required.")
                        : formik.touched.project_status && errors.project_status
                        ? errors.project_status
                        : ""
                    }
                    addItem={addItemObject}
                    onInputKeyDown={(e) =>
                      handlekeyDown(e, projectStatusTypeID)
                    }
                    options={projectStatusList}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                  />
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              buttonText={_t("Create Project")}
              disabled={isLoading || isDataLoading}
              isLoading={isLoading}
            />
          </div>
        </form>
      </Drawer>
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon={faClipboardListCheck}
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name?.trim() || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => {
            closeConfirmationModal();
          }}
        />
      )}
    </>
  );
};

export default AddNewProject;
