import React, { useCallback, useEffect, useMemo, useState } from "react";
// hook, utils, redux, healper
import { useTranslation } from "~/hook";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// FontAwesome File
import { RNDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/rfi-and-notices/dashboard/regular";
// Other
import {
  GRID_BUTTON_TAB,
  GRID_RFI_TYPE,
} from "~/modules/document/rfi-and-notices/utils/constasnts";
import RFINoticesList from "~/modules/document/rfi-and-notices/components/dashboard/RFINoticesList";
import AddScheduleNotice from "~/modules/document/rfi-and-notices/components/sidebar/addScheduleNotice/AddScheduleNotice";
import { AddComplianceNotice } from "~/modules/document/rfi-and-notices/components/sidebar/addComplianceNotice";
import { AddRequestInformation } from "~/modules/document/rfi-and-notices/components/sidebar/addRequestInformation";
import { RadioChangeEvent } from "antd";
import { getGConfig, getGModuleFilters, updateModuleFilter } from "~/zustand";
import debounce from "lodash/debounce";
import { setSearchValueAct } from "~/modules/document/rfi-and-notices/redux/slices/dashboardSlice";
import {
  useAppRFIDispatch,
  useAppRFISelector,
} from "~/modules/document/rfi-and-notices/redux/store";
import RFIStoreProvider from "~/modules/document/rfi-and-notices/redux/RFIStoreProvider";
import RFIFilter from "~/modules/document/rfi-and-notices/components/dashboard/RFIFilter";
import { RNDashboardSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/rfi-and-notices/dashboard/solid";
import { SelectField } from "~/shared/components/molecules/selectField";
import ScheduleNoticeDistrbution from "~/modules/document/rfi-and-notices/components/dashboard/ScheduleNoticeDistrbution";
import ScheduleNoticeStatus from "~/modules/document/rfi-and-notices/components/dashboard/ScheduleNoticeStatus";
import ComplianceNoticeStatus from "~/modules/document/rfi-and-notices/components/dashboard/ComplianceNoticeStatus";
import RFIsStatus from "~/modules/document/rfi-and-notices/components/dashboard/RFIsStatus";
import ComplianceNoticeTypeDistribution from "~/modules/document/rfi-and-notices/components/dashboard/ComplianceNoticeTypeDistribution";
import ComplianceRecentNotice from "~/modules/document/rfi-and-notices/components/dashboard/ComplianceRecentNotice";
import RFIRecentSubmission from "~/modules/document/rfi-and-notices/components/dashboard/RFIRecentSubmission";
import ActionItems from "~/modules/document/rfi-and-notices/components/dashboard/ActionItems";
import RecentResolvedCompliance from "~/modules/document/rfi-and-notices/components/dashboard/RecentResolvedCompliance​";
import RecentResponseReceived from "~/modules/document/rfi-and-notices/components/dashboard/RecentResponseReceived";
import { fetchDashData } from "~/modules/document/rfi-and-notices/redux/action/dashboardAction";

// Fort Awesome Library Add icons
RNDashboardRegularIconAdd();
RNDashboardSolidIconAdd();
const ManageRfiNoticesCom = () => {
  const { _t } = useTranslation();
  const {
    module_id,
    module_name,
    module_access,
    module_singular_name,
  }: GConfig = getGConfig();
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [addScheduleNoticeOpen, setAddScheduleNoticeOpen] =
    useState<boolean>(false);
  const [addComplianceNoticeOpen, setAddComplianceNoticeOpen] =
    useState<boolean>(false);
  const [addRequestInformationOpen, setAddRequestInformationOpen] =
    useState<boolean>(false);
  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);
  const [isRFILoading, setIsRFILoading] = useState<boolean>(false);
  const [rfiNoticeType, setRfiNoticeType] = useState<any>({});

  const { searchValue, isDataFetched }: IRFIDashState = useAppRFISelector(
    (state) => state.dashboard
  );
  const dispatch = useAppRFIDispatch();
  const filterSrv =
    (getGModuleFilters() as Partial<RFIModuleFilter> | undefined) || {};
  const { filter_my_list } = filterSrv;

  const filter = useMemo(() => {
    return {
      tab_status: filterSrv?.tab_status || "",
      type: filterSrv?.type || "",
      type_names: filterSrv?.type_names || "",
      status: filterSrv?.status || "",
    };
  }, [filterSrv]);

  const RFI_BUTTON_TAB = [
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-people-group" />
          {`All Records`}
        </div>
      ),
      value: "0",
    },
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-user" />
          {`My Records`}
        </div>
      ),
      value: "1",
    },
  ];

  const ADD_RFI_NOTOCE_OPTION = [
    {
      label: "RFI (Request for Information)",
      icon: "fa-regular fa-plus",
      value: "add_rfi",
      onClick: () => {
        setAddRequestInformationOpen(true);
      },
    },
    {
      label: "Schedule Notice",
      icon: "fa-regular fa-plus",
      value: "add_schedule_notice",
      onClick: () => {
        setAddScheduleNoticeOpen(true);
      },
    },
    {
      label: "Compliance Notice",
      icon: "fa-regular fa-plus",
      value: "add_compliance_notice",
      onClick: () => {
        setAddComplianceNoticeOpen(true);
      },
    },
  ];

  const onMyLeadTabButtonChange = (e: RadioChangeEvent) => {
    try {
      const listValue = e.target.value as string;
      updateFilter({
        // sales_rep: listValue == "1" ? user_id?.toString() : "",
        filter_my_list: listValue == "1" ? "1" : "",
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  const updateFilter = useCallback(
    (filter: Partial<IInitialRFIData["filter"]>) => {
      if (module_id) {
        updateModuleFilter({
          filter,
          moduleId: module_id.toString(),
          onError: (response: IApiCallResponse) => {
            notification.error({
              description: response.message,
            });
          },
        });
      }
    },
    [module_id]
  );

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setSearchValueAct(value));
    }, 500),
    []
  );

  useEffect(() => {
    if (!!filter?.type_names) {
      setRfiNoticeType({});
    }
  }, [filter?.type_names]);

  useEffect(() => {
    if (!isDataFetched) {
      dispatch(fetchDashData());
    }
  }, [isDataFetched]);

  return (
    <>
      <DashboardHeader
        searchPlaceHolder={module_name ? _t(`Search for ${module_name}`) : ""}
        viewSearch={viewSearch}
        setViewSearch={setViewSearch}
        searchVal={searchValue}
        filterComponent={
          <RFIFilter
            onClearSearch={() => {
              dispatch(setSearchValueAct(""));
            }}
          />
        }
        onSearchChange={debouncedOnSearchChange}
        rightComponent={
          <div className="flex flex-row sm:items-center items-start sm:gap-5 gap-2">
            {module_access === "full_access" ||
            module_access === "own_data_access" ? (
              <li>
                <DropdownMenu
                  contentClassName="w-[250px] add-items-drop-down"
                  options={ADD_RFI_NOTOCE_OPTION}
                  buttonClass="w-fit h-[26px] !bg-blue-100 hover:!bg-blue-100 p-0 add-select-dropdown rounded-r-sm"
                  children={
                    <div className="flex items-center gap-2">
                      <div className="h-[26px] w-6 flex items-center justify-center text-white bg-primary-900 rounded-l dark:!bg-dark-950">
                        <FontAwesomeIcon
                          icon="fa-regular fa-plus"
                          className="w-[13px] h-[13px] m-auto !text-white dark:!text-white"
                        />
                      </div>
                      <Typography className="text-13 text-primary-900 font-semibold">
                        {_t(module_singular_name ?? "RFI & Notice")}
                      </Typography>
                      <FontAwesomeIcon
                        className="pr-2 w-3 h-3 text-primary-900"
                        icon="fa-regular fa-chevron-down"
                      />
                    </div>
                  }
                />
              </li>
            ) : null}
          </div>
        }
      />
      <div
        className={`md:pt-[41px] pt-[39px] overflow-y-auto overflow-hidden ${
          !window.ENV.PAGE_IS_IFRAME
            ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
            : "h-screen"
        }`}
      >
        <ReadOnlyPermissionMsg view={module_access === "read_only"} />
        <div className="p-4">
          <div
            className={`grid 2xl:grid-cols-8 xl:grid-cols-6 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
              fullScreenTable
                ? "max-h-0 overflow-hidden"
                : "2xl:max-h-[900px] xl:max-h-[1100px] md:max-h-[1380px] max-h-[2780px]"
            }`}
          >
            <div className="common-card xl:col-span-2 h-[232px] 2xl:order-1 order-1">
              <ScheduleNoticeDistrbution />
            </div>
            <div className="common-card xl:col-span-2 h-[232px] 2xl:order-2 order-2">
              <ScheduleNoticeStatus />
            </div>
            <div className="common-card xl:col-span-2 h-[232px] 2xl:order-3 order-3">
              <ComplianceNoticeStatus />
            </div>
            <div className="common-card xl:col-span-2 h-[232px] 2xl:order-4 order-4">
              <RFIsStatus />
            </div>
            <div className="common-card xl:col-span-2 h-[232px] 2xl:order-5 order-5">
              <ComplianceNoticeTypeDistribution />
            </div>
            <div className="common-card xl:col-span-3 h-[232px] 2xl:order-6 order-7">
              <ComplianceRecentNotice />
            </div>
            <div className="common-card xl:col-span-3 h-[232px] 2xl:order-7 order-8">
              <RFIRecentSubmission />
            </div>
            <div className="common-card xl:col-span-2 h-[232px] 2xl:order-8 order-6">
              <ActionItems />
            </div>
            <div className="common-card xl:col-span-3 h-[232px] 2xl:order-9 order-9">
              <RecentResolvedCompliance />
            </div>
            <div className="common-card xl:col-span-3 h-[232px] 2xl:order-10 order-10">
              <RecentResponseReceived />
            </div>
          </div>
          <div
            className={`w-full ${
              fullScreenTable ? "lg:mt-2.5 mt-10" : "lg:mt-7 mt-14"
            }`}
          >
            <div className="relative h-7 z-[999] flex items-center justify-end">
              <AccordionButton
                onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                fullScreenTable={fullScreenTable}
              />
              <div className="flex justify-between items-center w-full sm:mb-7 mb-20 flex-wrap md:flex-nowrap">
                {module_access !== "own_data_access" && (
                  <div className="w-fit p-1 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0">
                    <ListTabButton
                      // RadioGroupClassName="sm:block"
                      value={filter_my_list == "1" ? "1" : "0"}
                      options={RFI_BUTTON_TAB}
                      activeclassName="active:bg-[#ffffff] !text-primary-900 !rounded-md"
                      className="sm:min-w-[100px] min-w-fit sm:px-1.5 !text-[#868D8D] px-2 !border-transparent bg-[#EEEFF0]"
                      onChange={onMyLeadTabButtonChange}
                    />
                  </div>
                )}
              </div>
              <div className="w-full flex text-end lg:mb-7 mb-20">
                <div className="w-full flex gap-2 text-end justify-end">
                  <div>
                    <SelectField
                      disabled={isRFILoading || !!filter?.type}
                      label=""
                      popupClassName="!w-[230px]"
                      className="bg-[#EBF1F9] disabled:opacity-50 text-left header-dropdown-select pl-3 !h-7 min-w-[150px] rounded"
                      fieldClassName="before:hidden"
                      placement="bottomRight"
                      value={rfiNoticeType?.type_names || ""}
                      labelPlacement="left"
                      options={GRID_RFI_TYPE}
                      onChange={(value: string | string[]) => {
                        let data = {
                          type: Number(value) || "",
                          type_names: "",
                        };
                        if (Number(value) == 122) {
                          data.type_names = "Schedule Notice";
                        }
                        if (Number(value) == 123) {
                          data.type_names = "Compliance Notice";
                        }
                        if (Number(value) == 124) {
                          data.type_names = "Request For Information";
                        }
                        setRfiNoticeType(data?.type_names !== "" ? data : {});
                      }}
                    />
                  </div>
                  <div>
                    <SelectField
                      label=""
                      popupClassName="!w-[150px]"
                      className="bg-[#EBF1F9] disabled:opacity-50 text-left header-dropdown-select pl-3 !h-7 min-w-[150px] rounded"
                      fieldClassName="before:hidden"
                      placement="bottomRight"
                      value={filter.tab_status || "all"}
                      labelPlacement="left"
                      options={GRID_BUTTON_TAB}
                      onChange={(value) => {
                        updateFilter({
                          tab_status: value as string,
                        });
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
              <RFINoticesList
                setIsRFILoading={setIsRFILoading}
                search={searchValue || ""}
              />
            </div>
          </div>
        </div>
      </div>
      {addRequestInformationOpen && (
        <AddRequestInformation
          addRequestInformationOpen={addRequestInformationOpen}
          setAddRequestInformationOpen={setAddRequestInformationOpen}
        />
      )}
      {addScheduleNoticeOpen && (
        <AddScheduleNotice
          addScheduleNoticeOpen={addScheduleNoticeOpen}
          setAddScheduleNoticeOpen={setAddScheduleNoticeOpen}
        />
      )}
      {addComplianceNoticeOpen && (
        <AddComplianceNotice
          addComplianceNoticeOpen={addComplianceNoticeOpen}
          setAddComplianceNoticeOpen={setAddComplianceNoticeOpen}
        />
      )}
    </>
  );
};

const ManageRfiNotices = () => {
  return (
    <RFIStoreProvider>
      {" "}
      <ManageRfiNoticesCom />{" "}
    </RFIStoreProvider>
  );
};
export default React.memo(ManageRfiNotices);

export { ErrorBoundary };
