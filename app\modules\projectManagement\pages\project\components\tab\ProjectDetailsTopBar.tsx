// fortawesome
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Hooks
import { useTranslation } from "~/hook";
// Atoms
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
// Other
import {
  ProjectStatusIconMap,
  ProjectfieldStatus,
} from "../../utils/constants";
import {
  getGConfig,
  getGModuleDashboard,
  setCommonSidebarCollapse,
  useGModules,
} from "~/zustand"; // In future this code move in redux, developer change this code
import { useAppProDispatch, useAppProSelector } from "../../redux/store";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Number, replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { getStatusForField } from "~/shared/utils/helper/common";
import {
  updateProjectApi,
  updateProjectContactsApi,
} from "../../redux/action/proDashAction";
import { useNavigate, useParams } from "@remix-run/react";
import delay from "lodash/delay";
import { ProjectListTableDropdownItems } from "../dashboard/ProjectListTableDropdownItems";
import ProjectStatusbar from "./projectStatusbar";
import { MenuProps } from "antd";
import { updateProjectDetail } from "../../redux/slices/proDetailsSlice";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import dayjs from "dayjs";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { getGlobalUser } from "~/zustand/global/user/slice";
import {
  getDirectaryIdByName,
  getDirectaryKeyById,
  setSendEmailOpenStatus,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import {
  fetchProjectContactsApi,
  fetchProjectDetailsApi,
} from "../../redux/action/projectDetailsAction";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";

const ProjectDetailsTopBar = (
  {
    sidebarCollapse,
    inputValues,
    setInputValues,
    onReloadDetails: onReloadProDetails,
    handleChangeFieldStatus,
    loadingStatus,
  }: IProjectDetailsTopBarProps // Developer side change this
) => {
  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();
  const params = useParams();
  const dispatch = useAppProDispatch();
  const gConfig: GConfig = getGConfig();
  // const [loadingStatus, setLoadingStatus] =
  //   useState<Array<IFieldStatus>>(ProjectfieldStatus);
  const { details, isDetailLoading }: IProjectDetailsInitialState =
    useAppProSelector((state) => state.proDetails);

  const [projectStatus, setProjectStatus] = useState(details?.project_status);
  const [openProjectContactAddDrawer, setOpenProjectContactAddDrawer] =
    useState<boolean>(false);
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);

  const user: IInitialGlobalData["user"] = getGlobalUser();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const { user_id, company_id } = user || {};
  const {
    date_format = "",
    image_resolution = "",
    save_a_copy_of_sent_pdf = 0,
    disable_client_when_project_completed = 1,
    is_custom_project_id = "",
  } = appSettings || {};
  const {
    module_id = 0,
    module_access = "read_only",
    module_key = "",
  } = currentMenuModule || {};

  const navigate = useNavigate();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();

  const dateFormatToConsider = useMemo(() => {
    return CFConfig.day_js_date_format;
  }, [CFConfig.day_js_date_format]);

  const StatusbarOption: Array<ModuleStatus> | undefined =
    gModuleDashboard?.module_setting?.module_status;

  const isClientAccessDisabled = useMemo(() => {
    // return Number(details.show_client_access) === 0;
    return (
      (disable_client_when_project_completed === 1 &&
        details.project_status === "completed") ||
      Number(details.show_client_access) === 0
    );
  }, [
    disable_client_when_project_completed,
    details.project_status,
    details.show_client_access,
  ]);

  useEffect(() => {
    setProjectStatus(inputValues.project_status);
  }, [inputValues]);

  // const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
  //   const checkStatus = loadingStatus.find(
  //     (item: IFieldStatus) => item?.field === field
  //   );
  //   if (
  //     !(
  //       (checkStatus?.status === "loading" ||
  //         checkStatus?.status === "success" ||
  //         checkStatus?.status === "save") &&
  //       (action === "ME" || action === "ML")
  //     )
  //   ) {
  //     setLoadingStatus((prevState: IFieldStatus[]) =>
  //       prevState.map((item) =>
  //         item.field === field ? { ...item, status: status } : item
  //       )
  //     );
  //   }
  // };

  const handleInpOnChange = ({
    target: { name, value },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const isCustomstatus = useMemo(() => {
    const statusList =
      StatusbarOption?.filter(
        (itm: TExtendedProjectModuleStatus) =>
          itm?.show_in_progress_bar !== "1" && itm.is_status === "1"
      ) || [];

    const statusExists = statusList.some((item) => item.key === projectStatus);

    return statusExists;
  }, [projectStatus, StatusbarOption]);

  const handleUpdateField = async (data: Partial<IProjectDetails>) => {
    const field = Object.keys(data)[0] as keyof IProjectDetails;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateProjectApi({
      ...data,
      module_key: module_key,
      id: params?.id,
    })) as IProjectDetailUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      let data_to_send_in_store: Partial<IProjectDetails> = data;
      if (data.project_status && data.project_status === "started") {
        data_to_send_in_store.start_date = dayjs()
          .startOf("day")
          .format(dateFormatToConsider);
      }
      if (data.project_status && data.project_status === "completed") {
        data_to_send_in_store.completion_date = dayjs()
          .startOf("day")
          .format(dateFormatToConsider);
        data_to_send_in_store.end_date = dayjs()
          .startOf("day")
          .format(dateFormatToConsider);
      }
      dispatch(updateProjectDetail({ ...data_to_send_in_store }));
    } else {
      notification.error({
        description: updateRes.message,
      });
      if (field === "project_status") {
        setProjectStatus(details.project_status);
        setInputValues((prev) => {
          return {
            ...prev,
            project_status: details.project_status,
            project_status_name: details.project_status_name,
          };
        });
      }
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const projectStatusList: IProjectStatusList[] = useMemo(
    () =>
      (
        StatusbarOption?.filter(
          (itm: TExtendedProjectModuleStatus) =>
            itm?.show_in_progress_bar === "1" && itm.is_status === "1"
        ) || []
      )
        .map((item, index): IProjectStatusList | null => {
          if (item && item.name) {
            return {
              label: replaceDOMParams(sanitizeString(item.name)) || "",
              value: item.key?.toString() || "",
              default_color: item.status_color || "",
              icon: item.key
                ? ProjectStatusIconMap[item.key]
                : "fa-regular fa-lock-keyhole-open",
              index,
            };
          }
          return null;
        })
        .filter((item): item is IProjectStatusList => item !== null),
    [StatusbarOption]
  );

  const status = useMemo(() => {
    const statusList = StatusbarOption?.filter(
      (item: TExtendedProjectModuleStatus) => item.is_status === "1"
    )?.map((item: ModuleStatus) => ({
      label: replaceDOMParams(sanitizeString(item.name)),
      key: item?.key?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.status_color,
          }}
        />
      ),
    }));
    const getSelectStatus = StatusbarOption?.find(
      (item) => item.key?.toString() === projectStatus?.toString()
    );

    const selectStatus = (
      <Tooltip
        title={replaceDOMParams(sanitizeString(getSelectStatus?.name)) || ""}
      >
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.status_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.status_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {replaceDOMParams(sanitizeString(getSelectStatus?.name)) || ""}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.status_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [StatusbarOption, projectStatusList, details, projectStatus, inputValues]);

  const handleStatus: MenuProps["onClick"] = (e) => {
    setProjectStatus(e.key);

    if (e.key !== inputValues?.project_status) {
      const selectedOption = status?.statusList?.find(
        (option) => option.key === e.key?.toString()
      );

      if (selectedOption) {
        handleUpdateField({
          project_status: e.key,
          project_status_name: selectedOption.label,
        });
        setInputValues({
          ...inputValues,
          project_status: e.key,
          project_status_name: selectedOption.label,
        });
      } else {
        const description = "Selected option not found in statusList";
        notification.error({
          description,
        });
      }
    }
  };

  const currentOptionIcon = useMemo(() => {
    let curOptIcon =
      projectStatusList.find(
        (option) => option.value === inputValues?.project_status
      )?.icon ?? null;

    return curOptIcon;
  }, [projectStatusList, StatusbarOption, isCustomstatus, inputValues]);

  const selectedContactsForEmail = useMemo(() => {
    const siteManager = {
      user_id: Number(details?.site_manager_id || 0),
      contact_id: details?.site_manager_contact_id ?? "",
      display_name: HTMLEntities.decode(
        sanitizeString(details?.site_manager_name ?? "")
      ),
      type_key:
        details?.site_manager_type === 1 ? 2 : details.site_manager_type,
      type_name: getDirectaryIdByName(
        Number(details?.site_manager_type) === 1
          ? 2
          : Number(details.site_manager_type)
      ),
      email: details?.site_manager_email,
      image: details?.site_manager_image ?? "",
    };

    const projectManager = {
      user_id: Number(details?.project_manager_id || 0),
      display_name: HTMLEntities.decode(
        sanitizeString(details?.project_manager_name ?? "")
      ),
      type_key:
        details?.project_manager_type === 1 ? 2 : details.project_manager_type,
      type_name: getDirectaryIdByName(
        Number(details?.project_manager_type) === 1
          ? 2
          : Number(details.project_manager_type)
      ),
      email: details?.project_manager_email,
      image: details?.project_manager_image ?? "",
    };

    const safetyContact = {
      user_id: Number(details?.safety_contact_id || 0),
      contact_id: details?.safety_additional_contact_id ?? "",
      display_name: HTMLEntities.decode(
        sanitizeString(details?.safety_contact_name ?? "")
      ),
      type_key:
        details?.safety_contact_type === 1 ? 2 : details.safety_contact_type,
      type_name: getDirectaryIdByName(
        Number(details?.safety_contact_type ?? "") === 1
          ? 2
          : Number(details.safety_contact_type ?? "")
      ),
      email: details?.safety_contact_email,
      image: details?.safety_contact_image ?? "",
    };

    const salesRep = {
      user_id: Number(details?.sales_rep || 0),
      display_name: HTMLEntities.decode(
        sanitizeString(details?.sales_rep_name ?? "")
      ),
      type_key: details?.sales_rep_type === 1 ? 2 : details.sales_rep_type,
      type_name: getDirectaryIdByName(
        Number(details?.sales_rep_type ?? "") === 1
          ? 2
          : Number(details.sales_rep_type ?? "")
      ),
      email: details?.sales_rep_email,
      image: details?.sales_rep_image ?? "",
    };

    const estimator = {
      user_id: Number(details?.estimator || 0),
      display_name: HTMLEntities.decode(
        sanitizeString(details?.estimator_name ?? "")
      ),
      type_key: details?.estimator_type === 1 ? 2 : details.estimator_type,
      type_name: getDirectaryIdByName(
        Number(details?.estimator_type ?? "") === 1
          ? 2
          : Number(details.estimator_type ?? "")
      ),
      email: details?.estimator_email,
      image: details?.estimator_image ?? "",
    };

    const mainCustomer = {
      user_id: Number(details?.customer_id || 0),
      contact_id: details?.customer_contact_id ?? "",
      display_name: HTMLEntities.decode(
        sanitizeString(details?.customer_name ?? "")
      ),
      type_key:
        details?.customer_type_key === 1 ? 2 : details.customer_type_key,
      type_name: getDirectaryIdByName(
        Number(details?.customer_type_key ?? "") === 1
          ? 2
          : Number(details.customer_type_key ?? "")
      ),
      email: details?.cust_email,
      image: details?.cust_image ?? "",
    };

    const projectContacts =
      details?.contacts?.map((c) => ({
        user_id: Number(c?.directory_id || 0),
        contact_id: c?.directory_contact_id,
        display_name: HTMLEntities.decode(
          sanitizeString(c?.display_name ?? "")
        ),
        type_key: c?.dir_type === 1 ? 2 : c.dir_type,
        type_name: getDirectaryIdByName(
          Number(c?.dir_type ?? "") === 1 ? 2 : Number(c.dir_type ?? "")
        ),
        email: c?.email,
        image: c?.image ?? "",
      })) ?? [];

    const allContacts = [
      siteManager,
      projectManager,
      safetyContact,
      salesRep,
      estimator,
      mainCustomer,
      ...projectContacts,
    ];

    // Filter unique by `user_id`
    const uniqueByUserId = Array.from(
      new Map(allContacts.map((item) => [item.user_id, item])).values()
    );

    return uniqueByUserId?.filter(
      (u) => Number(u.user_id) > 0
    ) as TselectedContactSendMail[];
  }, [details]);

  const selectedProjectContactData = useMemo(() => {
    return details?.contacts?.length
      ? (details?.contacts?.map((c) => {
          return {
            display_name: c?.display_name,
            contact_id: c?.directory_contact_id,
            user_id: c?.directory_id,
            type: c.dir_type,
            image: c.image,
            type_key: getDirectaryKeyById(
              Number(c.dir_type) === 1 ? 2 : Number(c.dir_type),
              undefined
            ),
          };
        }) as TselectedContactSendMail[])
      : [];
  }, [details?.contacts]);

  const handleProjectContactSelection = useCallback(
    async (data: Partial<TselectedContactSendMail>[]) => {
      let updateContact = null;

      if (Array.isArray(data)) {
        if (data?.length > 0) {
          const newIds = data
            .map((c) =>
              Number(c.contact_id ?? 0)
                ? `${c.user_id}|${c.contact_id}`
                : c.user_id
            )
            .join(",");

          const isSameIds =
            data.length === details?.contacts?.length &&
            data.every((c) =>
              details?.contacts?.some(
                (d) => Number(d.directory_id) === Number(c.user_id)
              )
            );

          if (!isSameIds) {
            updateContact = {
              project_contacts: newIds,
            };
          }
        } else {
          if (!details?.contacts?.length) {
            return;
          }
          updateContact = {
            project_contacts: "",
          };
        }

        if (updateContact) {
          const updateContactsRes = (await updateProjectContactsApi({
            ...updateContact,
            user_id: user_id?.toString() ?? "",
            company_id: company_id?.toString() ?? "",
            key_name: "Project Contacts",
            id: params?.id,
          })) as IProjectDetailUpdateApiRes;

          if (updateContactsRes.success) {
            dispatch(
              fetchProjectContactsApi({
                project_id: Number(details.id || "0"),
                record_type: "project",
              })
            );
          } else {
            notification.error({
              description:
                updateContactsRes.message ||
                "Failed to update project contacts",
            });
          }
        }
      }
    },
    [params?.id, details?.contacts]
  );

  const handleEmailApiCall = useCallback(
    async (
      tempFormData: SendEmailFormDataWithApiDefault,
      closeSendMailSidebar: () => void,
      ccMailCopy: boolean
    ) => {
      const formData = {
        ...tempFormData,
        send_me_copy: ccMailCopy ? 1 : 0,
        send_custom_email: 0,
        record_id: Number(details?.id),
        module_id: module_id,
        module_key: module_key,
        action: "send",
        op: "send_custom_email",
      };
      try {
        const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
        if (res) {
          if (res.success) {
            closeSendMailSidebar();
            setSendEmailOpenStatus(false);
          } else {
            notification.error({
              description: res.message,
            });
          }
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "",
        });
      }
    },
    []
  );

  return (
    <>
      <div className="pro-status-bar sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isDetailLoading || !StatusbarOption ? (
              <TopBarSkeleton statusList={true} num={5} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] !mr-auto xl:w-[calc(40%-200px)] md:w-[calc(100%-150px)] w-full 2xl:min-w-64">
                  <div
                    className={`w-11 h-11 flex items-center justify-center rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white 
                    ${
                      `bg-[${status?.getSelectStatus?.status_color}]` ||
                      "bg-[#C7A27C]"
                    }`}
                    style={{
                      backgroundColor: status?.getSelectStatus?.status_color,
                    }}
                  >
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon={currentOptionIcon || "fa-light fa-bars-progress"}
                    />
                  </div>
                  <div className="pl-2.5 flex flex-col gap-0.5 max-w-[calc(100%-50px)] w-full">
                    <div className="flex items-center gap-1">
                      <Tooltip title={_t("Project Color")} placement="top">
                        <Typography
                          style={{
                            backgroundColor:
                              inputValues.project_color || "#000000",
                          }}
                          className={`h-2.5 w-2.5 min-w-2.5 rounded-full bg-[${
                            inputValues.project_color || "#000000"
                          }]`}
                        ></Typography>
                      </Tooltip>
                      <Tooltip
                        title={`Project: ${HTMLEntities.decode(
                          sanitizeString(inputValues?.project_name || "")
                        )}`}
                        placement="topLeft"
                      >
                        <div
                          className={`max-w-full  ${
                            gConfig?.module_read_only
                              ? "w-fit"
                              : "xl:w-full sm:w-1/2 w-full"
                          }`}
                        >
                          <InputField
                            placeholder="Project"
                            labelPlacement="left"
                            name="project_name"
                            id="project_name"
                            className="h-7 !text-base font-medium"
                            formInputClassName="ellipsis-input-field"
                            readOnlyClassName="font-medium whitespace-nowrap truncate block"
                            labelClass="hidden"
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            value={HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name || "")
                            )}
                            maxLength={200}
                            disabled={isReadOnly}
                            editInline={true}
                            iconView={true}
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "project_name"
                            )}
                            onFocus={() => {
                              handleChangeFieldStatus({
                                field: "project_name",
                                status: "save",
                                action: "FOCUS",
                              });
                            }}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "project_name",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "project_name",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onBlur={(e) => {
                              const value = e?.target?.value.trim();
                              if (value === "") {
                                notification.error({
                                  description: "Project Name is required",
                                });
                              }
                              if (
                                value !== details?.project_name &&
                                value !== ""
                              ) {
                                handleUpdateField({ project_name: value });
                              } else {
                                handleChangeFieldStatus({
                                  field: "project_name",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  project_name: details.project_name,
                                });
                              }
                            }}
                            onChange={handleInpOnChange}
                          />
                        </div>
                      </Tooltip>
                    </div>
                    <div className="flex items-center 2xl:gap-2 xl:gap-1 gap-2  mt-[3px]">
                      <Tooltip
                        title={
                          isClientAccessDisabled
                            ? _t("Client Access is disabled for this Project")
                            : _t("Client Access is enabled for this Project")
                        }
                        placement="top"
                      >
                        <div className="-ml-0.5">
                          <FontAwesomeIcon
                            className={`w-3.5 h-3.5 text-primary-900 dark:text-white/90 ${
                              isClientAccessDisabled ? "opacity-50" : ""
                            }`}
                            icon={
                              isClientAccessDisabled
                                ? "fa-regular fa-user"
                                : "fa-solid fa-user"
                            }
                          />
                        </div>
                      </Tooltip>
                      <div className="flex gap-1">
                        <Dropdown
                          menu={{
                            items: status.statusList,
                            selectable: true,
                            selectedKeys: details.project_status
                              ? [projectStatus?.toString() ?? ""]
                              : [],
                            onClick: handleStatus,
                          }}
                          disabled={
                            isReadOnly ||
                            getStatusForField(
                              loadingStatus ?? [],
                              "project_status"
                            ) == "loading"
                          }
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(
                            loadingStatus ?? [],
                            "project_status"
                          )
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(
                              loadingStatus ?? [],
                              "project_status"
                            )}
                          />
                        )}
                      </div>

                      <Tooltip
                        title={`${HTMLEntities.decode(
                          sanitizeString(inputValues?.project_id || "")
                        )}`}
                        placement="topLeft"
                      >
                        <div
                          className={`max-w-full  ${
                            gConfig?.module_read_only
                              ? "w-fit"
                              : "xl:w-full sm:w-1/2 w-full"
                          }`}
                        >
                          <InputField
                            placeholder="Project #"
                            labelPlacement="left"
                            name="project_id"
                            id="project_id"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] text-sm font-medium py-0"
                            readOnlyClassName="text-sm font-medium whitespace-nowrap truncate block"
                            labelClass="hidden"
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            value={HTMLEntities.decode(
                              sanitizeString(inputValues?.project_id || "")
                            )}
                            maxLength={15}
                            disabled={
                              isReadOnly || is_custom_project_id !== 0
                                ? false
                                : true
                            }
                            editInline={true}
                            iconView={true}
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "project_id"
                            )}
                            onFocus={() => {
                              handleChangeFieldStatus({
                                field: "project_id",
                                status: "save",
                                action: "FOCUS",
                              });
                            }}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "project_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "project_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onBlur={(e) => {
                              const value = e?.target?.value.trim();
                              if (value === "") {
                                notification.error({
                                  description: "Project # is required.",
                                });
                              }
                              if (
                                value !== details?.project_id &&
                                value !== ""
                              ) {
                                handleUpdateField({ project_id: value });
                              } else {
                                handleChangeFieldStatus({
                                  field: "project_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  project_id: details.project_id,
                                });
                              }
                            }}
                            onChange={handleInpOnChange}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                <div className="w-auto flex-[0_0_auto]">
                  <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                    {!isDetailLoading && !isCustomstatus && (
                      <ProjectStatusbar
                        inputValues={inputValues}
                        setInputValues={setInputValues}
                        setProjectStatus={setProjectStatus}
                        handleUpdateField={handleUpdateField}
                        handleChangeFieldStatus={handleChangeFieldStatus}
                        projectStatusList={projectStatusList}
                        isReadOnly={isReadOnly}
                      />
                    )}
                  </ul>
                </div>
                <div className="flex justify-between xl:flex-[1_0_0%] xl:w-[calc(40%-200px)] md:w-fit w-full">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <IconButton
                      htmlType="button"
                      variant="default"
                      className="md:!hidden group/module-menu relative w-[34px] min-w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                      iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                      icon="fa-regular fa-bars"
                      onClick={() => setCommonSidebarCollapse(!sidebarCollapse)}
                    />
                  </div>
                  <ul className="flex items-center justify-end gap-2.5">
                    {params?.tab?.toLowerCase() === "contacts" && (
                      <>
                        <li>
                          <ButtonWithTooltip
                            tooltipTitle={_t("Add Project Contact")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-user-plus"
                            disabled={isDetailLoading || isReadOnly}
                            iconClassName={`!text-primary-900
                            ${
                              isDetailLoading
                                ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                                : "group-hover/buttonHover:!text-deep-orange-500"
                            }`}
                            className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                              isDetailLoading
                                ? "hover:bg-transparent"
                                : "hover:!bg-deep-orange-500/5"
                            }`}
                            onClick={() => setOpenProjectContactAddDrawer(true)}
                          />
                        </li>
                        <li>
                          <ButtonWithTooltip
                            tooltipTitle={_t("Email Project Contact")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-envelope"
                            disabled={isDetailLoading || isReadOnly}
                            iconClassName={`!text-primary-900
                            ${
                              isDetailLoading
                                ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                                : "group-hover/buttonHover:!text-deep-orange-500"
                            }`}
                            className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                              isDetailLoading
                                ? "hover:bg-transparent"
                                : "hover:!bg-deep-orange-500/5"
                            }`}
                            onClick={() => setSendEmailOpen(true)}
                          />
                        </li>
                      </>
                    )}
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        disabled={isDetailLoading}
                        iconClassName={`!text-primary-900
                        ${
                          isDetailLoading
                            ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                            : "group-hover/buttonHover:!text-deep-orange-500"
                        }`}
                        className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                          isDetailLoading
                            ? "hover:bg-transparent"
                            : "hover:!bg-deep-orange-500/5"
                        }`}
                        onClick={onReloadProDetails}
                      />
                    </li>
                    <li>
                      <ProjectListTableDropdownItems
                        data={details}
                        icon="fa-regular fa-ellipsis-vertical"
                        className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                        contentClassName="menu-h-scroll"
                        refreshTable={() => {
                          onReloadProDetails();
                        }}
                        isDetailView={true}
                      />
                    </li>
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {openProjectContactAddDrawer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={openProjectContactAddDrawer}
          closeDrawer={() => setOpenProjectContactAddDrawer(false)}
          singleSelecte={false}
          setCustomer={(data) => {
            handleProjectContactSelection(data);
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
          ]}
          selectedCustomer={selectedProjectContactData}
          groupCheckBox={true}
          projectId={Number(details?.id)}
          additionalContactDetails={1}
          modulesShouldNotHaveAddButton={
            params?.tab?.toLowerCase() !== "contacts" ? ["employee"] : []
          }
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
            setSendEmailOpenStatus(false);
          }}
          isViewAttachment={false}
          openSendEmailSidebar={sendEmailOpen}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
          ]}
          singleSelecte={false}
          emailApiCall={handleEmailApiCall}
          customEmailData={{
            subject: "",
          }}
          selectedCustomer={selectedContactsForEmail}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              CFConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
            save_a_copy_of_sent_pdf,
          }}
        />
      )}
    </>
  );
};

export default ProjectDetailsTopBar;
