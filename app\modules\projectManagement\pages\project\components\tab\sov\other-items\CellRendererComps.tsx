import React from "react";
import { Number, sanitizeString } from "~/helpers/helper";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { getItemTypeIcon } from "~/shared/utils/helper/common";
import { getGlobalInvoicedRemainFormat } from "../../../utils/common-util";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

export const MoveCellRenderer = React.memo(
  (params: TOtherItemsCellRenderer) => {
    if (params.node.rowPinned) return null;

    return (
      <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
        <FontAwesomeIcon
          className="w-4 h-4 text-[#4b5a76]"
          icon="fa-solid fa-grip-dots"
        />
      </div>
    );
  }
);

export const TypeCellRenderer = React.memo(
  (params: BaseCellRendererProps<Pick<ICellRendererProps, "_t">>) => {
    const { data } = params;
    if (params.node.rowPinned) {
      return null;
    }

    const typeName = HTMLEntities.decode(
      sanitizeString(data.item_type_name || "")
    );

    return (
      <>
        {data?.item_type ? (
          <Tooltip title={params?._t(typeName)}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              // icon="fa-regular fa-boxes-stacked"
              icon={getItemTypeIcon({
                type: data?.item_type?.toString() || "",
              })}
            />
          </Tooltip>
        ) : (
          <></>
        )}
      </>
    );
  }
);

export const ItemNameCellRenderer = React.memo(
  (params: BaseCellRendererProps<Pick<ICellRendererProps, "_t">>) => {
    if (params.node.rowPinned) {
      return null;
    }

    const { data } = params;
    const itemName = HTMLEntities.decode(sanitizeString(data.subject || ""));
    return (
      <Tooltip title={params?._t(itemName)}>
        <Typography className="table-tooltip-text">
          {params?._t(itemName)}
        </Typography>
      </Tooltip>
    );
  }
);

export const QtyCellRenderer = React.memo(
  (params: BaseCellRendererProps<Pick<ICellRendererProps, "formatter">>) => {
    if (params.node.rowPinned) {
      return null;
    }
    const { data } = params;

    return params?.formatter((data?.quantity || 0)?.toString()).value;
  }
);

export const CostCellRenderer = React.memo(
  (params: BaseCellRendererProps<Pick<ICellRendererProps, "formatter">>) => {
    const { data, formatter } = params;
    if (params.node.rowPinned) {
      return null;
    }

    const unitCost = formatter(
      Number(data?.unit_cost) !== 0
        ? (Number(data?.unit_cost) / 100 || 0).toFixed(2)
        : "0.00"
    ).value_with_symbol;

    return (
      <Tooltip title={unitCost}>
        <Typography className="table-tooltip-text">{unitCost}</Typography>
      </Tooltip>
    );
  }
);

export const UnitCellRenderer = React.memo(
  (params: BaseCellRendererProps<Pick<ICellRendererProps, "_t">>) => {
    const { data, _t } = params;
    if (params.node.rowPinned) {
      return null;
    }

    const unit = HTMLEntities.decode(sanitizeString(data.unit || ""));

    return (
      <Tooltip title={_t(unit)}>
        <Typography className="table-tooltip-text">
          {_t(unit) || "-"}
        </Typography>
      </Tooltip>
    );
  }
);

export const MUCellRenderer = React.memo(
  (params: BaseCellRendererProps<Pick<ICellRendererProps, "formatter">>) => {
    const { data, formatter } = params;
    if (params.node.rowPinned) {
      return params.value;
    }

    const is_markup_percentage = data?.is_markup_percentage ?? "";
    const total =
      Number(data?.quantity ?? 0) * (Number(data?.unit_cost ?? 0) / 100);
    const markup = Number(data?.markup);

    let muToShow = `${formatter("0").value}%`; // Default value

    if (is_markup_percentage?.toString() === "1") {
      muToShow = `${
        formatter(Number(markup) ? Math.round(markup ?? 0).toString() : "0.00")
          .value
      }%`;
    } else {
      if (total != 0) {
        const markupPercentage = markup / total - 100;
        muToShow = `${formatter(markupPercentage.toString())?.value}%`;
      }
    }

    return (
      <Tooltip title={muToShow}>
        <Typography className="table-tooltip-text">{muToShow}</Typography>
      </Tooltip>
    );
  }
);

export const InvoicedCellRenderer = React.memo(
  (
    params: BaseCellRendererProps<
      Pick<ICellRendererProps, "formatter" | "isPercentageBilling">
    >
  ) => {
    const { data } = params;
    if (params.node.rowPinned) {
      return (
        <div className="flex gap-1 overflow-hidden w-full justify-end">
          <Tooltip title={params.value}>
            <Typography className="table-tooltip-text">
              {params.value}
            </Typography>
          </Tooltip>
        </div>
      );
    }

    const currentAmount = Number(data?.current_amount || 0);
    const billed = params?.formatter(
      currentAmount !== 0 ? (currentAmount / 100)?.toFixed(2) : "0.00"
    ).value_with_symbol;
    const billPaid = Number(data?.bill_paid) ?? "0.00";

    return (
      <div className="flex gap-1 overflow-hidden w-full justify-end">
        <Tooltip title={billed}>
          <Typography className="table-tooltip-text !max-w-[calc(100%-63px)] block truncate">
            {billed}
          </Typography>
        </Tooltip>
        <Typography className="table-tooltip-text">
          {params?.isPercentageBilling
            ? `(${billPaid}%)`
            : `(#${data?.total_billed})`}
        </Typography>
      </div>
    );
  }
);

export const RemainCellRenderer = React.memo(
  (
    params: BaseCellRendererProps<
      Pick<ICellRendererProps, "formatter" | "isPercentageBilling">
    >
  ) => {
    const { data } = params;
    if (params.node.rowPinned) {
      return (
        <div className="flex gap-1 overflow-hidden w-full justify-end">
          <Tooltip title={params.value}>
            <Typography className="table-tooltip-text">
              {params.value}
            </Typography>
          </Tooltip>
        </div>
      );
    }

    const actualRemaining =
      Number(data?.total || 0) - Number(data?.current_amount || 0);

    const remaining = params?.formatter(
      actualRemaining !== 0 ? (actualRemaining / 100)?.toFixed(2) : "0.00"
    ).value_with_symbol;

    const remainingBillForPercent = 100 - Number(data?.bill_paid);
    const formattedRemainingPercent =
      remainingBillForPercent % 1 === 0
        ? remainingBillForPercent.toString() // No decimal, show integer
        : remainingBillForPercent.toFixed(2);

    const remainingBillForQty =
      Number(data?.quantity) - Number(data?.bill_paid);

    return (
      <div className="flex gap-1 overflow-hidden w-full justify-end">
        <Tooltip title={remaining}>
          <Typography className="table-tooltip-text !max-w-[calc(100%-63px)] block truncate">
            {remaining}
          </Typography>
        </Tooltip>
        <Typography className="table-tooltip-text">
          {params?.isPercentageBilling
            ? `(${formattedRemainingPercent}%)`
            : `(#${remainingBillForQty})`}
        </Typography>
      </div>
    );
  }
);

export const TotalCellRenderer = React.memo(
  (
    params: BaseCellRendererProps<
      Pick<ICellRendererProps, "formatter" | "isPercentageBilling">
    >
  ) => {
    const { data } = params;
    if (params.node.rowPinned) {
      return (
        <div className="flex gap-1 overflow-hidden w-full justify-end">
          <Tooltip title={params.value}>
            <Typography className="table-tooltip-text">
              {params.value}
            </Typography>
          </Tooltip>
        </div>
      );
    }

    const total = params?.formatter(
      Number(data?.total) !== 0
        ? (Number(data?.total) / 100 || 0).toFixed(2)
        : "0.00"
    ).value_with_symbol;

    return (
      <Tooltip title={total}>
        <Typography className="table-tooltip-text">{total}</Typography>
      </Tooltip>
    );
  }
);

export const TaxCellRenderer = React.memo(
  (params: TOtherItemsCellRenderer & { disabled?: boolean }) => {
    const data = params.data as ISOVBudgetItemsData & {
      isHovered?: boolean;
    }; // Extend type temporarily

    if (params.node.rowPinned) {
      return null;
    }

    const isApplied = Number(data?.apply_global_tax) === 1;
    const isHovered = data?.isHovered;

    let additionalCellClass = "opacity-0";

    if (isApplied || isHovered) {
      if (params?.disabled) {
        additionalCellClass = isApplied ? "opacity-60" : "opacity-0";
      } else {
        additionalCellClass = isApplied ? "opacity-100" : "opacity-60";
      }
    }

    console.log(data, isApplied, "additionalCellClass**");

    return (
      <div className="relative">
        <FontAwesomeIcon
          className={`w-3.5 h-3.5 text-primary-900 transition-opacity duration-200 pointer-events-none ${additionalCellClass}`}
          // className={`w-3.5 h-3.5 text-primary-900 transition-opacity duration-200 pointer-events-none ${
          //   params?.disabled || isHovered
          //     ? "opacity-60"
          //     : isApplied
          //     ? "opacity-100"
          //     : "opacity-0"
          // }`}
          icon="fa-regular fa-check"
          // icon={isApplied ? "fa-regular fa-check" : ""}
        />
      </div>
    );
  }
);

export const ActionCellRenderer = React.memo(
  (params: BaseCellRendererProps<ICellRendererProps>) => {
    const {
      data,
      setEditProjectItem,
      setItemToEdit,
      setDeleteItemId,
      isReadOnly,
      _t,
    } = params;
    if (params.node.rowPinned) {
      return params.value;
    }

    const showDelete = Number(data?.current_amount) === 0;

    return !isReadOnly ? (
      <div className="flex items-center gap-1.5 justify-center">
        <ButtonWithTooltip
          tooltipTitle={_t("View")}
          tooltipPlacement="top"
          icon="fa-solid fa-eye"
          onClick={() => {
            setEditProjectItem(true);
            setItemToEdit(data);
          }}
        />
        {showDelete && (
          <ButtonWithTooltip
            tooltipTitle={_t("Delete")}
            tooltipPlacement="top"
            icon="fa-regular fa-trash-can"
            onClick={() => setDeleteItemId(data.item_id)}
          />
        )}
      </div>
    ) : (
      <></>
    );
  }
);
