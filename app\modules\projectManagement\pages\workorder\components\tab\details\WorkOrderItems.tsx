import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { costDataBase } from "~/data";
import { useParams } from "@remix-run/react";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Spin } from "~/shared/components/atoms/spin";
import { Tag } from "~/shared/components/atoms/tag";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
//store
import { useWoAppDispatch, useWoAppSelector } from "../../../redux/store";
import { getGConfig, useGModules } from "~/zustand";
import {
  formatAmount,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { getCostCode } from "~/redux/action/getCostCodeAction";

import { useTranslation } from "~/hook";
import WorkOrderItem from "../sidebar/workOrderItem/WorkOrderItem";
import { ImportItemsEstimate } from "../sidebar/importItemsEstimate";
import { defaultConfig } from "~/data";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  addWorkorderItem,
  deleteWorkorderItem,
  updateWorkorderItem,
} from "../../../redux/action/workorderDetailsAction";
import {
  getUpdatedWorkOrderData,
  updateWorkorderItems,
} from "../../../redux/slices/workorderDetailsSlice";
import {
  GridOptions,
  IRowNode,
  ValueFormatterParams,
  ValueGetterParams,
  ValueParserParams,
  ValueSetterParams,
} from "ag-grid-community";
import debounce from "lodash/debounce";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { qtyNumberCheck, wholeNumberRegex } from "~/shared/utils/helper/common";
import { ColDef } from "ag-grid-community";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { calMarkupPercentageIv } from "~/modules/financials/pages/invoice/utils/helper";

import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { unitRoutes } from "~/route-services/unit.routes";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { addUnit, getUnitList } from "~/redux/action/unitActions";

const WorkOrderItems = ({
  setFilteredItem,
}: {
  setFilteredItem: React.Dispatch<
    React.SetStateAction<IWorkorderDetailsItem[]>
  >;
}) => {
  const { _t } = useTranslation();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const [open, setOpen] = useState<boolean>(false);
  const { items, details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const { codeCostData }: IGetCostCodeList = useWoAppSelector(
    (state) => state.costCode
  );
  const { module_singular_name, module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey, getGModuleByKey } = useGModules();
  const params = useParams();
  const [isOpenViewWorkOrderItem, setIsOpenViewWorkOrderItem] =
    useState<boolean>(false);
  const [workorderItemToView, setWorkorderItemToView] =
    useState<IWorkorderDetailsItem>();
  const [isWorkorderItemToViewEditable, setIsWorkorderItemToViewEditable] =
    useState<boolean>(true);
  const [isOpenSelectItemFromCID, setIsOpenSelectItemFromCID] =
    useState<boolean>(false);
  const [isWrokOrderItemAdd, setIsWrokOrderItemAdd] = useState<boolean>(false);
  const [initialSubMaterial, setInitialSubMaterial] = useState<
    Partial<CIDBItemSideData>[]
  >([]);

  const gridRef = useRef<ExtendedAgGridReact<IWorkorderDetailsItem> | null>(
    null
  );
  const [selectedData, setSelectedData] = useState<IWorkorderDetailsItem>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  
  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
    }
  }, []);

  const costItemsFromDatabase = useMemo(() => {
    const data = items
      ?.filter(
        (item: IWorkorderDetailsItem) => Number(item?.reference_item_id) > 0
      )
      ?.map((item: IWorkorderDetailsItem) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_id,
          item_id: item?.item_id as string,
        };
        if (item?.item_type_key === "item_material") {
          temData.material_id = item?.reference_item_id;
          temData.item_type = "161";
          temData.type_name = "Material";
        } else if (item?.item_type_key === "item_labour") {
          temData.labor_id = item?.reference_item_id;
          temData.item_type = "163";
          temData.type_name = "Labor";
        } else if (item?.item_type_key === "item_sub_contractor") {
          temData.contractor_id = item?.reference_item_id;
          temData.item_type = "164";
          temData.type_name = "Subcontractor";
        } else if (item?.item_type_key === "item_equipment") {
          temData.equipment_id = item?.reference_item_id;
          temData.item_type = "162";
          temData.type_name = "Equipment";
        } else if (item?.item_type_key === "item_other") {
          temData.other_item_id = item?.reference_item_id;
          temData.item_type = "165";
          temData.type_name = "Other Items";
        }
        return temData as Partial<CIDBItemSideData>;
      });
    return data;
  }, [items]);
  const dispatch = useWoAppDispatch();
  const [importItemsEstimate, setImportItemsEstimate] =
    useState<boolean>(false);
  const [filteredItems, setFilteredItems] =
    useState<IWorkorderDetailsItem[]>(items);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [deleteRecordLoading, setDeleteRecordLoading] =
    useState<boolean>(false);
  const [isDataUpdating, setIsDataUpdating] = useState<boolean>(false);
  const [itemToBeDelete, setItemToBeDelete] = useState<number>();
  const { formatter } = useCurrencyFormatter();
  const gConfig: GConfig = getGConfig();
  const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    if (items?.length) {
      const jobsiteDataArr: any = [];

      items.forEach((data: any) => {
        jobsiteDataArr.push({
          name: data.subject || "",
          reference_item_id: data.reference_item_id || "",
          item_id: data.item_id || "",
          material_id: data.reference_item_id || "",
          item_type: String(data.item_type || ""),
          type_name: data.item_type_name || "",
        });
      });
      setInitialSubMaterial(jobsiteDataArr);
    } else {
      setInitialSubMaterial([]);
    }
  }, [items]);

  useEffect(() => {
    setFilteredItems(items); // Reset the filtered data when items change
  }, [items]);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  const { itemTypes }: IWorkorderItemTypes = useWoAppSelector(
    (state) => state.workorderItemTypes
  );

  const applyFilter = () => {
    if (filter.length === 0) {
      // If no filter is applied, show all items
      setFilteredItems(items);
      setFilteredItem(items);
    } else {
      // Apply filter logic and check for undefined item_type_name
      const filteredData = items.filter(
        (item) =>
          item?.item_type_key && filter.includes(item.item_type_key as string)
      );
      setFilteredItems(filteredData);
      setFilteredItem(filteredData);
    }
    setIsLoading(false);
  };

  const uniCostDropdownHight =
    filteredItems?.length === 1
      ? 60
      : filteredItems?.length === 2
      ? 90
      : filteredItems?.length === 3
      ? 120
      : filteredItems?.length === 4
      ? 150
      : 180;

  // Call applyFilter when filter changes

  const handleFilterChange = debounce(() => {
    applyFilter();
  }, 300);

  useEffect(() => {
    handleFilterChange();
    return handleFilterChange.cancel;
  }, [items]);

  useEffect(() => {
    setIsLoading(true);
    handleFilterChange();
    return handleFilterChange.cancel;
  }, [filter]);

  useEffect(() => {
    dispatch(getCostCode({ daily_equipment_cost_code: 1 }));
  }, []);

  const addWorkOrderItemCallBack = useCallback(
    async (formData: IWorkOrderAddItemParmas) => {
      const response = (await addWorkorderItem(
        formData
      )) as IWorkorderDetailsAddItemApiRes;
      if (response?.success) {
        const updatedItems = dispatch(getUpdatedWorkOrderData()).items;
        const newItem = response.data;
        const newItems = [...updatedItems, ...newItem];
        dispatch(updateWorkorderItems({ items: newItems }));
      } else {
        notification.error({
          description: _t(
            response?.message || "Something went wrong while adding item"
          ),
        });
        dispatch(updateWorkorderItems({ items }));
      }
    },
    [addWorkorderItem]
  );

  const handleItemFromCostItemDatabase = async (data: CIDBItemSideData[]) => {
    // contractor_id; use this instead of sub_contract_id
    const removedItemIds = items
      .filter((jobItem) => {
        if (jobItem.reference_item_id && jobItem.reference_item_id > 0) {
          return !data.some((item) => {
            if (item?.type_name === "Material") {
              return item.material_id === jobItem.reference_item_id;
            } else if (item?.type_name === "Labor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.labor_id)
              );
            } else if (item?.type_name === "Subcontractor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.contractor_id)
              );
            } else if (item?.type_name === "Equipment") {
              return (
                Number(jobItem.reference_item_id) === Number(item.equipment_id)
              );
            } else if (item?.type_name === "Other Items") {
              return (
                Number(jobItem.reference_item_id) === Number(item.other_item_id)
              );
            } else if (item?.type_name === "Groups") {
              return Number(jobItem.reference_item_id) === Number(item.item_id);
            }
          });
        }
      })
      .map((item) => Number(item.item_id));

    const mutationPromises = [];
    if (removedItemIds.length) {
      removedItemIds.forEach((id) => {
        mutationPromises.push(handleWorkorderItemDelete(id));
      });
    }

    const itemsToBeAdd = data
      .filter(
        (jobItem) =>
          !items.some((item) => {
            if (jobItem?.type_name === "Material") {
              return (
                Number(item.reference_item_id) === Number(jobItem.material_id)
              );
            } else if (jobItem?.type_name === "Labor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.labor_id)
              );
            } else if (jobItem?.type_name === "Subcontractor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.contractor_id)
              );
            } else if (jobItem?.type_name === "Equipment") {
              return (
                Number(item.reference_item_id) === Number(jobItem.equipment_id)
              );
            } else if (jobItem?.type_name === "Other Items") {
              return (
                Number(item.reference_item_id) === Number(jobItem.other_item_id)
              );
            } else if (jobItem?.type_name === "Group") {
              return Number(item.reference_item_id) === Number(jobItem.item_id);
            }
          })
      )
      .map((item) => {
        let temData: IWorkOrderAddItem = {
          subject: item.name,
          quantity: item.quantity,
          unit: item.unit,
          hidden_markup: item.hidden_markup,
          // We have used it, because in the common comp.(AddMultiselectDirectorySide), you have added the unit cost & cost code Id as unitCost & costCodeId instead of unit_cost, please check that
          unit_cost: item.unit_cost
            ? Number(item.unit_cost)
            : Number(item?.unitCost) ?? "",
          cost_code_id: item.cost_code_id ?? item?.costCodeId ?? "",
          markup: item.markup?.toString() ?? "",
          is_markup_percentage: 1,
          total: item.total,
          assigned_to: "",
          assigned_to_contact_id: 0,
          contractor_id: item.contractor_id?.toString() || "",
          contractor_contact_id: 0,
          is_cost_item: 1,
          // description: item.description,
          description: item.notes || "",
          internal_notes: item.internalNotes || item.internal_notes,
          item_type: item?.item_type?.toString() || "",
        };
        if (item?.type_name === "Material") {
          temData.reference_item_id = item?.material_id?.toString();
        } else if (item?.type_name === "Labor") {
          temData.reference_item_id = item?.labor_id?.toString();
        } else if (item?.type_name === "Subcontractor") {
          temData.reference_item_id = item?.contractor_id?.toString();
        } else if (item?.type_name === "Equipment") {
          temData.reference_item_id = item?.equipment_id?.toString();
        } else if (item?.type_name === "Other Items") {
          temData.reference_item_id = item?.other_item_id?.toString();
        } else if (item?.type_name === "Group") {
          temData.reference_item_id =
            item?.equipment_id?.toString() ||
            item?.material_id?.toString() ||
            item?.labor_id?.toString() ||
            item?.contractor_id?.toString() ||
            item?.other_item_id?.toString() ||
            item?.item_id?.toString();
        }
        return temData;
      });

    if (itemsToBeAdd.length) {
      const workorderItem = {
        work_order_id: Number(params?.id),
        from: "panel",
        items: itemsToBeAdd,
      };

      const formData = getValuableObj(workorderItem);
      mutationPromises.push(addWorkOrderItemCallBack(formData));
    }
    await Promise.all(mutationPromises);
  };

  const handleItemFromEstimate = async (data: IWorkOrderEstimateItem[]) => {
    const itemsToBeAdd = data?.map((item) => {
      let temData: IWorkOrderAddItem = {
        subject: item.subject,
        cost_code_id: item.cost_code_id,
        quantity: item.quantity,
        unit: item.unit,
        unit_cost: item.unit_cost,
        tax_id: item.tax_id,
        markup: item.markup,
        is_markup_percentage: Number(item.is_markup_percentage),
        total: Number(item.total),
        assigned_to: item.assigned_to,
        assigned_to_contact_id: 0,
        description: item.description,
        internal_notes: item.internal_notes,
        estimate_item_id: item.item_id,
        estimate_id: item.estimate_id,
        item_type: item?.item_type || "",
      };
      // Keep it as comment for future use, if the item_type is not available in the Estimate item
      // if (item?.item_type_key === "Material") {
      //   temData.item_type = "161";
      // } else if (item?.item_type_key === "Labor") {
      //   temData.item_type = "163";
      // } else if (item?.item_type_key === "Sub Contractor") {
      //   temData.item_type = "164";
      // } else if (item?.item_type_key === "Equipment") {
      //   temData.item_type = "162";
      // } else if (item?.item_type_key === "Other") {
      //   temData.item_type = "165";
      // }
      return temData;
    });

    if (itemsToBeAdd.length > 0) {
      const workorderItem = {
        work_order_id: Number(params?.id),
        from: "panel",
        items: itemsToBeAdd,
      };

      const formData = getValuableObj(workorderItem);
      const response = (await addWorkorderItem(
        formData as IWorkOrderAddItemParmas
      )) as IWorkorderDetailsAddItemApiRes;

      if (response?.success) {
        const newItem = response.data;
        const updatedItems = dispatch(getUpdatedWorkOrderData()).items;
        const newItems = [...updatedItems, ...newItem];
        dispatch(updateWorkorderItems({ items: newItems }));
      }
    }
  };

  const handleWorkorderItemDelete = async (id: number) => {
    const deleteItemParams: IWorkorderDetailsDeleteItemPrams = {
      work_order_id: Number(params?.id),
      item_id: id,
    };
    const response = (await deleteWorkorderItem(
      deleteItemParams as IWorkorderDetailsDeleteItemPrams
    )) as IWorkorderDetailsDeleteItemApiRes;
    if (response?.success) {
      const updatedItems = dispatch(getUpdatedWorkOrderData()).items;
      const newItems = updatedItems.filter((item) => item.item_id !== id);
      setDeleteRecordLoading(false);
      setConfirmDialogOpen(false);
      dispatch(updateWorkorderItems({ items: newItems }));
    } else {
      notification.error({
        description: _t(
          response?.message ||
            `Something went wrong while deleting an item with ${id}`
        ),
      });
    }
  };

  const calculateProfitMarginPercentage = (
    total: number,
    markup: number
  ): number => {
    if (isNaN(total) || isNaN(markup) || total <= 0) {
      //
      return 0;
    }

    const profitMargin = (markup / total) * 100;
    return parseFloat(profitMargin.toFixed(0));
  };

  const handleDelete = () => {
    try {
      setDeleteRecordLoading(true);
      if (itemToBeDelete) {
        handleWorkorderItemDelete(itemToBeDelete);
      }
    } catch (error: unknown) {
      notification.error({
        description: _t((error as Error)?.message),
      });
    }
  };
  const handleInLineTaxEditing = async (newValue: IWorkorderDetailsItem) => {
    const workorderItem = {
      work_order_id: Number(params?.id),
      is_single_item: 1,
      items: [{ ...newValue }],
    };
    gridRef.current?.api?.applyTransaction({ update: [newValue] });

    const formData = getValuableObj(workorderItem);
    const response = (await updateWorkorderItem(
      formData as IWorkOrderUpdateItemParmas
    )) as IWorkorderDetailsAddItemApiRes;

    return response;
  };
  const handleInLineEditing = async (newValue: IWorkorderDetailsItem) => {
    const workorderItem = {
      work_order_id: Number(params?.id),
      is_single_item: 1,
      items: [
        {
          ...newValue,
        },
      ],
    };
    const newItems = items.map((item) => {
      if (Number(item.item_id) === Number(newValue.item_id)) {
        return { ...item, ...newValue };
      } else {
        return { ...item };
      }
    });
    dispatch(updateWorkorderItems({ items: newItems }));

    const formData = getValuableObj(workorderItem);
    const response = (await updateWorkorderItem(
      formData as IWorkOrderUpdateItemParmas
    )) as IWorkorderDetailsAddItemApiRes;
    if (response?.success === true) {
      const newItem = response.data[0];
      const newItems = items.map((item) => {
        if (item.item_id === response.data[0].item_id) {
          return newItem;
        } else {
          return item;
        }
      });
      dispatch(updateWorkorderItems({ items: newItems }));
    } else {
      dispatch(updateWorkorderItems({ items }));
    }
    return response;
  };

  const workorderItemsListOptions = [
    {
      label: `Add Item to ${module_singular_name}`,
      value: "add_item_to_work_order",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "Database",
      onClick: () => {
        setIsOpenSelectItemFromCID(true);
      },
    },
    {
      label: `Import from ${
        getGModuleByKey(defaultConfig.estimate_module)?.module_name
      }`,
      value: "import_estimate",
      onClick: () => {
        setImportItemsEstimate(true);
      },
    },
    {
      label: `Add Manual ${module_singular_name} Item`,
      value: "work_order_item",
      onClick: () => {
        setIsOpenViewWorkOrderItem(true);
        setIsWorkorderItemToViewEditable(false);
        setIsWrokOrderItemAdd(true);
      },
    },
  ];

  const handleDragAndDrop = async (items: IWorkorderInitialState["items"]) => {
    const indexedItems: IWorkorderInitialState["items"] = items.map(
      (row: IWorkorderDetailsItem, index) => ({
        ...row,
        order_item_no: index + 1,
      })
    );

    const oldItems = items;
    dispatch(updateWorkorderItems({ items: indexedItems }));

    const workorderItem = {
      work_order_id: Number(params?.id),
      from: "panel",
      items: indexedItems.map((item) => ({
        item_id: item.item_id,
        order_item_no: item.order_item_no,
        subject: item.subject,
      })),
    };

    const formData = getValuableObj(workorderItem);

    const response = (await updateWorkorderItem(
      formData as IWorkOrderUpdateItemParmas
    )) as IWorkorderDetailsAddItemApiRes;

    if (response?.success === false) {
      dispatch(updateWorkorderItems({ items: oldItems }));
    }
  };

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        item_equipment: "fa-regular fa-screwdriver-wrench",
        item_material: "fa-regular fa-block-brick",
        item_labour: "fa-regular fa-user-helmet-safety",
        item_sub_contractor: "fa-regular fa-file-signature",
        item_other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  const gridOptions: GridOptions = {
    stopEditingWhenCellsLoseFocus: true,
    getRowId: (params) => params.data.item_id?.toString(),
    onRowDragEnd: function (event) {
      // ag grid community provide "RowNode" But not working that's why use any
      const { node, overIndex } = event as {
        node: IRowNode;
        overIndex: number;
      };
      if (!gridOptions.api || !node) return;

      const rowData: IWorkorderInitialState["items"] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));

      if (node.rowIndex !== undefined && node.rowIndex !== null) {
        rowData.splice(overIndex, 0, rowData.splice(node?.rowIndex, 1)[0]);
        handleDragAndDrop(rowData);
      }
    },
  };

  const estimateItemsHandler = useCallback(
    async ({
      itemsToAdd,
      itemToBeDeleteId,
    }: {
      itemsToAdd: IWorkOrderEstimateItem[];
      itemToBeDeleteId: number[] | [];
    }) => {
      setIsDataUpdating(true);
      if (!!itemsToAdd.length) {
        await handleItemFromEstimate(itemsToAdd);
      }
      if (!!itemToBeDeleteId.length) {
        const deletePromises: Promise<any>[] = [];
        itemToBeDeleteId.forEach((itemId) => {
          deletePromises.push(handleWorkorderItemDelete(itemId));
        });

        await Promise.all(deletePromises);
      }
      setImportItemsEstimate(false);
      setIsDataUpdating(false);
    },
    [handleItemFromEstimate, handleWorkorderItemDelete]
  );

  const getTotalAmount = useCallback(
    (data: IWorkorderDetailsItem, qty: string, unit_cost: string | number) => {
      const markup_amount = Number(data.markup || 0);
      const is_markup_percentage = Number(data.is_markup_percentage);

      const total: number = Number(qty) * Number(unit_cost);
      let mainTotal: number = 0;

      if (is_markup_percentage === 1) {
        const markup = (total * markup_amount) / 100;
        mainTotal = Number(markup) + Number(total);
      } else {
        mainTotal = Number(qty) > 0 ? markup_amount : total;
      }
      return mainTotal;
    },
    []
  );

  const totalAmountForHeader = useMemo(() => {
    return filteredItems.reduce((acc: number, item: IWorkorderDetailsItem) => {
      const singleItemTotal = Number(item.total) ?? 0;
      return acc + singleItemTotal / 100;
    }, 0);
  }, [filteredItems]);

  const totalProfitAndPercentageForHeader: {
    profit: number;
    profitPercentage: number;
  } = useMemo(() => {
    let markupTotal = 0;
    let markupItemTotalWithoutTax = 0;

    filteredItems.forEach((item) => {
      const quantity = Number(item.quantity) || 0;
      const unitCost = Number(item.unit_cost || 0) / 100;
      const markup = (item.markup || "0") as string;
      const isMarkupPercentage =
        item.is_markup_percentage?.toString() as string;

      if (quantity !== 0 && unitCost > 0 && markup !== "") {
        const markupPercentage = parseInt(markup) / 100;

        if (isMarkupPercentage !== "") {
          if (markupPercentage !== 0) {
            markupItemTotalWithoutTax += quantity * unitCost;
          }

          if (parseInt(isMarkupPercentage) === 0 && markupPercentage !== 0) {
            markupTotal += markupPercentage - unitCost * quantity;
          } else {
            markupTotal += unitCost * quantity * markupPercentage;
          }
        }
      }
    });

    const markupPercentageTotal =
      100 * (markupTotal / markupItemTotalWithoutTax);
    const roundedMarkupPercentage = isNaN(markupPercentageTotal)
      ? 0
      : Math.round(markupPercentageTotal);

    return { profit: markupTotal, profitPercentage: roundedMarkupPercentage };
  }, [filteredItems]);

  const shouldAddItemsDropDownHide = useMemo(() => {
    const ddHideStatuses: string[] = ["182", "180", "324"];
    if (
      ddHideStatuses.includes(details.work_order_status?.toString() as string)
    ) {
      return true;
    }
    return false;
  }, [details]);

  const readOnlyMode = useMemo(() => {
    return gConfig?.module_read_only || shouldAddItemsDropDownHide;
  }, [gConfig, shouldAddItemsDropDownHide]);

  const costCodeOptions = useMemo(() => {
    const defaultOptions: Partial<ICostCode>[] = [
      {
        code_id: "0",
        csi_code: "",
        cost_code_name: "Select Cost Code",
      },
    ];

    return [...defaultOptions, ...codeCostData]?.map(
      (item: Partial<ICostCode>) =>
        `${item?.cost_code_name}` +
        `${!!item?.csi_code ? ` (${item?.csi_code})` : ""}`
    );
  }, [codeCostData]);

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t(`${module_singular_name} Items`)}
        iconProps={{
          icon: "fa-solid fa-clipboard-user",
          containerClassName:
            "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
          id: "Change_order_items_icon",
          colors: ["#50EBFD", "#5996E9"],
        }}
        headerRightButton={
          <div className="flex items-center gap-2 flex-wrap">
            <div>
              <Tag
                color="#0F732B1d"
                className="!text-[#0F732B] font-medium mx-auto !text-sm type-badge common-tag px-[9px]"
              >
                {_t("Profit")}:{" "}
                {/* remove Math.round for https://app.clickup.com/t/86cxxv66t  */}
                {
                  formatter(
                    formatAmount(totalProfitAndPercentageForHeader?.profit || 0)
                  ).value_with_symbol
                }{" "}
                ($
                {Math.round(
                  calculateProfitMarginPercentage(
                    totalAmountForHeader,
                    totalProfitAndPercentageForHeader?.profit
                  )
                )}
                %)
              </Tag>
            </div>
            <div className="flex items-center gap-1.5 bg-blue-100 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
              <FontAwesomeIcon
                className="w-[17px] h-[17px]"
                icon="fa-solid fa-money-check-dollar"
              />
              <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90">
                {
                  formatter(formatAmount(totalAmountForHeader || 0))
                    .value_with_symbol
                }
              </Typography>
            </div>
            {!isReadOnly && !shouldAddItemsDropDownHide && (
              <div className="w-fit">
                <DropdownMenu
                  options={workorderItemsListOptions}
                  buttonClass="w-fit h-auto"
                  contentClassName="add-items-drop-down"
                  placement="bottomRight"
                >
                  <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
                    <Typography className="text-primary-900 text-sm">
                      {_t(
                        `Add Item to ${
                          HTMLEntities.decode(
                            sanitizeString(module_singular_name)
                          ) ?? "Work Order"
                        }`
                      )}
                    </Typography>
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900"
                      icon="fa-regular fa-chevron-down"
                    />
                  </div>
                </DropdownMenu>
              </div>
            )}
            <div className="md:relative absolute top-0 right-0">
              <ModuleItemsFilter
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                filter={filter}
                onChangeFilter={setFilter}
                openFilter={open}
              />
            </div>
          </div>
        }
        children={
          <div className="pt-2">
            <div className="ag-theme-alpine">
              {isLoading ? (
                <Spin className="w-full h-[180px] flex items-center justify-center" />
              ) : (
                <StaticTable
                  ref={gridRef}
                  className="static-table"
                  gridOptions={gridOptions}
                  rowDragManaged={true}
                  animateRows={true}
                  stopEditingWhenCellsLoseFocus={true}
                  columnDefs={[
                    {
                      headerName: "",
                      minWidth: isReadOnly ? 0 : 30,
                      maxWidth: isReadOnly ? 0 : 30,
                      field: "move",
                      rowDrag: !isReadOnly,
                      cellClass:
                        "ag-cell-center ag-move-cell custom-move-icon-set",
                      cellRenderer: () => {
                        return (
                          <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
                            <FontAwesomeIcon
                              className="w-4 h-4 text-[#4b5a76]"
                              icon="fa-solid fa-grip-dots"
                            />
                          </div>
                        );
                      },
                    },
                    {
                      headerName: _t("Type"),
                      minWidth: 50,
                      maxWidth: 50,
                      field: "type",
                      suppressMovable: false,
                      suppressMenu: true,

                      headerClass: "ag-header-center",
                      cellClass: "ag-cell-center",
                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        return data.item_type_name ? (
                          <>
                            {Object.keys(iconByItemTypeName).map(
                              (key, index) => {
                                if (data.item_type_key === key) {
                                  return (
                                    <Tooltip
                                      title={data.item_type_name}
                                      key={index}
                                    >
                                      <FontAwesomeIcon
                                        className="w-4 h-4 text-primary-900 mx-auto"
                                        icon={iconByItemTypeName[key]}
                                      />
                                    </Tooltip>
                                  );
                                }
                              }
                            )}
                          </>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Item Name"),
                      field: "subject",
                      minWidth: 150,
                      flex: 2,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,

                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        return data.subject ? (
                          <Tooltip
                            title={HTMLEntities.decode(
                              sanitizeString(data.subject)
                            )}
                          >
                            <Typography className="table-tooltip-text">
                              {HTMLEntities.decode(
                                sanitizeString(data.subject)
                              )}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                      editable: () => (readOnlyMode ? false : true),
                      valueGetter: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        return HTMLEntities.decode(
                          sanitizeString(params?.data?.subject)
                        );
                      },
                      valueSetter: (params: {
                        data: IWorkorderDetailsItem;
                        newValue: string;
                      }) => {
                        if (!params.newValue?.trim()) {
                          notification.error({
                            description: "Item Name is required.",
                          });
                          return;
                        }
                        if (params.newValue) {
                          const valuesToBeUpdate: IWorkorderDetailsItem = {
                            item_id: params?.data?.item_id,
                            subject: replaceDOMParams(
                              sanitizeString(params.newValue)
                            ),
                          };
                          handleInLineEditing(valuesToBeUpdate);
                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("Cost Code"),
                      field: "cost_code_name",
                      minWidth: 180,
                      flex: 2,
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,

                      cellEditor: "agRichSelectCellEditor",
                      cellClass: "ag-cell-left",
                      cellRenderer: ToolTipCell,
                      keyCreator: (params) =>
                        params.value?.cost_code_name?.trim(),
                      valueFormatter: (params: ValueFormatterParams) =>
                        params.value?.cost_code_name,
                      valueParser: (params: ValueParserParams) =>
                        costCodeOptions.find(
                          (costCode) =>
                            costCode.cost_code_name === params.newValue
                        ),
                      editable: (params: CostItemTableCellRenderer) =>
                        gConfig?.module_read_only ? false : true,
                      cellEditorParams: {
                        values: costCodeOptions,
                        filterList: true,
                        searchType: "matchAny",
                        allowTyping: true,
                        valueListMaxHeight: uniCostDropdownHight,
                      },
                      valueGetter: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        return !!params.data?.cost_code_name
                          ? `${params.data?.cost_code_name}${
                              !!params.data?.cost_code
                                ? ` (${params.data?.cost_code})`
                                : ""
                            }`
                          : !params.data?.cost_code_name &&
                            !!params.data?.cost_code
                          ? ` (${params.data?.cost_code})`
                          : "";
                      },
                      valueSetter: (params: {
                        data: IWorkorderDetailsItem;
                        newValue: string;
                      }) => {
                        if (params.newValue) {
                          const newCostCodeName = HTMLEntities.decode(
                            sanitizeString(params.newValue)
                          ).trim();

                          const valuesToBeUpdate: IWorkorderDetailsItem = {
                            item_id: params?.data?.item_id,
                            cost_code_id:
                              codeCostData.find(
                                (item) =>
                                  (
                                    `${item?.cost_code_name.trim()}` +
                                    `${
                                      item?.csi_code &&
                                      ` (${item?.csi_code.trim()})`
                                    }`
                                  )?.trim() === newCostCodeName
                              )?.code_id || "",
                            cost_code_name:
                              newCostCodeName === "Select Cost Code"
                                ? ""
                                : newCostCodeName,
                          };

                          handleInLineEditing(valuesToBeUpdate);

                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("QTY"),
                      field: "quantity",
                      minWidth: 80,
                      maxWidth: 80,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",

                      suppressKeyboardEvent,
                      cellEditor: "agNumberCellEditor",
                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        const value = formatter(
                          formatAmount(data?.quantity?.toString() || 0, {
                            isQuantity: true,
                          })
                        );

                        return (
                          <>
                            {/* {data.quantity ? ( */}
                            <Tooltip
                              title={HTMLEntities.decode(
                                sanitizeString(value.value)
                              )}
                            >
                              <Typography className="table-tooltip-text">
                                {HTMLEntities.decode(
                                  sanitizeString(value.value)
                                )}
                              </Typography>
                            </Tooltip>
                            {/* ) 
                            : (
                              <div>-</div>
                            )} */}
                          </>
                        );
                      },
                      editable: () => (readOnlyMode ? false : true),
                      valueGetter: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        return params?.data?.quantity;
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          let nVal = params.newValue;
                          const updatedTotal: number = getTotalAmount(
                            params.data,
                            nVal,
                            params.data?.unit_cost as string | number
                          );

                          if (!nVal || nVal == null) {
                            const updatedData = {
                              ...params.data,
                              quantity: "",
                            };
                            params.node.setData(updatedData);
                            const valuesToBeUpdate: IWorkorderDetailsItem = {
                              item_id: params?.data?.item_id,
                              quantity: nVal,
                              total: updatedTotal,
                              order_item_no: params.data.order_item_no,
                            };
                            handleInLineEditing(valuesToBeUpdate);
                            return false;
                          }
                          const checkNum = qtyNumberCheck(nVal);
                          const integerPartLength = nVal
                            .toString()
                            .split(".")[0].length;

                          if (integerPartLength > 6 || !checkNum) {
                            notification.error({
                              description:
                                "Quantity should be less than or equal to 6 digits.",
                            });
                            return false;
                          }
                          if (!floatWithNegativeRegex.test(nVal)) {
                            notification.error({
                              description: _t(
                                "Decimal part should be less than or equal to 2 digits."
                              ),
                            });
                            return false;
                          }
                          if (nVal.toString().includes(".")) {
                            nVal = nVal.toFixed(2);
                          }

                          const updatedData = {
                            ...params.data,
                            quantity: nVal,
                          };
                          params.node.setData(updatedData);
                          const valuesToBeUpdate: IWorkorderDetailsItem = {
                            item_id: params?.data?.item_id,
                            quantity: nVal,
                            total: updatedTotal,
                            order_item_no: params.data.order_item_no,
                          };
                          handleInLineEditing(valuesToBeUpdate);
                        }
                        return true;
                      },
                    },
                    {
                      headerName: _t("Unit Cost"),
                      field: "unit_cost",
                      minWidth: 130,
                      maxWidth: 130,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      suppressMovable: false,
                      suppressMenu: true,

                      suppressKeyboardEvent,
                      cellEditor: "agNumberCellEditor",
                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        return (
                          <>
                            {data.unit_cost ? (
                              <Tooltip
                                title={`${
                                  formatter(
                                    formatAmount(Number(data?.unit_cost) / 100)
                                  ).value_with_symbol
                                }`}
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        Number(data?.unit_cost) / 100
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            ) : (
                              <div>{formatter("0.00").value_with_symbol}</div>
                            )}
                          </>
                        );
                      },
                      editable: () => (readOnlyMode ? false : true),
                      valueGetter: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        return Number(params?.data?.unit_cost) / 100;
                      },
                      valueSetter: (params: {
                        data: IWorkorderDetailsItem;
                        newValue: number | string;
                      }) => {
                        let newUnitCost = params.newValue
                          ? params.newValue.toString()
                          : "";

                        const [integerPart, decimalPart] =
                          newUnitCost.split(".");

                        if (Number(newUnitCost) < 0) {
                          notification.error({
                            description: _t(
                              "Negative values are not allowed for Unit Cost."
                            ),
                          });
                          return false; // Reject the negative value
                        }
                        const fullStr = BigInt(
                          Math.floor(Number(newUnitCost))
                        ).toString();
                        if (fullStr.length > 10) {
                          notification.error({
                            description: _t(
                              "Unit cost should be less than or equal to 10 digits."
                            ),
                          });
                          return false;
                        }

                        if (decimalPart && decimalPart.length > 2) {
                          notification.error({
                            description: _t(
                              "Decimal part should be less than or equal to 2 digits"
                            ),
                          });
                          return false;
                        }

                        if (!isNaN(Number(newUnitCost))) {
                          if (
                            Number(newUnitCost) ===
                            Number(params.data.unit_cost) / 100
                          ) {
                            return false;
                          }

                          const updatedTotal: number = getTotalAmount(
                            params.data,
                            params.data.quantity as string,
                            newUnitCost as string | number
                          );

                          const valuesToBeUpdate: IWorkorderDetailsItem = {
                            item_id: params?.data?.item_id,
                            unit_cost: Number(newUnitCost) * 100,
                            total:
                              params?.data?.is_markup_percentage !== 1
                                ? updatedTotal
                                : updatedTotal * 100,
                            order_item_no: params.data.order_item_no,
                          };
                          handleInLineEditing(valuesToBeUpdate);
                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("Unit"),
                      field: "unit",
                      minWidth: 100,
                      maxWidth: 100,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        return (
                          <>
                            {data.unit !== "" ? (
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(data.unit)
                                )}
                              >
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(data.unit)
                                  )}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <div>-</div>
                            )}
                          </>
                        );
                      },
                      ...(window.ENV.ENABLE_UNIT_DROPDOWN && {
                        suppressKeyboardEvent: (params) => {
                          if (params.event.key === "Enter") {
                            params.event.preventDefault();
                            return true; // Block Ag-Grid's default behavior
                          }
                          return false;
                        },
                        cellEditorParams: {
                          values: units,
                          onKeyDown: (
                            e: React.KeyboardEvent<HTMLInputElement>,
                            data: IWorkorderDetailsItem
                          ) => {
                            if (e.key === "Enter") {
                              const value = e?.currentTarget?.value?.trim();
                              const newType = onEnterSelectSearchValue(
                                e,
                                units?.map((unit) => ({
                                  label: unit?.name,
                                  value: "",
                                })) || []
                              );
                              if (newType) {
                                setNewUnitName(newType);
                                setSelectedData(data);
                              } else if (value) {
                                notification.error({
                                  description:
                                    "Records already exist, no new records were added.",
                                });
                              }
                            }
                          },
                        },
                        cellEditor: UnitCellEditor<ISTItemDetails>,
                      }),
                      editable: () => (readOnlyMode ? false : true),
                      valueGetter: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        return params?.data?.unit;
                      },
                      valueSetter: (
                        params: ValueSetterParams<IWorkorderDetailsItem>
                      ) => {
                        const newUnit = window.ENV.ENABLE_UNIT_DROPDOWN
                          ? (params?.newValue?.name?.trim() || "")?.toString()
                          : (params?.newValue || "")?.toString();

                        const [integerPart] = newUnit.split(".");

                        if (integerPart.length > 15) {
                          notification.error({
                            description: _t(
                              "Unit should be less than 15 characters"
                            ),
                          });
                          return false;
                        }

                        const valuesToBeUpdate: IWorkorderDetailsItem = {
                          item_id: params?.data?.item_id,
                          unit: newUnit,
                        };

                        handleInLineEditing(valuesToBeUpdate);
                        return true;
                      },
                    },
                    {
                      headerName: _t("MU%"),
                      field: "mu",
                      minWidth: 80,
                      maxWidth: 80,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      suppressMovable: false,

                      suppressKeyboardEvent,
                      suppressMenu: true,
                      // editable: (params: IWorkOrderItemTableCellRenderer) =>
                      //   Boolean(!isReadOnly) &&
                      //   Boolean(params?.data?.is_markup_percentage),
                      editable: () => (readOnlyMode ? false : true),
                      valueGetter: (params: IWorkOrderItemTableCellRenderer) =>
                        params.data?.markup?.toString()
                          ? params.data?.markup
                          : "",

                      cellRenderer: ({ data }: IIVLumpSumTableCellRenderer) => {
                        const markup = data.markup;
                        const markupToShow = calMarkupPercentageIv(data);

                        if (
                          markup === null ||
                          markup === undefined ||
                          markup === ""
                        ) {
                          return (
                            <Typography className="table-tooltip-text text-center text-gray-400">
                              -
                            </Typography>
                          );
                        }

                        return (
                          <div className="flex gap-1 overflow-hidden w-full justify-end">
                            {!data?.is_markup_percentage &&
                            Boolean(!isReadOnly) ? (
                              <Tooltip
                                title={_t(
                                  `To edit a ${
                                    formatter().currency_symbol
                                  } amount View and Edit the Item`
                                )}
                                placement="top"
                              >
                                <FontAwesomeIcon
                                  className="w-3.5 h-3.5 text-primary-900"
                                  icon="fa-regular fa-circle-info"
                                />
                              </Tooltip>
                            ) : null}
                            <Tooltip title={markupToShow}>
                              <Typography className="table-tooltip-text">
                                {markupToShow}
                              </Typography>
                            </Tooltip>
                          </div>
                        );
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          let nVal = params.newValue?.toString();

                          if (!nVal || nVal.trim() === "") {
                            const _updateData = {
                              ...params.data,
                              markup: null,
                            };
                            params.node.setData(_updateData);

                            const updatedTotal: number = getTotalAmount(
                              { ...params.data, markup: null },
                              params.data.quantity as string,
                              params.data.unit_cost as string | number
                            );

                            const valuesToBeUpdate: Partial<IWorkorderDetailsItem> =
                              {
                                item_id: params?.data?.item_id,
                                total: updatedTotal,
                                markup: null,
                              };
                            handleInLineEditing(
                              valuesToBeUpdate as IWorkorderDetailsItem
                            );
                            return true;
                          }
                          if (Number(params.newValue) < 0) {
                            notification.error({
                              description: _t(
                                "Negative values are not allowed for Markup."
                              ),
                            });
                            return false;
                          }
                          const checkNum = qtyNumberCheck(nVal);

                          if (
                            nVal &&
                            nVal.toString().length > 3 &&
                            (!nVal.toString().includes(".") || !checkNum)
                          ) {
                            notification.error({
                              description:
                                "Markup should be less than or equal to 3 digits.",
                            });
                            return false;
                          }
                          if (!wholeNumberRegex.test(nVal)) {
                            notification.error({
                              description: _t(
                                "Decimal is not allowed in Markup"
                              ),
                            });
                            return false;
                          }
                          if (nVal.toString().includes(".")) {
                            nVal = nVal.toFixed(0);
                          }
                          const updatedData = {
                            ...params.data,
                            markup: Number(nVal),
                          };
                          params.node.setData(updatedData);
                          const updatedTotal: number = getTotalAmount(
                            { ...params.data, markup: Number(nVal) },
                            params.data.quantity as string,
                            params.data.unit_cost as string | number
                          );

                          const valuesToBeUpdate: Partial<IWorkorderDetailsItem> =
                            {
                              item_id: params?.data?.item_id,
                              total: updatedTotal,
                              markup: nVal,
                            };
                          handleInLineEditing(
                            valuesToBeUpdate as IWorkorderDetailsItem
                          );
                          return true;
                        }
                        return true;
                      },
                    },
                    {
                      headerName: _t("Total"),
                      field: "total",
                      minWidth: 130,
                      maxWidth: 130,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      suppressMovable: false,
                      suppressMenu: true,
                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        return (
                          <>
                            <Tooltip
                              title={
                                data.total === "0"
                                  ? formatter(formatAmount("0.00"))
                                      .value_with_symbol
                                  : `${
                                      formatter(
                                        formatAmount(Number(data?.total) / 100)
                                      ).value_with_symbol
                                    }`
                              }
                            >
                              <Typography className="table-tooltip-text">
                                {data.total === "0"
                                  ? formatter(formatAmount("0.00"))
                                      .value_with_symbol
                                  : `${
                                      formatter(
                                        formatAmount(Number(data?.total) / 100)
                                      ).value_with_symbol
                                    }`}
                              </Typography>
                            </Tooltip>
                          </>
                        );
                      },
                    },
                    {
                      headerName: _t("Tax"),
                      field: "apply_global_tax",
                      minWidth: 50,
                      maxWidth: 50,
                      suppressMovable: false,
                      suppressMenu: true,

                      headerClass: "ag-header-center",
                      cellClass: "ag-cell-center flex justify-center",
                      editable: !readOnlyMode,
                      cellEditorParams: {
                        maxLength: 20,
                      },
                      cellRenderer: "agCheckboxCellRenderer",
                      cellEditor: "agCheckboxCellEditor",
                      valueGetter: (params: ValueGetterParams) => {
                        return !!params.data.apply_global_tax;
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          const updatedData = {
                            ...params.data,
                            apply_global_tax: Boolean(params.newValue),
                          };
                          params.node.setData(updatedData);
                          setTimeout(
                            () => handleInLineTaxEditing(updatedData),
                            0
                          );
                        }
                        return true;
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      minWidth: 80,
                      maxWidth: 80,
                      suppressMenu: true,
                      cellRenderer: (
                        params: IWorkOrderItemTableCellRenderer
                      ) => {
                        const { data } = params;
                        return (
                          <div className="flex items-center gap-1.5 justify-center">
                            <ButtonWithTooltip
                              tooltipTitle={_t("View")}
                              tooltipPlacement="top"
                              icon="fa-solid fa-eye"
                              onClick={() => {
                                setWorkorderItemToView(data);
                                setIsWorkorderItemToViewEditable(
                                  isReadOnly || shouldAddItemsDropDownHide
                                );
                                setIsOpenViewWorkOrderItem(true);
                                setIsWrokOrderItemAdd(false);
                              }}
                            />
                            {!isReadOnly && !shouldAddItemsDropDownHide && (
                              <ButtonWithTooltip
                                tooltipTitle={_t("Delete")}
                                tooltipPlacement="top"
                                icon="fa-regular fa-trash-can"
                                onClick={() => {
                                  setItemToBeDelete(data?.item_id as number);
                                  setConfirmDialogOpen(true);
                                }}
                              />
                            )}
                          </div>
                        );
                      },
                    },
                  ]}
                  rowData={filteredItems}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                    />
                  )}
                />
              )}
            </div>
          </div>
        }
      />

      {isOpenViewWorkOrderItem && (
        <WorkOrderItem
          isOpenViewWorkOrderItem={isOpenViewWorkOrderItem}
          setIsOpenViewWorkOrderItem={setIsOpenViewWorkOrderItem}
          formData={workorderItemToView}
          setWorkorderItemToView={setWorkorderItemToView}
          isViewOnly={isWorkorderItemToViewEditable}
          isReadOnly={isReadOnly}
          isWrokOrderItemAdd={isWrokOrderItemAdd}
          setIsWrokOrderItemAdd={setIsWrokOrderItemAdd}
          filteredItems={filteredItems}
        />
      )}

      {importItemsEstimate && (
        <ImportItemsEstimate
          importItemsEstimate={importItemsEstimate}
          setImportItemsEstimate={setImportItemsEstimate}
          estimateItemHandler={estimateItemsHandler}
          isDataUpdating={isDataUpdating}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          isLoading={deleteRecordLoading}
          modalIcon="fa-regular fa-trash-can"
          onAccept={() => {
            handleDelete();
          }}
          onDecline={() => setConfirmDialogOpen(false)}
          onCloseModal={() => setConfirmDialogOpen(false)}
        />
      )}

      {isOpenSelectItemFromCID && (
        <CidbItemDrawer
          closeDrawer={() => {
            setIsOpenSelectItemFromCID(false);
          }}
          options={costDataBase}
          singleSelecte={false}
          addItem={(data) => {
            handleItemFromCostItemDatabase(data as CIDBItemSideData[]);
          }}
          itemTypes={itemTypes.map((item) => {
            return {
              ...item,
              default_color: item.default_color?.toString(),
            };
          })}
          openSendEmailSidebar={isOpenSelectItemFromCID}
          data={costItemsFromDatabase as Partial<CIDBItemSideData>[]}
          initialSubMaterial={initialSubMaterial as CIDBItemSideData[]}
          cidbModuleVIseIdAndValue={{
            [defaultConfig.material_key]: {
              id: 161,
              value: defaultConfig.material_key,
            },
            [defaultConfig.equipment_key]: {
              id: 162,
              value: defaultConfig.equipment_key,
            },
            [defaultConfig.labor_key]: {
              id: 163,
              value: defaultConfig.labor_key,
            },
            [defaultConfig.subcontractor_key]: {
              id: 164,
              value: defaultConfig.subcontractor_key,
            },
            [defaultConfig.other_items_key]: {
              id: 165,
              value: defaultConfig.other_items_key,
            },
          }}
        />
      )}
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.item_id === selectedData?.item_id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode?.setData({
                      ...currentRowNode.data,
                      unit: newUnitName,
                    });

                    const response = await handleInLineEditing({
                      item_id: oldData?.item_id,
                      unit: newUnitName,
                    });
                    if (!response.success) {
                      currentRowNode?.setData(oldData);
                      notification.error({ description: response.message });
                    }
                  }
                }
                const existingColDefs = api.getColumnDefs();
                if (!existingColDefs) return;

                const updatedColDefs = existingColDefs.map((col) =>
                  "field" in col && col.field === "unit"
                    ? {
                        ...col,
                        filterParams: {
                          values:
                            newUnits.map((unit) => ({
                              label: unit.name?.toString(),
                              value: unit.name?.toString(),
                            })) ?? [],
                        },
                        cellEditorParams: {
                          ...col.cellEditorParams,
                          values: newUnits,
                        },
                      }
                    : col
                );

                api.setColumnDefs(updatedColDefs);

                // Ensure the grid header re-renders
                api.refreshHeader();
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
};

export default WorkOrderItems;
