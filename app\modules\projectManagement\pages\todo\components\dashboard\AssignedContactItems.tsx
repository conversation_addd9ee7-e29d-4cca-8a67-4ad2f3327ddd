// antd + ag
import { type RadioChangeEvent } from "antd";
import { useRef, useState, useMemo } from "react";
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { ApexChart } from "~/shared/components/atoms/chart";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import BarHorizontalChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarHorizontalChart.skeleton";
// Other
// redux
import { useAppTDDispatch, useAppTDSelector } from "../../redux/store";
// hooks
import { useApexCharts } from "~/shared/hooks/useApexCharts";
import { useTranslation } from "~/hook";
// constant
import { TODO_TAB } from "../../utils/constants";
import { sanitizeString } from "~/helpers/helper";
import { getGConfig } from "~/zustand";
import { fetchDashData } from "../../redux/action/dashboardAction";

const AssignedContactItems = () => {
  const gridApiRef = useRef<GridApi | null>(null);
  const [value, setValue] = useState<string>("chart");
  const { _t } = useTranslation();
  const optionsLine = useApexCharts({ type: "bar" });
  const { module_name }: GConfig = getGConfig();
  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) || _t("To Do's");

  const dispatch = useAppTDDispatch();
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);

  const {
    isDashLoading,
    openItemsByAssignedContact,
    openItemsByAssignedContactLastRefreshTime,
  }: IToDoDashState = useAppTDSelector((state) => state.dashboard);

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    await dispatch(
      fetchDashData({
        refresh_type: "open_items_by_assigned_contact",
      })
    );
    setIsCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Assigned To"),
      field: "full_name",
      minWidth: 200,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: IToDoDashAssignedContactTableCellRenderer) => {
        const { full_name } = data;
        const fullName = HTMLEntities.decode(sanitizeString(full_name));
        return fullName ? (
          <Tooltip title={fullName}>
            <Typography className="table-tooltip-text text-center">
              {fullName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: modulePLName,
      field: "to_do",
      minWidth: 100,
      maxWidth: 100,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMenu: true,
      cellRenderer: ({ data }: IToDoDashAssignedContactTableCellRenderer) => {
        const { total_count } = data;

        return total_count ? (
          <Tooltip title={total_count}>
            <Typography className="table-tooltip-text text-center">
              {total_count}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  const assignedContactNames = openItemsByAssignedContact.map((itemContact) =>
    HTMLEntities.decode(sanitizeString(itemContact?.full_name))
  );
  const itemTotalCounts = openItemsByAssignedContact.map(
    (itemCount) => itemCount.total_count
  );

  const chartSeriesData = [
    {
      name: modulePLName,
      data: itemTotalCounts,
    },
  ];

  const chartOptions = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        offsetY: -12,
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      stroke: {
        show: false,
        curve: "smooth",
        colors: undefined,
        width: 0,
        dashArray: 0,
      },
      markers: {
        size: [4, 7],
      },
      title: {
        text: undefined,
      },
      subtitle: {
        text: undefined,
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: "top", // top, center, bottom
          },
          horizontal: true,
        },
      },
      grid: {
        strokeDashArray: 5,
      },
      yaxis: {
        show: true,
      },
      xaxis: {
        categories: assignedContactNames,
        labels: {
          show: true,
          style: {
            fontSize: "12px",
            fontWeight: 600,
          },
          formatter: (val: number) => {
            return val?.toFixed(0);
          },
        },
        forceNiceScale: true,
      },
      colors: ["#79B7D9"],
      legend: {
        show: true,
        position: "top",
        markers: {
          radius: 100,
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
    };
  }, [assignedContactNames, itemTotalCounts, optionsLine]);
  return (
    <>
      <DashboardCardHeader
        title={`Open ${modulePLName} by Assigned Contact`}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={openItemsByAssignedContactLastRefreshTime}
        onClickRefresh={handleRefreshClick}
        rightContent={
          <div className="flex items-center ">
            <ListTabButton
              value={value}
              options={TODO_TAB}
              onChange={(e: RadioChangeEvent) => {
                setValue(e.target.value);
              }}
              className="first:border-r-0"
              activeclassName="!bg-[#EAE8E8]"
            />
          </div>
        }
      />
      <div className="py-2 px-2.5">
        {value === "chart" ? (
          <>
            {isDashLoading || isCashLoading ? (
              <BarHorizontalChartSkeleton sizeClassName="h-[170px] py-3.5" />
            ) : (
              <ApexChart
                series={chartSeriesData}
                options={chartOptions}
                type={"bar"}
                height={180}
              />
            )}
          </>
        ) : value === "table" ? (
          <div className="ag-theme-alpine xl:h-[170px] h-[209px]">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              rowData={
                !(isDashLoading || isCashLoading)
                  ? openItemsByAssignedContact
                  : undefined
              }
              noRowsOverlayComponent={() =>
                isDashLoading || isCashLoading ? (
                  <StaticTableRowLoading columnDefs={columnDefs} />
                ) : (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-td-open-assigned-contact.svg`}
                  />
                )
              }
            />
          </div>
        ) : (
          <></>
        )}
      </div>
    </>
  );
};
export default AssignedContactItems;
