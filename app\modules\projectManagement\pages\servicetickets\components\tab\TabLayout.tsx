import { useLocation, useNavigate, useParams } from "@remix-run/react";

// Atoms
import { Spin } from "~/shared/components/atoms/spin";

// Molecules
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";

// Other
import ServiceTicketTopBar from "~/modules/projectManagement/pages/servicetickets/components/tab/ServiceTicketTopBar";
import {
  useAppSTDispatch,
  useAppSTSelector,
} from "~/modules/projectManagement/pages/servicetickets/redux/store";
import {
  disableAutoScheduleAppoinment,
  fetchServiceTicketDetails,
  fetchServiceTicketNotes,
  sendCompleteStatusMail,
  updateSTClockInApi,
  updateSTDetailApi,
} from "~/modules/projectManagement/pages/servicetickets/redux/action/serviceTicketDetailsAction";
import { fetchServiceTicketCustomerDetails } from "~/modules/projectManagement/pages/servicetickets/redux/action/serviceTicketCustomerAction";
import { useEffect, useMemo, useRef, useState } from "react";
import { fetchServiceTicketItemDetails } from "~/modules/projectManagement/pages/servicetickets/redux/action/serviceTicketServiceItemAction";
import {
  getCommonSidebarCollapse,
  getGConfig,
  getGSettings,
  setGSettingsUpdateValue,
  useGModules,
} from "~/zustand";
import { useAppDispatch } from "~/redux/store";
import { getAttachmentsList } from "~/redux/action/getAttachmentsList";
import { getCostCode } from "~/redux/action/getCostCodeAction";
import {
  fetchSTDocumentHistoryDetails,
  fetchSTDocumentTimeCardsDetails,
} from "~/modules/projectManagement/pages/servicetickets/redux/action/serviceTicketDocumentsAction";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { statusIconMap } from "~/modules/projectManagement/pages/servicetickets/utils/constants";
import {
  incrementNotesListPage,
  isNewDataLoading,
  resetPage,
  updateSTDetail,
} from "~/modules/projectManagement/pages/servicetickets/redux/slices/serviceTicketDetailsSlice";
import { NO_ACCESS_ST_CODE } from "~/shared/constants";
import { routes } from "~/route-services/routes";
import { useTranslation } from "~/hook";
import {
  copyServiceTicketOnCompleteAPI,
  sendEnRouteStatusMailApi,
} from "~/modules/projectManagement/pages/servicetickets/redux/action/dashboardAction";
import { getItemTypes } from "~/redux/action/getItemTypes";
import { ScheduleService } from "~/modules/projectManagement/pages/servicetickets/components/tab/modal/scheduleService";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { resetAllSlices } from "~/modules/projectManagement/pages/servicetickets/redux/action/resetActions";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import dayjs from "dayjs";
import useInfiniteScrollHandler from "~/hook/useInfiniteScrollHandler";
import { getCommonAttachments } from "~/redux/action/commonAttachmentSection";
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const ManageServiceTicketsTab = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { pathname } = useLocation();
  const gConfig: GConfig = getGConfig();
  const { _t } = useTranslation();
  const containerRef = useRef<HTMLDivElement>(null);
  const { date_format, allow_service_ticket_in_timecard }: GSettings =
    getGSettings();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const params: RouteParams = useParams();
  const dispatch = useAppSTDispatch();
  const navigate = useNavigate();
  const commonDispatch = useAppDispatch();
  const { checkModuleAccessByKey } = useGModules();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    isDetailLoading,
    notes_data,
    details,
    page,
    isNext,
    isDataLoading,
  }: IServiceInitialState = useAppSTSelector((state) => state.serviceDetails);
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};
  const { module_id = 0 } = currentModule || {};
  const { codeCostData }: IGetCostCodeList = useAppSTSelector(
    (state) => state.costCode
  );
  const { timeCardList, documentHistoryList }: ISTDocumentInitialState =
    useAppSTSelector((state) => state.serviceTicketDocument);
  const { aws_files }: IGetAttachmentsList = useAppSTSelector(
    (state) => state.attachmentsList
  );
  const { service_ticket_job_status }: IGetStatusList = useAppSTSelector(
    (state) => state.statusList
  );
  const handleNotesScroll = useInfiniteScrollHandler({
    dispatch,
    hasNextPage: isNext,
    isLoading: isDataLoading,
    nextPageAction: incrementNotesListPage,
    loadingAction: isNewDataLoading,
  });
  const limit = 10;
  const [activeStep, setActiveStep] = useState<string | number>("");
  const [TempStatus, setTempStatus] = useState<string | number>("");
  const [isShowDate, setIsShowDate] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [scheduledServiceDialogOpen, setScheduledServiceDialogOpen] =
    useState<boolean>(false);

  const [newSelectedStatus, setNewSelectedStatus] = useState<string>("");
  const [statusModalLoading, setStatusModalLoading] = useState<boolean>(false);
  const [addSTLoading, setAddSTLoading] = useState<boolean>(false);

  useEffect(() => {
    containerRef?.current?.scrollTo({ top: 0, behavior: "smooth" });
  }, [pathname]); // Run this effect whenever the tab changes

  useEffect(() => {
    if (details?.job_status) {
      setActiveStep(details?.job_status?.toString());
    }
  }, [details?.job_status]);

  useEffect(() => {
    if (details?.project_id) {
      dispatch(getItemTypes({ project_id: Number(details?.project_id) }));
    }
  }, [details?.project_id]);

  const handleReloadDetails = (id: string) => {
    fetchData(true);
    dispatch(resetPage());
    EventLogger.log(
      EVENT_LOGGER_NAME.service_tickets + EVENT_LOGGER_ACTION.view,
      1
    );
  };

  const getServiceTicketDetails = async () => {
    const resData = (await dispatch(
      fetchServiceTicketDetails({ id: params.id })
    ).unwrap()) as IServiceTicketDetailApiRes;

    if (!resData?.success) {
      notification.error({
        description: resData?.message || "No data found.",
      });

      if (resData?.statusCode == NO_ACCESS_ST_CODE) {
        if (window && window.ENV && window.ENV.PANEL_URL) {
          navigate(`${routes.ACCESS_DENIED.url}`);
        }
      } else {
        navigate(`${routes.MANAGE_SERVICE_TICKETS.url}`);
      }
    }
  };

  useEffect(() => {
    // if (service_ticket_job_status?.length == 0) {
    //   commonDispatch(
    //     getStatusList({
    //       types: [
    //         CFConfig.service_ticket_job_status,
    //         CFConfig.service_ticket_priority,
    //         CFConfig.service_ticket_appointment_status,
    //       ],
    //     })
    //   );
    // }
    fetchData();
  }, [pathname, params.id]);

  const fetchData = (isReload: boolean = false) => {
    const tab = pathname.split(params.id + "/")?.[1] || "details";
    if (tab === "details" || tab === undefined) {
      if (isReload) {
        getServiceTicketDetails();
      }
    }
    if (codeCostData.length == 0 || isReload) {
      dispatch(
        getCostCode({
          timecard_cost_code: 1,
          project_id: details.project_id,
        })
      );
    }
    if (tab != "details" && isReload) {
      getServiceTicketDetails();
    }
    if (tab === "customer") {
      // if (Object.keys(customerDetails).length == 0 || isReload) {
      dispatch(fetchServiceTicketCustomerDetails({ id: params?.id || "" }));
      // }
    }
    if (tab === "service") {
      if (isReload) {
        dispatch(fetchServiceTicketItemDetails({ id: params?.id || "" }));
      }
      if (codeCostData.length == 0 || isReload) {
        dispatch(
          getCostCode({
            timecard_cost_code: 1,
            project_id: details.project_id,
          })
        );
      }
    }
    // if (tab === "billing") {
    //   if (isReload) {
    //     dispatch(fetchSTBillingInvoiceDetails({ id: params?.id || "" }));
    //   }
    //   if (isReload) {
    //     dispatch(fetchSTBillingPaymentDetails({ id: params?.id || "" }));
    //   }
    // }
    if (tab === "documents") {
      if (isReload) {
        dispatch(
          fetchSTDocumentHistoryDetails({
            id: params?.id || "",
            customer_id: details.customer_id,
          })
        );
      }
      if (isReload) {
        dispatch(fetchSTDocumentTimeCardsDetails({ id: params?.id || "" }));
      }
    }
    if (tab === "notes") {
      // if (!notes_data || notes_data.length == 0 || isReload) {
      if (isReload) {
        dispatch(
          fetchServiceTicketNotes({
            id: params?.id || "",
            start: 0,
            limit: limit,
          })
        );
      }
    }
    if (tab === "files") {
      // if (!aws_files || aws_files.length == 0 || isReload) {
      if (isReload) {
        dispatch(
          getCommonAttachments({
            record_id: Number(params?.id),
            module_key: gConfig?.module_key,
          })
        );
      }
    }
  };

  useEffect(() => {
    const tab = pathname.split(params.id + "/")?.[1];
    if (tab === "documents") {
      if (documentHistoryList.length == 0 && details?.customer_id) {
        dispatch(
          fetchSTDocumentHistoryDetails({
            id: params?.id || "",
            customer_id: details.customer_id,
          })
        );
      }
      // if (timeCardList.length == 0) {
      //   dispatch(fetchSTDocumentTimeCardsDetails({ id: params?.id || "" }));
      // }
    }
  }, [details?.customer_id, pathname, params.id]);

  useEffect(() => {
    const tab = pathname.split(params.id + "/")?.[1];
    if (tab === "notes") {
      dispatch(
        fetchServiceTicketNotes({
          id: params?.id || "",
          start: 0,
          limit: limit,
        })
      );
    }
    if (tab === "files") {
      dispatch(
        getCommonAttachments({
          record_id: Number(params?.id),
          module_key: CFConfig.service_ticket_module,
        })
      );
    }
  }, [pathname, params?.id]);
  useEffect(() => {
    const tab = pathname.split(params.id + "/")?.[1];

    if (page && tab === "notes")
      dispatch(
        fetchServiceTicketNotes({
          id: params?.id || "",
          start: page * limit,
          limit: limit,
        })
      );
  }, [page, pathname]);
  const completedStatusMail = () => {
    dispatch(
      sendCompleteStatusMail({
        id: params?.id || "",
      })
    );
  };
  const statusList: ISTStatusList[] =
    useMemo(
      () =>
        service_ticket_job_status?.map((item: IGetStatusData) => ({
          label: replaceDOMParams(sanitizeString(item.display_name)),
          value: item.type_id.toString(),
          default_color: item.default_color,
          icon: statusIconMap[item.name],
          sort_order: Number(item?.sort_order),
          show_in_progress_bar: Number(item?.show_in_progress_bar),
        })),
      [service_ticket_job_status]
    ) || [];

  const handleUpdateField = async (data: ISTDetailFieldsBoolean) => {
    const field = Object.keys(data)[0] as keyof IServiceTicketDetails;

    const updateRes = (await updateSTDetailApi({
      service_ticket_id: params?.id,
      ...(data.service_date_only
        ? {
            service_date:
              backendDateFormat(
                data.service_date_only as string,
                date_format
              ) ?? null,
          }
        : {}),
      ...data,
    })) as IServiceTicketDetailApiRes;

    if (updateRes?.success) {
      dispatch(updateSTDetail({ ...updateRes?.data, ...data }));
    }

    return updateRes;
  };

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(gConfig?.module_key) === "read_only",
    [gConfig?.module_key]
  );

  const ST_STATUS_LIST: ISTDetailsTopBarProps["STStatusList"] = useMemo(
    () =>
      statusList
        ? statusList.filter(Boolean).map((item: ISTStatusList) => ({
            label: HTMLEntities.decode(sanitizeString(item.label)),
            key: item.value?.toString() || "",
            sort_order: Number(item.sort_order),
            icon: (
              <FontAwesomeIcon
                icon="fa-solid fa-square"
                className="h-3.5 w-3.5"
                style={{
                  color: item.default_color,
                }}
              />
            ),
            show_in_progress_bar: item.show_in_progress_bar,
            default_color: item.default_color,
            progress_bar_icon: item.icon,
          }))
        : [],
    [statusList]
  );

  const ACTIVE_STATUS = useMemo(
    () =>
      ST_STATUS_LIST.find(
        (item) => item.key.toString() === activeStep.toString()
      ),
    [ST_STATUS_LIST, activeStep]
  );

  const ST_STATUS_PROGRESS_BAR_LIST = useMemo(
    () => ST_STATUS_LIST.filter((item) => item.show_in_progress_bar !== 0),
    [ST_STATUS_LIST]
  );

  const modifySTTimer = async (data: ISTClockInParam) => {
    try {
      const result = (await updateSTClockInApi({
        service_ticket_id: params?.id ?? "",
        ...data,
      })) as IServiceTicketDetailApiRes;
      return result;
    } catch (error) {
      return;
    }
  };

  const handleStatusChange: ISTDetailsTopBarProps["handleStatusChange"] =
    async (status) => {
      if (status?.key?.toString() === details.job_status?.toString()) {
        return;
      }
      if (status) {
        if (
          status.key.toString() === "261" ||
          (details.service_date_only &&
            (status.key.toString() === "152" ||
              status.key.toString() === "149" ||
              status.key.toString() === "151" ||
              status.key.toString() === "263" ||
              status.key.toString() === "153"))
        ) {
          /**
           * "Unscheduled" value is 261
           * "Cancelled" value is 152
           * "Requires Follow up" value is 153
           * "Scheduled" value is 149
           * "Completed" value is 151
           * "En Route" value is 263
           */

          if (
            status.key.toString() === "151" &&
            allow_service_ticket_in_timecard != 1 &&
            details.check_status &&
            ["check_in", "resume", "pause"].includes(details.check_status)
          ) {
            const requestData = {
              check_in: 0,
              check_pause: 0,
              check_resume: 0,
              check_out: 1,
            };
            const stopRes = await modifySTTimer(requestData);
            if (!stopRes?.success) {
              return;
            }
            EventLogger.log(
              EVENT_LOGGER_NAME.checked_out_from_service_ticket,
              1
            );
            dispatch(updateSTDetail(stopRes?.data));
          }

          const res = handleUpdateField({ job_status: status.key.toString() });

          if (status.key.toString() === "263") {
            setNewSelectedStatus(status?.key?.toString() || "");
          }
          if (status.key.toString() === "151") {
            const result = await res;
            if (result.success) {
              setNewSelectedStatus(status?.key?.toString() || "");
              setScheduledServiceDialogOpen(true);
            } else {
              if (result.statusCode === 400) {
                notification.error({
                  description: result.message,
                });
              }
            }
          }

          return;
        } else if (status.key.toString() === "150") {
          // "Checked-In" value is 150
          let otherData: ISTDetailFieldsBoolean = {};
          if (!details.service_date_only) {
            const date = dayjs;
            let newDate = date();
            try {
              newDate = date.utc().tz(window.TIMEZONE?.timezone_utc_tz_id);
            } catch (error) {
              console.error(
                `Invalid timezone:`,
                window.TIMEZONE?.timezone_utc_tz_id
              );
            }
            otherData = {
              service_date_only: newDate.format(date_format),
              service_time: newDate.format("hh:mm A"),
            };
          }
          handleUpdateField({
            job_status: status.key.toString(),
            ...otherData,
          });
          return;
        }
      }
      // other all status handle from modals
      setNewSelectedStatus(status?.key?.toString() || "");
    };

  const getStatusBar = (responsive = false) => {
    const isEverySortOrderZero = ST_STATUS_PROGRESS_BAR_LIST.every(
      (item) => item.sort_order === 0
    );

    let activeTabIndex = ST_STATUS_PROGRESS_BAR_LIST.findIndex(
      (item) => item.key.toString() === activeStep.toString()
    );
    return (
      <>
        {ACTIVE_STATUS && Number(ACTIVE_STATUS.key) !== 152 && (
          <ul
            className={
              "items-center justify-center w-[calc(100%-0px)] hidden " +
              (responsive
                ? "xl:w-[calc(100%-30px)] xl:hidden sm:flex mb-[15px]"
                : "2xl:-ml-3.5 xl:flex")
            }
          >
            {ST_STATUS_PROGRESS_BAR_LIST.map((item, index) => {
              const isActive = activeTabIndex >= 0 && activeTabIndex >= index;
              return (
                <li
                  key={index}
                  className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                    isActive ? "before:bg-primary-900" : "before:bg-[#ACAEAF]"
                  } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                >
                  <ProgressBarHeader
                    option={{ ...item, icon: item.progress_bar_icon }}
                    isActive={isActive}
                    onClick={() => {
                      if (!isReadOnly) handleStatusChange(item);
                      // if (!isReadOnly)
                      //   handleStepClick(item.key || "");
                    }}
                  />
                </li>
              );
            })}
          </ul>
        )}
      </>
    );
  };

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [params, containerRef.current]);

  return (
    <>
      <div
        ref={containerRef}
        className={`ease-in-out duration-300 w-full overflow-y-auto ${
          sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
        }`}
        onScroll={handleNotesScroll}
      >
        <ServiceTicketTopBar
          data={details}
          sidebarCollapse={sidebarCollapse}
          onReloadSTDetails={() =>
            handleReloadDetails(params?.id?.toString() || "")
          }
          setActiveStep={setActiveStep}
          isLoading={isDetailLoading}
          statusBar={getStatusBar()}
          handleStatusChange={handleStatusChange}
          STStatusList={ST_STATUS_LIST}
          activeStatus={ACTIVE_STATUS}
        />
        {!isDetailLoading && (
          <ReadOnlyPermissionMsg
            className="p-4 pt-0"
            view={gConfig?.module_access === "read_only"}
          />
        )}
        <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
          <div
            className={`px-[15px] pb-[15px] ${
              gConfig?.module_access === "read_only"
                ? window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-198px)] min-h-[calc(100dvh-272px)]"
                  : "md:min-h-[calc(100dvh-341px)] min-h-[calc(100dvh-420px)]"
                : window.ENV.PAGE_IS_IFRAME
                ? "md:min-h-[calc(100dvh-164px)] min-h-[calc(100dvh-210px)]"
                : "md:min-h-[calc(100dvh-307px)] min-h-[calc(100dvh-362px)]"
            }`}
          >
            {isDetailLoading ? (
              <Spin
                className={`flex items-center justify-center ${
                  window.ENV.PAGE_IS_IFRAME
                    ? "md:h-[calc(100vh-161px)] h-[calc(100vh-171px)]"
                    : "md:h-[calc(100vh-304px)] h-[calc(100vh-332px)]"
                }`}
              />
            ) : (
              getStatusBar(true)
            )}
            {children}
          </div>
          {/* Remove old footer(ModuleTimelineFooter) and add new footer (TimeLineFooter) */}
          <TimeLineFooter
            data={{
              addedDate: details?.date_added || "",
              addedTime: details?.time_added || "",
              addedBy: details?.scheduled_by_name || "",
              moduleId: module_id,
              recordId: Number(params.id),
            }}
            sidebarCollapse={sidebarCollapse}
          />
        </div>
      </div>
      {Boolean(newSelectedStatus) && (
        <ConfirmModal
          isOpen={Boolean(newSelectedStatus)}
          isLoading={statusModalLoading}
          modalIcon={
            !details.service_date_only
              ? "fa-regular fa-file-check"
              : "fa-regular fa-clipboard-list-check"
          }
          modaltitle={
            !details.service_date_only
              ? "Confirmation"
              : _t("Update Service Ticket Status")
          }
          description={
            !details.service_date_only
              ? _t(
                  "A service date is required to assign any status other than 'Unscheduled'. Without a service date, the status will remain 'Unscheduled'. Are you sure you want to proceed?"
                )
              : _t(
                  `Do you want to send an update to the customer notifying them that the service has been En Route ?`
                )
          }
          onCloseModal={() => setNewSelectedStatus("")}
          onAccept={async () => {
            if (newSelectedStatus === "152") {
              // "Cancelled" value is 152
              setStatusModalLoading(true);
              await handleUpdateField({ job_status: newSelectedStatus });
              setStatusModalLoading(false);
              setNewSelectedStatus("");
              return;
              // https://app.clickup.com/t/86cxeh5na
              // } else if (newSelectedStatus === "153") {
              //   // "Requires Follow up" value is 153 and "Unscheduled" value is 261
              //   setStatusModalLoading(true);
              //   await handleUpdateField({ job_status: "261" });
              //   setStatusModalLoading(false);
              //   setNewSelectedStatus("");
            } else if (!details.service_date_only) {
              setScheduledServiceDialogOpen(true);
            } else {
              // send notify
              const sendEnRouteStatusMail = async () => {
                try {
                  setStatusModalLoading(true);
                  const res = (await sendEnRouteStatusMailApi({
                    service_ticket_id: Number(params?.id),
                  })) as IDownloadSericeTicketRes;
                  setNewSelectedStatus("");
                  setStatusModalLoading(false);
                  if (res?.success) {
                    // Successfully sent notification
                  } else {
                    notification.error({ description: res?.message });
                  }
                } catch (error) {
                  setNewSelectedStatus("");
                  setStatusModalLoading(false);
                  notification.error({
                    description: "Error sending notification.",
                  });
                }
              };
              sendEnRouteStatusMail();
            }
          }}
          onDecline={() => setNewSelectedStatus("")}
        />
      )}

      {scheduledServiceDialogOpen && Boolean(newSelectedStatus) && (
        <ScheduleService
          isOpen={scheduledServiceDialogOpen}
          isLoading={
            (statusModalLoading && !details.service_date_only) || addSTLoading
          }
          handleUpdateServiceTicket={async (data) => {
            if (!details.service_date_only) {
              setStatusModalLoading(true);
              await handleUpdateField({
                job_status: newSelectedStatus,
                service_date_only: data.date,
                service_time: data.time || "",
              });
              setStatusModalLoading(false);
              if (newSelectedStatus === "149" || newSelectedStatus === "153") {
                setScheduledServiceDialogOpen(false);
                setNewSelectedStatus("");
              } else if (newSelectedStatus === "263") {
                setScheduledServiceDialogOpen(false);
              }
            } else if (data.nextAppoinment === "1") {
              const serviceTicketData = {
                ...data,
                service_date:
                  backendDateFormat(data.date as string, date_format) ?? null,
                service_time: data.time1,
                service_end_time: data.time2,
                job_status: "149",
              };
              delete serviceTicketData.date;

              const addServiceTicket = async () => {
                if (!details.service_ticket_id?.toString()) {
                  notification.error({
                    description: "Something went wrong!",
                  });
                  return;
                }
                setAddSTLoading(true);
                const response = await copyServiceTicketOnCompleteAPI(
                  getValuableObj(serviceTicketData),
                  details.service_ticket_id?.toString()
                );
                setAddSTLoading(false);
                if (response?.success) {
                  setScheduledServiceDialogOpen(false);
                  setNewSelectedStatus("");
                  dispatch(resetAllSlices());
                  setTimeout(() => {
                    navigate(
                      `/manage-service-tickets/${response.data?.service_ticket_id}`
                    );
                  }, 100);
                } else {
                  notification.error({ description: response?.message });
                }
                if (data?.completed === "1") {
                  completedStatusMail();
                }
              };
              addServiceTicket();
            } else if (newSelectedStatus === "151") {
              if (data?.completed === "1") {
                completedStatusMail();
              }
              if (data?.notShowAgain) {
                dispatch(disableAutoScheduleAppoinment()).then(() =>
                  setGSettingsUpdateValue("auto_schedule_appointment", 0)
                );
              }
              setScheduledServiceDialogOpen(false);
              setNewSelectedStatus("");
            }
          }}
          onCloseModal={() => {
            setScheduledServiceDialogOpen(false);
            setNewSelectedStatus("");
          }}
          isShowDate={!details.service_date_only}
          details={details}
        />
      )}
    </>
  );
};

export default ManageServiceTicketsTab;
