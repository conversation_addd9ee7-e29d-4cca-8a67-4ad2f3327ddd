import parse from "html-react-parser";
import isEmpty from "lodash/isEmpty";
import { getGCompanySettings } from "~/zustand";
import { useTranslation } from "~/hook";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { fetchDashWeatherData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import CloudList from "./CloudList";

// atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import WizardRefreshIcon from "~/shared/components/molecules/wizardRefreshIcon/WizardRefreshIcon";
// Other
import { getGlobalUser } from "~/zustand/global/user/slice";

const PartiallyCloud = () => {
  const { _t } = useTranslation();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    weather_zip = "",
    latitude = "",
    longitude = "",
    country = "",
  } = user || {};

  const { zip_code: company_zip }: GCompanySettings = getGCompanySettings();
  const dispatch = useAppDLDispatch();

  const { isWeatherLoading, weather }: IDailyLogIntlState = useAppDLSelector(
    (state) => state.dashboard
  );
  const numbers = Array.from({ length: 7 }, (_, index) => index + 1);

  const handleRefreshWidget = async () => {
    const weatherParams = {
      zipCode:
        weather_zip || (!latitude && !longitude ? company_zip : undefined),
      lat: !weather_zip ? latitude || undefined : undefined,
      long: !weather_zip ? longitude || undefined : undefined,
      country: country || "",
    } as IWeatherParamsRes;

    dispatch(fetchDashWeatherData(weatherParams));
  };

  return (
    <>
      {isWeatherLoading ? (
        <>
          <div className="grid gap-2 h-full">
            <div className="h-[41px] flex items-center justify-between p-2 ">
              <div className="animate-pulse bg-black/10 rounded w-[200px] h-4"></div>
              <div className="animate-pulse bg-black/10 rounded w-[16px] h-[16px]"></div>
            </div>
            <div className="flex items-center justify-between h-[90px] px-[15px]">
              {numbers.map((number) => (
                <div
                  className="grid gap-2 items-center justify-center"
                  key={number}
                >
                  <div className="flex justify-center">
                    <div className="animate-pulse bg-black/10 rounded w-[20px] h-2"></div>
                  </div>
                  <div className="animate-pulse bg-black/10 rounded w-[30px] h-[30px]"></div>
                  <div className="animate-pulse bg-black/10 rounded w-[30px] h-2"></div>
                </div>
              ))}
            </div>
            <div className="animate-pulse bg-black/10 rounded w-[300px] h-3 mx-[15px]"></div>
          </div>
        </>
      ) : !isEmpty(weather) ? (
        <>
          <div className="flex items-center justify-between py-1.5 px-[15px] min-h-[41px] md:h-[41px] h-auto sm:flex-nowrap flex-wrap common-card-head">
            <Typography className="xl:!text-[15px] !text-sm !mb-0 font-semibold inline-block !text-black dark:!text-white/90">
              {weather && weather.hourly_summary
                ? _t(weather.hourly_summary)
                : ""}
            </Typography>

            <div className="flex items-center gap-1">
              {!isEmpty(weather) ? (
                <Tooltip title={_t(`Last fetched on: ${weather?.fetched_on}`)}>
                  <FontAwesomeIcon
                    className="h-3.5 w-3.5 -mt-px"
                    icon="fa-regular fa-clock"
                  />
                </Tooltip>
              ) : (
                <Tooltip
                  title={_t(
                    "Weather data is based on the businesses Zip Code that is set within Settings > Profile."
                  )}
                >
                  <FontAwesomeIcon
                    className="h-3.5 w-3.5 -mt-px"
                    icon="fa-regular fa-info-circle"
                  />
                </Tooltip>
              )}
              <WizardRefreshIcon
                onClick={() => {
                  if (!isWeatherLoading) {
                    handleRefreshWidget();
                  }
                }}
                isLoading={isWeatherLoading || false}
              />
            </div>
          </div>
          <div className="grid items-center h-[calc(100%-41px)] px-[15px]">
            <ul className="flex items-center w-full justify-between py-1.5">
              {weather?.list?.map((items: IweaterList, ind) => {
                const image = items?.weather?.[0].icon;
                const temprecher = parse(items?.temp || "") as string;
                const day = items?.day ? _t(items?.day) : "";
                return (
                  <CloudList
                    key={ind}
                    image={image}
                    temprecher={temprecher}
                    day={day}
                  />
                );
              })}
            </ul>
            <Typography className="block w-full font-medium text-black text-xs">
              {weather?.weather_summary ? _t(weather?.weather_summary) : ""}
            </Typography>
          </div>
        </>
      ) : (
        <div className="grid items-center h-full px-[15px]">
          <div className="flex items-center justify-between min-h-[41px] md:h-[41px] h-auto sm:flex-nowrap flex-wrap common-card-head">
            <Typography className="xl:!text-[15px] !text-sm !mb-0 font-semibold inline-block !text-black dark:!text-white/90">
              {weather && weather.hourly_summary
                ? _t(weather.hourly_summary)
                : ""}
            </Typography>

            <div className="flex items-center gap-1">
              <Tooltip
                title={_t(
                  "Weather data is based on the businesses Zip Code that is set within Settings > Profile."
                )}
              >
                <FontAwesomeIcon
                  className="h-3.5 w-3.5 -mt-px"
                  icon="fa-regular fa-info-circle"
                />
              </Tooltip>

              <WizardRefreshIcon
                onClick={() => {
                  if (!isWeatherLoading) {
                    handleRefreshWidget();
                  }
                }}
                isLoading={isWeatherLoading || false}
              />
            </div>
          </div>
          <NoRecords
            className="mx-auto"
            text={_t("There is no weather data available")}
            image={`${window.ENV.CDN_URL}assets/images/no-records-weather.svg`}
          />
        </div>
      )}
    </>
  );
};

export default PartiallyCloud;
