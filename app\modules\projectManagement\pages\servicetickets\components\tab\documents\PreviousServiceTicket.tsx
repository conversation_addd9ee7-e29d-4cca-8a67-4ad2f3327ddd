// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";

// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";

// Hook
import { useTranslation } from "~/hook";

// Other
import { useAppSTSelector } from "../../../redux/store";
import { sanitizeString } from "~/helpers/helper";
import { defaultConfig } from "~/data";
import { getGModuleByKey } from "~/zustand";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
import FieldRedirectButton from "~/shared/components/molecules/fieldRedirect/fieldRedirectButton/FieldRedirectButton";
import { routes } from "~/route-services/routes";

const PreviousServiceTicket = () => {
  const { _t } = useTranslation();
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.service_ticket_module
  );
  const {
    documentHistoryList,
    isDocumentDetailLoading,
  }: ISTDocumentInitialState = useAppSTSelector(
    (state) => state.serviceTicketDocument
  );

  const columnDefs = [
    {
      headerName: _t("Ticket") + " #",
      field: "company_ticket_id",
      minWidth: 135,
      maxWidth: 135,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (row: { data: ISTDocumentHistoryDetails }) => {
        const ticketId = row?.data?.company_ticket_id;
        return ticketId ? (
          <Tooltip title={ticketId}>
            <Typography className="table-tooltip-text">{ticketId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      maxWidth: 230,
      minWidth: 230,
      field: "date_added",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (row: { data: ISTDocumentHistoryDetails }) => {
        const { service_format_date, service_time } = row.data;
        const format =
          service_format_date && service_time
            ? "datetime"
            : service_format_date
            ? "date"
            : service_time
            ? "time"
            : "";
        return service_format_date || service_time ? (
          <DateTimeCard
            format={format}
            date={service_format_date}
            time={service_time}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Title"),
      field: "title",
      maxWidth: 150,
      minWidth: 150,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (row: { data: ISTDocumentHistoryDetails }) => {
        const summary = HTMLEntities.decode(sanitizeString(row?.data?.title));
        return summary ? (
          <Tooltip title={summary}>
            <Typography className="table-tooltip-text">{summary}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Tech"),
      field: "service_tech_name",
      maxWidth: 150,
      minWidth: 150,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellStyle: { textAlign: "center" },
      cellRenderer: (row: { data: ISTDocumentHistoryDetails }) => {
        const name = row?.data?.service_tech_name?.split(",");
        const image = row?.data?.service_tech_image?.split(",");
        return (
          <>
            {name ? (
              <AvatarGroup
                max={{
                  count: 1,
                  style: {
                    color: "#223558",
                    backgroundColor: "#ECF1F9",
                  },
                }}
                size={24}
                className="flex justify-center"
                prefixCls="multi-avatar-scroll"
              >
                {image.length > 0
                  ? image.map((items: string, index: number) => {
                      return (
                        <div
                          key={index}
                          className={`flex items-center ${
                            index === 0 ? "" : "gap-2 py-0.5 px-1"
                          }`}
                        >
                          {index === 0 ? (
                            <Tooltip
                              title={HTMLEntities.decode(name[index])}
                              placement="top"
                            >
                              <div>
                                <AvatarProfile
                                  user={{
                                    name: name[index],
                                    image: items || "",
                                  }}
                                  iconClassName="text-[11px] font-semibold"
                                />
                              </div>
                            </Tooltip>
                          ) : (
                            <Tooltip title={name[index]} placement="top">
                              <div className="p-1 flex items-center gap-1">
                                <AvatarProfile
                                  user={{
                                    name: name[index],
                                    image: items || "",
                                  }}
                                  iconClassName="text-[11px] font-semibold"
                                />
                                <Typography className="max-w-[160px] truncate block">
                                  {name[index]}
                                </Typography>
                              </div>
                            </Tooltip>
                          )}
                        </div>
                      );
                    })
                  : "-"}
              </AvatarGroup>
            ) : (
              "-"
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Time On Job"),
      field: "job_on_time",
      maxWidth: 150,
      minWidth: 150,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
    },
    {
      headerName: _t("Notes"),
      field: "notes",
      minWidth: 180,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (row: { data: ISTDocumentHistoryDetails }) => {
        const notes = HTMLEntities.decode(sanitizeString(row?.data?.notes));
        return notes ? (
          <Tooltip title={notes}>
            <Typography className="table-tooltip-text">{notes}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 50,
      maxWidth: 50,
      cellClass: "ag-cell-center",
      cellRenderer: (row: { data: ISTDocumentHistoryDetails }) => {
        return row.data.service_ticket_id ? (
          <FieldRedirectButton
            href={
              routes.MANAGE_SERVICE_TICKETS.url +
              "/" +
              row.data.service_ticket_id
            }
            tooltipTitle={_t("Open detail in a new tab")}
            iconClassName="!w-3.5 !h-3.5"
          />
        ) : (
          "-"
        );
      },
    },
  ];
  return (
    <CrudCommonCard
      headerTitle={`Previous ${module?.plural_name}`}
      iconClassName="items-center"
      iconProps={{
        icon: "fa-solid fa-ticket",
        containerClassName:
          "bg-[linear-gradient(180deg,#7FA3FF1a_0%,#3387FD1a_100%)]",
        id: "previous_service_tickets_icon",
        colors: ["#7FA3FF", "#3387FD"],
      }}
      children={
        <div className="pt-2">
          <div className="ag-theme-alpine">
            {isDocumentDetailLoading ? (
              <Spin className="flex items-center justify-center h-[180px]" />
            ) : (
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={documentHistoryList?.filter(
                  (obj) => obj.company_ticket_id !== null
                )}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-service-tickets.svg`}
                  />
                )}
              />
            )}
          </div>
        </div>
      }
    />
  );
};

export default PreviousServiceTicket;
