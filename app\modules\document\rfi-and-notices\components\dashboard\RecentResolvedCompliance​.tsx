// React + ag-grid
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "@remix-run/react";
// Hook + redux + helper
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import {
  useAppRFIDispatch,
  useAppRFISelector,
} from "~/modules/document/rfi-and-notices/redux/store";
import { fetchDashData } from "~/modules/document/rfi-and-notices/redux/action/dashboardAction";
import { GridApi, GridReadyEvent } from "ag-grid-community";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { routes } from "~/route-services/routes";
import { Tag } from "~/shared/components/atoms/tag";

const RecentResolvedCompliance = () => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  // States
  const [isRefreshLoading, setIsRefreshLoading] = useState<boolean>(false);

  const gridApiRef = useRef<GridApi | null>(null);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  const dispatch = useAppRFIDispatch();
  const {
    isDashLoading,
    recentResolvedComplianceData,
    recentResolvedComplianceDataLastRefreshTime,
  } = useAppRFISelector((state) => state.dashboard);

  const [rowData, setRowData] = useState<IPastDueCompliance[]>([]);

  useEffect(() => {
    if (!isRefreshLoading && recentResolvedComplianceData) {
      setRowData(recentResolvedComplianceData);
    }
  }, [recentResolvedComplianceData, isRefreshLoading]);

  const handleRefreshWidget = async () => {
    setIsRefreshLoading(true);
    setRowData([]);
    await dispatch(
      fetchDashData({ refresh_type: "recent_resolved_compliance" })
    );
    setIsRefreshLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: any) => {
        const { project_name } = data;
        const projectName = HTMLEntities.decode(sanitizeString(project_name));
        return projectName ? (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Compliance") + " #",
      field: "compliance",
      minWidth: 110,
      maxWidth: 110,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: any) => {
        const { prefix_company_correspondence_id } = data;
        return prefix_company_correspondence_id ? (
          <Tooltip title={prefix_company_correspondence_id}>
            <Typography className="table-tooltip-text text-center">
              {prefix_company_correspondence_id}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "item_type_name",
      minWidth: 130,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: any) => {
        const { data } = params;
        const noticeTypeName = HTMLEntities.decode(
          sanitizeString(data?.notice_type_name)
        );
        return noticeTypeName ? (
          <Tooltip title={noticeTypeName}>
            <Typography className="table-tooltip-text text-center">
              {noticeTypeName}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("To"),
      field: "to",
      minWidth: 80,
      maxWidth: 80,
      flex: 2,
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const { user_to_names, user_to, user_image, user_to_names_count } =
          data;
        let toNamesList: string[] = [];
        let toNamesImages: string[] = [];
        let toNamesIds: string[] = [];
        const to_names = user_to_names || [];
        const formattedToNames = user_to_names?.[0]?.replace(/,+/g, "")?.trim();
        const to_user_image = user_image?.split(",") || [];
        const to_ids = user_to?.split(",") || [];
        to_names?.forEach((name: string, index: number) => {
          if (name?.trim() !== "") {
            toNamesList.push(name.trim());
            toNamesIds.push(to_ids[index]?.trim() || "");
            toNamesImages.push(to_user_image[index]?.trim() || "");
          }
        });

        const cleanFirstName = to_names?.[0]
          ?.replace(/[^a-zA-Z\s]/g, "")
          ?.trim();
        const companyName = data?.company_name || "";

        return (
          <>
            {user_to_names !== null &&
            formattedToNames !== "" &&
            user_to_names !== undefined ? (
              <div className="flex items-center gap-2 overflow-hidden max-w-full w-fit">
                {user_to_names.length > 1 ? (
                  <>
                    <AvatarGroup
                      max={{
                        count: 1,
                        style: {
                          color: "#223558",
                          backgroundColor: "#ECF1F9",
                        },
                      }}
                      size={24}
                      className="flex justify-start"
                      prefixCls="multi-avatar-scroll"
                    >
                      {toNamesList?.map((item: string, index: number) => {
                        const dName = HTMLEntities.decode(sanitizeString(item));
                        const userImg = toNamesImages[index];
                        return (
                          <div
                            key={index}
                            className={`flex items-center ${
                              index === 0 ? "" : "gap-2 py-0.5 px-1"
                            }`}
                          >
                            <Tooltip title={dName} placement="top">
                              <div
                                className={`flex items-center gap-1 max-w-full ${
                                  index === 0 ? "" : "p-1"
                                }`}
                              >
                                <AvatarProfile
                                  user={{
                                    name: item,
                                    image:
                                      !userImg ||
                                      userImg.trim().replaceAll("'", "") === ""
                                        ? undefined
                                        : userImg,
                                  }}
                                  iconClassName="text-[11px] font-semibold"
                                />
                                {index > 0 && (
                                  <Typography className="">
                                    {_t(dName)}
                                  </Typography>
                                )}
                              </div>
                            </Tooltip>
                          </div>
                        );
                      })}
                    </AvatarGroup>
                  </>
                ) : (
                  <Tooltip title={formattedToNames}>
                    <div className="flex items-center gap-2 overflow-hidden max-w-full w-fit">
                      <AvatarProfile
                        user={{
                          name: formattedToNames,
                          image: to_user_image[0] || "",
                        }}
                        iconClassName="text-[11px] font-semibold"
                      />
                    </div>
                  </Tooltip>
                )}
              </div>
            ) : (
              "-"
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Resolution Date"),
      field: "date_added",
      maxWidth: 135,
      minWidth: 135,
      suppressMenu: true,
      cellRenderer: ({ data }: any) => {
        const { resolution_date } = data;
        return resolution_date ? (
          <DateTimeCard format="date" date={resolution_date} />
        ) : (
          "-"
        );
      },
    },
  ];
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-recently-resolved-compliance-notice.svg`}
    />
  );

  return (
    <>
      <DashboardCardHeader
        title={_t("Recently Resolved Compliance Notice")}
        showRefreshIcon={true}
        isRefreshing={isRefreshLoading}
        refreshIconTooltip={recentResolvedComplianceDataLastRefreshTime}
        onClickRefresh={handleRefreshWidget}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine ag-grid-cell-pointer h-[175px]">
          <StaticTable
            key={isDashLoading ? "loading" : "loaded"}
            className="static-table"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            noRowsOverlayComponent={
              isDashLoading || isRefreshLoading ? noRowsOverlay : noData
            }
            onCellClicked={(params: any) => {
              if (!!params?.data?.correspondence_id) {
                navigate(
                  `${routes.MANAGE_RFI_NOTICES.url}/${params.data.correspondence_id}`
                );
              }
            }}
          />
        </div>
      </div>
    </>
  );
};
export default RecentResolvedCompliance;
