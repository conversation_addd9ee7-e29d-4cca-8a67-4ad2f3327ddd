import { useEffect, useRef, useState } from "react";
import { useParams } from "@remix-run/react";
import dayjs from "dayjs";
import delay from "lodash/delay";
import { defaultConfig } from "~/data";
import isEmpty from "lodash/isEmpty";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Button } from "~/shared/components/atoms/button";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { InputField } from "~/shared/components/molecules/inputField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";

// Hook
import { useTranslation } from "~/hook";

// redux slice, action, and provider
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import {
  deleteDLSubOnjonSiteApi,
  updateDLPeopleDetailApi,
} from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  addUpSubsJobsiteDataAct,
  deleteSubsJobsiteDataAct,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/peopleSlice";

// constanst and helper
import {
  DLSubsOnJobsiteField,
  fieldStatus,
} from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";
import { sanitizeString } from "~/helpers/helper";
import {
  getStatusForField,
  onKeyDownDigit,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { dirTypeKeyById } from "~/modules/people/directory/utils/constasnts";

const SubsOnJobsiteCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const { id }: RouteParams = useParams();
  const dispatch = useAppDLDispatch();
  const { jobsiteData, isPeopleTabLoading }: IDLPeopleInitialState =
    useAppDLSelector((state) => state.dailyLogPeople);
  const { details }: IDLDetailsInitialState = useAppDLSelector(
    (state) => state.dailyLogDetails
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [subsJobsite, setSubsJobsite] = useState<Partial<ISubsEmpDataValues[]>>(
    []
  );
  const [selectedJobSiteId, setSelectedJobSiteId] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);

  useEffect(() => {
    if (jobsiteData?.length) {
      const jobsiteDataArr: Partial<ISubsEmpDataValues[]> = [];

      jobsiteData.forEach((data: IDLSubsonJobsite) => {
        const dirType = Object.entries(dirTypeKeyById).find(
          ([_, val]) => val == data.dir_type
        )?.[0];

        jobsiteDataArr.push({
          module_display_name: data.directory_company_name,
          display_name: data.directory_name,
          user_id: data.directory_id,
          type_key: dirType || "",
          image: data?.image,
        });
      });
      setSubsJobsite(jobsiteDataArr);
    } else {
      setSubsJobsite([]);
    }
  }, [jobsiteData]);

  const handleSaveSobsite = async (data: ISubsEmpDataValues[]) => {
    setIsLoading(true);
    const subsOnSiteFrm: IDLSubsJobsite[] = [];

    const removedItemIds = jobsiteData
      .filter(
        (jobItem) => !data.some((item) => item.user_id === jobItem.directory_id)
      )
      .map((item) => Number(item.item_id));

    if (removedItemIds.length) {
      const deleteRes = (await deleteDLSubOnjonSiteApi({
        dailyLogId: Number(id),
        itemId: removedItemIds,
      })) as IDLPeopleDetailsUpdateApiRes;
      if (deleteRes?.success) {
        dispatch(deleteSubsJobsiteDataAct({ item_id: removedItemIds }));
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
    }

    const addedItems = data.filter(
      (item) =>
        !jobsiteData.some((jobItem) => jobItem.directory_id === item.user_id)
    );
    if (addedItems.length) {
      data.forEach((item) => {
        if (
          item?.user_id &&
          !jobsiteData.some((jobItem) => jobItem.directory_id == item?.user_id)
        ) {
          subsOnSiteFrm.push({
            itemId: 0,
            directoryId: item.user_id,
            timeOnSite: "",
            notes: "",
            image: item?.image,
          });
        }
      });
      const addSubsEmpRes = (await updateDLPeopleDetailApi({
        subEmpOnSite: subsOnSiteFrm,
        logId: Number(id),
      })) as ISubsEmpAddUpRes;
      setIsLoading(false);

      if (addSubsEmpRes?.success) {
        dispatch(
          addUpSubsJobsiteDataAct({
            data: addSubsEmpRes?.data?.sub_employee_on_site,
            action: "add",
          })
        );
      } else {
        notification.error({
          description: addSubsEmpRes?.message,
        });
      }
    }
  };

  const handleDeleteSubsJobSite = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteDLSubOnjonSiteApi({
        dailyLogId: Number(id),
        itemId: [selectedJobSiteId || 0],
      })) as IDLPeopleDetailsUpdateApiRes;

      if (deleteRes?.success) {
        dispatch(
          deleteSubsJobsiteDataAct({ item_id: [selectedJobSiteId || 0] })
        );
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const handleAddUpSubsJobsite = (data: IDLUpSubsonJobsite) => {
    dispatch(addUpSubsJobsiteDataAct({ data, action: "update" }));
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Subs on Jobsite")}
        iconProps={{
          icon: "fa-solid fa-user",
          containerClassName:
            "bg-[linear-gradient(180deg,#42dd9b1a_0%,#3cb9b31a_100%)]",
          id: "subson_jobsite_user",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        headerRightButton={
          <Button
            type="primary"
            className="shadow-none !text-primary-900 !bg-blue-100 h-7 leading-4"
            onClick={() => {
              setIsOpenSelectCustomer(true);
            }}
            disabled={isReadOnly}
          >
            {_t("Assign Subs to Jobsite")}
          </Button>
        }
        children={
          <>
            {isPeopleTabLoading ? (
              <Spin className="w-full h-[83px] flex items-center justify-center" />
            ) : (
              <div
                className={`grid ${
                  jobsiteData.length > 0
                    ? "min-[1536px]:grid-cols-3 lg:grid-cols-2 grid-cols-1 gap-2.5 pt-2"
                    : ""
                }`}
              >
                {jobsiteData.length > 0 ? (
                  jobsiteData.map((item: IDLSubsonJobsite) => (
                    <AssignManHours
                      key={item.item_id}
                      subsOnJobSite={item}
                      isLoading={isLoading}
                      isReadOnly={isReadOnly}
                      onAddUpSubsJobSite={handleAddUpSubsJobsite}
                      onDeleteSubsJobSite={() => {
                        setSelectedJobSiteId(item?.item_id);
                        setIsDeleteConfirmOpen(true);
                      }}
                    />
                  ))
                ) : (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-dl-subs-on-jobsite.svg`}
                  />
                )}
              </div>
            )}
          </>
        }
      />
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            dispatch(setActiveField(defaultConfig.employee_key));
            setIsOpenSelectCustomer(false);
          }}
          projectId={details.projectId}
          singleSelecte={false}
          options={[
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "my_project",
            "by_service",
          ]}
          additionalContactDetails={0}
          setCustomer={(data) => {
            handleSaveSobsite(data as ISubsEmpDataValues[]);
            setSubsJobsite(data as ISubsEmpDataValues[]);
          }}
          selectedCustomer={subsJobsite as TselectedContactSendMail[]}
          groupCheckBox={true}
        />
      )}
      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteSubsJobSite}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

const AssignManHours = ({
  subsOnJobSite,
  isReadOnly,
  onAddUpSubsJobSite,
  onDeleteSubsJobSite,
}: IDLSubsOnJobSiteListProps) => {
  const { _t } = useTranslation();
  const loadingStatusRef = useRef(fieldStatus);
  const decInpRef = useRef<HTMLInputElement>(null);

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [inputValues, setInputValues] =
    useState<IDLSubsonJobsite>(DLSubsOnJobsiteField);

  useEffect(() => {
    setInputValues(subsOnJobSite);
  }, []);

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        checkStatus &&
        (checkStatus.status === "loading" ||
          checkStatus.status === "success" ||
          checkStatus.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleChangeDate = (val: string) => {
    setInputValues({
      ...inputValues,
      time_on_site: val,
    });
    handleUpdateField({
      timeOnSite: val,
    });
  };

  const handleUpdateField = async (data: IDLDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDLSubsonJobsite;
    if (field !== "timeOnSite") {
      setInputValues({ ...inputValues, ...data });
    }
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateDLPeopleDetailApi({
      logId: subsOnJobSite.daily_log_id.toString(),
      subEmpOnSite: [
        {
          itemId: inputValues.item_id,
          directoryId: inputValues.directory_id,
          empOnSite: inputValues.emp_on_site || null,
          timeOnSite: inputValues.time_on_site || null,
          notes: inputValues.notes || null,
          ...data,
        },
      ],
    })) as ISubsEmpAddUpRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      onAddUpSubsJobSite(updateRes.data.sub_employee_on_site[0]);
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: subsOnJobSite[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  return (
    <div className="p-[15px] group/subs-jobsite !pt-[10px] shadow-[0_4px_15px_0] shadow-[#00000017] rounded-xl dark:shadow-[0_4px_24px_0] dark:shadow-dark-900">
      <div className="flex items-center justify-between pb-1">
        <div className="w-[calc(100%-40px)]">
          <Tooltip
            title={HTMLEntities.decode(
              sanitizeString(inputValues?.directory_company_name)
            )}
          >
            <Typography className="truncate max-w-fit w-full block text-base text-primary-900 font-medium">
              {HTMLEntities.decode(
                sanitizeString(inputValues?.directory_company_name)
              )}
            </Typography>
          </Tooltip>
        </div>
        <ButtonWithTooltip
          tooltipTitle={_t("Delete")}
          disabled={isReadOnly}
          tooltipPlacement="top"
          icon="fa-regular fa-trash-can"
          iconClassName="group-hover/buttonHover:!text-[#FF0000] group-focus-within/buttonHover:!text-[#FF0000]"
          className="opacity-0 group-hover/subs-jobsite:opacity-100 focus-within:opacity-100 focus-within:!bg-[#FF00001a] hover:!bg-[#FF00001a]"
          onClick={onDeleteSubsJobSite}
        />
      </div>
      <div>
        {!isEmpty(inputValues.first_name || inputValues.last_name) ? (
          <Tooltip
            title={`${HTMLEntities.decode(
              sanitizeString(inputValues.first_name)
            )} ${HTMLEntities.decode(sanitizeString(inputValues.last_name))}`}
          >
            <Typography className="truncate max-w-fit w-full block text-13 text-primary-900">
              {`${HTMLEntities.decode(
                sanitizeString(inputValues.first_name)
              )} ${HTMLEntities.decode(sanitizeString(inputValues.last_name))}`}
            </Typography>
          </Tooltip>
        ) : (
          <Typography className="text-13 text-primary-900">-</Typography>
        )}
      </div>
      <div className="pt-2">
        <ul
          className={`border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 pl-3.5 ${
            isReadOnly ? "pr-2" : "pr-1.5"
          }`}
        >
          <li>
            <ul className="w-full">
              <li className="flex justify-between items-center">
                <Typography className="text-13 block text-primary-900 py-[5px]">
                  {_t("Number of Sub's Employees")}
                </Typography>
                <div className="sm:w-32 w-[70px]">
                  <InputField
                    className="cf-field-right !border-b-0 field-text-13 h-7 !text-[#008000] font-semibold"
                    placeholder={"# " + _t("Emp.")}
                    labelPlacement="left"
                    name="emp_on_site"
                    editInline={true}
                    iconView={true}
                    readOnly={isReadOnly}
                    readOnlyClassName="justify-end sm:w-full !text-13"
                    value={
                      inputValues?.emp_on_site && inputValues.emp_on_site != 0
                        ? inputValues.emp_on_site
                        : ""
                    }
                    fixStatus={getStatusForField(loadingStatus, "empOnSite")}
                    inputStatusClassName="!w-[15px] !h-[15px]"
                    iconClassName="!w-3 !h-3"
                    onChange={handleInpOnChange}
                    onMouseEnter={() => {
                      handleChangeFieldStatus({
                        field: "empOnSite",
                        status: "edit",
                        action: "ME",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      handleChangeFieldStatus({
                        field: "empOnSite",
                        status: "button",
                        action: "ML",
                      });
                    }}
                    onFocus={() =>
                      handleChangeFieldStatus({
                        field: "empOnSite",
                        status: "save",
                        action: "FOCUS",
                      })
                    }
                    onBlur={(e) => {
                      const value = e.target.value.trim();
                      if (
                        Number(value) != subsOnJobSite?.emp_on_site &&
                        (subsOnJobSite?.emp_on_site != null || value)
                      ) {
                        handleUpdateField({ empOnSite: value });
                      } else {
                        handleChangeFieldStatus({
                          field: "empOnSite",
                          status: "button",
                          action: "BLUR",
                        });
                      }
                    }}
                    onKeyDown={(
                      event: React.KeyboardEvent<HTMLInputElement>
                    ) => {
                      if (event.key === "Enter") {
                        setInputValues({
                          ...inputValues,
                          emp_on_site: Number(event?.currentTarget?.value),
                        });
                      }
                      return onKeyDownDigit(event, {
                        integerDigits: 3,
                      });
                    }}
                  />
                </div>
              </li>
            </ul>
            <div className="py-0.5 relative">
              <Typography className="w-5 h-5 flex items-center justify-center bg-white rounded-full !shadow-[0px_0px_10px] !shadow-black/10 !absolute top-1/2 -translate-y-1/2 left-[calc(50%-4px)] -translate-x-1/2">
                <FontAwesomeIcon
                  className="w-3 h-3 text-primary-900 dark:text-white"
                  icon="fa-regular fa-xmark"
                />
              </Typography>
            </div>
            <ul className="w-full">
              <li className="flex justify-between items-center">
                <Typography className="text-13 block text-primary-900 py-[5px]">
                  {_t("Hours on Site")}
                </Typography>
                <div className="w-32">
                  <TimePickerField
                    label=""
                    name="time_on_site"
                    placeholder="00:00"
                    className={`cf-field-right py-1 field-text-13 h-7 block !border-b-0 text-green-sucess font-semibold !text-[#008000] ${
                      getStatusForField(loadingStatus, "timeOnSite") ==
                        "loading" ||
                      getStatusForField(loadingStatus, "timeOnSite") ==
                        "success" ||
                      getStatusForField(loadingStatus, "timeOnSite") == "error"
                        ? "pr-7"
                        : "pr-2.5"
                    }`}
                    labelPlacement="top"
                    editInline={true}
                    iconView={true}
                    readOnly={isReadOnly}
                    readOnlyClassName="justify-end sm:w-full !text-13"
                    allowClear={false}
                    suffixIcon={false}
                    showNow={false}
                    format="HH:mm"
                    value={dayjs(
                      inputValues?.time_on_site?.trim() || "00:00",
                      "HH:mm"
                    )}
                    statusIconClassName="flex"
                    fixStatus={getStatusForField(loadingStatus, "timeOnSite")}
                    inputStatusClassName="!w-[15px] !h-[15px]"
                    iconClassName="!w-3 !h-3"
                    onChange={(_, val) => {
                      if (inputValues?.time_on_site !== val) {
                        handleChangeDate(val as string);
                      }
                    }}
                  />
                </div>
              </li>
            </ul>
          </li>
        </ul>
        <div className="my-3.5 border relative border-dashed border-gray-300">
          <Typography className="w-5 h-5 flex items-center justify-center bg-white rounded-full !shadow-[0px_0px_10px] !shadow-black/10 !absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
            <FontAwesomeIcon
              className="w-3 h-3 text-primary-900 dark:text-white"
              icon="fa-regular fa-equals"
            />
          </Typography>
        </div>
        <ul className="w-full border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-3.5">
          <li className="flex justify-between">
            <Typography className="text-13 block text-primary-900 font-semibold">
              {_t("Total Man Hours")}
            </Typography>
            <Typography className="text-[#E25A32] text-13 text-right sm:w-32 w-[70px] block font-semibold">
              {subsOnJobSite?.total_hours_on_site !== "0"
                ? subsOnJobSite.total_hours_on_site
                : "00:00"}
            </Typography>
          </li>
        </ul>
        <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-0.5 pl-3.5 pr-2.5 mt-[7px]">
          <li className="flex">
            <Typography className="text-13 flex items-center text-primary-900 font-semibold whitespace-nowrap h-[30px]">
              {_t("Notes")}:
            </Typography>
            <TextAreaField
              labelPlacement="left"
              formInputClassName="sm:gap-0.5"
              labelClass="hidden"
              className="!text-13"
              readOnlyClassName="!text-13"
              editInline={true}
              iconView={true}
              readOnly={isReadOnly}
              placeholder={_t("Work Notes")}
              ref={decInpRef}
              name="notes"
              value={inputValues?.notes || ""}
              fixStatus={getStatusForField(loadingStatus, "notes")}
              onChange={handleInpOnChange}
              onMouseEnter={() => {
                handleChangeFieldStatus({
                  field: "notes",
                  status: "edit",
                  action: "ME",
                });
              }}
              onMouseLeaveDiv={() => {
                handleChangeFieldStatus({
                  field: "notes",
                  status: "button",
                  action: "ML",
                });
              }}
              onFocus={() =>
                handleChangeFieldStatus({
                  field: "notes",
                  status: "save",
                  action: "FOCUS",
                })
              }
              onBlur={(e) => {
                const value = e.target.value.trim();
                if (
                  value !== subsOnJobSite?.notes &&
                  (subsOnJobSite?.notes !== null || value)
                ) {
                  handleUpdateField({ notes: value });
                } else {
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "button",
                    action: "BLUR",
                  });
                  setInputValues({
                    ...inputValues,
                    notes: subsOnJobSite.notes,
                  });
                }
              }}
              onClickStsIcon={() => {
                if (getStatusForField(loadingStatus, "notes") === "edit") {
                  decInpRef.current?.focus();
                }
              }}
            />
          </li>
        </ul>
      </div>
    </div>
  );
};

export default SubsOnJobsiteCard;
