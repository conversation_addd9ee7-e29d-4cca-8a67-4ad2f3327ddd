import { useEffect } from "react";
import { useParams } from "@remix-run/react";
// Hook + redux
import { getCommonSidebarCollapse, getGConfig, getGSettings } from "~/zustand";
import {
  useAppSMDispatch,
  useAppSMSelector,
} from "~/modules/people/safetymeetings/redux/store";
import { fetchSMDetails } from "~/modules/people/safetymeetings/redux/action/sMDetailsAction";
import {
  fetchSMFilePhoto,
  fetchSMNotes,
} from "~/modules/people/safetymeetings/redux/action";
import { resetSMFiles } from "~/modules/people/safetymeetings/redux/slices/sMFilePhotoSlice";
import { resetSMNotes } from "~/modules/people/safetymeetings/redux/slices/sMNotesSlice";
import { fetchSafetyTopicData } from "~/modules/people/safetymeetings/redux/action/commonSMAction";

// Components
import { Spin } from "~/shared/components/atoms/spin";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
import SafetyMeetingTopBar from "~/modules/people/safetymeetings/components/tab/SafetyMeetingTopBar";
import SafetyMeetingDetailsTab from "~/modules/people/safetymeetings/components/tab/SafetyMeetingDetailsTab";
import FilePhotoTab from "~/modules/people/safetymeetings/components/tab/FilePhotoTab";
import NotesTab from "~/modules/people/safetymeetings/components/tab/NotesTab";

const ManageSafetyMeetingTab = () => {
  const { id: sMId, tab }: RouteParams = useParams();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const { module_id, module_key, module_access }: GConfig = getGConfig();
  const { comment_order }: GSettings = getGSettings();
  const dispatch = useAppSMDispatch();
  const { isDetailLoading, details }: ISMDetailsInitialState = useAppSMSelector(
    (state) => state.safetyMeetingDetails
  );
  const { isFileDataFetched }: ISMFilePhotoInitialState = useAppSMSelector(
    (state) => state.sMFilePhoto
  );
  const { isNotesDataFetched }: ISMNotesInitialState = useAppSMSelector(
    (state) => state.safetyMeetingNotes
  );

  const fetchData = (isReload: boolean = false) => {
    if (!module_id || module_id == 0) {
      return;
    }

    if (tab === "files" && sMId && (isReload || !isFileDataFetched)) {
      dispatch(
        fetchSMFilePhoto({
          record_id: parseInt(sMId),
          module_key: module_key,
        })
      );
    }
    if (tab === "notes" && sMId && (isReload || !isNotesDataFetched)) {
      dispatch(
        fetchSMNotes({
          record_id: parseInt(sMId),
          module_key: module_key,
          module_id: module_id,
          order: comment_order,
        })
      );
    }

    if (isReload) {
      dispatch(fetchSMDetails({ id: sMId || "", add_event: true }));
      dispatch(
        fetchSafetyTopicData({
          start: 0,
          limit: 0,
          only_topics: 1,
          status: "0",
          language: "0,1",
        })
      );
    }
  };

  useEffect(() => {
    fetchData();
  }, [tab, module_id]);

  useEffect(() => {
    if (sMId) {
      dispatch(resetSMFiles());
      dispatch(resetSMNotes());
    }
  }, [sMId]);

  return (
    <>
      <div
        className={`ease-in-out duration-300 w-full overflow-y-auto ${
          sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
        }`}
      >
        <SafetyMeetingTopBar
          sidebarCollapse={sidebarCollapse}
          onReloadDetails={() => fetchData(true)}
        />

        {!isDetailLoading && (
          <ReadOnlyPermissionMsg
            className="p-4 pt-0"
            view={module_access === "read_only"}
          />
        )}
        <div className={window.ENV.PAGE_IS_IFRAME ? "" : "md:pb-0 pb-10"}>
          <div
            className={`px-[15px] pb-[15px] ${
              module_access === "read_only"
                ? window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-180px)] min-h-[calc(100dvh-230px)]"
                  : "md:min-h-[calc(100dvh-344px)] min-h-[calc(100dvh-415px)]"
                : window.ENV.PAGE_IS_IFRAME
                ? "md:min-h-[calc(100dvh-146px)] min-h-[calc(100dvh-178px)]"
                : "md:min-h-[calc(100dvh-306px)] min-h-[calc(100dvh-357px)]"
            }`}
          >
            {isDetailLoading ? (
              <Spin
                className={`flex items-center justify-center ${
                  window.ENV.PAGE_IS_IFRAME
                    ? "md:h-[calc(100dvh-161px)] h-[calc(100dvh-212px)]"
                    : "md:h-[calc(100dvh-304px)] h-[calc(100dvh-360px)]"
                }`}
              />
            ) : (
              <>
                {tab === "details" || tab === undefined ? (
                  <SafetyMeetingDetailsTab />
                ) : tab === "files" ? (
                  <FilePhotoTab />
                ) : (
                  tab === "notes" && <NotesTab />
                )}
              </>
            )}
          </div>
          <TimeLineFooter
            data={{
              addedDate: details?.date_added || "",
              addedTime: details?.time_added || "",
              addedBy: details?.added_by || "",
              moduleId: module_id,
              recordId: Number(details?.meeting_id || ""),
            }}
            sidebarCollapse={sidebarCollapse}
            isLoading={isDetailLoading}
          />
        </div>
      </div>
    </>
  );
};

export default ManageSafetyMeetingTab;
