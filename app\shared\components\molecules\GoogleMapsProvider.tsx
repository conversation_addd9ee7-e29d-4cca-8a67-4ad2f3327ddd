// components/providers/GoogleMapsProvider.tsx
import { Loader } from "@googlemaps/js-api-loader";
import { useEffect, useState } from "react";
import { Spin } from "~/shared/components/atoms/spin";

interface GoogleMapsProviderProps {
  children: React.ReactNode;
}

export const GoogleMapsProvider = ({ children }: GoogleMapsProviderProps) => {
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const load = async () => {
      const loader = new Loader({
        apiKey: "AIzaSyDaEBvq3cffsjIZrU-S3o7mOqsRP43PeNc",
        libraries: ["places", "geometry"],
      });

      await loader.load();
      setReady(true);
    };

    load();
  }, []);

  if (!ready) {
    console.warn("Google map isn't loaded yet!");
    return (
      <Spin
        className="w-full flex items-center justify-center"
        style={{ height: "calc(100dvh - 50px)" }}
      />
    );
  }

  return <>{children}</>;
};
