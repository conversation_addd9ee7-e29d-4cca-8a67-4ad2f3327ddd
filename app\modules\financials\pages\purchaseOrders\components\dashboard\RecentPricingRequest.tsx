// Atoms
import { <PERSON>lt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { SelectField } from "~/shared/components/molecules/selectField";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Hook
import { useTranslation } from "~/hook";
// Other
import { RECENT_REQUEST } from "../../utils/constants";
import { useAppPODispatch, useAppPOSelector } from "../../redux/store";
import { useEffect, useRef, useState, useCallback } from "react";
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
import { fetchDashData } from "../../redux/action/dashboardAction";
import { useNavigate } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { formatValueToString } from "../../utils/function";

const RecentPricingRequest = () => {
  const { _t } = useTranslation();
  const dispatch = useAppPODispatch();
  const navigate = useNavigate();
  const gridApiRef = useRef<GridApi | null>(null);
  const gridApiRefLoading = useRef<GridApi | null>(null);
  const {
    isDashLoading,
    recent_pricing_request,
    recentPricingRequestLastRefresTime,
  }: IPODashState = useAppPOSelector((state) => state.dashboard);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  const onGridLoadingReady = (params: GridReadyEvent) => {
    gridApiRefLoading.current = params?.api as GridApi;
  };
  const [isLoading, setIsLoading] = useState(false);
  const [rowData, setRowData] = useState<IRecentlyDeliveredItems[]>([]);
  const [selectedFilter, setSelectedFilter] =
    useState<keyof IRecentPricingRequest>("recently_submitted");
  const [lastRefreshTime, setLastRefreshTime] = useState(0);

  const handleRefreshWidget = useCallback(async () => {
    const now = Date.now();
    // Prevent multiple simultaneous calls and debounce rapid clicks (500ms)
    if (isLoading || isDashLoading || now - lastRefreshTime < 500) return;

    setLastRefreshTime(now);
    setIsLoading(true);
    setRowData([]);
    try {
      await dispatch(fetchDashData({ refresh_type: "recent_pricing_request" }));
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, isDashLoading, lastRefreshTime, dispatch]);
  useEffect(() => {
    console.log("useEffect triggered:", {
      recent_pricing_request,
      selectedFilter,
      data: recent_pricing_request?.[selectedFilter],
      isDashLoading,
      isLoading,
    });

    // Always update rowData when recent_pricing_request changes, regardless of loading state
    if (
      recent_pricing_request &&
      recent_pricing_request[selectedFilter] !== undefined
    ) {
      console.log("Setting rowData:", recent_pricing_request[selectedFilter]);
      setRowData(recent_pricing_request[selectedFilter] || []);
    } else if (!isDashLoading && !isLoading) {
      // Only clear data if we're not loading
      console.log("Clearing rowData");
      setRowData([]);
    }
  }, [recent_pricing_request, selectedFilter, isDashLoading, isLoading]);
  const columnDefs = [
    {
      headerName: _t("Submitted"),
      maxWidth: rowData?.length > 0 ? 135 : 100,
      minWidth: rowData?.length > 0 ? 135 : 100,
      field: "date_added",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashRecentPricingTableCellRenderer) => {
        return <DateTimeCard format="date" date={data?.date_added} />;
      },
    },
    {
      headerName: _t("PO") + " #",
      field: "prefix_company_purchase_order_id",
      minWidth: rowData?.length > 0 ? 122 : 60,
      flex: 2,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashRecentPricingTableCellRenderer) => {
        const id = formatValueToString(data?.prefix_company_purchase_order_id);
        return !!id ? (
          <Tooltip title={id}>
            <Typography className="table-tooltip-text">{id}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Vendor"),
      field: "vendor",
      minWidth: rowData?.length > 0 ? 122 : 80,
      flex: 2,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashRecentPricingTableCellRenderer) => {
        const vendor = formatValueToString(data?.vender_name);
        return vendor ? (
          <Tooltip title={vendor}>
            <Typography className="table-tooltip-text">
              {data?.vender_name ? vendor : "-"}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];
  const noData = () => (
    <NoRecords
      className="gap-2"
      rootClassName="w-16"
      image={`${window.ENV.CDN_URL}assets/images/no-records-po-recent-pricing-requests.svg`}
    />
  );
  const noRowsOverlay = () => (
    <>
      <div className="xl:block hidden">
        <StaticTableRowLoading columnDefs={columnDefs} limit={3} />
      </div>
      <div className="xl:hidden block">
        <StaticTableRowLoading columnDefs={columnDefs} limit={5} />
      </div>
    </>
  );
  console.log("Debug:", {
    isDashLoading,
    isLoading,
    condition: isDashLoading || isLoading,
    rowDataLength: rowData?.length,
    rowData: rowData,
    selectedFilter,
    recent_pricing_request_full: recent_pricing_request,
    recent_pricing_request_filtered: recent_pricing_request?.[selectedFilter],
    available_keys: recent_pricing_request
      ? Object.keys(recent_pricing_request)
      : [],
  });
  return (
    <>
      <DashboardCardHeader
        title={_t("Recent Pricing Requests")}
        showRefreshIcon={true}
        refreshIconTooltip={recentPricingRequestLastRefresTime}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isLoading}
        rightContent={
          <SelectField
            labelPlacement="top"
            applyBorder={true}
            formInputClassName="w-[160px] overflow-visible"
            fieldClassName="before:hidden"
            containerClassName="overflow-visible"
            className="border-select-filed rounded h-7"
            options={RECENT_REQUEST}
            showSearch={false}
            popupClassName="popup-select-option-header"
            value={selectedFilter}
            onChange={(value) =>
              setSelectedFilter(value as keyof IRecentPricingRequest)
            }
          />
        }
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine xl:h-[135px] h-[208px]">
          {isDashLoading || isLoading ? (
            <StaticTable
              className="static-table ag-grid-cell-pointer"
              columnDefs={columnDefs}
              onGridReady={onGridLoadingReady}
              rowData={[]}
              enableOpenInNewTab={true}
              generateOpenInNewTabUrl={(data: { purchase_order_id?: number }) =>
                `${routes.MANAGE_PURCHASE_ORDERS.url}/${data?.purchase_order_id}`
              }
              noRowsOverlayComponent={noRowsOverlay}
            />
          ) : (
            <StaticTable
              className="static-table ag-grid-cell-pointer"
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              rowData={rowData ?? []}
              noRowsOverlayComponent={
                // (isDashLoading || isLoading) && rowData?.length === 0
                //   ? noRowsOverlay
                //   : rowData.length === 0
                // ?
                noData
                // : null
              }
              enableOpenInNewTab={true}
              generateOpenInNewTabUrl={(data: { purchase_order_id?: number }) =>
                `${routes.MANAGE_PURCHASE_ORDERS.url}/${data?.purchase_order_id}`
              }
            />
          )}
        </div>
        {/* {isDashLoading || isLoading ? (
          <>
            <div className="xl:block hidden">
              <StaticTableRowLoading columnDefs={columnDefs} limit={4} />
            </div>
            <div className="xl:hidden block">
              <StaticTableRowLoading columnDefs={columnDefs} limit={6} />
            </div>
          </>
        ) : ( */}
        {/* <div className="ag-theme-alpine xl:h-[135px] h-[208px]">
          <StaticTable
            className="static-table ag-grid-cell-pointer"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData ?? []}
            noRowsOverlayComponent={
              (isDashLoading || isLoading) && rowData?.length === 0
                ? noRowsOverlay
                : rowData.length === 0
                ? noData
                : null
            }
            enableOpenInNewTab={true}
            generateOpenInNewTabUrl={(data: { purchase_order_id?: number }) =>
              `${routes.MANAGE_PURCHASE_ORDERS.url}/${data?.purchase_order_id}`
            }
          />
        </div> */}
        {/* )} */}
      </div>
    </>
  );
};

export default RecentPricingRequest;
