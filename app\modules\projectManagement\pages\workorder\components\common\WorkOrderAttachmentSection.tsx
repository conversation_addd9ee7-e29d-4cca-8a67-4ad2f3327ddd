// React + ag-grid
import { useMemo, useState } from "react";
import { useParams } from "@remix-run/react";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { GradientIcon } from "~/shared/components/molecules/gradientIcon";
// organisms
import { attachmentSection as AttachmentSection } from "~/shared/components/organisms/attachmentSection";
// Redux + Store
import { addUpDelFilesAct } from "~/redux/slices/commonAttachmentSlice";
import { useWoAppDispatch, useWoAppSelector } from "../../redux/store";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { filterAttachmentFiles } from "~/shared/utils/helper/common";
import { useTranslation } from "~/hook";
import { addWorkorderFilePhotoApi } from "../../redux/action/workorderFilePhotoAction";
import { defaultConfig } from "~/data";

const WorkOrderAttachmentSection = () => {
  const { _t } = useTranslation();
  const [res, setRes] = useState({});
  const { module_id, module_key, module_access }: GConfig = getGConfig();
  const { id }: RouteParams = useParams();
  const { details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );

  const { checkModuleAccessByKey } = useGModules();
  const { date_format, image_resolution }: GSettings = getGSettings();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const dispatch = useWoAppDispatch();
  const { isFilePhotoLoading, aws_files }: ICommonFilePhotoInitialState =
    useWoAppSelector((state) => state.commonAttachmentData);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSaveAttachment = async (files: IFile[]) => {
    if (!isLoading) {
      const formData: IAddToDoFilePhotoFrm = {
        primary_id: Number(id),
        module_id: module_id,
        module_key: module_key,
      };
      if (files.length) {
        const { attachImage, awsFilesUrl }: IFilterAttachmentFiles =
          filterAttachmentFiles(files);
        formData.attach_image = attachImage.length
          ? attachImage.join(",")
          : undefined;
        const updatedAwsFilesUrl = awsFilesUrl.map((file) => ({
          ...file,
          isheic: file.file_ext.toLowerCase() === "heic" ? 1 : 0, // Add 'isheic' key
        }));
        formData.files = updatedAwsFilesUrl.length
          ? updatedAwsFilesUrl
          : undefined;
        formData.project_id = details.project_id;
      } else {
        return false;
      }
      setIsLoading(true);
      const addFilesRes = (await addWorkorderFilePhotoApi(
        formData
      )) as IAddWorkOrderFilePhotoRes;
      setRes(addFilesRes);
      if (addFilesRes?.success) {
        dispatch(
          addUpDelFilesAct({
            files: addFilesRes?.data?.aws_files,
            action: "add",
          })
        );
      } else {
        notification.error({
          description: addFilesRes?.message,
        });
      }

      setIsLoading(false);
      setRes({});
    }
  };
  const NoRecordComp = () => {
    return (
      <>
        <GradientIcon
          title={_t(`Files`)}
          svgIcons={{
            icon: "fa-solid fa-file-image",
            containerClassName:
              "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
            id: "add_attachments_icon",
            colors: ["#50EBFD", "#5996E9"],
          }}
        />

        <NoRecords
          className="mx-auto"
          image={`${window.ENV.CDN_URL}assets/images/no-records-files.svg`}
        />
      </>
    );
  };
  return !Boolean((aws_files ?? []).length) && isReadOnly ? (
    <NoRecordComp />
  ) : (
    <AttachmentSection
      projectid={details.project_id}
      validationParams={{
        date_format,
        file_support_module_access: checkModuleAccessByKey(
          defaultConfig.file_support_key
        ),
        image_resolution,
        module_key,
        module_id,
        module_access,
      }}
      isReadOnly={isReadOnly}
      isAddAllow={!isReadOnly}
      title={_t("Files")}
      files={aws_files || []}
      isLoadingSection={isFilePhotoLoading && aws_files?.length < 1}
      isLoading={isLoading}
      onAddAttachment={(data) => {
        const filtered = data.filter(
          (file) =>
            !(aws_files || []).some((exist) => exist.image_id === file.image_id)
        );
        handleSaveAttachment(filtered);
      }}
      onFileUpdated={(data) => {
        if (!data?.success) {
          notification.error({
            description: data?.message || "Something went wrong!",
          });
          return;
        }
        dispatch(
          addUpDelFilesAct({
            files: data?.isAddNew == 1 ? [data.data] : data.data,
            action: data?.isAddNew == 1 ? "add" : "update",
          })
        );
      }}
      onDeleteFile={(data) => {
        dispatch(
          addUpDelFilesAct({
            files: data,
            action: "delete",
          })
        );
      }}
      addFilesRes={res}
    />
  );
};

export default WorkOrderAttachmentSection;
