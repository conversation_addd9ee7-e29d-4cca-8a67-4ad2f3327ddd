import { type RadioChangeEvent } from "antd";
import { useRef, useState, useMemo } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { ApexChart } from "~/shared/components/atoms/chart";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import BarHorizontalChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarHorizontalChart.skeleton";
// Other
import { useApexCharts } from "~/shared/hooks/useApexCharts";
import { type Grid<PERSON><PERSON>, type GridReadyEvent } from "ag-grid-community";
import { WORK_ORDER_TAB } from "../../utils/constasnts";
import { useWoAppDispatch, useWoAppSelector } from "../../redux/store";
import { fetchWorkOrderDashboardApi } from "../../redux/action/workorderDashAction";
import { sanitizeString } from "~/helpers/helper";

const OpenItemsAssignedContact = () => {
  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const dispatch = useWoAppDispatch();
  const {
    resOpenItemsByAssignedPerson,
    isInitialLoad,
    resOpenItemsByAssignedPersonLastRefreshTime,
  } = useWoAppSelector((state) => state.workorderDashboard);
  const gridApiRef = useRef<GridApi | null>(null);
  const [value, setValue] = useState<string>("chart");
  const { _t } = useTranslation();

  const handleClickRefresh = async () => {
    setisCashLoading(true);
    await dispatch(
      fetchWorkOrderDashboardApi({
        refreshType: "res_open_items_by_assigned_person",
      })
    );
    setisCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Assigned To"),
      field: "assigned_to",
      minWidth: 150,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: ({ data }: IOpenItemByAssigneeTableCellRenderer) => {
        const assignedTo = HTMLEntities.decode(
          sanitizeString(data.assignee_name)
        );
        return assignedTo ? (
          <Tooltip title={assignedTo}>
            <Typography className="table-tooltip-text text-center">
              {assignedTo}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "# " + _t("of Work Orders"),
      field: "no_of_work_orders",
      minWidth: 130,
      maxWidth: 130,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMenu: true,
    },
  ];

  const SeriesDataarea = [
    {
      name: _t("Work Orders"),
      data: resOpenItemsByAssignedPerson?.data.map(
        (assignee: IResOpenIteamByAssigneeData) => assignee.no_of_work_orders
      ),
    },
  ];

  const optionsLine = useApexCharts({ type: "bar" });
  const options = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      stroke: {
        show: false,
        curve: "smooth",
        colors: undefined,
        width: 2,
        dashArray: 0,
      },
      markers: {
        size: [4, 7],
      },

      title: {
        text: undefined,
      },
      subtitle: {
        text: undefined,
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: "top", // top, center, bottom
          },
          horizontal: true,
        },
      },
      grid: {
        strokeDashArray: 5,
      },
      yaxis: {
        show: true,
      },
      xaxis: {
        categories: resOpenItemsByAssignedPerson?.data.map(
          (assignee: IResOpenIteamByAssigneeData) =>
            HTMLEntities.decode(sanitizeString(assignee.assignee_name))
        ),
        labels: {
          show: true,
          style: {
            fontSize: "12px",
            fontWeight: 600,
          },
        },
      },
      colors: ["#79B7D9"],
      legend: {
        show: true,
        position: "top",
        markers: {
          radius: 100,
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
    };
  }, [resOpenItemsByAssignedPerson]);

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  return (
    <>
      <DashboardCardHeader
        title={_t("Open Work Order by Assigned Person")}
        rightContent={
          <div className="flex items-center ">
            <ListTabButton
              value={value}
              options={WORK_ORDER_TAB}
              onChange={(e: RadioChangeEvent) => {
                setValue(e.target.value);
              }}
              className="first:border-r-0"
              activeclassName="!bg-[#EAE8E8] "
            />
          </div>
        }
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={resOpenItemsByAssignedPersonLastRefreshTime}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        {value === "chart" ? (
          <>
            {isInitialLoad || isCashLoading ? (
              <BarHorizontalChartSkeleton
                sizeClassName="h-[200px] py-3.5"
                yAxisLength={4}
                count={4}
              />
            ) : (
              resOpenItemsByAssignedPerson && (
                <ApexChart
                  key={JSON.stringify(resOpenItemsByAssignedPerson)}
                  series={SeriesDataarea}
                  options={options}
                  type={"bar"}
                  height={200}
                />
              )
            )}
          </>
        ) : value === "table" ? (
          <div className="ag-theme-alpine h-[209px]">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              rowData={
                !(isInitialLoad || isCashLoading)
                  ? resOpenItemsByAssignedPerson?.data
                  : undefined
              }
              noRowsOverlayComponent={() =>
                isInitialLoad || isCashLoading ? (
                  <StaticTableRowLoading columnDefs={columnDefs} />
                ) : (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-td-open-assigned-contact.svg`}
                  />
                )
              }
            />
          </div>
        ) : (
          <></>
        )}
      </div>
    </>
  );
};
export default OpenItemsAssignedContact;
