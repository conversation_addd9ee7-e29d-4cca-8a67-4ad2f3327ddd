// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Hook
import { useTranslation } from "~/hook";
// Other
import { useAppPODispatch, useAppPOSelector } from "../../redux/store";
import { useEffect, useRef, useState } from "react";
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
import { fetchDashData } from "../../redux/action/dashboardAction";
import { formatValueToString } from "../../utils/function";
import { useNavigate } from "@remix-run/react";
import { routes } from "~/route-services/routes";

const DeliveriesRunningLate = () => {
  const { _t } = useTranslation();
  const gridApiRef = useRef<GridApi | null>(null);
  const {
    isDashLoading,
    pastDeliveries,
    pastDeliveriesLastRefresTime,
  }: IPODashState = useAppPOSelector((state) => state.dashboard);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  const navigate = useNavigate();
  const dispatch = useAppPODispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [rowData, setRowData] = useState<IPastDeliveries[]>([]);
  const handleRefreshWidget = async () => {
    setIsLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refresh_type: "past_deliveries" }));
    setIsLoading(false);
  };
  useEffect(() => {
    if (!isLoading && pastDeliveries) {
      setRowData(pastDeliveries || []);
    }
  }, [pastDeliveries, isLoading]);
  const columnDefs = [
    {
      headerName: _t("Est. Delivery"),
      maxWidth: 135,
      minWidth: 135,
      field: "date_added",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashPastDeliveriesTableCellRenderer) => {
        return !!data?.date_added ? (
          <DateTimeCard
            format="date"
            date={formatValueToString(data?.date_added)}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("PO") + " #",
      field: "prefix_company_purchase_order_id",
      minWidth: 125,
      flex: 2,
      suppressMenu: true,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      cellRenderer: ({ data }: IPODashPastDeliveriesTableCellRenderer) => {
        const id = formatValueToString(data?.prefix_company_purchase_order_id);
        return !!id ? (
          <Tooltip title={id}>
            <Typography className="table-tooltip-text">{id}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Vendor"),
      field: "supplier_company_name",
      minWidth: 125,
      flex: 2,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      suppressMenu: true,
      cellRenderer: ({ data }: IPODashPastDeliveriesTableCellRenderer) => {
        const vendor = formatValueToString(data?.supplier_company_name);
        return !!vendor ? (
          <Tooltip title={vendor}>
            <Typography className="table-tooltip-text">{vendor}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-po-deliveries-running-late.svg`}
    />
  );
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  return (
    <>
      <DashboardCardHeader
        title={_t("Deliveries Running Late")}
        showRefreshIcon={true}
        onClickRefresh={handleRefreshWidget}
        refreshIconTooltip={pastDeliveriesLastRefresTime}
        isRefreshing={isLoading}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            className="static-table ag-grid-cell-pointer"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            key={isDashLoading ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isDashLoading || isLoading ? noRowsOverlay : noData
            }
            enableOpenInNewTab={true}
            generateOpenInNewTabUrl={(data: { purchase_order_id?: number }) =>
              `${routes.MANAGE_PURCHASE_ORDERS.url}/${data?.purchase_order_id}`
            }
          />
        </div>
      </div>
    </>
  );
};

export default DeliveriesRunningLate;
