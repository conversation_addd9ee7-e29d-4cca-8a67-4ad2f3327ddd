.apexcharts-legend-text,
.apexcharts-text tspan {
  font-family: "Open Sans", sans-serif !important;
}

.contacts-type-chart .apexcharts-inner circle,
.chart-donut-circle-remove .apexcharts-inner circle {
  @apply hidden;
}

.dark .apexcharts-xaxis-texts-g tspan,
.dark .apexcharts-yaxis-texts-g tspan,
.dark .apexcharts-datalabels-group text {
  @apply fill-white/90;
}


.dark .apexcharts-legend-text {
  @apply !text-white/90;
}

.dark .apexcharts-gridlines-horizontal .apexcharts-gridline,
.dark .apexcharts-grid-borders .apexcharts-gridline {
  @apply !stroke-white/10;
}

.dark .apexcharts-grid-borders line {
  @apply !stroke-white/30;
}

.apexcharts-tooltip .apexcharts-tooltip-title,
.apexcharts-tooltip .apexcharts-tooltip-text{
  @apply !font-sans
}

.apexcharts-tooltip .apexcharts-tooltip-title {
  @apply mb-0 py-1 px-1.5;
}

.apexcharts-tooltip .apexcharts-tooltip-text .apexcharts-tooltip-text-y-value {
  @apply m-0;
}

.apexcharts-tooltip .apexcharts-tooltip-series-group.apexcharts-active {
  @apply pb-0 leading-[14px];
}

.apexchart-custom-tooltip .apexcharts-tooltip{
  @apply !border-none !bg-transparent
}

.apexcharts-tooltip-custom{
  @apply border border-[#e3e3e3] bg-[#fffffff5] text-black font-medium rounded-[5px];
}

.apexcharts-tooltip-custom .tooltip-title{
  @apply bg-[#eceff1] border-b border-[#ddd] py-1 px-1.5 text-xs;
}

.apexcharts-tooltip-custom .tooltip-content{
  @apply px-2.5 py-[5px] text-xs flex items-center;
}

.apexcharts-tooltip-custom .tooltip-marker{
  @apply bg-[#95a4c1] w-3 h-3 rounded-full mr-2.5 block;
}

.apexcharts-legend-text .apexcharts-legend-value{
  @apply text-gray-400
}

.apexcharts-legend-text{
  @apply !text-xs
}

.apexcharts-legend.apexcharts-align-center.apx-legend-position-top{
  @apply !top-0
}

.donut-chart circle,
.project-unpaid-items circle,
.todo-open-items-project circle,
.hours-money-charts circle,
.won-lost-chart circle {
  @apply rounded-full fill-white outline outline-1 outline-[#2235584d] dark:outline-white/30 h-12 w-12 drop-shadow-[0_0_3px_#00000066] dark:fill-dark-800 dark:drop-shadow-[0_0_3px_#ffffff4d];
}

.radialchart-common circle {
  @apply dark:fill-dark-800;
}

.cost-by-type .apexcharts-canvas {
  @apply 2xl:!w-full
}

.cost-by-type svg,
.cost-by-type foreignObject{
  @apply h-[120px] w-full
}


.cost-by-type circle,
.cost-based-type .apexcharts-pie circle {
  cy: 44px;
}

.donut-chart circle,
.hours-money-charts circle {
  r: 40;
}

.project-unpaid-items circle {
  r: 48;
}
.todo-open-items-project circle {
  r: 43;
}

.overdue-hover-div:hover .project-unpaid-items [seriesName="Paid"],
.overdue-hover-div:hover .project-unpaid-items [seriesName="Unpaid"],
.paid-hover-div:hover .project-unpaid-items [seriesName="Overdue"],
.paid-hover-div:hover .project-unpaid-items [seriesName="Unpaid"],
.unpaid-hover-div:hover .project-unpaid-items [seriesName="Overdue"],
.unpaid-hover-div:hover .project-unpaid-items [seriesName="Paid"],
.overdue-hover-div:hover .project-unpaid-items [seriesName="Paid"],
.overdue-hover-div:hover .project-unpaid-items [seriesName="Unpaid"],
.overdue-hover-div:hover .contacts-type-chart [seriesName="Paid"],
.overdue-hover-div:hover .contacts-type-chart [seriesName="Unpaid"],
.paid-hover-div:hover .contacts-type-chart [seriesName="Overdue"],
.paid-hover-div:hover .contacts-type-chart [seriesName="Unpaid"],
.unpaid-hover-div:hover .contacts-type-chart [seriesName="Overdue"],
.unpaid-hover-div:hover .contacts-type-chart [seriesName="Paid"],
.scheduled-hover-div:hover .contacts-type-chart [seriesName="ThisxWeek"],
.close-hover-div:hover .contacts-type-chart [seriesName="LastxWeek"],
.scheduled-hover-div:hover .contacts-type-chart [seriesName="Closed"],
.close-hover-div:hover .contacts-type-chart [seriesName="Scheduled"] {
  transition: 0.15s ease all;
  opacity: 0.2;
}

.apexcharts-legend-series {
  @apply flex items-center;
}

.chart-outer-block.est_reg_amount [seriesName="OvertimexAmount"],
.chart-outer-block.est_reg_amount [seriesName="CmtxxRegularxAmount"],
.chart-outer-block.overtime_amount [seriesName="CmtxxRegularxAmount"],
.chart-outer-block.overtime_amount [seriesName="EstxxRegularxAmount"],
.chart-outer-block.cmt_reg_amount [seriesName="OvertimexAmount"],
.chart-outer-block.cmt_reg_amount [seriesName="EstxxRegularxAmount"] {
  @apply opacity-20 ease-in;
}

.hours-money-block .chart-outer-block {
  @apply flex flex-col-reverse;
}

.cost-by-type .apexcharts-legend {
  @apply flex flex-wrap w-[205px] !flex-row p-0 !right-0;
}

.cost-by-type .apexcharts-legend .apexcharts-legend-series {
  @apply w-[50%] !m-0;
}

.financial-summary-project .apexcharts-legend {
  @apply w-[185px] !justify-start;
}

.billing-summary-chart text {
  @apply !font-normal
}

.billing-summary-chart .apexcharts-legend-text {
  @apply !text-[11px]
}

.billing-summary-chart .apexcharts-legend-series,
.cost-based-type .apexcharts-legend-series {
  @apply w-fit !my-0.5 !mx-[5px] !text-xs
}

.billing-summary-chart .apexcharts-legend {
  display: flex;
  inset: auto 0px 1px !important;
  
}

.cost-based-type div[type="donut"],
.cost-based-type div[type="donut"] .apexcharts-canvas,
.cost-based-type div[type="donut"] .apexcharts-canvas svg,
.cost-based-type div[type="donut"] .apexcharts-canvas foreignObject {
  @apply !min-h-[150px] !h-[150px];
}

.radialchart-common,
.radialchart-common [type="radialBar"],
.radialchart-common .apexcharts-canvas,
.radialchart-common .apexcharts-svg,
.radialchart-common .apexcharts-inner {
  @apply h-[150px]
}

.cost-based-type div[type="donut"] {
  @apply !min-h-[150px] h-[150px];
}

.cost-by-type .apexcharts-inner {
  @apply 2xl:translate-x-0 2xl:translate-y-3;
}

.donut-chart .apx-legend-position-right:not(.donut-chart.legend-small-gap .apx-legend-position-right){
 @apply  gap-3
}
.legend-small-gap.donut-chart .apx-legend-position-right{
 @apply  gap-0.5
}
/* --------------------NEW CSS------------20/08/2024-------------- */
.overtime-amount:hover .apexcharts-series[seriesName="Closed"],
.reg-amount:hover .apexcharts-series[seriesName="Scheduled"]{
  transition: 0.15s ease all;
  opacity: 0.2;
}

.overdue-amount:hover .apexcharts-series[seriesName="Paid"],
.overdue-amount:hover .apexcharts-series[seriesName="Unpaid"],
.paid-amount:hover .apexcharts-series[seriesName="Overdue"],
.paid-amount:hover .apexcharts-series[seriesName="Unpaid"],
.unpaid-amount:hover .apexcharts-series[seriesName="Overdue"],
.unpaid-amount:hover .apexcharts-series[seriesName="Paid"]
{
  transition: 0.15s ease all;
  opacity: 0.2;
}

.apexcharts-tooltip{
  @apply max-w-72 !whitespace-normal break-words
}

.apexcharts-bar-series .apexcharts-series [val="0"]{
  @apply fill-none border-none
}

.cost_by_type_chart .apexcharts-legend {
  @apply !flex-row flex-wrap w-[186px] overflow-hidden p-0 !bottom-auto !right-0
}
.cost_by_type_chart .apexcharts-legend .apexcharts-legend-series{
  @apply w-[45%] h-5 !ml-0
}
.cost_by_type_chart .apexcharts-inner{
  @apply translate-x-0 translate-y-0 w-full
}

.cost_by_type_chart circle{
  @apply hidden
}

.invoice-summary-chart .apexcharts-datalabels-group{
  @apply translate-y-1.5
}

.invoice-summary-chart .apexcharts-legend{
  @apply !p-0 !flex-nowrap overflow-hidden;
}

.invoice-summary-chart .apexcharts-legend-series{
  @apply !ml-0 last:!mr-0;
}

.apexcharts-legend-marker{
  @apply shrink-0
}

.est-reg-amount:hover .apexcharts-series[seriesName="OvertimexAmount"],
.est-reg-amount:hover .apexcharts-series[seriesName="CmtxxRegularxAmount"],
.overtime-amount:hover .apexcharts-series[seriesName="EstxxRegularxAmount"],
.overtime-amount:hover .apexcharts-series[seriesName="CmtxxRegularxAmount"],
.cmt-reg-amount:hover .apexcharts-series[seriesName="EstxxRegularxAmount"],
.cmt-reg-amount:hover .apexcharts-series[seriesName="OvertimexAmount"]
{
  transition: 0.15s ease all;
  opacity: 0.2;
}

.pro_source_chart .apexcharts-canvas,
.pro_source_chart .apexcharts-canvas svg,
.pro_source_chart [type="radialBar"] {
  @apply !h-[140px]
}

.task_assignment_chart .apexcharts-canvas,
.task_assignment_chart .apexcharts-canvas svg,
.task_assignment_chart [type="radialBar"]{
  @apply !w-[150px] !h-[140px]
}

.financial-summary-widget .apexcharts-legend{
  @apply !mx-auto !inset-x-0
}

.financial-summary-widget .origin-amt-chart .apexcharts-legend{
  @apply !w-[162px]
}

.financial-summary-widget .total-prj-amt-chart .apexcharts-legend{
  @apply !w-[145px]
}

.financial-summary-widget .amt-invoice-chart .apexcharts-legend{
  @apply !w-[120px]
}

.no-legend-border .apexcharts-legend .apexcharts-legend-marker {
  stroke: none;
}

.won-lost-chart circle{
  cx: 95px;
  cy: 95px;
  transform: scale(0.9);
  r:50;
}

.donut-chart-big-round circle{
  r:44;
}

.apexcharts-legend-series .apexcharts-legend-marker svg{
  @apply w-2.5 h-2.5 rounded-full
}

@media (min-width: 1100px) {
  .permits-by-type-chart .apexcharts-legend {
    @apply w-[220px] justify-center !bottom-auto grid grid-cols-2 !top-1/2 -translate-y-1/2;
  }
}

@media (max-width: 1630px) and (min-width: 1536px){
  .cost-by-type .apexcharts-legend{
    @apply w-[175px]
  }
}

@media (max-width: 1535px) and (min-width: 992px){
  .cost-by-type svg, 
  .cost-by-type foreignObject,
  .cost-by-type .apexcharts-canvas{
    @apply !h-[242px]
  }

  .cost-by-type svg .apexcharts-legend {
    @apply w-full h-[70px]
  }
}

@media (max-width: 1535px) and (min-width: 1024px){

  .cost_by_type_chart svg .apexcharts-legend{
    @apply !bottom-0 !left-1/2 -translate-x-1/2
  }
  .cost_by_type_chart .apexcharts-inner{
    @apply translate-x-[16%]
  }
  
  .cost_by_type_chart .apexcharts-canvas,
  .cost_by_type_chart .apexcharts-canvas > svg,
  .cost_by_type_chart .apexcharts-canvas svg foreignObject,
  .cost_by_type_chart{
    @apply !h-[230px]
  }
  .cost_by_type_chart .apexcharts-canvas > svg{
    @apply !translate-y-0
  }
}

@media (max-width: 1023px) {
  .cost_by_type_chart .apexcharts-canvas>svg {
    @apply !translate-y-0;
  }
}

@media(max-width:480px) {
  .cost_by_type_chart svg .apexcharts-legend {
    @apply !top-[100px] !bottom-0 !left-1/2 -translate-x-1/2
  }

  .cost_by_type_chart .apexcharts-inner {
    @apply translate-x-[25%]
  }

  .cost_by_type_chart .apexcharts-canvas,
  .cost_by_type_chart .apexcharts-canvas>svg,
  .cost_by_type_chart .apexcharts-canvas svg foreignObject,
  .cost_by_type_chart {
    @apply !h-[200px]
  }

  .cost_by_type_chart .apexcharts-canvas>svg {
    @apply !translate-y-0
  }
}

@media(max-width:450px){
  .pro_source_chart,
  .task_assignment_chart{
    @apply flex flex-wrap
  }
  .pro_source_chart li,
  .task_assignment_chart li{
    @apply w-[48%]
  }
  .invoice-summary-chart{
    @apply flex flex-wrap
  }
}

@media(max-width:400px) {
  .cost_by_type_chart .apexcharts-inner {
    @apply translate-x-[20%]
  }
}

@media(max-width:350px) {
  .cost_by_type_chart .apexcharts-inner {
    @apply translate-x-[15%]
  }
}
