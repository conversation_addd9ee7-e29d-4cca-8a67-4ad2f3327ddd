import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
// store
import {
  getGConfig,
  getGModuleFilters,
  setGModuleFilter,
  setIsFilterBeingApplied,
  setIsFilterUpdated,
  useGModules,
} from "~/zustand";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// Organisms
import { KanbanList } from "~/shared/components/organisms/kanbanView";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// FontAwesome File
import { WODashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/work-order/dashboard/regular";
import { WODashboardSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/work-order/dashboard/solid";
// Other
import OpenItemsProject from "~/modules/projectManagement/pages/workorder/components/dashboard/OpenItemsProject";
import OpenItemsAssignedContact from "~/modules/projectManagement/pages/workorder/components/dashboard/OpenItemsAssignedContact";
import ItemsStatus from "~/modules/projectManagement/pages/workorder/components/dashboard/ItemsStatus";
import WorkOrderList from "~/modules/projectManagement/pages/workorder/components/dashboard/WorkOrderList";
import { AddWorkOrder } from "~/modules/projectManagement/pages/workorder/components/sidebar";
import {
  fetchWorkOrderDashboardApi,
  getWorkOrderKanbanListApi,
} from "~/modules/projectManagement/pages/workorder/redux/action/workorderDashAction";
import WorkOrderFilter from "~/modules/projectManagement/pages/workorder/components/dashboard/WorkOrderFilter";
import { defaultConfig } from "~/data";
import PageDashboardHeader from "~/components/page/common/page-dashboard-header";
import uniq from "lodash/uniq";
import debounce from "lodash/debounce";
import isEqual from "lodash/isEqual";
import {
  updateKanbanSettingApi,
  updateKanbanSortingApi,
} from "~/redux/action/kanbanSettings";
import Sortable from "sortablejs";
import { WorkOrderTableDropdownItems } from "~/modules/projectManagement/pages/workorder/components/dashboard/WorkOrderTableDropdownItems";
import WorkorderStoreProvider from "~/modules/projectManagement/pages/workorder/redux/workorderStoreProvider";
import { RadioChangeEvent } from "antd";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { updateWorkOrderDetailApi } from "~/modules/projectManagement/pages/workorder/redux/action/workorderDetailsAction";
import { escapeHtmlEntities, Number } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { useNavigate, useSearchParams } from "@remix-run/react";
import { useWoAppDispatch } from "~/modules/projectManagement/pages/workorder/redux/store";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import { getGlobalUser } from "~/zustand/global/user/slice";

// Fort Awesome Library Add icons
WODashboardRegularIconAdd();
WODashboardSolidIconAdd();

const ManageWorkOrderCom = () => {
  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);

  const { _t } = useTranslation();
  const {
    module_id,
    module_name,
    page_is_iframe,
    module_access,
    module_singular_name,
    module_key,
  }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const navigate = useNavigate();
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingChild, setLoadingChild] = useState<boolean>(false);
  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [isDefaultViewKanbanLoading, setIsDefaultViewKanbanLoading] =
    useState<boolean>(false);
  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [kanbanSetting, setKanbanSetting] = useState<
    IKanbanSetting | IWorkOrderKanBanSettings
  >();
  const [kanbanListData, setKanbanListData] = useState<
    IWorkOrderKanBanColumnList[]
  >([]);
  const { searchProps, search, setModuleName }: IModuleDataSearchParams =
    useModuleDataSearch(defaultConfig.work_order_module, module_name);
  const [workorderToBeUpdate, setWorkorderToBeUpdate] =
    useState<IWorkOrderKanBanDataList | null>(null);
  const [isMyWoKanban, setIsMyWoKanban] = useState<boolean>(false);
  const limit = 15;
  const [pageBySection, setPageBySection] = useState<{ [key: string]: number }>(
    {}
  );

  const [loadingBySection, setLoadingBySection] = useState<{
    [key: string]: boolean;
  }>({});

  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);

  const filterSrv: Partial<WorkOrdersFilter> | undefined =
    getGModuleFilters() as Partial<WorkOrdersFilter> | undefined;

  const user: IInitialGlobalData["user"] = getGlobalUser();
  const [hasMoreBySection, setHasMoreBySection] = useState<
    Record<string, boolean>
  >({});

  const { formatter } = useCurrencyFormatter();

  const [searchParams, setSearchParams] = useSearchParams();

  const dispatch = useWoAppDispatch();

  const kanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);

  useEffect(() => {
    setModuleName(module_name);
  }, [module_name]);

  const filter = useMemo(() => {
    return {
      start_date: filterSrv?.start_date || "",
      end_date: filterSrv?.end_date || "",
      status:
        filterSrv?.status && filterSrv?.status !== "0" ? filterSrv?.status : "",
      project: filterSrv?.project || "",
      module_status: filterSrv?.module_status ?? "0",
      is_my_wo: filterSrv?.is_my_wo || "0",
    };
  }, [filterSrv]);
  const stageFilterKeys = (filter?.status?.split(",") || []).filter(
    (key) => key.trim() !== ""
  );
  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true); // Set loading state
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.work_order_module,
        })) as IIsKanbanEnableApiRes;

        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1); // Set the result based on API response
        } else {
          setIsKanbanEnabled(false); // Set false if API response indicates failure
        }
      } catch (error) {
        setIsKanbanEnabled(false); // Set false in case of an error
      } finally {
        setIsKanbanLoading(false); // Reset loading state
      }
    };

    fetchKanbanView();
  }, []);

  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const fetchKanbanWorkOrderList = async (
    type: string,
    isLoad: boolean = true
  ) => {
    type === "" ? setIsLoading(true) : setLoadingChild(isLoad);
    const pageForType = pageBySection[type] || 0;

    const tempFil: IWorkOrderTempFil = {
      is_my_wo: filter.is_my_wo,
      module_status: filter.module_status,
    };
    if (filter?.start_date) {
      tempFil.start_date = filter.start_date || "";
    }
    if (filter?.end_date) {
      tempFil.end_date = filter.end_date || "";
    }
    if (filter?.status) {
      tempFil.status = filter.status;
      tempFil.work_order_status_kanban = filter.status;
    }
    if (filter?.project) {
      tempFil.project = filter.project;
    }

    let dataParams: IWorkOrderKanBanListParmas = {
      filter: tempFil,
      start: pageForType,
      limit: limit,
      is_kanban: 1,
      ignore_filter: 1,
      any_status: !!type ? type : undefined,
      search: escapeHtmlEntities(search),
    };
    if (search === "") {
      delete dataParams.search;
    }

    try {
      const resData = (await getWorkOrderKanbanListApi(
        dataParams
      )) as IWorkOrderKanBanListApiRes;
      if (resData?.success) {
        let newTypes = resData?.data;

        newTypes = newTypes?.map((item) => {
          item.kanban_data = item.kanban_data?.map((kanbanData) => {
            kanbanData.total = `${_t("Amount: ")} ${
              formatter((Number(kanbanData.total || 0) / 100)?.toString())
                ?.value_with_symbol
            }`;
            kanbanData.prefix_company_work_order_id = `${_t("WO #")}${
              kanbanData.prefix_company_work_order_id ?? ""
            }`;
            return kanbanData;
          });
          return item;
        });

        if (pageForType === 0) {
          const data = newTypes.filter((item) => item !== null);
          setKanbanListData(data);
        } else {
          setKanbanListData((prevData) => {
            if (prevData.length === 0) {
              return newTypes;
            }

            const updateData = prevData.map((prevSection) => {
              const newSection = newTypes.find((d) => {
                if (d !== null) {
                  return d.item_id === prevSection.item_id;
                }
              });
              if (newSection) {
                const updatedSection = { ...prevSection };

                const newKanbanData = newSection.kanban_data.filter(
                  (newItem) =>
                    !updatedSection.kanban_data.some(
                      (existingItem) =>
                        existingItem.work_order_id === newItem.work_order_id
                    )
                );

                updatedSection.kanban_data.push(...newKanbanData);
                return updatedSection;
              }
              return prevSection;
            });
            return updateData;
          });
        }
        const newHasMoreBySection = newTypes.reduce(
          (
            acc: Record<number, boolean>,
            section: IWorkOrderKanBanColumnList
          ) => {
            const sectionDataLength = section.kanban_data.length;
            acc[section.item_id] = sectionDataLength >= limit;
            return acc;
          },
          {}
        );
        setHasMoreBySection((prev) => ({
          ...prev,
          ...newHasMoreBySection,
        }));
        setKanbanSelected(
          stageFilterKeys.length > 0
            ? stageFilterKeys
            : resData?.kanban_estimate_type_selected || []
        );
        setKanbanSetting(resData?.kanban_setting);
      } else {
        setKanbanListData([]);
        setKanbanSetting(undefined);
        notification.error({
          description: resData?.message || "Something went wrong!",
        });
      }
      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } catch (err) {
      setIsLoading(false);
      notification.error({
        description: (err as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsLoading(false);
      setLoadingChild(false);
    }
  };

  const handleLoadMore = (val: number) => {
    const isSectionLoading = loadingBySection[val];
    if (isSectionLoading) return;

    const hasMoreForSection = hasMoreBySection[val];
    const currentSectionPage = pageBySection[val] || 0;
    if (hasMoreForSection) {
      const nextPage = currentSectionPage + 1;
      setPageBySection((prev) => ({
        ...prev,
        [val]: nextPage,
      }));

      if (currentSectionPage !== 0) {
        setLoadingBySection((prev) => ({
          ...prev,
          [val]: true,
        }));
        fetchKanbanWorkOrderList(val.toString());
      }
    }
  };
  const previousValues = useRef({
    search,
  });

  const debouncedFetch = debounce(() => {
    fetchKanbanWorkOrderList("");
  }, 100);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search,
    };

    if (kanbanView) debouncedFetch();

    if (filterSrv && !isEqual(previousValues.current, currentValues)) {
      previousValues.current = { ...currentValues };
      setKanbanListData([]);
    }
  }, [filterSrv, search, kanbanView]);
  const handleCollapse = async (
    columnId: string,
    isCollapseCard: string,
    key: string
  ) => {
    // Optimistic update: Modify the UI state immediately before API call
    let updatedKanbanSelected = uniq(kanbanSelected);
    if (stageFilterKeys?.length > 0) {
      updatedKanbanSelected =
        !kanbanSelected.includes(columnId) && !kanbanSelected.includes(key)
          ? [...kanbanSelected, columnId]
          : kanbanSelected.filter(
              (value) => value !== columnId && value !== key
            );

      setKanbanSelected(updatedKanbanSelected);
      return;
    } else {
      if (updatedKanbanSelected.includes(columnId)) {
        updatedKanbanSelected = updatedKanbanSelected.filter(
          (value) => value !== columnId
        );
      } else {
        updatedKanbanSelected.push(columnId);
      }

      // Update the state to reflect changes immediately
      setKanbanSelected(updatedKanbanSelected);
    }

    try {
      const requestKanbanSetting = {
        module_field_id: updatedKanbanSelected,
        default_view: kanbanSetting?.default_view?.toString() ?? "0",
        module_id: module_id,
      };

      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;

      if (responseKanbanSetting.success) {
        // Update state based on API response
        setKanbanSetting(responseKanbanSetting?.data);
        setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        // Revert changes if the API call fails
        setKanbanSelected((prevState) => kanbanSelected);
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      // Revert changes if an error occurs
      setKanbanSelected((prevState) => kanbanSelected);
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanbanLoading(false);
    }
  };

  const handleDragDropEnd = async (event: Sortable.SortableEvent) => {
    const currentArray = kanbanListData?.map((data) => ({
      column_id: data.item_id,
      sort_order: Number(data.sort_order),
      sorting_id: data.sorting_id.toString(),
      column_name: data.name,
      type_id: data.item_id.toString(),
    }));

    const kanban_sorting: IKanbanSortingArray[] = currentArray.map(
      (data, index) => ({
        ...data,
        sort_order: index,
      })
    );

    try {
      const requestKanbanSetting: IWorkorderKanbanSorting = {
        kanban_sorting: kanban_sorting,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSortingApi(
        requestKanbanSetting
      )) as IKanbanSortingApiRes;
      if (!responseKanbanSetting.success) {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const handleCardDragDropEnd = async (event: Sortable.SortableEvent) => {
    const { from, to } = event;
    const fromColumnId =
      from?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    const toColumnId = to?.closest("[data-id]")?.getAttribute("data-id") ?? "";

    if (fromColumnId !== toColumnId) {
      const data = {
        work_order_status: toColumnId,
      };
      try {
        const updateRes = (await updateWorkOrderDetailApi({
          id: workorderToBeUpdate?.work_order_id,
          ...data,
        })) as ApiCallResponse;

        if (updateRes.success) {
          setKanbanListData((prevData) =>
            prevData.map((item) => {
              if (item.item_id == Number(toColumnId)) {
                return {
                  ...item,
                  total_count: (parseInt(item.total_count) + 1).toString(),
                };
              } else if (item.item_id == Number(fromColumnId)) {
                return {
                  ...item,
                  total_count: (parseInt(item.total_count) - 1).toString(),
                };
              }
              return item;
            })
          );
        } else {
          fetchKanbanWorkOrderList("", false);
          notification.error({
            description: updateRes?.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error)?.message,
        });
      }
    }
  };

  const handleDefaultViewKanabanChange = async (val: boolean) => {
    setIsDefaultViewKanbanLoading(true);
    try {
      const requestKanbanSetting = {
        default_view: val ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        // setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanbanLoading(false);
    }
  };

  const filterCallComplete = () => {
    setIsFilterUpdated(true);
  };

  const beforeFilterUpdateCallback = () => {
    setIsFilterUpdated(false);
    setIsFilterBeingApplied(true);
  };

  const updateFilter = useCallback(
    (filter: Partial<ToDoFilter>) => {
      if (module_id) {
        beforeFilterUpdateCallback();
        setGModuleFilter(filter, user, module_id, filterCallComplete);
      }
    },
    [module_id]
  );

  const handleMyWoKanabanChange = async (data: Partial<WorkOrdersFilter>) => {
    setIsMyWoKanban(true);
    try {
      updateFilter(data);
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsMyWoKanban(false);
    }
  };

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const WORK_ORDER_RIGHT_BUTTON_TAB = [
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-people-group" />
          {`All ${module_name}`}
        </div>
      ),
      value: "0",
    },
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-user" />
          {`My ${module_name}`}
        </div>
      ),
      value: "1",
    },
  ];

  useEffect(() => {
    dispatch(fetchWorkOrderDashboardApi());
  }, []);

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setDrawerOpen(true);
      if (isReadOnly) {
        setSearchParams({});
      }
    }
  }, [searchParams.get("action"), isReadOnly]);

  const CARD_VIEW_CHECK_BOX: {
    title: string;
    isChecked: boolean;
    onClick: MouseEventHandler<HTMLDivElement>;
  }[] = [
    {
      title: `My ${module_name}`,
      isChecked: filterSrv?.is_my_wo?.toString() == "1",
      onClick: () => {
        handleMyWoKanabanChange({
          is_my_wo: filterSrv?.is_my_wo?.toString() == "1" ? "0" : "1",
        });
      },
    },
    {
      title: "Set as Default View",
      isChecked: kanbanSetting?.default_view?.toString() == "1",
      onClick: () => {
        handleDefaultViewKanabanChange(
          kanbanSetting?.default_view?.toString() == "1" ? false : true
        );
      },
    },
  ];

  return (
    <>
      <WorkorderStoreProvider>
        <PageDashboardHeader
          viewSearch={viewSearch}
          setViewSearch={setViewSearch}
          moduleId={module_id}
          searchProps={searchProps}
          leftComponent={
            <>
              {!kanbanView ? (
                <li>
                  <ButtonWithTooltip
                    tooltipTitle={_t("Card View")}
                    tooltipPlacement="top"
                    icon="fa-brands fa-trello"
                    iconClassName="h-5 w-5"
                    className="!w-7 !h-7"
                    onClick={() => {
                      setIsKanbanEnabled(true);
                      // dispatch(setIsKanbanEnabled(true));
                    }}
                  />
                </li>
              ) : (
                <li>
                  <ButtonWithTooltip
                    tooltipTitle={_t("Return to Dashboard")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-square-list"
                    iconClassName="h-5 w-5"
                    className="!w-7 !h-7"
                    onClick={() => {
                      setIsKanbanEnabled(false);
                      // dispatch(setIsKanbanEnabled(false));
                    }}
                  />
                </li>
              )}
              <WorkOrderFilter
                beforeFilterUpdateCallback={beforeFilterUpdateCallback}
                kanbanView={isKanbanEnabled}
              />
            </>
          }
          rightComponent={
            <div className="flex flex-row sm:items-center items-start sm:gap-5 gap-2">
              {kanbanView ? (
                <>
                  <div className="md:flex hidden items-center sm:gap-5 gap-2">
                    {module_access !== "own_data_access" && (
                      <CustomCheckBox
                        className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] whitespace-nowrap"
                        checked={filterSrv?.is_my_wo?.toString() == "1"}
                        onChange={(e) =>
                          handleMyWoKanabanChange({
                            is_my_wo: e.target.checked ? "1" : "0",
                          })
                        }
                        disabled={isMyWoKanban}
                        loadingProps={{
                          isLoading: isMyWoKanban,
                          className: "bg-[#ffffff]",
                        }}
                      >
                        {_t(`My ${module_name}`)}
                      </CustomCheckBox>
                    )}
                    <CustomCheckBox
                      className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] whitespace-nowrap"
                      checked={kanbanSetting?.default_view?.toString() == "1"}
                      onChange={(e: CheckboxChangeEvent) =>
                        handleDefaultViewKanabanChange(e.target.checked)
                      }
                      disabled={isDefaultViewKanbanLoading}
                      loadingProps={{
                        isLoading: isDefaultViewKanbanLoading,
                        className: "bg-[#ffffff]",
                      }}
                    >
                      {_t("Set as Default View")}
                    </CustomCheckBox>
                  </div>
                  <Popover
                    placement="bottomRight"
                    content={
                      <div className="dark:bg-dark-900 min-w-[155px]">
                        <ul className="py-2 px-1 grid gap-0.5">
                          {CARD_VIEW_CHECK_BOX.map((item, i) => {
                            return (
                              <li
                                className={`rounded bg-blue-50 dark:bg-dark-800`}
                                key={i}
                              >
                                <div
                                  className="flex items-center justify-between cursor-pointer px-2 py-0.5"
                                  onClick={item.onClick}
                                >
                                  <Typography
                                    className={`text-primary-900 dark:text-white/90`}
                                  >
                                    {item.title}
                                  </Typography>
                                  {item?.isChecked && (
                                    <FontAwesomeIcon
                                      className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                      icon="fa-regular fa-check"
                                    />
                                  )}
                                </div>
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    }
                    trigger="click"
                    open={open}
                    className="flex md:hidden"
                    onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                  >
                    <div className="flex relative items-center justify-center">
                      <ButtonWithTooltip
                        icon="fa-regular fa-gear"
                        tooltipTitle=""
                        tooltipPlacement="top"
                        iconClassName="h-5 w-5"
                        className="!w-7 !h-7"
                        onClick={() => {}}
                      />
                    </div>
                  </Popover>
                </>
              ) : (
                <></>
              )}
              <>
                {!isReadOnly && (
                  <li>
                    <AddButton onClick={() => setDrawerOpen(true)}>
                      {_t(module_singular_name ?? "Work Order")}
                    </AddButton>
                  </li>
                )}
              </>
            </div>
          }
        />
        {!kanbanView && !isKanbanLoading ? (
          <div
            className={`md:pt-[41px] pt-[39px] overflow-y-auto overflow-hidden ${
              !page_is_iframe
                ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
                : "h-screen"
            }`}
          >
            <ReadOnlyPermissionMsg view={module_access === "read_only"} />
            <div className="p-4">
              <div
                className={`grid xl:grid-cols-3 lg:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                  fullScreenTable
                    ? "max-h-0 overflow-hidden"
                    : "min-[1536px]:max-h-[650px] md:max-h-[900px] max-h-[2000px]"
                }`}
              >
                <div className="common-card h-[266px]">
                  <OpenItemsProject />
                </div>
                <div className="common-card h-[266px]">
                  <OpenItemsAssignedContact />
                </div>
                <div className="common-card xl:col-span-1 lg:col-span-2 h-[266px]">
                  <ItemsStatus />
                </div>
              </div>
              <div
                className={`w-full ${
                  fullScreenTable ? "sm:mt-2.5 mt-10" : "sm:mt-7 mt-14"
                }`}
              >
                <div className="relative h-7 z-[999] flex items-center justify-end">
                  <AccordionButton
                    onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                    fullScreenTable={fullScreenTable}
                  />
                  {module_access !== "own_data_access" && (
                    <div className="w-full flex text-end sm:mb-7 mb-20">
                      <div className="w-full">
                        <div className="w-fit p-1 bg-[#EEEFF0] rounded dark:bg-dark-800">
                          <ListTabButton
                            value={filter?.is_my_wo}
                            options={WORK_ORDER_RIGHT_BUTTON_TAB}
                            className="sm:min-w-[100px] min-w-fit sm:px-1.5 !text-[#868D8D] px-2 !border-transparent bg-[#EEEFF0]"
                            activeclassName="active:bg-[#ffffff] !text-primary-900 !rounded-md"
                            onChange={(e: RadioChangeEvent) => {
                              handleMyWoKanabanChange({
                                is_my_wo: e.target.value,
                              });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                  <WorkOrderList
                    setDrawerOpen={setDrawerOpen}
                    fetchKanbanWorkOrderList={() =>
                      fetchKanbanWorkOrderList("")
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="pt-[41px] md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)] overflow-y-auto overflow-hidden">
            <ReadOnlyPermissionMsg
              view={isReadOnly}
              className="px-4 sm:pt-4 pt-1"
              textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
            />
            {(!isKanbanLoading && isLoading) || kanbanListData.length ? (
              <div className="flex pt-4 pb-2.5 px-1.5 transition-all ease-in-out duration-300 overflow-x-auto">
                <KanbanList
                  className="!border-l"
                  list={kanbanListData} //  kanban data
                  setList={setKanbanListData}
                  loading={!kanbanListData.length || isLoading}
                  kanbanSelected={kanbanSelected}
                  collapseClick={(workOrderColumn) => {
                    handleCollapse(
                      workOrderColumn?.item_id?.toString(),
                      workOrderColumn?.is_collapse_card?.toString(),
                      workOrderColumn?.key?.toString()
                    );
                  }}
                  cardDetailsClick={(workOrder) => {
                    setWorkorderToBeUpdate(workOrder);
                    navigate(
                      `${routes.MANAGE_WORKORDER.url}/${workOrder?.work_order_id}`
                    );
                  }}
                  childLoader={loadingChild}
                  loadMore={(val) => {
                    handleLoadMore(val);
                  }}
                  colum={{
                    headerName: "name",
                    parentId: "item_id",
                    count: "total_count",
                    collapse: "is_collapse_card",
                    color: "status_color",
                    child: "kanban_data",
                    childCard: {
                      cardId: "work_order_id", // child card id pass
                      cardFirstFirst: "project_name",
                      cardMiddleFirst: "subject",
                      cardLastFirst: "total",
                      cardMiddleSecond: "prefix_company_work_order_id",
                      cardImg: "user_profile_image",
                      imgName: "assignee",
                    },
                  }}
                  handleColumnDragDropEnd={handleDragDropEnd}
                  handleCardDragDropEnd={handleCardDragDropEnd}
                  isReadOnly={isReadOnly}
                  handleMouseMove={(workOrder) => {
                    setWorkorderToBeUpdate(workOrder);
                  }}
                >
                  <div onClick={(e) => e.stopPropagation()}>
                    {(module_access === "full_access" ||
                      module_access === "own_data_access") && (
                      <WorkOrderTableDropdownItems
                        data={workorderToBeUpdate}
                        icon="fa-solid fa-ellipsis-h"
                        refreshTable={() => fetchKanbanWorkOrderList("")}
                        className="m-0 hover:!bg-[#0000000f] invisible group-hover/kanbanitem:visible"
                        iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                        isCallApi={true}
                      />
                    )}
                  </div>
                </KanbanList>
              </div>
            ) : (
              ""
            )}
          </div>
        )}

        {drawerOpen && !isReadOnly && (
          <AddWorkOrder
            drawerOpen={drawerOpen}
            setDrawerOpen={setDrawerOpen}
            editView={false}
          />
        )}
      </WorkorderStoreProvider>
    </>
  );
};

const ManageWorkOrder = () => {
  return (
    <WorkorderStoreProvider>
      {" "}
      <ManageWorkOrderCom />{" "}
    </WorkorderStoreProvider>
  );
};

export default React.memo(ManageWorkOrder);

export { ErrorBoundary };
