// React + ag-grid
import { useEffect, useState } from "react";
import { useNavigate } from "@remix-run/react";
import debounce from "lodash/debounce";
import uniq from "lodash/uniq";
import isEmpty from "lodash/isEmpty";
// Organisms
import { KanbanList } from "~/shared/components/organisms/kanbanView";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import InvoiceListAction from "../InvoiceListAction";
// Constants
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { escapeHtmlEntities } from "~/helpers/helper";
import { STATUS_CODE } from "~/shared/constants";
import { invoiceAgingKeys } from "../../utils/constants";
// Redux
import { useAppIVSelector } from "../../redux/store";
import { getGConfig, getGSettings, getGModuleFilters } from "~/zustand";
import { routes } from "~/route-services/routes";
import {
  getInvoiceKanbanListApi,
  updateKanbanCardDragAndDrop,
} from "../../redux/action/dashboardAction";
import {
  updateKanbanSettingApi,
  updateKanbanSortingApi,
} from "~/redux/action/kanbanSettings";
import Sortable from "sortablejs";

const InvoiceKanban = ({
  kanbanSetting,
  setKanbanSetting,
  kanbanSelected,
  setKanbanSelected,
  search,
}: IIVKanbanProps) => {
  const { formatter, unformatted } = useCurrencyFormatter();
  const navigate = useNavigate();
  const { module_id, module_key, module_access }: GConfig = getGConfig();
  const filter = getGModuleFilters() as
    | Partial<IInvoiceModuleFilter>
    | undefined;
  const { inv_total_balance_due }: GSettings = getGSettings();

  // States
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [selectedData, setSelectedData] =
    useState<IInvoiceKanbanRowDataList | null>();
  const [selectedInvoiceData, setSelectedInvoiceData] =
    useState<IInvoiceKanbanRowDataList | null>();
  const [kanbanListData, setKanbanListData] = useState<
    IInvoiceKanbanColumnDataList[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingChild, setLoadingChild] = useState<boolean>(false);
  const [pageBySection, setPageBySection] = useState<{
    [key: string]: number;
  }>({});
  const [hasMoreBySection, setHasMoreBySection] = useState<
    Record<string, boolean>
  >({});
  const [loadingBySection, setLoadingBySection] = useState<{
    [key: string]: boolean;
  }>({});
  const stageFilterKeys = (
    filter?.billing_status_kanban?.split(",") || []
  ).filter((key) => key.trim() !== "");

  // fetch kanban to do list
  const fetchKanbanInvoiceList = async (
    type: string,
    isLoad: boolean = true
  ) => {
    type === "" ? setIsLoading(true) : setLoadingChild(isLoad);
    const pageForType = pageBySection[type] || 0;
    const filterObj = !isEmpty(filter) ? getValuableObj(filter) : undefined;
    if (filterObj?.status === STATUS_CODE.ALL) {
      delete filterObj.status;
    }

    if (!filter) return;

    delete filterObj?.billing_status;
    delete filterObj?.billing_status_names;
    let dataParams = {
      length: 15,
      page: pageForType,
      ignore_filter: 1, // data-base
      search: !!search ? escapeHtmlEntities(search || "") : undefined,
      filter: !isEmpty(filterObj) ? filterObj : undefined,
    };

    try {
      const resData = (await getInvoiceKanbanListApi(
        dataParams
      )) as IInvoiceKanbanApiRes;
      if (resData?.success) {
        setIsLoading(false);
        const newTypes = (resData?.data?.kanban_items || [])
          ?.filter((data) => data.key !== "invoice_charge")
          ?.map((data) => {
            return {
              ...data,
              kanban_data: (data?.kanban_data ?? [])?.map((kanbanData) => {
                return {
                  ...kanbanData,
                  company_invoice_id: !!kanbanData.prefix_company_invoice_id
                    ? `Inv. #${kanbanData.prefix_company_invoice_id}`
                    : "",
                  total: formatter((Number(kanbanData?.total) / 100).toFixed(2))
                    .value_with_symbol,
                  amount: formatter(
                    (
                      Number(
                        inv_total_balance_due == 1
                          ? Number(kanbanData?.total) -
                              Number(kanbanData?.payment_amount)
                          : kanbanData?.total
                      ) / 100
                    ).toFixed(2)
                  ).value_with_symbol,
                };
              }),
            };
          });
        const newHasMoreBySection = newTypes?.reduce(
          (
            acc: Record<number, boolean>,
            section: IInvoiceKanbanColumnDataList
          ) => {
            const sectionDataLength = section.kanban_data.length;
            acc[section.item_id] = sectionDataLength >= 15;
            return acc;
          },
          {}
        );

        setHasMoreBySection((prev) => ({
          ...prev,
          ...newHasMoreBySection,
        }));
        if (pageForType == 0) {
          setKanbanListData(newTypes);
        } else {
          setKanbanListData((prevData) => {
            if (prevData.length === 0) {
              return newTypes;
            }
            const updateData = prevData.map((prevSection) => {
              const newSection = newTypes.find(
                (d) => d.item_id === prevSection.item_id
              );
              if (newSection) {
                const updatedSection = { ...prevSection };

                const newKanbanData = newSection.kanban_data.filter(
                  (newItem) =>
                    !updatedSection.kanban_data.some(
                      (existingItem) =>
                        existingItem.invoice_id === newItem.invoice_id
                    )
                );
                updatedSection.kanban_data.push(...newKanbanData);
                return updatedSection;
              }
              return prevSection;
            });
            return updateData;
          });
        }
        setKanbanSelected(
          stageFilterKeys.length > 0
            ? stageFilterKeys
            : resData?.data?.kanban_estimate_type_selected || []
        );
        setKanbanSetting(resData?.data?.kanban_setting);
      } else {
        setKanbanListData([]);
        setKanbanSetting(undefined);
      }

      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } catch (err) {
      setIsLoading(false);
      notification.error({
        description: (err as Error)?.message || "Something went wrong!",
      });
    } finally {
      setLoadingChild(false);
    }
  };

  const debouncedFetch = debounce(() => {
    fetchKanbanInvoiceList("");
  }, 100);

  useEffect(() => {
    debouncedFetch();
  }, [search, JSON.stringify(filter)]);

  const handleLoadMore = (val: number) => {
    const isSectionLoading = loadingBySection[val];
    if (isSectionLoading) return;

    const hasMoreForSection = hasMoreBySection[val];
    const currentSectionPage = pageBySection[val] || 0;
    if (hasMoreForSection) {
      const nextPage = currentSectionPage + 1;
      setPageBySection((prev) => ({
        ...prev,
        [val]: nextPage,
      }));
      if (currentSectionPage !== 0) {
        setLoadingBySection((prev) => ({
          ...prev,
          [val]: true,
        }));
        fetchKanbanInvoiceList(val.toString());
      }
    }
  };

  const handleColspan = async (
    columnId: string,
    isCollapseCard: string,
    key: string
  ) => {
    // Optimistically update the kanbanSelected state
    const updatedKanbanSelected = uniq(
      stageFilterKeys?.length > 0
        ? !kanbanSelected.includes(columnId) && !kanbanSelected.includes(key)
          ? [...kanbanSelected, columnId]
          : kanbanSelected.filter(
              (value) => value !== columnId && value !== key
            )
        : kanbanSelected.includes(columnId)
        ? kanbanSelected.filter((value) => value !== columnId)
        : [...kanbanSelected, columnId]
    ).filter((data) => data.trim() !== "");

    setKanbanSelected(updatedKanbanSelected);
    if (stageFilterKeys.length > 0) {
      return;
    }
    const requestKanbanSetting = {
      module_field_id: updatedKanbanSelected.length
        ? updatedKanbanSelected
        : undefined,
      default_view: kanbanSetting?.default_view?.toString() ?? "0",
      module_id,
    };

    try {
      const response = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;

      if (response.success) {
        setKanbanSetting(response.data);
        setKanbanSelected(response.kanban_project_selected);
      } else {
        // Revert the optimistic update on failure
        setKanbanSelected(kanbanSelected);
        notification.error({
          description: response.message || "Something went wrong!",
        });
      }
    } catch (error) {
      // Revert the optimistic update on error
      setKanbanSelected(kanbanSelected);
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  const handleInvoiceColumn = async (event: Sortable.SortableEvent) => {
    setIsDragging(true);
    const currentArray = kanbanListData?.map((data) => ({
      column_id: data?.item_id,
      sort_order: Number(data?.sort_order),
      sorting_id: data?.sorting_id?.toString(),
      column_name: data?.name,
      type_id: data?.item_id?.toString(),
    }));

    const kanban_sorting: IKanbanSortingArray[] = currentArray.map(
      (data, index) => ({
        ...data,
        sort_order: index,
      })
    );

    try {
      const requestKanbanSetting: IKanbanSorting = {
        kanban_sorting: kanban_sorting,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSortingApi(
        requestKanbanSetting
      )) as IKanbanSortingApiRes;
      if (!responseKanbanSetting.success) {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    } finally {
      setIsDragging(false);
    }
  };

  const { customStatusList }: ICustomStatusListInitialState = useAppIVSelector(
    (state) => state.customStatusListData
  );

  const handleCardDragAndDropInvoice = async (
    event: Sortable.SortableEvent
  ) => {
    const { from, to } = event;
    const fromColumnId =
      from?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    const toColumnId = to?.closest("[data-id]")?.getAttribute("data-id") ?? "";
    const isValid = await validateCardDragAndDrop(
      fromColumnId,
      toColumnId,
      selectedData
    );
    if (fromColumnId !== toColumnId && !!selectedData?.invoice_id && isValid) {
      const requestKanbanPriorityUpdate = {
        module_key: module_key,
        invoice_id: selectedData?.invoice_id,
        approval_type: toColumnId,
      };
      try {
        const responseKanbanPriorityUpdate = (await updateKanbanCardDragAndDrop(
          requestKanbanPriorityUpdate
        )) as IKanbanPriorityUpdateApiRes;

        if (responseKanbanPriorityUpdate?.success) {
          setKanbanListData((prevData) =>
            prevData.map((item) => {
              if (item.item_id == Number(toColumnId)) {
                return {
                  ...item,
                  total_count: (parseInt(item.total_count) + 1).toString(),
                };
              } else if (item.item_id == Number(fromColumnId)) {
                return {
                  ...item,
                  total_count: (parseInt(item.total_count) - 1).toString(),
                };
              }
              return item;
            })
          );
        } else {
          fetchKanbanInvoiceList("", false);
          notification.error({
            description:
              responseKanbanPriorityUpdate.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "Something went wrong!",
        });
      }
    } else {
      fetchKanbanInvoiceList("", false);
    }
  };

  const validateCardDragAndDrop = async (
    fromId: string,
    toId: string,
    selectedData: IInvoiceKanbanRowDataList | null | undefined
  ) => {
    if (!selectedData || !fromId || !toId) {
      return false;
    }
    const e_sender_status_id = customStatusList?.find(
      (data) => data.item_id.toString() == fromId
    )?.key;
    const status_id = customStatusList?.find(
      (data) => data.item_id.toString() == toId
    )?.key;

    if (
      status_id &&
      e_sender_status_id &&
      (invoiceAgingKeys.includes(status_id) ||
        (invoiceAgingKeys.includes(e_sender_status_id) &&
          status_id == "invoice_submitted"))
    ) {
      notification.error({
        description:
          "You can't move aging record from other status or other record in aging status.",
      });
      return false;
    } else if (!selectedData.due_date && status_id == "invoice_submitted") {
      notification.error({
        description:
          "You can't change status, because due date is blank and that need to be required to change status.",
      });
      return false;
    } else if (status_id == "invoice_paid") {
      let paymentTotal = Number(selectedData.payment_amount);
      let invTotal = Number(unformatted(selectedData.total)) * 100;

      if (invTotal > paymentTotal) {
        notification.error({
          description:
            "This invoice cannot be marked as Paid as there is an outstanding balance against it. A Payment must be posted to bring the balance to 0.",
        });
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  };

  return (
    <KanbanList
      list={kanbanListData} //  kanban data
      setList={setKanbanListData}
      loading={!kanbanListData.length && isLoading}
      childLoader={loadingChild}
      loadMore={(val) => {
        handleLoadMore(val);
      }}
      collapseClick={(invoiceColumn) => {
        handleColspan(
          invoiceColumn?.item_id?.toString(),
          invoiceColumn?.is_collapse_card?.toString(),
          invoiceColumn?.key?.toString()
        );
      }}
      cardDetailsClick={(invoice) => {
        if (!!invoice?.invoice_id) {
          navigate(`${routes.MANAGE_INVOICE.url}/${invoice.invoice_id}`);
        }
      }}
      kanbanSelected={kanbanSelected}
      handleCardDragDropEnd={handleCardDragAndDropInvoice}
      handleColumnDragDropEnd={handleInvoiceColumn}
      isReadOnly={isDragging || module_access === "read_only"}
      handleMouseMove={(invoice) => {
        setSelectedData(invoice);
      }}
      onActionClick={(invoice) => {
        setSelectedInvoiceData(invoice);
      }}
      colum={{
        headerName: "name",
        parentId: "item_id",
        count: "total_count",
        collapse: "is_collpase_card",
        color: "status_color",
        child: "kanban_data",
        childCard: {
          cardId: "invoice_id", // child card id pass
          cardFirstFirst: "customer_name",
          cardMiddleFirst: "company_invoice_id",
          cardLastFirst: "project_name",
          cardMiddleSecond: "amount",
        },
      }}
    >
      {module_access !== "read_only" && (
        <InvoiceListAction
          isKanbanDropDown={true}
          isDetailDropDown={false}
          paramsData={{
            invoice_id: selectedInvoiceData?.invoice_id,
            is_deleted: selectedInvoiceData?.is_deleted,
            email_subject: selectedInvoiceData?.email_subject,
            project_id: selectedInvoiceData?.project_id,
            approval_type_key: selectedInvoiceData?.approval_type_key,
            billed_to: selectedInvoiceData?.billed_to,
            billed_to_contact: selectedInvoiceData?.billed_to_contact,
            customer_id: selectedInvoiceData?.customer_id,
            due_date: selectedInvoiceData?.due_date,
            contact_id: selectedInvoiceData?.customer_contact_id,
            due_balance: selectedInvoiceData?.due_balance,
            show_payment_link: selectedInvoiceData?.show_payment_link,
            prefix_company_invoice_id:
              selectedInvoiceData?.prefix_company_invoice_id,
          }}
          onActionComplete={() => {
            fetchKanbanInvoiceList("");
          }}
        />
      )}
    </KanbanList>
  );
};
export default InvoiceKanban;
