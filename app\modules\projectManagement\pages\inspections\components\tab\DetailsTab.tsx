import { useMemo } from "react";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
// Organisms
import { CustomFieldForm } from "~/shared/components/organisms/customField";
// Other
import DetailsCard from "./details/DetailsCard";
import { useGModules } from "~/zustand";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import ChecklistCard from "./details/ChecklistCard";
import { useInAppSelector } from "../../redux/store";
import { useParams } from "@remix-run/react";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import PermitDetails from "./details/PermitDetails";

const DetailsTab = () => {
  const { id }: RouteParams = useParams();
  const { checkModuleAccessByKey } = useGModules();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { isReadOnlyCustomField, isNoAccessCustomField }: ICustomFieldAccess =
    getCustomFieldAccess();
  const { isDetailLoading } = useInAppSelector(
    (state) => state.inspectionDetails
  );

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(currentModule?.module_key) === "read_only",
    [currentModule?.module_key]
  );

  return (
    <div className="grid gap-2.5">
      <div className="grid xl:grid-cols-2 items-start gap-2.5">
        <div className="py-3 px-[15px] common-card h-fit">
          <DetailsCard />
        </div>
        <div className="grid gap-2.5">
          <div className="py-3 px-[15px] common-card">
            <PermitDetails />
          </div>
          {!isNoAccessCustomField && (
            <div className="common-card py-3 px-[15px] h-fit">
              {isDetailLoading ? (
                <Spin className="w-full h-[150px] flex items-center justify-center" />
              ) : (
                <CustomFieldForm
                  requestBody={{
                    moduleId: currentModule?.module_id || 0,
                    recordId: id,
                  }}
                  spinClassName="h-[150px]"
                  isReadOnly={isReadOnly || isReadOnlyCustomField}
                />
              )}
            </div>
          )}
        </div>
      </div>
      <div className="py-3 px-[15px] common-card h-fit">
        <ChecklistCard />
      </div>
    </div>
  );
};

export default DetailsTab;
