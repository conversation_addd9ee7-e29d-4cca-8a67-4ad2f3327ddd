import { useEffect, useMemo, useRef, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Search } from "~/shared/components/atoms/search";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
// Other
import {
  GetDetailRowDataParams,
  GetRowIdFunc,
  GetRowIdParams,
  GridReadyEvent,
  SortChangedEvent,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import useTableGridData from "~/shared/hooks/useTableGridData";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { cidbItemRoutes } from "~/route-services/cidb-item.routes";
// Lodash
import isEmpty from "lodash/isEmpty";
import { sanitizeString } from "~/helpers/helper";
import { useRevalidator } from "@remix-run/react";
import { TableRowDetailLoading } from "~/shared/components/molecules/tableRowDetailLoading";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { MLESOITEMS_SELECTOPTION_TAB_KEYS } from "~/modules/settings/costItemsDatabase/utils/constants";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const PreviousImportsTab = ({
  module,
}: {
  module: ICIDBDetailsTopBarProps["module"];
}) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const revalidator = useRevalidator();
  const [searchVal, setSearchVal] = useState<string>("");
  const [isDelConfirmOpen, setIsDelConfirmOpen] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IPrevImpCIDB>>({});

  const { datasource, gridRowParams } = useTableGridData();
  const agGridRef = useRef<AgGridReact>(null);
  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id = 0 } = currentModule || {};

  const getRowId = useMemo<GetRowIdFunc>(() => {
    return (params: GetRowIdParams) => {
      return params.data.import_id.toString();
    };
  }, []);
  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  // API CAll
  const fetchImportData = async () => {
    if (gridRowParams) {
      const { changeGridParams, gridParams } = gridRowParams;

      if (isEmpty(changeGridParams) || isEmpty(gridParams)) {
        return;
      }

      try {
        const { start, length: limit } = changeGridParams;

        const column = changeGridParams.order_by_name || "date_added";
        const direction = changeGridParams.order_by_dir || "desc";

        // Handle only for `date_added` Default Descending Sort for  date_added column
        if (column === "date_added") {
          const headerCell = document.querySelector(
            ".ag-header-cell[col-id='date_added']"
          );

          if (headerCell) {
            const sortAscIcon = headerCell.querySelector(
              ".ag-sort-ascending-icon"
            ) as HTMLElement;
            const sortDescIcon = headerCell.querySelector(
              ".ag-sort-descending-icon"
            ) as HTMLElement;

            // Ensure both elements exist
            if (sortAscIcon && sortDescIcon) {
              if (direction === "asc") {
                sortAscIcon.classList.remove("ag-hidden");
                sortDescIcon.classList.add("ag-hidden");
              } else {
                sortAscIcon.classList.add("ag-hidden");
                sortDescIcon.classList.remove("ag-hidden");
              }
            }

            headerCell.classList.remove(
              "ag-header-cell-sorted-asc",
              "ag-header-cell-sorted-desc"
            );

            // Add appropriate sort class to header
            headerCell.classList.add(
              direction === "asc"
                ? "ag-header-cell-sorted-asc"
                : "ag-header-cell-sorted-desc"
            );
          }
        }

        let gridData: {
          rowCount?: number;
          rowData: IPrevImpCIDB[];
        } = {
          rowData: [],
        };
        const params = await getWebWorkerApiParams({
          otherParams: {
            start,
            limit,
            module_id: window.location.pathname.endsWith(
              "/" + MLESOITEMS_SELECTOPTION_TAB_KEYS.all_items
            )
              ? module_id
              : module?.module_id,
            column: column,
            direction: direction,
          },
        });

        const response = (await webWorkerApi({
          url: cidbItemRoutes.get_previous_user_imports,
          method: "post",
          data: params,
        })) as ICIDBGetPrevItemsListApiResponse;

        const rowCount = gridParams.api?.getDisplayedRowCount() ?? 0;
        if (response?.data?.length < length) {
          gridData = {
            ...gridData,
            rowCount: rowCount + (response?.data?.length ?? 0) - 1,
          };
        }
        gridData = {
          ...gridData,
          rowData: (response?.data as IPrevImpCIDB[]) ?? [],
        };
        gridParams.success(gridData);
        if (
          (!response?.success || gridData.rowData.length <= 0) &&
          params.start === 0
        ) {
          gridParams.api.showNoRowsOverlay();
        } else if (response?.success && gridData.rowData.length > 0) {
          gridParams.api.hideOverlay();
        }
      } catch (error) {
        gridParams?.success({ rowCount: 0, rowData: [] });
        gridParams?.api.showNoRowsOverlay();
        gridParams?.fail();
      }
    }
  };

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    refreshAgGrid();
  }, [searchVal]);

  useEffect(() => {
    fetchImportData();
    return () => {};
  }, [gridRowParams, module_id]);

  const columnDefs = [
    {
      headerName: _t("Detail"),
      field: "detail",
      cellRenderer: "agGroupCellRenderer",
      suppressMenu: true,
      minWidth: 60,
      maxWidth: 60,
      flex: 1,
      cellClass: "expand-icon",
      sortable: false,
    },
    {
      headerName: _t("Records Added"),
      field: "upload_count",
      minWidth: 180,
      maxWidth: 180,
      suppressMenu: true,
      sortable: true,
      flex: 1,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
    },
    {
      headerName: _t("Imported By"),
      field: "emp_name",
      minWidth: 135,
      flex: 1,
      suppressMenu: true,
      sortable: true,
      headerClass: "ag-header-center",
      cellRenderer: (params: IPrevImpTableCellRenderer) => {
        const { data } = params;
        const name = HTMLEntities.decode(sanitizeString(data?.emp_name)) || "";

        return name ? (
          <Tooltip title={name}>
            <div className="w-fit mx-auto">
              <AvatarProfile
                user={{
                  name,
                  image: data?.image,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Import Date"),
      field: "date_added",
      minWidth: 135,
      maxWidth: 200,
      flex: 1,
      suppressMenu: true,
      sortable: true,
      cellRenderer: (params: IPrevImpTableCellRenderer) => {
        const { data } = params;
        return data?.import_date ? (
          <DateTimeCard format="date" date={data?.import_date} />
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Undo Import"),
      field: "childrenData",
      suppressMenu: true,
      sortable: false,
      maxWidth: 200,
      flex: 1,
      headerClass: "ag-header-right",
      cellRenderer: (params: IPrevImpTableCellRenderer) => {
        return (
          <div className="flex items-center gap-2 justify-end">
            <ButtonWithTooltip
              tooltipTitle={_t("Delete")}
              tooltipPlacement="top"
              icon="fa-regular fa-trash-can"
              onClick={() => {
                setSelectedData(params?.data);
                setIsDelConfirmOpen(true);
              }}
            />
          </div>
        );
      },
    },
  ];

  const detailCellRendererParams = {
    // level 2 grid options
    detailGridOptions: {
      suppressDragLeaveHidesColumns: true,
      columnDefs: [
        {
          headerName: "#",
          field: "id",
          suppressMenu: true,
          minWidth: 60,
          maxWidth: 60,
          sortable: false,
          headerClass: "ag-header-left",
          cellClass: "ag-cell-left",
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            return (
              <Tooltip title={params.rowIndex + 1}>
                <Typography className="table-tooltip-text">
                  {params.rowIndex + 1}
                </Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("SKU"),
          field: "sku",
          suppressMenu: true,
          minWidth: 110,
          maxWidth: 110,
          sortable: true,
          headerClass: "ag-header-left",
          cellClass: "ag-cell-left",
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            const sku = params?.data?.sku;
            return (
              <Tooltip title={sku}>
                <Typography className="table-tooltip-text">{sku}</Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Name"),
          field: "name",
          suppressMenu: true,
          minWidth: 150,
          flex: 2,
          sortable: true,
          headerClass: "ag-header-left",
          cellClass: "ag-cell-left",
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            const userName = HTMLEntities.decode(
              sanitizeString(params?.data?.name)
            );
            return (
              <Tooltip title={userName}>
                <Typography className="table-tooltip-text">
                  {userName}
                </Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Unit Cost"),
          field: "unit_cost",
          minWidth: 120,
          maxWidth: 120,
          suppressMenu: true,
          sortable: true,
          cellClass: "ag-cell-right",
          headerClass: "ag-header-right",
          valueGetter: (params: IImportCIDBListTableCellRenderer) => {
            return Number(params?.data?.unit_cost || 0);
          },
          comparator: (valueA: number, valueB: number) => {
            return valueA - valueB;
          },
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            const value = params?.data?.unit_cost;
            const unitCost = Number(value || "0")
              ? formatter((Number(value || "0") / 100).toFixed(2))
                  .value_with_symbol
              : "";
            return (
              <Tooltip title={unitCost}>
                <Typography className="table-tooltip-text">
                  {unitCost}
                </Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Unit"),
          field: "unit",
          minWidth: 80,
          maxWidth: 80,
          sortable: true,
          headerClass: "ag-header-left",
          cellClass: "ag-cell-left",
          suppressMenu: true,
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            const unit = params?.data?.unit;
            return (
              <Tooltip title={unit}>
                <Typography className="table-tooltip-text">{unit}</Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("MU") + " %",
          field: "markup",
          minWidth: 83,
          maxWidth: 83,
          sortable: true,
          headerClass: "ag-header-right",
          cellClass: "ag-cell-right",
          suppressMenu: true,
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            const markup = params?.data?.markup;
            return (
              <Tooltip title={markup}>
                <Typography className="table-tooltip-text">{markup}</Typography>
              </Tooltip>
            );
          },
        },
        {
          headerName: _t("Total"),
          field: "",
          minWidth: 120,
          maxWidth: 120,
          cellClass: "ag-cell-right",
          headerClass: "ag-header-right",
          suppressMenu: true,
          sortable: true,
          valueGetter: (params: IImportCIDBListTableCellRenderer) => {
            const { unit_cost, hidden_markup, markup } = params?.data || {};
            let total = Number(unit_cost || "0");
            if (total) {
              if (hidden_markup !== null && Number(hidden_markup) !== 0) {
                total += total * (Number(hidden_markup) / 100);
              }
              if (markup !== null && Number(markup) !== 0) {
                total += total * (Number(markup) / 100);
              }
            }
            return total;
          },
          comparator: (valueA: number, valueB: number) => {
            return valueA - valueB;
          },
          cellRenderer: (params: IImportCIDBListTableCellRenderer) => {
            const { unit_cost, hidden_markup, markup } = params?.data || {};
            let total = Number(unit_cost || "0");
            if (total) {
              if (hidden_markup !== null && Number(hidden_markup) !== 0) {
                total += total * (Number(hidden_markup) / 100);
              }
              if (markup !== null && Number(markup) !== 0) {
                total += total * (Number(markup) / 100);
              }
            }
            const displayTotal = total
              ? formatter((total / 100).toFixed(2)).value_with_symbol
              : "";
            return (
              <Tooltip title={displayTotal}>
                <Typography className="table-tooltip-text">
                  {displayTotal}
                </Typography>
              </Tooltip>
            );
          },
        },
      ],
      noRowsOverlayComponent: () => (
        <NoRecords
          image={`${window.ENV.CDN_URL}assets/images/no-records-imports.svg`}
        />
      ),
      defaultColDef: { flex: 1 },
      loadingOverlayComponent: TableRowDetailLoading,
      suppressContextMenu: true,
    },

    getDetailRowData: async ({
      data,
      successCallback,
      node,
    }: GetDetailRowDataParams<
      ICIDBGetPrevItemsListApiResponse["data"][0] & {
        items: ICIDBDetailedPrevItemApiResponse["data"];
      },
      ICIDBDetailedPrevItemApiResponse["data"][0]
    >) => {
      try {
        const importId = Number(data.import_id);
        if (!importId || !Number(data.upload_count)) {
          successCallback([]);
          return;
        } else if (data.items?.length) {
          successCallback(data.items);
          return;
        }

        const dataParams = await getWebWorkerApiParams({
          otherParams: {
            importId,
          },
        });

        const response = (await webWorkerApi({
          url: cidbItemRoutes.get_imports_data,
          method: "post",
          data: dataParams,
        })) as ICIDBDetailedPrevItemApiResponse;

        if (response?.success && response?.data?.length) {
          successCallback(response.data);
          if (node.parent?.id) {
            const newData = {
              ...data,
              items: response.data,
            };
            const rowNode = agGridRef.current?.api.getRowNode(node.parent?.id);
            if (rowNode) {
              rowNode.setData(newData);
              const rowCount = (response.data || []).length;
              node.setRowHeight(rowCount ? rowCount * 42 + 64 : 210);
              rowNode.setExpanded(false);
              rowNode.setExpanded(true);
            }
          }
        } else {
          successCallback([]);
        }
      } catch (error) {
        console.error("Error fetching import details:", error);
        successCallback([]);
      }
    },
  };

  const onCloseDelModal = () => {
    setSelectedData({});
    setIsDelConfirmOpen(false);
  };

  // Collapse all rows when the popup is opened
  useEffect(() => {
    if (agGridRef?.current) {
      agGridRef?.current?.api?.forEachNode((node) => {
        node.setExpanded(false);
      });
    }
  }, []);

  const handleDelete = async () => {
    const importId = selectedData?.import_id;
    const moduleId = selectedData?.module_id;

    if (!isDeleting && selectedData?.import_id) {
      setIsDeleting(true);

      const dataParams = await getWebWorkerApiParams({
        otherParams: {
          module_id: moduleId,
          import_id: importId,
          check_in_use: 1,
        },
      });

      const deleteRes = (await webWorkerApi({
        url: cidbItemRoutes.delete_imports_data,
        method: "post",
        data: dataParams,
      })) as ICIDBDeletePrevItemApiResponse;

      if (deleteRes?.success) {
        revalidator.revalidate();
        refreshAgGrid();
      }
      if (!deleteRes?.success) {
        notification.error({
          description: deleteRes?.message,
        });
      }
      onCloseDelModal();
      setIsDeleting(false);
    }
  };

  return (
    <>
      {/* <Search
        placeholder="Search"
        className="search-input-border search-input-bg-transparent relative px-4 bg-[#F8F8FA] border-b border-solid border-[#0505050f] dark:bg-white/5"
        onChange={({ target: { value } }) => {
          debouncedSetSearchVal(value);
        }}
      /> */}
      <div className="w-full px-4">
        <div className="p-2 bg-white dark:bg-dark-600 common-list-table shadow-[0_0_10px] shadow-black/10 rounded">
          <div className="ag-theme-alpine h-[calc(100dvh-100px)] ant-collapse overflow-y-auto">
            <DynamicTable
              ref={agGridRef}
              // getRowId={getRowId}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              onSortChanged={onSortChanged}
              masterDetail={true}
              detailCellRendererParams={detailCellRendererParams}
              className="multi-list-table static-multi-table static-table group-item-table"
              icons={{
                groupExpanded: `<div class="ag-icon-tree-open cell-renderer-icon relative">
                              <div class="ant-tooltip custom-tooltip tooltip-placement-right left-6">
                                <div class="ant-tooltip-arrow absolute"></div>
                                <div class="ant-tooltip-content relative">
                                  <div class="ant-tooltip-inner" role="tooltip">Collapse</div>
                                </div>
                              </div>
                              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 512 512"><path d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"/></svg>
                            </div>
                          `,
                groupContracted: `<div class="ag-icon-tree-closed cell-renderer-icon relative">
                              <div class="ant-tooltip custom-tooltip tooltip-placement-right left-6">
                                <div class="ant-tooltip-arrow absolute"></div>
                                <div class="ant-tooltip-content relative">
                                  <div class="ant-tooltip-inner" role="tooltip">Expand</div>
                                </div>
                              </div>
                              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 512 512"><path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"/></svg>
                            </div>
                          `,
              }}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-imports.svg`}
                />
              )}
              // Use `getRowHeight` for dynamic row height calculation
              getRowHeight={(params) => {
                if (params.node && params.node.detail) {
                  // Detail row height calculation
                  const items = params.data?.item ?? [];
                  if (items.length) {
                    return items.length * 42.4 + 64; // 42 is row height and 64 is header height
                  } else {
                    return 210;
                  }
                }
                // Default height for main rows
                return undefined; // Default height for parent rows
              }} // Attach dynamic row height
            />
          </div>
        </div>
      </div>
      {isDelConfirmOpen && (
        <ConfirmModal
          isOpen={isDelConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t(
            "This will delete all items associated with this import. Do you really want to delete this import?"
          )}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDelete}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}
    </>
  );
};

export default PreviousImportsTab;
