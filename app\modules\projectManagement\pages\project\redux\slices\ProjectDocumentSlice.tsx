import { createSlice } from "@reduxjs/toolkit";
import { fetchProjectDocumentsModules } from "../action/ProjectDocumentAction";

const initialState: IProDocumentInitialState = {
  documentsData: {
    daily_logs: [],
    daily_logs_count: [],
    document_writer: [],
    document_writers_count: [],
    checklists: [],
    checklists_count: [],
    incidents: [],
    incidents_count: [],
    inspections: [],
    inspections_count: [],
    notes: [],
    notes_count: [],
    permits: [],
    permits_count: [],
    punchlists: [],
    punchlists_count: [],
    correspondences: [],
    correspondences_count: [],
    safety_meeting: [],
    safety_meetings_count: [],
    service_ticket: [],
    service_tickets_count: [],
    submittals: [],
    submittals_count: [],
    todos: [],
    todos_count: [],
  },
  projectTypes: [],
  isInitialLoad: false,
};

export const proDashDocumentSlice = createSlice({
  name: "proDashDocument",
  initialState,
  reducers: {
    setIsInitialLoad: (state, action) => {
      state.isInitialLoad = action.payload;
    },
    addCustomProjectType: (state, { payload }) => {
      const data = payload;
      data.item_type = data.item_type.toString();
      data.item_id = data.item_id.toString();
      state.projectTypes = [data, ...state.projectTypes];
    },
    setDocumentData: (state, { payload }) => {
      state.documentsData = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchProjectDocumentsModules.pending, (state, action) => {
      // state.documentsData = [];
      state.isInitialLoad = true;
    });
    builder.addCase(
      fetchProjectDocumentsModules.fulfilled,
      (state, { payload, meta }) => {
        const { success, data } = payload as IProjectDocumentApiRes;
        state.documentsData = {
          ...state.documentsData,
          ...data,
        };
        state.isInitialLoad = false;
      }
    );
    builder.addCase(fetchProjectDocumentsModules.rejected, (state) => {
      state.documentsData = initialState.documentsData;
      state.isInitialLoad = true;
    });
  },
});

// function getValidEntries(obj: Record<string, any>): Record<string, any> {
//   return Object.fromEntries(
//     Object.entries(obj).filter(
//       ([_, value]) => value?.length > 0 && value !== null && value !== undefined
//     )
//   );
// }

export const { setIsInitialLoad, addCustomProjectType, setDocumentData } =
  proDashDocumentSlice.actions;
export default proDashDocumentSlice.reducer;
