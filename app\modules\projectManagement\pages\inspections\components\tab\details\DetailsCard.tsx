// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Hook
import { useTranslation } from "~/hook";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useInspectionLogDetail } from "../../../hooks/useInspectionDetails";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { useInAppDispatch, useInAppSelector } from "../../../redux/store";
import { Number, sanitizeString } from "~/helpers/helper";
import dayjs, { Dayjs } from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {
  backendDateFormat,
  backendTimeFormat,
  displayDateFormat,
  displayTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { getGConfig } from "~/zustand";
import { sendNotificationToAssignee } from "../../../redux/action/inspectionDetailsAction";
import isEmpty from "lodash/isEmpty";
import {
  updateFalseScrollDownToDetail,
  updateTrueScrollDownToDetail,
} from "../../../redux/slices/inspectionDetailsSlice";

const DetailsCard = () => {
  const { _t } = useTranslation();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAssignedToOpen, setIsAssignedToOpen] = useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSendNotificationLoading, setIsSendNotificationLoading] =
    useState(false);
  const [isDateTimeVisible, setDateTimeVisible] = useState<boolean>(false);
  const dateTimeVisibleSelectRef = useRef<HTMLLIElement>(null);
  const notesRef = useRef<HTMLTextAreaElement | null>(null);
  const dtDivRef = useRef<HTMLLIElement>(null);
  dayjs.extend(isSameOrAfter);
  dayjs.extend(isSameOrBefore);
  const gConfig = getGConfig();
  const { details } = useInAppSelector((state) => state.inspectionDetails);
  const dispatch = useInAppDispatch();
  const { inspectionAgency } = useInAppSelector(
    (state) => state.statusListData
  );
  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
    dispatch(updateFalseScrollDownToDetail());
  };

  const inspectionAgencyOptions = useMemo(() => {
    let agencies = inspectionAgency.map((agency) => {
      return {
        label: HTMLEntities.decode(sanitizeString(agency.display_name)),
        value: agency.type_id.toString(),
      };
    });
    return agencies || [];
  }, [inspectionAgency]);

  const {
    handleUpdateField,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    onFocusUpdateFieldStatus,
    updateInputFieldOnBlur,
    onBlurUpdateFieldStatus,
    onChangeInputField,
    loadingStatus,
    isReadOnly,
    inputVals,
    updateDataInStore,
    onlyIntegerRegex,
  } = useInspectionLogDetail();

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return backendDateFormat(date as string, CFConfig.day_js_date_format);
    }
    return undefined;
  };

  const handleChangeInspectionDate = (date: Dayjs | Date | null) => {
    if (!Array.isArray(date)) {
      if (!!date) {
        const today = getDate(details.permit_expire_date?.toString()) as string; // Get the start of today
        const inputDate = dayjs(date).startOf("day");
        const isValidDate = inputDate?.isSameOrBefore(today);

        const newDateToSendInApi = inputDate.format("YYYY-MM-DD");
        const newDateToUpdateInStore = inputDate.format(
          CFConfig.day_js_date_format
        );
        if (isValidDate || !details.permit_expire_date) {
          handleUpdateField({
            data_to_send_in_api: {
              inspection_date: newDateToSendInApi,
            },
            data_to_update_in_store: {
              inspection_date: newDateToUpdateInStore,
            },
          });
        } else {
          const errorMessage =
            "Inspection Date should be less than or equal to permit expire date.";
          notification.error({
            description: errorMessage,
          });
        }
      } else {
        handleUpdateField({
          data_to_send_in_api: {
            inspection_date: null,
          },
        });
      }
    }
  };

  const handleChangeInspectionTime = (time: string | string[] | null) => {
    if (!Array.isArray(time)) {
      const newValue = time ? backendTimeFormat(time) : "";
      if (!!time) {
        handleUpdateField({
          data_to_send_in_api: {
            inspection_time: newValue,
          },
          data_to_update_in_store: {
            inspection_time: time,
          },
        });
      } else {
        handleUpdateField({
          data_to_send_in_api: {
            inspection_time: null,
          },
        });
      }
    }
  };

  const selectedCustomerData = useMemo(() => {
    return Number(details.assigned_to ?? 0)
      ? ([
          {
            display_name: details?.assigned_to_name,
            user_id: details?.assigned_to,
            contact_id: details?.contact_id || 0,
            type: details.dir_type,
            type_key: getDirectaryKeyById(
              details.dir_type === 1 ? 2 : Number(details.dir_type ?? ""),
              gConfig
            ),
            image: details?.assigned_to_image,
          },
        ] as TselectedContactSendMail[])
      : [];
  }, [details]);

  const getUpdatedContact = useCallback(
    (contact: Array<Partial<TselectedContactSendMail>>) => {
      let data = contact[0];

      if (data?.user_id !== undefined) {
        const assigned_to_dir_type_key =
          data.type_key !== "contact"
            ? data.type_key || ""
            : data.parent_type_key || "";

        return {
          data_to_update_in_store: {
            assignee_to_company_name: data?.company_name,
            assigned_to_name: data?.display_name,
            assigned_to_image: data?.image,
            dir_type: getDirectaryIdByKey(
              assigned_to_dir_type_key as CustomerTabs,
              gConfig
            ),
          },
          data_to_send_in_api: {
            assigned_to: data?.user_id,
            contact_id:
              data?.contact_id !== "" ? Number(data?.contact_id ?? "") : null,
          },
        };

        return null;
      } else {
        return {
          data_to_update_in_store: {
            assignee_to_company_name: "",
            assigned_to_name: null,
            dir_type: null,
          },
          data_to_send_in_api: {
            assigned_to: null,
            contact_id: null,
          },
        };
      }
    },
    [details]
  );

  const handleContactSelection = (
    data: Array<Partial<TselectedContactSendMail>>
  ) => {
    const updatedData = getUpdatedContact(data);

    if (updatedData) {
      handleUpdateField(updatedData);
    }
  };

  const handleDrawerSelection = (
    data: Array<Partial<TselectedContactSendMail>>
  ) => {
    handleContactSelection(data);
  };

  const handleSendNotification = async () => {
    setIsSendNotificationLoading(true);
    try {
      const response = (await sendNotificationToAssignee({
        id: details.inspection_id,
      })) as ApiCallResponse;

      if (response.success) {
        setIsSendNotificationLoading(false);
        closeConfirmationModal();
      } else {
        notification.error({
          description: response.message || "Something went wrong!",
        });
        setIsSendNotificationLoading(false);
        closeConfirmationModal();
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
      setIsSendNotificationLoading(false);
      closeConfirmationModal();
    }
  };

  const handleNameOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;

    const datePickerDropdown = document.querySelector(".ant-picker-dropdown");
    const timePickerDropdowns = document.querySelectorAll(
      ".ant-picker-time-panel"
    );

    if (
      (datePickerDropdown && datePickerDropdown.contains(clickedElement)) ||
      Array.from(timePickerDropdowns).some((timePickerDropdown) =>
        timePickerDropdown.contains(clickedElement)
      )
    ) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg"
    ) {
      setDateTimeVisible(false);
      return;
    }

    if (dtDivRef.current && !dtDivRef.current.contains(clickedElement)) {
      setDateTimeVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleNameOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleNameOutsideClick);
    };
  }, []);

  const dateTimeInputValue = useMemo(() => {
    let value = "";
    if (!!details.inspection_date) {
      value = details.inspection_date;
    }
    if (!!details.inspection_time) {
      value += (value ? " " : "") + details.inspection_time;
    }
    return value;
  }, [details.inspection_date, details.inspection_time]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        headerRightButton={
          details?.show_client_access == "1" && details?.is_access == "1" ? (
            <div className="flex items-center gap-2">
              <CustomCheckBox
                checked={inputVals.is_shared === 1}
                className="gap-1.5 whitespace-nowrap"
                disabled={
                  isReadOnly ||
                  getStatusForField(loadingStatus, "is_shared") === "loading"
                }
                loadingProps={{
                  isLoading:
                    getStatusForField(loadingStatus, "is_shared") === "loading",
                  className: "bg-[#ffffff]",
                }}
                onChange={(e) => {
                  updateInputFieldOnBlur({
                    field: "is_shared",
                    value: e.target.checked === true ? 1 : 0,
                  });
                }}
              >
                {_t("Share with Client")}
              </CustomCheckBox>
            </div>
          ) : (
            <></>
          )
        }
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li className="overflow-hidden">
                <InputField
                  label={_t("Type")}
                  placeholder={_t("Inspection Type")}
                  labelPlacement="left"
                  required={true}
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={HTMLEntities.decode(
                    sanitizeString(inputVals.inspection_type || "")
                  )}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "inspection_type"
                  )}
                  onFocus={() => {
                    onFocusUpdateFieldStatus({
                      field: "inspection_type",
                    });
                  }}
                  onMouseEnter={() => {
                    onMouseEnterUpdateFieldStatus({
                      field: "inspection_type",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    onMouseLeaveUpdateFieldStatus({
                      field: "inspection_type",
                    });
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = e.target.value;

                    onChangeInputField({
                      field: "inspection_type",
                      value: inputValue,
                    });
                  }}
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    const value = HTMLEntities.encode(e?.target?.value.trim());
                    if (value !== details?.inspection_type) {
                      updateInputFieldOnBlur({
                        field: "inspection_type",
                        value,
                        message: "Inspection Type field is required.",
                        required: true,
                      });
                    } else {
                      updateDataInStore({
                        inspection_type: details.inspection_type,
                      });
                      onBlurUpdateFieldStatus({
                        field: "inspection_type",
                      });
                    }
                  }}
                />
              </li>
              <li>
                <SelectField
                  label={_t("Agency")}
                  placeholder={_t("Select Agency")}
                  name="inspection_agency"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  disabled={isReadOnly}
                  readOnly={isReadOnly}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  allowClear={!!Number(details?.inspection_agency ?? "")}
                  options={inspectionAgencyOptions || []}
                  value={
                    details.inspection_agency
                      ? inspectionAgencyOptions.filter((item) => {
                          return (
                            details?.inspection_agency?.toString() ===
                            item?.value?.toString()
                          );
                        })
                      : []
                  }
                  onSelect={(value) => {
                    updateInputFieldOnBlur({
                      field: "inspection_agency",
                      value: Number(value) || "",
                    });
                  }}
                  onClear={() => {
                    updateInputFieldOnBlur({
                      field: "inspection_agency",
                      value: "",
                    });
                  }}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "inspection_agency"
                  )}
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("Inspected By")}
                  placeholder={_t("Inspected By")}
                  labelPlacement="left"
                  required={true}
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={HTMLEntities.decode(
                    sanitizeString(inputVals.inspected_by || "")
                  )}
                  fixStatus={getStatusForField(loadingStatus, "inspected_by")}
                  onFocus={() => {
                    onFocusUpdateFieldStatus({
                      field: "inspected_by",
                    });
                  }}
                  onMouseEnter={() => {
                    onMouseEnterUpdateFieldStatus({
                      field: "inspected_by",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    onMouseLeaveUpdateFieldStatus({
                      field: "inspected_by",
                    });
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const inputValue = e.target.value;

                    onChangeInputField({
                      field: "inspected_by",
                      value: inputValue,
                    });
                  }}
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    const value = HTMLEntities.encode(e?.target?.value.trim());
                    if (value !== details?.inspected_by) {
                      updateInputFieldOnBlur({
                        field: "inspected_by",
                        value: value,
                      });
                    } else {
                      updateDataInStore({
                        inspected_by: details.inspected_by,
                      });
                      onBlurUpdateFieldStatus({
                        field: "inspected_by",
                      });
                    }
                  }}
                />
              </li>
              <li
                className={`overflow-hidden flex ${
                  !isDateTimeVisible ? "hidden" : ""
                }`}
                ref={dtDivRef}
              >
                <InlineField
                  label={_t("Date/Time")}
                  labelPlacement="left"
                  field={
                    <div className="grid grid-cols-2 gap-1 items-center w-full">
                      <DatePickerField
                        label=""
                        placeholder={_t("Select Date")}
                        labelPlacement="left"
                        name="inspection_date"
                        id="inspection_date"
                        disabled={isReadOnly}
                        readOnly={isReadOnly}
                        editInline={true}
                        iconView={true}
                        format={CFConfig.day_js_date_format}
                        allowClear={true}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "inspection_date"
                        )}
                        value={
                          displayDateFormat(
                            details.inspection_date?.toString().trim(),
                            CFConfig.day_js_date_format
                          ) ?? null
                        }
                        onChange={(date) => handleChangeInspectionDate(date)}
                        onMouseLeave={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "inspection_date",
                          });
                        }}
                      />
                      <TimePickerField
                        label=""
                        placeholder="00:00"
                        labelPlacement="left"
                        id="inspection_time"
                        name="inspection_time"
                        disabled={isReadOnly}
                        readOnly={isReadOnly}
                        editInline={true}
                        iconView={true}
                        format="hh:mm A"
                        allowClear={true}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "inspection_time"
                        )}
                        value={displayTimeFormat(
                          details.inspection_time?.trim()
                        )}
                        onChange={(_, val) => {
                          handleChangeInspectionTime(val);
                        }}
                        onMouseLeave={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "inspection_time",
                          });
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li
                className={`overflow-hidden ${
                  isDateTimeVisible ? "hidden" : ""
                }`}
                ref={dateTimeVisibleSelectRef}
              >
                <InlineField
                  label={_t("Date/Time")}
                  labelPlacement="left"
                  field={
                    <div className="relative w-full group/edit">
                      <InputField
                        labelPlacement="left"
                        placeholder={_t("Select Date")}
                        editInline={true}
                        iconView={true}
                        required={false}
                        readOnly={isReadOnly}
                        readOnlyClassName="!h-[34px]"
                        value={dateTimeInputValue}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "inspection_date_time"
                        )}
                        onFocus={() => {
                          setDateTimeVisible(true);
                        }}
                        onMouseLeaveDiv={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "inspection_date_time",
                          });
                        }}
                        onChange={() => {}}
                      />
                      {(!!inputVals.inspection_date ||
                        !!inputVals.inspection_time) &&
                      !isReadOnly &&
                      !["loading", "success", "error"].includes(
                        getStatusForField(loadingStatus, "inspection_date_time")
                      ) ? (
                        <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                          <FontAwesomeIcon
                            icon="fa-solid fa-circle-xmark"
                            className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                            onClick={() => {
                              handleUpdateField({
                                data_to_send_in_api: {
                                  inspection_date: null,
                                  inspection_time: null,
                                },
                              });
                            }}
                          />
                        </div>
                      ) : null}
                    </div>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Assigned To")}
                  name="assigned_to"
                  placeholder={_t("Select Assignee")}
                  labelPlacement="left"
                  iconView={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  editInline={true}
                  value={HTMLEntities.decode(
                    sanitizeString(details.assigned_to_name ?? "")
                  )}
                  avatarProps={{
                    user: {
                      name: HTMLEntities.decode(
                        sanitizeString(details.assigned_to_name || "")
                      ),
                      image: details?.assigned_to_image || "",
                    },
                  }}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "assigned_to"),
                  }}
                  rightIcon={
                    details?.assigned_to ? (
                      <div className="flex items-center gap-1">
                        <ContactDetailsButton
                          onClick={(e) => {
                            e.stopPropagation();

                            dispatch(updateTrueScrollDownToDetail());
                            setContactDetailDialogOpen(true);
                          }}
                        />
                        <DirectoryFieldRedirectionIcon
                          directoryId={details?.assigned_to.toString()}
                          directoryTypeKey={
                            details?.dir_type
                              ? getDirectaryKeyById(
                                  Number(details?.dir_type) === 1
                                    ? 2
                                    : Number(details?.dir_type),
                                  gConfig
                                )
                              : ""
                          }
                        />
                        {!isReadOnly && (
                          <ButtonWithTooltip
                            tooltipTitle={_t("Send Notification")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-paper-plane"
                            iconClassName="w-3.5 h-3.5"
                            onClick={(e) => {
                              e.stopPropagation();
                              dispatch(updateTrueScrollDownToDetail());
                              setIsConfirmDialogOpen(true);
                            }}
                          />
                        )}
                      </div>
                    ) : (
                      <></>
                    )
                  }
                  onClick={() => setIsAssignedToOpen(true)}
                />
              </li>
              {/* <li className="overflow-hidden">
                <InlineField
                  label={_t("Permit") + " #"}
                  labelPlacement="left"
                  field={
                    <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
                      <div
                        className={`hover:w-full focus-within:w-full focus:w-full ${
                          !details.permit_id ||
                          ["loading", "success", "error"].includes(
                            getStatusForField(loadingStatus, "permit_id")
                          )
                            ? "w-full"
                            : "max-w-[calc(100%-24px)]"
                        }`}
                      >
                        <SelectField
                          placeholder={_t("Select Permit") + " #"}
                          name="permit_id"
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          disabled={isReadOnly}
                          readOnly={isReadOnly}
                          filterOption={(input, option) =>
                            filterOptionBySubstring(
                              input,
                              option?.label as string
                            )
                          }
                          allowClear={!!Number(details?.permit_id ?? "")}
                          options={permitLogOptions || []}
                          value={
                            details.permit_id
                              ? permitLogOptions.filter((item) => {
                                  return (
                                    details?.permit_id?.toString() ===
                                    item?.value?.toString()
                                  );
                                })
                              : []
                          }
                          onSelect={(value) => {
                            updateInputFieldOnBlur({
                              field: "permit_id",
                              value: Number(value) || "",
                            });
                          }}
                          onClear={() => {
                            updateInputFieldOnBlur({
                              field: "permit_id",
                              value: "",
                            });
                            updateInputFieldOnBlur({
                              field: "permit_expire_date",
                              value: null,
                            });
                          }}
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "permit_id"
                          )}
                        />
                      </div>
                      {details.permit_id && (
                        <PermitFieldRedirectionIcon
                          permitId={Number(details?.permit_id)}
                        />
                      )}
                    </div>
                  }
                />
              </li> */}
              {/* <li>
                <DatePickerField
                  label={_t("Permit Expire Date")}
                  placeholder={_t("Select Date")}
                  labelPlacement="left"
                  id="permit_expire_date"
                  disabled={
                    !!details.permit_expire_date ||
                    !details.permit_id ||
                    isReadOnly
                  }
                  readOnly={isReadOnly}
                  editInline={true}
                  iconView={true}
                  format={CFConfig.day_js_date_format}
                  allowClear={true}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "permit_expire_date"
                  )}
                  value={
                    displayDateFormat(
                      details.permit_expire_date?.toString().trim(),
                      CFConfig.day_js_date_format
                    ) ?? null
                  }
                  onChange={(date) => handleChangePermitDate(date)}
                  onMouseLeave={() => {
                    onMouseLeaveUpdateFieldStatus({
                      field: "permit_expire_date",
                    });
                  }}
                />
              </li> */}
              <li>
                <TextAreaField
                  ref={notesRef}
                  label={_t("Corrections Needed")}
                  placeholder={_t("Corrections Needed")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={HTMLEntities.decode(
                    sanitizeString(inputVals.notes || "")
                  )}
                  onClickStsIcon={() => {
                    notesRef.current?.focus();
                  }}
                  fixStatus={getStatusForField(loadingStatus, "notes")}
                  onFocus={() => {
                    onFocusUpdateFieldStatus({
                      field: "notes",
                    });
                  }}
                  onMouseEnter={() => {
                    onMouseEnterUpdateFieldStatus({
                      field: "notes",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    onMouseLeaveUpdateFieldStatus({
                      field: "notes",
                    });
                  }}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                    const inputValue = e.target.value;

                    onChangeInputField({
                      field: "notes",
                      value: inputValue,
                    });
                  }}
                  onBlur={(e: React.FocusEvent<HTMLTextAreaElement>) => {
                    const value = HTMLEntities.encode(e?.target?.value.trim());
                    if (value !== details?.notes) {
                      updateInputFieldOnBlur({
                        field: "notes",
                        value: value,
                      });
                    } else {
                      updateDataInStore({
                        notes: details.notes,
                      });
                      onBlurUpdateFieldStatus({
                        field: "notes",
                      });
                    }
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />

      <ConfirmModal
        isOpen={isConfirmDialogOpen}
        modalIcon="fa-regular fa-paper-plane"
        size="510px"
        modaltitle={_t("Send Notification")}
        description={_t(
          `Do you want to send a notification to the assigned contacts? Notifications are sent based on the preferences within the user's account.`
        )}
        onCloseModal={closeConfirmationModal}
        onAccept={() => {
          handleSendNotification();
        }}
        isLoading={isSendNotificationLoading}
        onDecline={closeConfirmationModal}
      />

      {isAssignedToOpen && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isAssignedToOpen}
          closeDrawer={() => setIsAssignedToOpen(false)}
          singleSelecte={true}
          setCustomer={(data) => {
            handleDrawerSelection(data);
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={selectedCustomerData}
          groupCheckBox={true}
          projectId={Number(details?.project_id)}
          additionalContactDetails={1}
        />
      )}

      {contactDetailDialogOpen && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          contactId={details?.assigned_to || ""}
          readOnly={isReadOnly}
          additional_contact_id={details.contact_id || 0}
          sendEmailDrawer={{
            customEmailData: { subject: details?.email_subject },
          }}
        />
      )}
    </>
  );
};

export default DetailsCard;
