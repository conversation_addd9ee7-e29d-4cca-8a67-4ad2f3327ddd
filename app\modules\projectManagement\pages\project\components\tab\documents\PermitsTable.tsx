import { useEffect, useState } from "react";

// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { PermitFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/permitFieldRedirectionIcon";
// other
import { useAppProSelector } from "../../../redux/store";
import { useParams } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getGConfig } from "~/zustand";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { sendMessageKeys } from "~/components/page/$url/data";
import { PermitDetails } from "../sidebar";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { sanitizeString } from "~/helpers/helper";

const PermitsTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const permits = documentsData?.permits ?? [];
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const permitsModule = getGlobalModuleByKey(CFConfig.project_permit_module);

  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const { authorization }: GConfig = getGConfig();

  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });
  const [allPermits, setAllPermits] = useState(permits);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);

  const displayedPermits = isShowingMore
    ? allPermits
    : allPermits.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.permits_count?.[0]?.number_of_permit ?? 0
  );
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);
  const [viewData, setViewData] = useState<PermitsData>();
  const [contactId, setcontactId] = useState<number>(0);
  const [additionalContact, setAdditionContact] = useState<number>(0);

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllPermits(permits);
      setIsInitialLoad(false);
    } else {
      const newPermits = permits.filter(
        (item) => !allPermits.some((p) => p.permit_id === item.permit_id)
      );
      if (newPermits.length > 0) {
        setAllPermits((prev) => [...prev, ...newPermits]);
      } else {
        setAllPermits(permits);
      }
    }
  }, [permits, isInitialLoad]);

  useEffect(() => {
    if (permits.length) {
      setCollapse(["1"]);
    }
  }, [permits]);

  const handleShowMore = () => {
    if (allPermits.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["project_permits", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const handleViewClick = async (data: PermitsData) => {
    setIsViewOpen(true);
    setViewData(data);
  };

  const columnDefs = [
    {
      headerName: _t("Type"),
      field: "permit_type_name",
      minWidth: 135,
      maxWidth: 135,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const permit_type_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={permit_type_name}>
            <Typography className="table-tooltip-text">
              {permit_type_name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Agency"),
      field: "agency_name",
      minWidth: 130,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const agency_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={agency_name}>
            <Typography className="table-tooltip-text">
              {agency_name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Expires"),
      field: "expire_date",
      maxWidth: 390,
      minWidth: 390,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: string }) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },
    {
      headerName: "",
      field: "permit_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ data }: { data: PermitsData }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                handleViewClick(data);
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_PROJECT_PERMITS.url +
                    "/" +
                    (data.permit_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
              }}
            />
            <div className="w-6">
              <PermitFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                permitId={Number(data.permit_id)}
              />
            </div>
          </div>
        );
      },
    },
  ];
  return (
    <>
      <CollapseSingleTable
        title={_t(permitsModule?.plural_name || "Permit")}
        addButton={permitsModule?.module_name || "Permit"}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }
          const newURL = new URL(
            routes.MANAGE_PROJECT_PERMITS.url + "/",
            window.location.origin
          );
          newURL.searchParams.set("authorize_token", tempAuthorization);
          newURL.searchParams.set("iframecall", "1");
          newURL.searchParams.set("from_remix", "1");
          newURL.searchParams.set("action", "new");
          newURL.searchParams.set("project", id?.toString());
          setIframeData({
            addUrl: newURL.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedPermits}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-permits.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectDocumentsModules(false, [
              "project_permits",
              "counts",
            ]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectDocumentsModules(false, [
                "project_permits",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}

      {isViewOpen && (
        <PermitDetails
          setIsPermitsDetails={setIsViewOpen}
          isPermitsDetails={isViewOpen}
          isViewOnly={false}
          formData={viewData}
          onClick={() =>
            window.open(
              routes.MANAGE_PROJECT_PERMITS.url +
                "/" +
                viewData?.permit_id +
                "/details",
              "_blank"
            )
          }
          setIsOpenContactDetails={setIsOpenContactDetails}
          setAdditionContact={setAdditionContact}
          setcontactId={setcontactId}
        />
      )}

      {isOpenContactDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetails}
          onCloseModal={() => setIsOpenContactDetails(false)}
          contactId={contactId}
          additional_contact_id={additionalContact}
        />
      )}
    </>
  );
};

export default PermitsTable;
