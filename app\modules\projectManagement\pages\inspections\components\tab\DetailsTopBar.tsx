// Hooks
import { useTranslation } from "~/hook";
// React + ag-grid
import { useMemo, useState } from "react";

// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";

import {
  getGModuleDashboard,
  getGSettings,
  setCommonSidebarCollapse,
  useGModules,
} from "~/zustand";
import { useInAppSelector } from "../../redux/store";
import { getStatusForField } from "~/shared/utils/helper/common";
import { Number, sanitizeString } from "~/helpers/helper";
import { useInspectionLogDetail } from "../../hooks/useInspectionDetails";
import { MenuProps } from "antd";
import { InspectionsTableDropdownItems } from "../dashboard/InspectionsTableDropdownItems";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { INSPECTION_STATUS_ICON } from "../../utils/constants";

const DetailsTopBar = ({
  sidebarCollapse,
  onReloadDirDetails,
}: ISMDetailsTopBarProps) => {
  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();
  const currentModule = getCurrentMenuModule();
  const { module_access } = currentModule || {};
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(currentModule?.module_key) === "read_only",
    [currentModule?.module_key]
  );

  const onChangeEquipProject = (projects: IProject[]) => {
    if (projects.length) {
      if (projects[0].id?.toString() !== details.project_id?.toString()) {
        handleUpdateField({
          data_to_send_in_api: {
            project_id: Number(projects[0].id),
            project_name: projects[0].project_name,
            show_client_access:
              projects[0].show_client_access?.toString() || "0",
            permit_expire_date: null,
            permit_id: null,
          },
        });
      }
    } else {
      updateDataInStore(details);
      notification.error({
        description: "Project field is required.",
      });
    }
  };

  const { details, isDetailLoading } = useInAppSelector(
    (state) => state.inspectionDetails
  );

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const StatusbarOption: Array<ModuleStatus> | undefined =
    gModuleDashboard?.module_setting?.module_status;
  const { is_custom_inspection_id }: GSettings = getGSettings();

  const [isSelectProjectForEquipOpen, setIsSelectProjectForEquipOpen] =
    useState<boolean>(false);

  const {
    handleUpdateField,
    handleChangeFieldStatus,
    updateDataInStore,
    onMouseLeaveUpdateFieldStatus,
    loadingStatus,
    onFocusUpdateFieldStatus,
    onMouseEnterUpdateFieldStatus,
    updateInputFieldOnBlur,
    onChangeInputField,
    inputVals,
    setInputVals,
  } = useInspectionLogDetail();

  const status = useMemo(() => {
    const statusList = StatusbarOption?.map((item: ModuleStatus) => ({
      label: HTMLEntities.decode(sanitizeString(item.name)),
      key: item?.item_id?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.status_color,
          }}
        />
      ),
    }));

    const getSelectStatus = StatusbarOption?.find(
      (item) =>
        item.item_id?.toString() === details.inspection_status?.toString()
    );

    const selectStatus = (
      <Tooltip
        title={HTMLEntities.decode(sanitizeString(getSelectStatus?.name)) || ""}
      >
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.status_color
              ? getSelectStatus.status_color + "1d"
              : "#2235581d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.status_color || "#bdbdbd",
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {HTMLEntities.decode(sanitizeString(getSelectStatus?.name)) ||
              "Select Status"}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.status_color || "#bdbdbd",
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [StatusbarOption, details.inspection_status]);

  const handleStatus: MenuProps["onClick"] = (e) => {
    if (e.key?.toString() !== details?.inspection_status?.toString()) {
      const selectedOption = status?.statusList?.find(
        (option) => option.key === e.key?.toString()
      );

      if (selectedOption) {
        handleUpdateField({
          data_to_send_in_api: {
            inspection_status: Number(e.key),
          },
          data_to_update_in_store: {
            inspection_status_name: selectedOption.label,
          },
        });
      } else {
        const description = "Selected option not found in statusList";
        notification.error({
          description,
        });
      }
    }
  };

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] md:h-[66px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isDetailLoading ? (
              <TopBarSkeleton num={3} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] !mr-auto xl:w-[calc(40%-200px)] md:w-[calc(100%-150px)] w-full">
                  <div
                    className="w-11 h-11 flex items-center justify-center bg-[#7E8DAC] rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white"
                    style={{
                      backgroundColor: status.getSelectStatus?.status_color,
                    }}
                  >
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon={
                        INSPECTION_STATUS_ICON[
                          status?.getSelectStatus
                            ?.key as keyof typeof INSPECTION_STATUS_ICON
                        ]
                      }
                    />
                  </div>
                  <div
                    className={`flex flex-col gap-1 w-[calc(100%-44px)] ${
                      module_access === "read_only" ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <ButtonField
                      label=""
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      isDisabled={isReadOnly}
                      readOnly={isReadOnly}
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      onClick={() => setIsSelectProjectForEquipOpen(true)}
                      placeholder={_t("Select Project")}
                      required={true}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-base h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-base font-medium"
                      statusProps={{
                        status: getStatusForField(loadingStatus, "project_id"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "project_id",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      value={HTMLEntities.decode(
                        sanitizeString(details?.project_name || "")
                      )}
                      headerTooltip={`Project: ${HTMLEntities.decode(
                        sanitizeString(details?.project_name || "")
                      )}`}
                      rightIcon={
                        details?.project_name && (
                          <ProjectFieldRedirectionIcon
                            projectId={details?.project_id?.toString() || ""}
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          />
                        )
                      }
                    />
                    <div
                      className={`flex items-center gap-2 ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex gap-2 items-center">
                        <Dropdown
                          menu={{
                            items: status.statusList,
                            selectable: true,
                            selectedKeys: details.inspection_status
                              ? [details.inspection_status?.toString()]
                              : [],
                            onClick: handleStatus,
                          }}
                          disabled={isReadOnly}
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status.selectStatus || "Select status"}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "inspection_status")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(
                              loadingStatus,
                              "inspection_status"
                            )}
                          />
                        )}
                      </div>
                      <Tooltip
                        title={HTMLEntities.decode(
                          sanitizeString(
                            isFocused
                              ? inputVals?.custom_inspection_id ||
                                  inputVals?.company_inspection_id // On focus, show only custom_inspection_id
                              : `Insp. #${
                                  inputVals?.custom_inspection_id ||
                                  inputVals?.company_inspection_id
                                }`
                          )
                        )}
                        placement="topLeft"
                      >
                        <div
                          className={`overflow-hidden ${
                            isReadOnly || is_custom_inspection_id !== 0
                              ? "w-full max-w-[262px]"
                              : "w-fit"
                          }
                          `}
                        >
                          <InputField
                            placeholder={_t("Insp.") + " #"}
                            labelPlacement="left"
                            name="custom_inspection_id"
                            id="custom_inspection_id"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] text-sm font-medium py-0"
                            readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate sm:block flex"
                            maxLength={20}
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            disabled={
                              isReadOnly || is_custom_inspection_id !== 0
                                ? false
                                : true
                            }
                            readOnly={isReadOnly}
                            value={HTMLEntities.decode(
                              sanitizeString(
                                isFocused
                                  ? inputVals?.custom_inspection_id ??
                                      inputVals?.company_inspection_id // On focus, show only custom_inspection_id
                                  : `Insp. #${
                                      inputVals?.custom_inspection_id ??
                                      inputVals?.company_inspection_id
                                    }`
                              )
                            )}
                            editInline={true}
                            iconView={true}
                            onChange={(e) => {
                              const newVal = e.target.value?.trimStart();
                              onChangeInputField({
                                field: "custom_inspection_id",
                                value: newVal,
                              });
                            }}
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "custom_inspection_id"
                            )}
                            onMouseEnter={() => {
                              onMouseEnterUpdateFieldStatus({
                                field: "custom_inspection_id",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              onMouseLeaveUpdateFieldStatus({
                                field: "custom_inspection_id",
                              });
                            }}
                            onFocus={() => {
                              setIsFocused(true);
                              onFocusUpdateFieldStatus({
                                field: "custom_inspection_id",
                              });

                              setInputVals((prev) => {
                                return {
                                  ...prev, // Spread the previous state to preserve other properties
                                  custom_inspection_id:
                                    prev.custom_inspection_id, // If no custom_inspection_id, return the same value
                                };
                              });
                            }}
                            onBlur={(e) => {
                              setIsFocused(false);
                              const value = HTMLEntities.encode(
                                e?.target?.value.trim()
                              );
                              updateInputFieldOnBlur({
                                field: "custom_inspection_id",
                                value,
                                message: "Insp. # field is required.",
                                required: true,
                              });
                            }}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center md:w-fit w-full">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {}}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <IconButton
                      htmlType="button"
                      variant="default"
                      className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                      iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                      icon="fa-regular fa-bars"
                      onClick={() => setCommonSidebarCollapse(!sidebarCollapse)}
                    />
                  </div>
                  <ul className="flex items-center justify-end gap-2.5">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName={`!text-primary-900
                          ${
                            isDetailLoading
                              ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                              : "group-hover/buttonHover:!text-deep-orange-500"
                          }`}
                        className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                          isDetailLoading
                            ? "hover:bg-transparent"
                            : "hover:!bg-deep-orange-500/5"
                        }`}
                        onClick={onReloadDirDetails}
                      />
                    </li>
                    {(currentModule?.module_access === "full_access" ||
                      currentModule?.module_access === "own_data_access") &&
                    details ? (
                      <li>
                        <InspectionsTableDropdownItems
                          data={details}
                          className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-300 hover:!bg-deep-orange-500/5"
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          contentClassName="w-[250px]"
                          tooltipcontent={_t("More")}
                          refreshTable={() => {
                            onReloadDirDetails();
                          }}
                        />
                      </li>
                    ) : null}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {isSelectProjectForEquipOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectForEquipOpen}
          setOpen={setIsSelectProjectForEquipOpen}
          module_key={currentModule?.module_key}
          selectedProjects={
            details?.project_id && details?.project_name
              ? [
                  {
                    id: !isNaN(Number(details.project_id))
                      ? Number(details.project_id)
                      : details.project_id,
                    project_name: _t(
                      HTMLEntities.decode(sanitizeString(details.project_name))
                    ),
                  },
                ]
              : []
          }
          onProjectSelected={(data) => {
            onChangeEquipProject(data);
          }}
          isRequired={false}
        />
      )}
    </>
  );
};

export default DetailsTopBar;
