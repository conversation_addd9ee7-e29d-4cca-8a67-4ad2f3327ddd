// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
import { useEffect, useMemo, useRef, useState } from "react";
import { getGConfig } from "~/zustand";
import { useInAppDispatch, useInAppSelector } from "../../redux/store";
import { fetchInspectionsDashboardApi } from "../../redux/action/inspectionDashAction";
import { sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { useNavigate } from "@remix-run/react";
import { CellClickedEvent } from "ag-grid-community";

const InspectionsWithTasksToComplete = () => {
  const { _t } = useTranslation();
  const { module_name }: GConfig = getGConfig();
  const dispatch = useInAppDispatch();
  const gridApiRef = useRef<GridApi | null>(null);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IInspectionWithTasksToCompeleteData[]>(
    []
  );
  const { get_inspections_with_tasks_to_complete, isDashboardLoading } =
    useInAppSelector((state) => state.inspectionsDashboard);

  const hasData = useMemo(() => {
    return (
      get_inspections_with_tasks_to_complete &&
      get_inspections_with_tasks_to_complete?.data.length > 0
    );
  }, [get_inspections_with_tasks_to_complete]);

  useEffect(() => {
    if (!isCashLoading && get_inspections_with_tasks_to_complete?.data) {
      setRowData(get_inspections_with_tasks_to_complete?.data);
    }
  }, [get_inspections_with_tasks_to_complete, isCashLoading]);

  const navigate = useNavigate();
  const handleClickRefresh = async () => {
    setisCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchInspectionsDashboardApi({
        refresh_type: "get_inspections_with_tasks_to_complete",
      })
    );
    setisCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Insp.") + " #",
      field: "custom_insp_id",
      minWidth: hasData ? 120 : 100,
      maxWidth: hasData ? 120 : 100,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionWithTasksToCompeleteData }) => {
        const { data } = params;
        const idToolT = `Insp. #${data?.custom_insp_id}`;

        return data.custom_insp_id ? (
          <Tooltip title={idToolT}>
            <Typography className="table-tooltip-text">
              {data.custom_insp_id}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: hasData ? 130 : 100,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionWithTasksToCompeleteData }) => {
        const { data } = params;
        return data.project_name ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.project_name))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.project_name))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "type",
      minWidth: hasData ? 130 : 80,
      maxWidth: hasData ? 130 : 80,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: { data: IInspectionWithTasksToCompeleteData }) => {
        const { data } = params;
        return data.inspection_type ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.inspection_type))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.inspection_type))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noRowsOverlay = () => (
    <StaticTableRowLoading columnDefs={columnDefs} limit={6} />
  );

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-ins-task-complete.svg`}
    />
  );

  return (
    <>
      <DashboardCardHeader
        title={_t(`${module_name} with Tasks to Complete`)}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={
          get_inspections_with_tasks_to_complete?.last_refres_time
        }
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        {/* Notes: Developer side set uncomment */}
        {/* {isDashLoading && (
          <Spin className="w-full h-[203px] flex items-center justify-center" />
        )} */}
        <div className="ag-theme-alpine h-[209px] ag-grid-cell-pointer">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            key={isDashboardLoading ? "loading" : "loaded"}
            onCellClicked={(params: CellClickedEvent) => {
              const column = params.column;
              if (
                column.getColDef().field !== "" &&
                column.getColDef().field !== "email"
              ) {
                navigate(
                  `${routes.MANAGE_INSPECTION.url}/${params?.data?.inspection_id}`
                );
              }
            }}
            noRowsOverlayComponent={
              isCashLoading || isDashboardLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};
export default InspectionsWithTasksToComplete;
