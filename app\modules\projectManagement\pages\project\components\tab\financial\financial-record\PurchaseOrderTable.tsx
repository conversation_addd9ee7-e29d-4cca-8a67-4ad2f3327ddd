import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { PurchaseOrdersFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/purchaseOrdersFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { routes } from "~/route-services/routes";
import { useNavigate, useParams } from "@remix-run/react";
import {
  getDefaultStatuscolor,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { sendMessageKeys } from "~/components/page/$url/data";
import { getGConfig } from "~/zustand";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";

const PurchaseOrderTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { getGlobalModuleByKey } = useGlobalModule();
  const PurchaseOrderModule = getGlobalModuleByKey(
    CFConfig.purchase_order_module
  );
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const purchaseOrders = financialData?.purchase_orders ?? [];

  const [selectedId, setSelectedId] = useState<number>(0);
  const navigate = useNavigate();
  const { id } = useParams();
  const [allPurchaseOrders, setAllPurchaseOrders] = useState<
    IProjectPurchaseOrdersData[]
  >([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const purchaseFinancialData = isShowingMore
    ? allPurchaseOrders
    : allPurchaseOrders?.slice(0, dataLimit);
  const totalCount = Number(
    financialData?.purchase_orders_count?.[0]?.number_of_purchase_order ?? 0
  );
  const totalAmount = Number(
    financialData?.purchase_orders_count?.[0]?.total ?? 0
  );

  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  const [collapse, setCollapse] = useState<string[]>([]);

  useEffect(() => {
    if (isInitialLoad) {
      setAllPurchaseOrders(purchaseOrders);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(
      purchaseOrders?.map((po) => [po?.purchase_order_id, po])
    );

    const mergedPurchaseOrders = allPurchaseOrders?.map((existing) => {
      const updated = updatedMap?.get(existing?.purchase_order_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(
      allPurchaseOrders?.map((po) => po?.purchase_order_id)
    );
    const newPurchaseOrders = purchaseOrders?.filter(
      (po) => !existingIds?.has(po?.purchase_order_id)
    );

    const nextAll = [...mergedPurchaseOrders, ...newPurchaseOrders];

    const hasChanged =
      nextAll?.length !== allPurchaseOrders?.length ||
      nextAll?.some(
        (po, i) => JSON.stringify(po) !== JSON.stringify(allPurchaseOrders[i])
      );

    if (hasChanged) {
      setAllPurchaseOrders(nextAll);
    }
  }, [purchaseOrders, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "purchase_order") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allPurchaseOrders.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["purchase_orders"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "#",
      field: "company_purchase_order_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const poId = `PO #${HTMLEntities.decode(sanitizeString(value))}`;
        return value ? (
          <Tooltip title={poId}>
            <Typography className="table-tooltip-text">{poId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "order_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: "Subject",
      field: "subject",
      minWidth: 200,
      maxWidth: 200,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const subject = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "Supplier",
      field: "supplier_company_name",
      minWidth: 120,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) =>
        value ? (
          <Tooltip title={HTMLEntities.decode(sanitizeString(value))}>
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(value))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        ),
    },
    {
      headerName: _t("Status"),
      field: "billing_status_name",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: PurchaseOrder }) => {
        const status = data.billing_status_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.status_color || ""
        );

        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <div className="table-tooltip-text text-center">-</div>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formattedValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return formattedValue ? (
          <Tooltip title={formattedValue}>
            <Typography className="table-tooltip-text">
              {formattedValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "purchase_order_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: PurchaseOrder }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_PURCHASE_ORDERS.url +
                    "/" +
                    (data?.purchase_order_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <PurchaseOrdersFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              purchaseOrdersId={data.purchase_order_id}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(PurchaseOrderModule?.plural_name ?? "Purchase Orders")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(PurchaseOrderModule?.module_name ?? "Purchase Order")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_PURCHASE_ORDERS.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_PURCHASE_ORDERS.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={purchaseFinancialData}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-purchase-orders.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, [
              "purchase_orders",
              "counts",
            ]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, [
                "purchase_orders",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default PurchaseOrderTable;
