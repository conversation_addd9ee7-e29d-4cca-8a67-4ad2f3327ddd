import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  useMemo,
  useRef,
  useState,
} from "react";
import { Form, useNavigate } from "@remix-run/react";

// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";

// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { Text<PERSON>rea<PERSON>ield } from "~/shared/components/molecules/textAreaField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import FieldRedirectButton from "~/shared/components/molecules/fieldRedirect/fieldRedirectButton/FieldRedirectButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";

// Organisms
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";

// Shared
import {
  filterOptionBySubstring,
  floatNumberRegex,
  onKeyDownCurrency,
  onKeyDownNumber,
  wholeNumberRegex,
} from "~/shared/utils/helper/common";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { getModuleAccess } from "~/shared/utils/helper/module";
import { removeDuplicatesFile } from "~/shared/utils/helper/removeDuplicatesFile";

// Formik
import { useFormik } from "formik";

// zustand
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";

// Other
import ItemGroupTable from "./itemGroupTable";
import { useTranslation } from "~/hook";
import dayjs from "dayjs";
import { sanitizeString } from "~/helpers/helper";
import { costCodeRoutes } from "~/route-services/cost-code.routes";
import { addUnit } from "~/redux/action/unitActions";
import ItemUploadProductImage from "./ItemUploadProductImage";
import { getGSettings } from "~/zustand";
import { NoRecords } from "~/shared/components/molecules/noRecords";

const AddItems = ({
  formType = "add",
  addItemsOpen,
  setAddItemsOpen,
  isViewOnly,
  type,
  formInitialValues,
  formValidationSchema,
  formOnSubmit,
  loading,
  module,
  onRemoveFile,
  onBuildId,
  handleUpdateClick,
  readOnly = false,
  fetchingFormItemCount = 0,
  onNavigate,
  canNavigatePrevious,
  canNavigateNext,
  addItemsRef,
  hasChanges,
  setHasChanges,
  refreshAgGrid,
  unitList = [],
  setUnitList = () => {},
}: IAddItemsProps) => {
  const { _t } = useTranslation();
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const { cost_code_sort_order, cost_code_display_order }: GSettings =
    getGSettings();

  const [sideMenuOpen, setSideMenuOpen] = useState<boolean>(false);
  const navigate = useNavigate();
  const [sideActive, setSideActive] = useState<string | null>("details");
  const [isManualClick, setIsManualClick] = useState<boolean>(false);
  const [unitSelectShow, setUnitSelectShow] = useState<boolean>(false);
  const [unitCostSelectShow, setUnitCostSelectShow] = useState<boolean>(false);
  const [isUploadingProductImage, setIsUploadingProductImage] =
    useState<boolean>(false);

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { checkGlobalModulePermissionByKey } = useGlobalModule();
  const { image_resolution = "" } = appSettings || {};

  const [submited, setSubmited] = useState<boolean>(false);
  const [isSelectDirectoryType, setIsSelectDirectoryType] =
    useState<string>("");
  const [selectVendorOptions, setSelectVendorOptions] = useState<
    CustomerEmailTab[]
  >([]);
  const [selectOperatorOptions, setSelectOperatorOptions] = useState<
    CustomerEmailTab[]
  >([]);
  const [isContactDetails, setIsContactDetails] = useState<
    "supplier" | "operator" | false
  >(false);
  const [costCodes, setCostCodes] = useState<
    ICostCodeGroupListResponse["data"]["cost_codes"]
  >([]);
  const [isLoadingCostCodes, setIsLoadingCostCodes] = useState(true);
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  const [attachedFile, setAttachedFile] = useState({});
  const [isNotes, setNotes] = useState(true);
  const [isFileDeleted, setIsFileDeleted] = useState<boolean>(false);
  const [deletedFile, setDeletedFile] = useState<string>("");

  const formik = useFormik({
    initialValues: formInitialValues,
    validationSchema: formValidationSchema,
    onSubmit: formOnSubmit,
  });

  useEffect(() => {
    if (isFileDeleted) {
      if (
        Number(deletedFile) >= 0 &&
        Number(deletedFile) <= formik.values.photos.length
      ) {
        formik.values.photos.splice(Number(deletedFile), 1);
      }
      formik.setValues((prev) => ({
        ...prev,
        photos: formik.values.photos,
      }));
    }
  }, [deletedFile, isFileDeleted]);

  useImperativeHandle(addItemsRef, () => ({
    formik,
  }));

  useEffect(() => {
    if (addItemsOpen) {
      setTimeout(() => {
        document.getElementById("details")?.scrollIntoView();
      }, 150);
      setSideActive("details");
      setIsManualClick(false);
      setSubmited(false);
      if (setHasChanges) {
        setHasChanges(false);
      }
    }
  }, [addItemsOpen]);

  useEffect(() => {
    if (addItemsOpen) {
      formik.setValues(formInitialValues);
    }
  }, [addItemsOpen, formInitialValues]);

  useEffect(() => {
    (async function () {
      setIsLoadingCostCodes(true);
      try {
        const data = await getWebWorkerApiParams({
          otherParams: {
            order_by:
              cost_code_sort_order === "by_number" ? "csi_number" : "csi_name",
            order_by_dir: "ASC",
          },
        });
        const response = (await webWorkerApi({
          url: costCodeRoutes.group_list,
          method: "post",
          data: data,
        })) as ICostCodeGroupListResponse;

        setCostCodes(response.data.cost_codes || []);
      } catch (error) {
        console.error("Failed to fetch cost codes", error);
      } finally {
        setIsLoadingCostCodes(false);
      }
    })();
  }, []);

  const title = useMemo(() => {
    let title = "";
    if (formType === "add") {
      title = "Add ";
    }
    if (module?.original_module_name) {
      title += module.original_module_name;
    } else {
      title += "Item";
    }
    if (formType === "edit") {
      title += " Details";
    }
    return title;
  }, [formType, module?.original_module_name, type]);

  const buttonTest = useMemo(() => {
    if (formType !== "add") return "Save";

    let title = "Create ";
    if (type !== "group") {
      if (module?.original_module_name) {
        title += module.original_module_name;
      } else {
        title += "Item";
      }
    } else {
      title += "Item Group";
    }
    return title;
  }, [formType, module?.original_module_name, type]);

  const TABS = [
    {
      key: "details",
      name: _t("Details"),
      icon: "fa-solid fa-file",
    },
    ...(type === "equipment"
      ? [
          {
            key: "equipment",
            name: _t("Equipment Info"),
            icon: "fa-solid fa-ruler-combined",
          },
        ]
      : []),
    {
      key: "notes",
      name: _t("Notes"),
      icon: "fa-solid fa-memo",
    },
    {
      key: "files",
      icon: "fa-solid fa-file-image",
      name: _t("Files"),
    },
  ];

  const costCodeValue = useMemo(() => {
    if (isLoadingCostCodes) return "";

    const value = costCodes.reduce((obj, costCode) => {
      const found = costCode.sub_code.find(
        (subCode) =>
          subCode.code_id?.toString() === formik.values.cost_code?.toString()
      );
      if (found) return found;

      return obj;
    }, undefined as ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0] | undefined);

    if (!value)
      return formik.values.cost_code_name
        ? formik.values.cost_code_name + ` (Archived)`
        : "";

    return value.code_id?.toString() || "";
  }, [
    costCodes,
    formik.values.cost_code,
    formik.values.cost_code_name,
    isLoadingCostCodes,
  ]);

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      if (isManualClick) return;
      const scrollTop = scrollContainer.scrollTop;
      let closestSection: string | null = null;
      let minDistance = Infinity;

      TABS.forEach((tab) => {
        const section = sectionRefs.current[tab.key];
        if (section) {
          const rect = section.getBoundingClientRect();
          const containerRect = scrollContainer.getBoundingClientRect();
          const distance = Math.abs(rect.top - containerRect.top);

          if (distance < minDistance) {
            minDistance = distance;
            closestSection = tab.key;
          }
        }
      });

      if (closestSection && closestSection !== sideActive) {
        setSideActive(closestSection);
      }
    };

    scrollContainer.addEventListener("scroll", handleScroll);
    return () => {
      scrollContainer.removeEventListener("scroll", handleScroll);
    };
  }, [TABS, sideActive, isManualClick]);

  const sellingCost = useMemo(() => {
    if (formik.values.unit_cost) {
      if (formik.values.hidden_markup) {
        const unitCost = Number(formik.values.unit_cost);
        const hiddenMarkup = Number(formik.values.hidden_markup);
        const hiddenMarkupUnitCost = (unitCost * hiddenMarkup) / 100;
        return (unitCost + hiddenMarkupUnitCost).toFixed(2);
      }
      return formik.values.unit_cost.toString();
    }
    return "0.00";
  }, [formik.values.unit_cost, formik.values.hidden_markup]);

  const totalCost = useMemo(() => {
    if (formik.values.markup) {
      const cost = Number(sellingCost);
      const markup = Number(formik.values.markup);
      const markupCost = (cost * markup) / 100;
      return (cost + markupCost).toFixed(2);
    }
    return sellingCost;
  }, [sellingCost, formik.values.markup]);
  const subCodeOptionLabel = useCallback(
    (subCode) => {
      const { csi_code, csi_name, is_deleted } = subCode || {};
      const deletedLabel = is_deleted?.toString() === "1" ? " (Archived)" : "";
      const hasCode = !!csi_code;

      switch (cost_code_display_order) {
        case "number_name":
          return `${
            hasCode ? `${csi_code}${csi_name ? " - " : ""}` : ""
          }${csi_name}${deletedLabel}`;

        case "number_in_name":
          return `${hasCode ? `(${csi_code}) ` : ""}${csi_name}${deletedLabel}`;

        case "name_in_number":
          return `${csi_name}${hasCode ? ` (${csi_code})` : ""}${deletedLabel}`;

        case "name_number":
          return `${csi_name}${
            hasCode ? `${csi_name ? " - " : ""} ${csi_code}` : ""
          }${deletedLabel}`;

        default:
          return csi_name || ""; // Fallback if display order is not recognized
      }
    },
    [cost_code_display_order, cost_code_sort_order]
  );
  const parseCodeParts = (code = "") => {
    if (!code) return [null, null];
    const [main, sub] = code.split("-");
    return [
      parseInt(main.replace(/^0+/, "") || "0", 10),
      sub !== undefined ? parseInt(sub.replace(/^0+/, "") || "0", 10) : null,
    ];
  };

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  return (
    <>
      <Drawer
        open={addItemsOpen}
        rootClassName="drawer-open"
        width={975}
        push={false}
        maskClosable={false}
        classNames={{
          header: "!hidden",
          body: "!p-0 !overflow-hidden ",
        }}
      >
        <div className="sidebar-body">
          <div className="flex">
            <div
              className={`w-60 md:min-w-[240px] md:max-w-[240px] md:relative absolute flex-[1_0_0%] z-20 ${
                sideMenuOpen
                  ? "h-screen w-full"
                  : "md:h-screen md:w-full w-0 h-0"
              }`}
            >
              <div
                className={`md:hidden block absolute bg-black/20 ${
                  sideMenuOpen ? "h-full w-full" : "h-0 w-0"
                }`}
                onClick={() => setSideMenuOpen(false)}
              ></div>
              <div
                className={`overflow-y-auto h-screen relative w-[240px] md:bg-gray-200/50 bg-gray-200 dark:bg-dark-800 transition-all ease-in-out duration-300 ${
                  sideMenuOpen ? "left-0" : "md:left-0 -left-[100%]"
                }`}
              >
                <ul className="py-[18px] md:pt-[18px] pt-9 pl-[18px] flex flex-col gap-2.5">
                  {TABS.map((tab) => (
                    <li
                      className={`flex items-center rounded-l-lg hover:bg-[linear-gradient(90deg,#3f4c653d_24%,#3f4c6500_100%)] dark:hover:bg-[linear-gradient(90deg,#343f54_24%,#1e2732_100%)] ${
                        sideActive === tab.key
                          ? "bg-[linear-gradient(90deg,#3f4c653d_24%,#3f4c6500_100%)] dark:bg-[linear-gradient(90deg,#343f54_24%,#1e2732_100%)]"
                          : ""
                      }`}
                    >
                      <a
                        key={tab.key}
                        href={`#${tab.key}`}
                        className={`w-full text-left !border-0 justify-start !h-full rounded-none shadow-none rounded-l-lg !text-primary-900 dark:text-white/90 font-normal capitalize py-4 pl-3.5 !pr-2 whitespace-pre-wrap flex items-center gap-2.5 !bg-transparent hover:!bg-transparent hover:!shadow-none`}
                        onClick={(e) => {
                          e.preventDefault(); // Prevent default anchor behavior
                          setIsManualClick(true);
                          setSideActive(tab.key);
                          sectionRefs.current[tab.key]?.scrollIntoView({
                            behavior: "smooth",
                          });
                          setTimeout(() => {
                            setIsManualClick(false);
                          }, 1000);
                        }}
                      >
                        <div className="w-[30px] flex justify-center text-primary-900/60 dark:text-white/60">
                          <FontAwesomeIcon
                            className="w-5 h-5"
                            icon={tab.icon}
                          />
                        </div>
                        {tab.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="flex w-full max-w-[735px] flex-[1_0_0%] overflow-hidden flex-col">
              <div className="md:px-4 pl-10 pr-5 py-2.5 border-b border-gray-300 dark:border-white/10 flex items-center relative justify-start">
                <Button
                  className="md:!hidden flex !w-6 h-6 !absolute left-2.5"
                  type="text"
                  onClick={() => setSideMenuOpen((prev) => !prev)}
                  icon={
                    <FontAwesomeIcon
                      className="text-base w-[18px] h-[18px] text-primary-gray-80 dark:text-white/90"
                      icon="fa-regular fa-bars"
                    />
                  }
                />
                <div className="flex justify-between w-full">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
                      <FontAwesomeIcon
                        className="w-4 h-4 text-primary-900"
                        icon={module?.icon || CFConfig.other_items_icon}
                      />
                    </div>
                    <Header
                      level={5}
                      className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
                    >
                      {title}
                    </Header>
                  </div>
                  <div className="flex items-center sm:gap-1.5 gap-1">
                    <div className="flex items-center gap-2">
                      {formType !== "add" && type !== "group" && (
                        <div className="flex items-center sm:gap-2 gap-0 pr-2">
                          <ButtonWithTooltip
                            tooltipTitle={_t("Previous")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-chevron-left"
                            className="item-pre-next-button disabled:bg-transparent"
                            onClick={() => {
                              setIsManualClick(true);
                              onNavigate?.(-1);
                              setSideActive("details");
                              setTimeout(() => {
                                sectionRefs.current["details"]?.scrollIntoView({
                                  behavior: "smooth",
                                });
                                setIsManualClick(false);
                              }, 150);
                            }}
                            disabled={!canNavigatePrevious?.()}
                          />
                          <ButtonWithTooltip
                            tooltipTitle={_t("Next")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-chevron-right"
                            className="item-pre-next-button disabled:bg-transparent"
                            onClick={() => {
                              setIsManualClick(true);
                              onNavigate?.(1);
                              setSideActive("details");
                              setTimeout(() => {
                                sectionRefs.current["details"]?.scrollIntoView({
                                  behavior: "smooth",
                                });
                                setIsManualClick(false);
                              }, 150);
                            }}
                            disabled={!canNavigateNext?.()}
                          />
                        </div>
                      )}
                      <ButtonWithTooltip
                        tooltipTitle={
                          <>
                            Status:{" "}
                            {formik.values.status === "0"
                              ? _t("Active")
                              : _t("Archived")}{" "}
                            <br /> Click to{" "}
                            {formik.values.status === "0"
                              ? _t("Archive")
                              : _t("Activate")}{" "}
                            the item
                          </>
                        }
                        tooltipPlacement="top"
                        icon={
                          formik.values.status === "0"
                            ? "fa-regular fa-box-archive"
                            : "fa-regular fa-regular-active"
                        }
                        className={`!bg-transparent hover:!bg-transparent focus:!bg-transparent group/buttonHover ${
                          type !== "group" ? "" : "mr-2"
                        } !w-4 ${readOnly ? "!cursor-no-drop" : ""}`}
                        onClick={() => {
                          if (!readOnly)
                            formik.setFieldValue(
                              "status",
                              formik.values.status === "0" ? "1" : "0"
                            );
                        }}
                      />
                      {type !== "group" && (
                        <ButtonWithTooltip
                          tooltipTitle={
                            !readOnly
                              ? formik.values.is_favorite
                                ? _t("Remove from Favorites")
                                : _t("Add to Favorites")
                              : ""
                          }
                          tooltipPlacement="top"
                          icon={
                            formik.values.is_favorite
                              ? "fa-solid fa-star"
                              : "fa-regular fa-star"
                          }
                          iconClassName="!text-deep-orange-500 group-hover/buttonHover:!text-deep-orange-500"
                          className={`!bg-transparent hover:!bg-transparent focus:!bg-transparent group/buttonHover ${
                            readOnly ? "!cursor-no-drop" : ""
                          }`}
                          onClick={() => {
                            if (!readOnly)
                              formik.setValues((prev) => ({
                                ...prev,
                                is_favorite: !prev.is_favorite,
                              }));
                          }}
                        />
                      )}
                    </div>
                    <CloseButton
                      onClick={() => {
                        if (hasChanges) {
                          refreshAgGrid?.();
                        }
                        setAddItemsOpen(false);
                        formik.resetForm();
                        navigate(
                          window.location.pathname + window.location.search
                        );
                      }}
                    />
                  </div>
                </div>
              </div>
              <Form
                method="post"
                noValidate
                className="py-4"
                onSubmit={readOnly ? undefined : formik.handleSubmit}
              >
                <div
                  ref={scrollContainerRef}
                  className="px-4 h-[calc(100dvh-132px)] overflow-y-auto scroll-smooth"
                >
                  <div className="grid gap-4">
                    <div
                      id="details"
                      ref={(el) => (sectionRefs.current["details"] = el)}
                      className="grid gap-4"
                    >
                      <SidebarCardBorder
                        addGap={true}
                        cardTitle={_t("Details")}
                      >
                        <div className="w-full">
                          <InputField
                            label={_t("Name")}
                            labelPlacement="top"
                            isRequired={true}
                            name="name"
                            value={HTMLEntities.decode(
                              sanitizeString(formik.values.name || "")
                            )}
                            className="p-0"
                            onChange={formik.handleChange}
                            addonAfterIcon={true}
                            errorMessage={
                              (submited && formType === "add") ||
                              formType === "edit"
                                ? formik.errors.name
                                : ""
                            }
                            onBlur={(e) => {
                              formik.setFieldValue(
                                "name",
                                e.currentTarget.value?.trim()
                              );
                            }}
                            disabled={readOnly}
                            onPressEnter={handleEnterKeyPress}
                            // addonAfter={
                            //   type === "group" && (
                            //     <ButtonWithTooltip
                            //       tooltipTitle={
                            //         <>
                            //           Status:{" "}
                            //           {formik.values.status === "0"
                            //             ? _t("Active")
                            //             : _t("Archived")}{" "}
                            //           <br /> Click to{" "}
                            //           {formik.values.status === "0"
                            //             ? _t("Archive")
                            //             : _t("Activate")}{" "}
                            //           the item
                            //         </>
                            //       }
                            //       tooltipPlacement="top"
                            //       icon={
                            //         formik.values.status === "0"
                            //           ? "fa-regular fa-box-archive"
                            //           : "fa-regular fa-regular-active"
                            //       }
                            //       className="!bg-transparent hover:!bg-transparent focus:!bg-transparent group/buttonHover !w-4"
                            //       onClick={() => {
                            //         if (!readOnly)
                            //           formik.setFieldValue(
                            //             "status",
                            //             formik.values.status === "0" ? "1" : "0"
                            //           );
                            //       }}
                            //     />
                            //   )
                            // }
                          />
                        </div>
                        {type !== "group" && (
                          <div className="md:grid-cols-2 grid gap-5">
                            <div className="grid gap-5 md:order-1 order-2 overflow-hidden">
                              <div className="w-full overflow-hidden">
                                <InputField
                                  label={_t("SKU")}
                                  labelPlacement="top"
                                  name="sku"
                                  value={formik.values.sku}
                                  onChange={formik.handleChange}
                                  disabled={readOnly}
                                  onBlur={(e) => {
                                    formik.setFieldValue(
                                      "sku",
                                      e.currentTarget.value?.trim()
                                    );
                                  }}
                                  onPressEnter={handleEnterKeyPress}
                                />
                              </div>
                              <div className="w-full overflow-hidden">
                                <SelectField
                                  label={_t("Cost Code")}
                                  labelPlacement="top"
                                  name="cost_code"
                                  options={
                                    costCodes?.map((costCode) => {
                                      let groupTitle = "";
                                      if (costCode.csi_code) {
                                        groupTitle += costCode.csi_code;
                                      }
                                      groupTitle += " " + costCode.csi_name;
                                      return {
                                        ...costCode,
                                        label: groupTitle,
                                        title: groupTitle,
                                        options: costCode.sub_code
                                          .filter(
                                            (subCode) =>
                                              subCode.is_deleted?.toString() !==
                                                "1" ||
                                              (subCode.is_deleted?.toString() ===
                                                "1" &&
                                                formik.values.cost_code?.toString() ===
                                                  subCode.code_id?.toString())
                                          )
                                          .map((subCode) => ({
                                            ...subCode,
                                            code_id:
                                              subCode?.code_id?.toString(),
                                            csi_name: subCode?.csi_name,
                                            label: subCodeOptionLabel(subCode),
                                            value: subCode.code_id?.toString(),
                                          }))
                                          .sort((a, b) => {
                                            if (
                                              cost_code_sort_order ===
                                              "by_number"
                                            ) {
                                              const [aMain, aSub] =
                                                parseCodeParts(a.csi_code);
                                              const [bMain, bSub] =
                                                parseCodeParts(b.csi_code);

                                              if (
                                                aMain &&
                                                bMain &&
                                                aMain !== bMain
                                              )
                                                return aMain - bMain;

                                              if (
                                                aSub === null &&
                                                bSub !== null
                                              )
                                                return -1;
                                              if (
                                                aSub !== null &&
                                                bSub === null
                                              )
                                                return 1;
                                              if (
                                                aSub !== null &&
                                                bSub !== null
                                              )
                                                return aSub - bSub;

                                              return 0;
                                            }

                                            return a.csi_name.localeCompare(
                                              b.csi_name
                                            );
                                          }),
                                      };
                                    }) || []
                                  }
                                  value={costCodeValue}
                                  onChange={(value = "") => {
                                    if (value?.toString()) {
                                      formik.setFieldValue(
                                        "cost_code",
                                        value?.toString() || ""
                                      );
                                    } else {
                                      formik.setValues((prev) => ({
                                        ...prev,
                                        cost_code: value?.toString() || "",
                                        cost_code_name: "",
                                      }));
                                    }
                                  }}
                                  showSearch
                                  filterOption={(input, option) =>
                                    filterOptionBySubstring(
                                      input,
                                      option?.label as string
                                    )
                                  }
                                  allowClear={true}
                                  readOnly={readOnly}
                                />
                              </div>
                              <div className="w-full overflow-hidden">
                                <SelectField
                                  mode="tags"
                                  label={_t("Variations")}
                                  maxTagPlaceholder={(omittedValues) => {
                                    const tooltipText = omittedValues
                                      .map((tag) => {
                                        const rawValue =
                                          typeof tag === "string"
                                            ? tag
                                            : typeof tag === "object" &&
                                              tag !== null
                                            ? tag.label || tag.value
                                            : "";

                                        const stringValue =
                                          typeof rawValue === "string"
                                            ? rawValue.trim()
                                            : "";

                                        return HTMLEntities.decode(
                                          sanitizeString(stringValue)
                                        );
                                      })
                                      .join(", ");

                                    return (
                                      <Tooltip title={tooltipText}>
                                        <span>{`${omittedValues.length} selected`}</span>
                                      </Tooltip>
                                    );
                                  }}
                                  labelPlacement="top"
                                  formInputClassName="overflow-visible"
                                  containerClassName="overflow-visible"
                                  className={`remove-down-arrow multi-selected-tags-padding ${
                                    readOnly ? "disabled-selectfield" : ""
                                  }`}
                                  name="itemVariations"
                                  open={false}
                                  maxTagValue="responsive"
                                  showSearch={false}
                                  allowClear={false}
                                  value={
                                    Array.isArray(formik.values.variations)
                                      ? formik.values.variations.map((tag) =>
                                          HTMLEntities.decode(
                                            sanitizeString(tag.trim()) || ""
                                          )
                                        )
                                      : []
                                  }
                                  onChange={(values) => {
                                    let tempValues =
                                      formik.values.variations || [];
                                    if (Array.isArray(values)) {
                                      tempValues = values.map((tag) =>
                                        tag.trim()
                                      );
                                    } else {
                                      tempValues = [values.trim()];
                                    }
                                    formik.setFieldValue(
                                      "variations",
                                      tempValues
                                    );
                                  }}
                                  onCloseTag={(data) => {
                                    const valArr = !!formik?.values?.variations
                                      ? formik?.values?.variations
                                      : [];
                                    const filterVal = valArr?.filter(
                                      (item) => item != data
                                    );
                                    formik.setFieldValue(
                                      "variations",
                                      filterVal
                                    );
                                  }}
                                  onKeyDown={(event) => {
                                    if (
                                      event.key === "Tab" &&
                                      !event.shiftKey &&
                                      !event.altKey &&
                                      !event.ctrlKey &&
                                      !event.metaKey
                                    ) {
                                      event.preventDefault();
                                      setUnitCostSelectShow(true);
                                    }
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                            </div>
                            <ItemUploadProductImage
                              disabled={readOnly}
                              value={formik.values.product_image}
                              module={module}
                              onChange={(value) => {
                                formik.setFieldValue("product_image", value);
                              }}
                              isUploadingProductImage={isUploadingProductImage}
                              setIsUploadingProductImage={
                                setIsUploadingProductImage
                              }
                            />
                          </div>
                        )}
                      </SidebarCardBorder>
                      {type !== "group" && (
                        <>
                          <SidebarCardBorder cardTitle={_t("Pricing")}>
                            <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                              <li className="flex justify-between items-center">
                                <Typography className="text-13 block text-primary-900 dark:text-white/90">
                                  {_t("Unit Cost") +
                                    `  (${formatter().currency_symbol})`}
                                  {/* Todo this https://app.clickup.com/t/86cz7686q */}
                                  {/* {type !== "equipment" && (
                                    <Typography className="inline-block ml-0.5 leading-4 text-primary-900">
                                      *
                                    </Typography>
                                  )} */}
                                </Typography>
                                <div className="sm:w-40 w-28">
                                  {unitCostSelectShow ? (
                                    <InputNumberField
                                      name="unit_cost"
                                      id="unit_cost"
                                      rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                                      placeholder={_t("Item Unit Cost")}
                                      isRequired={type !== "equipment"}
                                      formatter={(value) =>
                                        typeof value?.toString() === "string"
                                          ? inputFormatter(value.toString())
                                              .value
                                          : ""
                                      }
                                      value={formik.values.unit_cost?.toString()}
                                      onChange={(value) =>
                                        formik.setFieldValue(
                                          "unit_cost",
                                          value || ""
                                        )
                                      }
                                      // errorMessage={
                                      //   (submited && formType === "add") ||
                                      //   formType === "edit"
                                      //     ? formik.errors.unit_cost
                                      //     : ""
                                      // }
                                      onKeyDown={(event) => {
                                        onKeyDownCurrency(event, {
                                          integerDigits: 10,
                                          decimalDigits: 2,
                                          unformatted,
                                          allowNegative: false,
                                          decimalSeparator:
                                            inputFormatter().decimal_separator,
                                        });

                                        if (
                                          event.key === "Tab" &&
                                          !event.shiftKey &&
                                          !event.altKey &&
                                          !event.ctrlKey &&
                                          !event.metaKey
                                        ) {
                                          event.preventDefault();
                                          setUnitSelectShow(true);
                                        }
                                      }}
                                      onBlur={(e) => {
                                        setUnitCostSelectShow(false);
                                      }}
                                      disabled={readOnly}
                                      labelPlacement="left"
                                      formInputClassName={
                                        isViewOnly
                                          ? "flex items-center justify-end"
                                          : ""
                                      }
                                      autoFocus
                                    />
                                  ) : (
                                    <div
                                      className={`text-[#008000] ${
                                        readOnly
                                          ? "cursor-not-allowed"
                                          : "cursor-pointer"
                                      } text-13 font-semibold text-right leading-[22px]`}
                                      onClick={() => {
                                        if (!readOnly) {
                                          setUnitCostSelectShow(true);
                                        }
                                      }}
                                    >
                                      {formik.values.unit_cost ? (
                                        formatter(
                                          Number(
                                            formik?.values?.unit_cost
                                          )?.toFixed(2)
                                        )?.value_with_symbol
                                      ) : (
                                        <div className="text-[#bdbdbd]">
                                          {_t("Item Unit Cost")}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  {/* Todo this https://app.clickup.com/t/86cz7686q */}
                                  {/* {formik.errors.unit_cost && (
                                    <div className="pt-1 text-right">
                                      <Typography className="text-xs text-red-400">
                                        {(submited && formType === "add") ||
                                        formType === "edit"
                                          ? formik.errors.unit_cost
                                          : ""}
                                      </Typography>
                                    </div>
                                  )} */}
                                </div>
                              </li>
                              <li>
                                <ul className="py-0.5 relative">
                                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                                    <FontAwesomeIcon
                                      className="w-3 h-3 text-primary-900 dark:text-white"
                                      icon="fa-regular fa-xmark"
                                    />
                                  </li>
                                </ul>
                              </li>
                              <li className="flex justify-between items-center">
                                <Typography className="text-13 block text-primary-900 dark:text-white/90">
                                  {_t("Unit")}
                                  {/* Todo this https://app.clickup.com/t/86cz7686q */}
                                  {/* {type !== "equipment" && (
                                    <Typography className="inline-block ml-0.5 leading-4 text-primary-900">
                                      *
                                    </Typography>
                                  )} */}
                                </Typography>
                                <div className="sm:w-40 w-28">
                                  {unitSelectShow ? (
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      labelPlacement="left"
                                      maxLength={15}
                                      iconView={true}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitList.map((unit) => ({
                                          label: unit.name.toString(),
                                          value: unit.name.toString(),
                                        })) ?? []
                                      }
                                      value={formik.values.unit?.toString()}
                                      onChange={(value) => {
                                        formik.setFieldValue(
                                          "unit",
                                          value?.toString() || ""
                                        );
                                      }}
                                      onInputKeyDown={(e) => {
                                        const value =
                                          e.currentTarget.value?.trim();
                                        if (e.key === "Enter") {
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitList?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewUnitName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        } else if (
                                          value?.length > 14 &&
                                          e.key.length === 1
                                        ) {
                                          e.preventDefault();
                                        }
                                      }}
                                      readOnly={readOnly}
                                      isRequired={type !== "equipment"}
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onBlur={() => {
                                        setUnitSelectShow(false);
                                      }}
                                      autoFocus
                                      defaultOpen
                                      disabled={readOnly}
                                    />
                                  ) : (
                                    <div
                                      className={`text-[#008000] ${
                                        readOnly
                                          ? "cursor-not-allowed"
                                          : "cursor-pointer"
                                      } text-13 font-semibold text-right leading-[22px]`}
                                      onClick={() => {
                                        if (!readOnly) {
                                          setUnitSelectShow(true);
                                        }
                                      }}
                                    >
                                      {unitList.find(
                                        (unit) =>
                                          unit.name === formik.values.unit
                                      )?.name || (
                                        <div className="text-[#bdbdbd] font-normal">
                                          {_t("Unit")}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                  {/* Todo this https://app.clickup.com/t/86cz7686q */}
                                  {/* {formik.errors.unit && (
                                    <div className="pt-1 text-right">
                                      <Typography className="text-xs text-red-400">
                                        {(submited && formType === "add") ||
                                        formType === "edit"
                                          ? formik.errors.unit
                                          : ""}
                                      </Typography>
                                    </div>
                                  )} */}
                                </div>
                              </li>
                            </ul>
                            <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                              <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                                <FontAwesomeIcon
                                  className="w-3 h-3 text-primary-900 dark:text-white"
                                  icon="fa-regular fa-plus"
                                />
                              </li>
                            </ul>
                            <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                              <li className="flex justify-between items-center">
                                <Typography className="text-13 block text-primary-900 dark:text-white/90">
                                  {_t("Hidden Markup") + " (%)"}
                                </Typography>
                                <div className="sm:w-40 w-28">
                                  <InputNumberField
                                    name="hidden_markup"
                                    id="hidden_markup"
                                    rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                                    placeholder={_t("Hidden Markup")}
                                    labelPlacement="left"
                                    formInputClassName={
                                      isViewOnly
                                        ? "flex items-center justify-end"
                                        : ""
                                    }
                                    maxLength={3}
                                    formatter={(value) =>
                                      typeof value?.toString() === "string"
                                        ? inputFormatter(value.toString()).value
                                        : ""
                                    }
                                    value={formik.values.hidden_markup}
                                    onChange={(value) => {
                                      if (
                                        value === null ||
                                        value === undefined
                                      ) {
                                        formik.setFieldValue(
                                          "hidden_markup",
                                          null
                                        );
                                        return;
                                      }

                                      const strValue = value.toString();

                                      // Length limit
                                      if (strValue.length > 3) return;

                                      // Check digit length before decimal and without minus
                                      const cleaned = strValue
                                        .split(".")[0]
                                        .replace("-", "");
                                      if (cleaned.length > 10) return;

                                      // Max value constraint
                                      if (Number(value) > 999) return;

                                      formik.setFieldValue(
                                        "hidden_markup",
                                        value?.toString() ? value : null
                                      );
                                    }}
                                    disabled={readOnly}
                                    onKeyDown={(e) =>
                                      onKeyDownNumber(e, {
                                        decimalDigits: 0,
                                        integerDigits: 3,
                                        allowNegative: false,
                                      })
                                    }
                                  />
                                </div>
                              </li>
                            </ul>
                            <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                              <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                                <FontAwesomeIcon
                                  className="w-3 h-3 text-primary-900 dark:text-white"
                                  icon="fa-regular fa-equals"
                                />
                              </li>
                            </ul>
                            <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                              <li className="flex items-center justify-between">
                                <Typography className="text-13 block text-primary-900 font-semibold">
                                  {_t("Selling Cost") +
                                    ` (${formatter().currency_symbol})`}
                                </Typography>
                                <div className="sm:w-[260px] w-32 flex justify-end items-center">
                                  <Typography
                                    className="!text-red-600 text-13 font-semibold"
                                    disabled={isViewOnly}
                                  >
                                    {
                                      formatter(
                                        Number(sellingCost || 0)?.toFixed(2)
                                      ).value_with_symbol
                                    }
                                  </Typography>
                                </div>
                              </li>
                            </ul>
                            <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                              <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                                <FontAwesomeIcon
                                  className="w-3 h-3 text-primary-900 dark:text-white"
                                  icon="fa-regular fa-plus"
                                />
                              </li>
                            </ul>
                            <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                              <li className="flex justify-between items-center">
                                <Typography className="text-13 block text-primary-900 dark:text-white/90">
                                  {_t("Markup") + " (%)"}
                                </Typography>
                                <div className="sm:w-40 w-28">
                                  <InputNumberField
                                    name="markup"
                                    id="markup"
                                    rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                                    placeholder={_t("Markup")}
                                    labelPlacement="left"
                                    formInputClassName={
                                      isViewOnly
                                        ? "flex items-center justify-end"
                                        : ""
                                    }
                                    value={formik.values.markup}
                                    maxLength={3}
                                    formatter={(value) =>
                                      typeof value?.toString() === "string"
                                        ? inputFormatter(value.toString()).value
                                        : ""
                                    }
                                    onChange={(value) => {
                                      if (
                                        value === null ||
                                        value === undefined
                                      ) {
                                        formik.setFieldValue("markup", null);
                                        return;
                                      }

                                      const strValue = value.toString();

                                      // Length limit
                                      if (strValue.length > 3) return;

                                      // Check digit length before decimal and without minus
                                      const cleaned = strValue
                                        .split(".")[0]
                                        .replace("-", "");
                                      if (cleaned.length > 10) return;

                                      // Max value constraint
                                      if (Number(value) > 999) return;

                                      formik.setFieldValue(
                                        "markup",
                                        value?.toString() ? value : null
                                      );
                                    }}
                                    disabled={readOnly}
                                    onKeyDown={(e) =>
                                      onKeyDownNumber(e, {
                                        decimalDigits: 0,
                                        integerDigits: 3,
                                        allowNegative: false,
                                      })
                                    }
                                  />
                                </div>
                              </li>
                            </ul>
                            <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                              <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                                <FontAwesomeIcon
                                  className="w-3 h-3 text-primary-900 dark:text-white"
                                  icon="fa-regular fa-equals"
                                />
                              </li>
                            </ul>
                            <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                              <li className="flex items-center justify-between">
                                <Typography className="text-13 block text-primary-900 font-semibold">
                                  {_t("Total Cost") +
                                    ` (${formatter().currency_symbol})`}
                                </Typography>
                                <div className="sm:w-[260px] w-32 flex justify-end items-center">
                                  <Typography
                                    className="!text-red-600 text-13 font-semibold"
                                    disabled={isViewOnly}
                                  >
                                    {formatter(totalCost).value_with_symbol}
                                  </Typography>
                                </div>
                              </li>
                            </ul>
                          </SidebarCardBorder>
                          {type !== "sub_contractor" && (
                            <SidebarCardBorder addGap={true}>
                              <>
                                <div className="w-full">
                                  <ButtonField
                                    label={_t("Supplier")}
                                    name="supplier"
                                    labelPlacement="top"
                                    value={HTMLEntities.decode(
                                      sanitizeString(
                                        formik.values.supplier?.display_name ||
                                          ""
                                      )
                                    )}
                                    avatarProps={{
                                      user: {
                                        name: HTMLEntities.decode(
                                          sanitizeString(
                                            formik.values.supplier
                                              ?.display_name || ""
                                          )
                                        ),
                                        image: formik.values.supplier?.image,
                                      },
                                    }}
                                    onClick={() => {
                                      setIsSelectDirectoryType("supplier");
                                      setSelectVendorOptions([
                                        CFConfig.contractor_key,
                                        CFConfig.vendor_key,
                                        CFConfig.misc_contact_key,
                                        "by_service",
                                      ]);
                                    }}
                                    addonBefore={
                                      formik.values.supplier?.user_id ? (
                                        <div className="flex items-center gap-1">
                                          <ContactDetailsButton
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              setIsContactDetails("supplier");
                                            }}
                                          />
                                          <DirectoryFieldRedirectionIcon
                                            directoryId={formik.values.supplier.user_id.toString()}
                                            directoryTypeKey={
                                              formik.values.supplier
                                                ?.type_key || ""
                                            }
                                          />
                                        </div>
                                      ) : undefined
                                    }
                                    isDisabled={readOnly}
                                  />
                                </div>
                                <div className="w-full">
                                  <InputField
                                    label={_t("Product Link")}
                                    labelPlacement="top"
                                    name="supplier_link"
                                    addonAfterIcon={true}
                                    suffix={
                                      <>
                                        {formik.values.supplier_link &&
                                        !formik.errors.supplier_link ? (
                                          <FieldRedirectButton
                                            href={
                                              formik.values.supplier_link.match(
                                                /^(https?:\/\/)/i
                                              )
                                                ? formik.values.supplier_link
                                                : `https://${formik.values.supplier_link}`
                                            }
                                            tooltipTitle={_t(
                                              "Open URL in a new tab."
                                            )}
                                          />
                                        ) : undefined}
                                      </>
                                    }
                                    value={formik.values.supplier_link}
                                    onChange={formik.handleChange}
                                    errorMessage={formik.errors.supplier_link}
                                    onBlur={(e) => {
                                      formik.setFieldValue(
                                        "supplier_link",
                                        e.currentTarget.value?.trim()
                                      );
                                    }}
                                    disabled={readOnly}
                                    onPressEnter={handleEnterKeyPress}
                                  />
                                </div>
                                {type === "equipment" && (
                                  <div className="w-full">
                                    <TextAreaField
                                      label={_t("Warranty Details")}
                                      name="warranty_details"
                                      labelPlacement="top"
                                      value={formik.values.warranty_details}
                                      onChange={formik.handleChange}
                                      onBlur={(e) => {
                                        formik.setFieldValue(
                                          "warranty_details",
                                          e.currentTarget.value?.trim()
                                        );
                                      }}
                                      disabled={readOnly}
                                    />
                                  </div>
                                )}
                              </>
                            </SidebarCardBorder>
                          )}
                        </>
                      )}
                      {type === "sub_contractor" && (
                        <SidebarCardBorder addGap={true}>
                          <>
                            <div className="w-full">
                              <ButtonField
                                label={_t("Subcontractor")}
                                name="supplier"
                                labelPlacement="top"
                                value={HTMLEntities.decode(
                                  sanitizeString(
                                    formik.values.supplier?.display_name || ""
                                  )
                                )}
                                avatarProps={{
                                  user: {
                                    name: HTMLEntities.decode(
                                      sanitizeString(
                                        formik.values.supplier?.display_name ||
                                          ""
                                      )
                                    ),
                                    image: formik.values.supplier?.image,
                                  },
                                }}
                                onClick={() => {
                                  setIsSelectDirectoryType("supplier");
                                  setSelectVendorOptions([
                                    CFConfig.contractor_key,
                                    CFConfig.vendor_key,
                                    CFConfig.misc_contact_key,
                                    "by_service",
                                  ]);
                                }}
                                addonBefore={
                                  formik.values.supplier?.user_id ? (
                                    <div className="flex items-center gap-1">
                                      <ContactDetailsButton
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setIsContactDetails("supplier");
                                        }}
                                      />
                                      <DirectoryFieldRedirectionIcon
                                        directoryId={formik.values.supplier.user_id.toString()}
                                        directoryTypeKey={
                                          formik.values.supplier?.type_key || ""
                                        }
                                      />
                                    </div>
                                  ) : undefined
                                }
                                isDisabled={readOnly}
                              />
                            </div>
                            <div className="w-full">
                              <InputField
                                label={_t("Subcontractor Link")}
                                labelPlacement="top"
                                name="supplier_link"
                                addonAfterIcon={true}
                                suffix={
                                  <>
                                    {formik.values.supplier_link &&
                                    !formik.errors.supplier_link ? (
                                      <FieldRedirectButton
                                        href={
                                          formik.values.supplier_link.match(
                                            /^(https?:\/\/)/i
                                          )
                                            ? formik.values.supplier_link
                                            : `https://${formik.values.supplier_link}`
                                        }
                                        tooltipTitle={_t(
                                          "Open URL in a new tab."
                                        )}
                                      />
                                    ) : undefined}
                                  </>
                                }
                                value={formik.values.supplier_link}
                                onChange={formik.handleChange}
                                errorMessage={formik.errors.supplier_link}
                                onBlur={(e) => {
                                  formik.setFieldValue(
                                    "supplier_link",
                                    e.currentTarget.value?.trim()
                                  );
                                }}
                                disabled={readOnly}
                                onPressEnter={handleEnterKeyPress}
                              />
                            </div>
                          </>
                        </SidebarCardBorder>
                      )}
                      {type === "group" && (
                        <ItemGroupTable
                          formik={formik}
                          readOnly={readOnly}
                          itemCount={fetchingFormItemCount}
                        />
                      )}
                    </div>
                    {type === "equipment" && (
                      <div
                        id="equipment"
                        ref={(el) => (sectionRefs.current["equipment"] = el)}
                        className="grid gap-4"
                      >
                        <SidebarCardBorder
                          addGap={true}
                          cardTitle={_t("Equipment Info")}
                        >
                          <>
                            <div className="grid md:grid-cols-2 gap-5">
                              <div className="w-full">
                                <InputField
                                  label={_t("Plate") + " #"}
                                  labelPlacement="top"
                                  name="plate_number"
                                  type="plate_number"
                                  value={formik.values.plate_number}
                                  onChange={formik.handleChange}
                                  onBlur={(e) => {
                                    formik.setFieldValue(
                                      "plate_number",
                                      e.currentTarget.value?.trim()
                                    );
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                              <div className="w-full">
                                <DatePickerField
                                  label={_t("Registration Expiration")}
                                  labelPlacement="top"
                                  placeholder=""
                                  name="registration_expire_on"
                                  format={CFConfig.day_js_date_format}
                                  value={
                                    formik.values.registration_expire_on
                                      ? dayjs(
                                          formik.values.registration_expire_on,
                                          CFConfig.day_js_date_format
                                        )
                                      : undefined
                                  }
                                  onChange={(value, dateString) => {
                                    formik.setFieldValue(
                                      "registration_expire_on",
                                      dateString
                                    );
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                            </div>
                            <div className="grid md:grid-cols-2 gap-5">
                              <div className="w-full">
                                <InputField
                                  label={_t("Insurance Carrier")}
                                  labelPlacement="top"
                                  name="insurance_carrier"
                                  type="insurance_carrier"
                                  value={formik.values.insurance_carrier}
                                  onChange={formik.handleChange}
                                  onBlur={(e) => {
                                    formik.setFieldValue(
                                      "insurance_carrier",
                                      e.currentTarget.value?.trim()
                                    );
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                              <div className="w-full">
                                <DatePickerField
                                  label={_t("Policy Expiration")}
                                  labelPlacement="top"
                                  name="policy_expire_on"
                                  format={CFConfig.day_js_date_format}
                                  placeholder=""
                                  value={
                                    formik.values.policy_expire_on
                                      ? dayjs(
                                          formik.values.policy_expire_on,
                                          CFConfig.day_js_date_format
                                        )
                                      : undefined
                                  }
                                  onChange={(value, dateString) => {
                                    formik.setFieldValue(
                                      "policy_expire_on",
                                      dateString
                                    );
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                            </div>
                            <div className="grid md:grid-cols-2 gap-5">
                              <div className="w-full">
                                <InputField
                                  label={_t("Policy") + " #"}
                                  labelPlacement="top"
                                  name="policy_number"
                                  type="policy_number"
                                  value={formik.values.policy_number}
                                  onChange={formik.handleChange}
                                  onBlur={(e) => {
                                    formik.setFieldValue(
                                      "policy_number",
                                      e.currentTarget.value?.trim()
                                    );
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                              <div className="w-full">
                                <DatePickerField
                                  label={_t("Purchase Date")}
                                  labelPlacement="top"
                                  name="purchase_date"
                                  format={CFConfig.day_js_date_format}
                                  placeholder=""
                                  value={
                                    formik.values.purchase_date
                                      ? dayjs(
                                          formik.values.purchase_date,
                                          CFConfig.day_js_date_format
                                        )
                                      : undefined
                                  }
                                  onChange={(value, dateString) => {
                                    formik.setFieldValue(
                                      "purchase_date",
                                      dateString
                                    );
                                  }}
                                  disabled={readOnly}
                                />
                              </div>
                            </div>
                          </>
                        </SidebarCardBorder>
                        <SidebarCardBorder addGap={true}>
                          <>
                            <div className="grid md:grid-cols-3 gap-5">
                              <div className="w-full">
                                <DatePickerField
                                  label={_t("Last Meter Reading Date")}
                                  labelPlacement="top"
                                  name="last_meter_reading_date"
                                  showTime
                                  format={`${CFConfig.day_js_date_format}  ${
                                    formik.values.last_meter_reading
                                      ? `(${formik.values.last_meter_reading})`
                                      : ""
                                  }`}
                                  placeholder=""
                                  value={
                                    formik.values.last_meter_reading_date
                                      ? dayjs(
                                          formik.values.last_meter_reading_date,
                                          CFConfig.day_js_date_format
                                        )
                                      : undefined
                                  }
                                  onChange={(value, dateString) => {
                                    formik.setFieldValue(
                                      "last_meter_reading_date",
                                      dateString
                                    );
                                  }}
                                  disabled={true}
                                />
                              </div>
                              <div className="w-full">
                                <DatePickerField
                                  label={_t("Last Oil Change Date")}
                                  labelPlacement="top"
                                  name="oil_changed_date"
                                  format={`${CFConfig.day_js_date_format}  ${
                                    formik.values.oil_changed_time
                                      ? `(${formik.values.oil_changed_time})`
                                      : ""
                                  }`}
                                  placeholder=""
                                  showTime
                                  value={
                                    formik.values.oil_changed_date
                                      ? dayjs(
                                          formik.values.oil_changed_date,
                                          CFConfig.day_js_date_format
                                        )
                                      : undefined
                                  }
                                  onChange={(value, dateString) => {
                                    formik.setFieldValue(
                                      "last_oil_change_date",
                                      dateString
                                    );
                                  }}
                                  disabled={true}
                                />
                              </div>
                              <div className="w-full">
                                <DatePickerField
                                  label={_t("Last Tire Rotation Date")}
                                  labelPlacement="top"
                                  name="last_tire_rotation_date"
                                  format={`${CFConfig.day_js_date_format} ${
                                    formik.values.last_tire_rotation_time
                                      ? `(${formik.values.last_tire_rotation_time})`
                                      : ""
                                  }`}
                                  placeholder=""
                                  showTime
                                  value={
                                    formik.values.last_tire_rotation_date
                                      ? dayjs(
                                          formik.values.last_tire_rotation_date,
                                          CFConfig.day_js_date_format
                                        )
                                      : undefined
                                  }
                                  onChange={(value, dateString) => {
                                    formik.setFieldValue(
                                      "last_tire_rotation_date",
                                      dateString
                                    );
                                  }}
                                  disabled={true}
                                />
                              </div>
                            </div>
                            <div className="w-full">
                              <ButtonField
                                label={_t("Default Operator")}
                                name="operator"
                                labelPlacement="top"
                                value={HTMLEntities.decode(
                                  sanitizeString(
                                    formik.values.operator?.display_name || ""
                                  )
                                )}
                                avatarProps={{
                                  user: {
                                    name: HTMLEntities.decode(
                                      sanitizeString(
                                        formik.values.operator?.display_name ||
                                          ""
                                      )
                                    ),
                                    image: formik.values.operator?.image,
                                  },
                                }}
                                onClick={() => {
                                  setIsSelectDirectoryType("operator");
                                  setSelectOperatorOptions([
                                    CFConfig.employee_key,
                                    "my_crew",
                                  ]);
                                }}
                                addonBefore={
                                  formik.values.operator?.user_id ? (
                                    <div className="flex items-center gap-1">
                                      <ContactDetailsButton
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setIsContactDetails("operator");
                                        }}
                                      />
                                      <DirectoryFieldRedirectionIcon
                                        directoryId={formik.values.operator.user_id.toString()}
                                        directoryTypeKey={
                                          formik.values.operator?.type_key || ""
                                        }
                                      />
                                    </div>
                                  ) : undefined
                                }
                                isDisabled={readOnly}
                              />
                            </div>
                          </>
                        </SidebarCardBorder>
                      </div>
                    )}
                    <div
                      id="notes"
                      ref={(el) => (sectionRefs.current["notes"] = el)}
                    >
                      <SidebarCardBorder addGap={true} cardTitle={_t("Notes")}>
                        {type !== "group" && (
                          <div className="w-full">
                            <TextAreaField
                              label={_t("Description")}
                              name="notes"
                              labelPlacement="top"
                              value={formik.values.notes}
                              onChange={formik.handleChange}
                              disabled={readOnly}
                              placeholder={_t(
                                "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                              )}
                              onBlur={(e) => {
                                formik.setFieldValue(
                                  "notes",
                                  e.currentTarget.value?.trim()
                                );
                              }}
                            />
                          </div>
                        )}
                        <div className="w-full">
                          <TextAreaField
                            label={_t("Internal Note")}
                            name="internal_notes"
                            labelPlacement="top"
                            value={formik.values.internal_notes}
                            onChange={formik.handleChange}
                            disabled={readOnly}
                            placeholder={_t(
                              "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                            )}
                            onBlur={(e) => {
                              formik.setFieldValue(
                                "internal_notes",
                                e.currentTarget.value?.trim()
                              );
                            }}
                          />
                        </div>
                      </SidebarCardBorder>
                    </div>
                    <div
                      id="files"
                      ref={(el) => (sectionRefs.current["files"] = el)}
                    >
                      <SidebarCardBorder cardTitle={_t("Files")}>
                        {readOnly &&
                        (!formik.values.photos ||
                          formik.values.photos.length === 0) ? (
                          <NoRecords
                            image={`${window.ENV.CDN_URL}assets/images/no-records-files.svg`}
                          />
                        ) : (
                          <AttachmentCard
                            isAddAllow={!readOnly}
                            editView={formType == "add" ? false : true}
                            isReadOnly={readOnly}
                            files={formik.values.photos}
                            onAddAttachment={(data) => {
                              setAttachedFile(data);
                              formik.setValues((prev) => {
                                const newFiles =
                                  data as IAddFileRequestBodyChild[];
                                const existingFiles = prev.photos.filter(
                                  (file) =>
                                    !newFiles.some(
                                      (nf) => nf.image_id === file.image_id
                                    )
                                );

                                return {
                                  ...prev,
                                  photos: [...newFiles, ...existingFiles].map(
                                    (file) => ({ ...file })
                                  ),
                                };
                              });
                              onRemoveFile();
                            }}
                            setUpdatedData={(
                              data: Partial<IgetUpdatedFileRes>
                            ) => {
                              const newFile = data?.data;
                              if (!newFile) return;
                              formik.setValues((prev) => {
                                const fileExists = prev.photos.some(
                                  (file) => file.image_id === newFile?.image_id
                                );
                                if (fileExists) {
                                  return {
                                    ...prev,
                                    photos: prev.photos.map((item) =>
                                      newFile?.original_url ===
                                      item.original_url
                                        ? {
                                            ...item,
                                            file_path:
                                              newFile?.file_path ||
                                              item.file_path,
                                            file_url:
                                              newFile?.file_path ||
                                              item.file_path,
                                            thumb_file_path:
                                              newFile?.thumb_file_path ||
                                              item.thumb_file_path,
                                          }
                                        : item
                                    ),
                                  };
                                } else {
                                  return {
                                    ...prev,
                                    photos: [
                                      {
                                        ...newFile,
                                        file_path: newFile?.file_path,
                                        file_url: newFile?.file_path,
                                        thumb_file_path:
                                          newFile?.thumb_file_path,
                                      },
                                      ...prev.photos,
                                    ],
                                  };
                                }
                              });
                              onRemoveFile();
                            }}
                            isShowDeleteMenu={true}
                            onDeleteFile={(data: Partial<IFile>) => {
                              const key = Object.keys(data)[0] as keyof IFile;
                              const value = data[key] as string;
                              formik.setValues((prev) => ({
                                ...prev,
                                photos: prev.photos.filter(
                                  (file) =>
                                    !(key in file && file[key] === value)
                                ),
                              }));
                              onRemoveFile();
                            }}
                            validationParams={{
                              date_format: CFConfig.day_js_date_format,
                              file_support_module_access:
                                checkGlobalModulePermissionByKey(
                                  CFConfig.file_support_key
                                ),
                              image_resolution,
                              module_key: module?.module_key || "",
                              module_id: module?.module_id || 0,
                              module_access: getModuleAccess(module),
                            }}
                            addFilesRes={attachedFile}
                            setIsFileDeleted={setIsFileDeleted}
                            setSelectedFilesData={(values) => {
                              formik.setValues((prev) => ({
                                ...prev,
                                photos: values,
                              }));
                              onRemoveFile();
                            }}
                          />
                        )}
                      </SidebarCardBorder>
                    </div>
                    {onBuildId && (
                      <Typography className="text-[#4B4B4B] text-md font-medium block mt-1">
                        {_t("Imported from 1build.com")} (
                        <Tooltip
                          title={
                            !readOnly ? "Click here to Update Cost" : undefined
                          }
                        >
                          <span
                            className={`hover:underline ${
                              readOnly ? "cursor-not-allowed" : "cursor-pointer"
                            } text-primary-900`}
                            onClick={(e) => {
                              if (readOnly) return;
                              handleUpdateClick?.();
                              setTimeout(() => {
                                document
                                  .getElementById("details")
                                  ?.scrollIntoView();
                              }, 150);
                            }}
                          >
                            {_t("Update")}
                          </span>
                        </Tooltip>
                        )
                      </Typography>
                    )}
                  </div>
                </div>
                {!isViewOnly && (
                  <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
                    <PrimaryButton
                      htmlType="submit"
                      onClick={() => setSubmited(true)}
                      buttonText={_t(buttonTest)}
                      disabled={loading || readOnly || isUploadingProductImage}
                      isLoading={loading}
                    />
                  </div>
                )}
              </Form>
            </div>
          </div>
        </div>
      </Drawer>
      {isSelectDirectoryType && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setIsSelectDirectoryType("");
          }}
          singleSelecte={true}
          options={
            isSelectDirectoryType === "supplier"
              ? selectVendorOptions
              : selectOperatorOptions
          }
          activeTab={
            isSelectDirectoryType === "supplier"
              ? CFConfig.vendor_key
              : CFConfig.employee_key
          }
          openSelectCustomerSidebar={Boolean(isSelectDirectoryType)}
          setCustomer={(data) => {
            if (data.length) {
              if (isSelectDirectoryType === "supplier") {
                formik.setFieldValue("supplier", data[0]);
              } else if (isSelectDirectoryType === "operator") {
                formik.setFieldValue("operator", data[0]);
              }
            } else {
              if (isSelectDirectoryType === "supplier") {
                formik.setFieldValue("supplier", undefined);
              } else if (isSelectDirectoryType === "operator") {
                formik.setFieldValue("operator", undefined);
              }
            }
          }}
          selectedCustomer={
            isSelectDirectoryType === "supplier"
              ? formik.values.supplier
                ? [formik.values.supplier as TselectedContactSendMail]
                : []
              : formik.values.operator
              ? [formik.values.operator as TselectedContactSendMail]
              : []
          }
          groupCheckBox={isSelectDirectoryType ? false : true}
          additionalContactDetails={0}
        />
      )}
      {isContactDetails ? (
        <ContactDetailsModal
          isOpenContact={!!isContactDetails}
          onCloseModal={() => setIsContactDetails(false)}
          contactId={
            isContactDetails === "supplier"
              ? formik.values.supplier?.user_id
              : formik.values.operator?.user_id
          }
          additional_contact_id={0}
        />
      ) : (
        ""
      )}

      {newUnitName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Unit To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewUnitName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = await addUnit({
                name: newUnitName,
              });
              if (response.success) {
                setUnitList([{ name: newUnitName }, ...unitList]);
                formik?.setFieldValue("unit", newUnitName);
                setNewUnitName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewUnitName("")}
        />
      )}
    </>
  );
};

export default AddItems;
