import { useMemo } from "react";
import {
  defaultModuleFilter,
  getGConfig,
  getGModuleDashboard,
  getGModuleFilters,
  setIsFilterUpdated,
} from "~/zustand";
import ModuleDashboardFilter from "~/components/page/common/page-dashboard-header/filter";
import { STATUS_CODE } from "~/shared/constants";
import { defaultConfig } from "~/data";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { useTranslation } from "~/hook";
import { PROJECT_MODULE_STATUS_LIST_FOR_FILTER } from "../../utils/constants";
import { Number, sanitizeString } from "~/helpers/helper";
import { useAppProSelector } from "../../redux/store";
import dayjs from "dayjs";

const ProjectFilter = ({
  kanbanView,
  beforeFilterUpdateCallback,
}: IProjectFilterProps) => {
  const { _t } = useTranslation();
  const { module_name, module_id }: GConfig = getGConfig();
  const { clearSearch }: IModuleDataSearchParams = useModuleDataSearch(
    defaultConfig.project_module
  );
  const { projectTypes } = useAppProSelector((state) => state.proDashboard);
  const filter = getGModuleFilters() as Partial<ProjectFilter> | undefined;
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const projectStatus = useMemo(() => {
    return gModuleDashboard.module_setting?.module_status?.filter(
      (item) => item.is_status === "1"
    );
  }, [gModuleDashboard.module_setting?.module_status]);

  const filterItems: FilterItem<Partial<ProjectFilter>>[] = useMemo(() => {
    return [
      {
        type: "datepicker",
        label: "Date Range",
        valueSetter: ({
          start_date = "",
          end_date = "",
        }: Partial<ProjectFilter>) => {
          const startDate = start_date
            ? dayjs(start_date)?.format("YYYY-MM-DD")
            : "";
          const endDate = end_date ? dayjs(end_date)?.format("YYYY-MM-DD") : "";

          return [startDate, endDate];
        },
        valueGetter: (data: [string, string] | undefined) => {
          let filter: Partial<ProjectFilter> = {};

          if (data) {
            filter = {
              start_date: data[0],
              end_date: data[1],
            };
          }
          return {
            start_date: filter.start_date || "",
            end_date: filter.end_date || "",
          };
        },
        name: "date_range",
        id: "date_range",
      },
      {
        type: "select",
        label: _t("Project Type"),
        options: projectTypes
          .filter((item) => Number(item.is_status) === 0)
          ?.map((item) => {
            return {
              label: HTMLEntities.decode(sanitizeString(item.name)),
              value: item.key,
            };
          }),
        valueSetter: ({ project_type = "" }: Partial<ProjectFilter>) => {
          return (
            project_type.split(",").filter((ele: string) => ele !== "") ?? ""
          );
        },
        valueGetter: (
          _: string | string[] | undefined,
          options: DefaultOptionType | DefaultOptionType[] | undefined
        ) => {
          if (Array.isArray(options)) {
            return {
              project_type:
                options
                  .map((option: DefaultOptionType) => option?.value)
                  .join(",") ?? "",
              project_type_names:
                options
                  .map((option: DefaultOptionType) => option?.label)
                  .join(", ") ?? "",
            };
          }
          return {
            project_type: "",
            project_type_names: "",
          };
        },
        name: "project_type",
        multiple: true,
      },
      {
        type: "customer",
        label: "Customer",
        valueSetter: ({
          customer = "",
          customer_contact_id = "",
        }: Partial<ProjectFilter>) => {
          const cus = customer
            .toString()
            .split(",")
            .filter((user_id) => user_id !== "0" && user_id !== "")
            .map((user_id) => ({ user_id: Number(user_id) }));
          const c_id =
            customer.length > 0
              ? customer_contact_id
                  .toString()
                  .split(",")
                  .filter((contact_id) => contact_id != "0" && contact_id != "")
                  .map((contact_id) => ({ contact_id: Number(contact_id) }))
              : [];
          if (cus?.length > 0 || c_id?.length > 0) {
            return { customer: cus, contact_id: c_id };
          } else {
            return "";
          }
        },
        valueGetter: (
          customer:
            | Partial<CustomerSelectedData>
            | Array<Partial<CustomerSelectedData>>
            | undefined
        ) => {
          if (customer) {
            return Array.isArray(customer)
              ? {
                  customer:
                    customer
                      .map(
                        (item: Partial<CustomerSelectedData>) => item.user_id
                      )
                      .join(",") ?? "",
                  customer_contact_id:
                    customer
                      .map(
                        (item: Partial<CustomerSelectedData>) =>
                          item?.contact_id
                      )
                      .join(",") ?? "",
                  customer_names:
                    customer.length > 2
                      ? `${customer.length} Selected`
                      : customer
                          .map(
                            (item: Partial<CustomerSelectedData>) =>
                              item.display_name
                          )
                          .join(","),
                }
              : {
                  customer: customer.user_id?.toString() || "",
                  customer_contact_id: customer.contact_id?.toString() || "",
                  customer_names: customer.display_name || "",
                };
          }
          return {
            customer: "",
            customer_names: "",
          };
        },
        name: "customer",
        showNewDirectoryAddButton: false,
        showDirectoryContact: true,
        additionalContactDetails: true,
        options: [defaultConfig.customer_key],
      },
      {
        type: "select",
        label: _t("Record Status"),
        defaultSelectedValue: STATUS_CODE.ACTIVE,
        options: PROJECT_MODULE_STATUS_LIST_FOR_FILTER,
        valueSetter: ({ status = "" }: Partial<ProjectFilter>) =>
          status === "" ? "2" : status ?? STATUS_CODE.ACTIVE,
        valueGetter: (data: string | string[] | undefined) => {
          if (!Array.isArray(data)) {
            return {
              status: data === "2" ? "" : data,
            };
          }
          return {
            status: STATUS_CODE.ACTIVE,
          };
        },
        name: "status",
        multiple: false,
      },
      {
        type: "select",
        hide: kanbanView === false ? false : true,
        label: _t("Project Status"),
        options: projectStatus
          ? projectStatus?.map((item) => {
              if (filter?.tab === "open") {
                return {
                  label: sanitizeString(item.name),
                  value: item?.key,
                  disabled: item.key === "completed" ? true : false,
                };
              } else if (filter?.tab === "completed") {
                return {
                  label: sanitizeString(item.name),
                  value: item?.key,
                  disabled: item.key === "completed" ? false : true,
                };
              } else {
                return {
                  label: sanitizeString(item.name),
                  value: item?.key,
                  disabled: false,
                };
              }
            })
          : [],
        valueSetter: ({ project_status = "" }: Partial<ProjectFilter>) => {
          return (
            project_status.split(",").filter((ele: string) => ele !== "") ?? ""
          );
        },
        valueGetter: (
          _: string | string[] | undefined,
          options: DefaultOptionType | DefaultOptionType[] | undefined
        ) => {
          if (Array.isArray(options)) {
            return {
              project_status:
                options
                  .map((option: DefaultOptionType) => option?.value)
                  .join(",") ?? "",
              project_status_names:
                options
                  .map((option: DefaultOptionType) => option?.label)
                  .join(", ") ?? "",
            };
          }
          return {
            project_status: "",
            project_status_names: "",
          };
        },
        name: "project_status",
        multiple: true,
        showSearch: false,
      },
      {
        type: "select",
        hide: kanbanView === false ? true : false,
        label: _t("Project Status"),
        options: projectStatus
          ? projectStatus?.map((item) => {
              return {
                label: sanitizeString(item.name),
                value: item?.key,
              };
            })
          : [],
        valueSetter: ({
          project_status_kanban = "",
        }: Partial<ProjectFilter>) => {
          return (
            project_status_kanban
              .split(",")
              .filter((ele: string) => ele !== "") ?? ""
          );
        },
        valueGetter: (
          _: string | string[] | undefined,
          options: DefaultOptionType | DefaultOptionType[] | undefined
        ) => {
          if (Array.isArray(options)) {
            return {
              project_status_kanban:
                options
                  .map((option: DefaultOptionType) => option?.value)
                  .join(",") ?? "",
              project_status_kanban_names:
                options
                  .map((option: DefaultOptionType) => option?.label)
                  .join(", ") ?? "",
            };
          }
          return {
            project_status_kanban: "",
            project_status_kanban_names: "",
          };
        },
        name: "project_status_kanban",
        multiple: true,
        showSearch: false,
      },
      {
        type: "customer",
        label: "Project Contact",
        valueSetter: ({ project_contacts = "" }: Partial<ProjectFilter>) => {
          return project_contacts
            .toString()
            .split(",")
            .filter((user_id) => user_id !== "0" && user_id !== "")
            .map((user_id) => ({ user_id: Number(user_id) }));
        },
        valueGetter: (
          data:
            | Partial<CustomerSelectedData>
            | Array<Partial<CustomerSelectedData>>
            | undefined
        ) => {
          if (data) {
            return Array.isArray(data)
              ? {
                  project_contacts:
                    data
                      .map(
                        (item: Partial<CustomerSelectedData>) => item.user_id
                      )
                      .join(",") ?? "",
                  project_contacts_names:
                    data.length > 2
                      ? `${data.length} Selected`
                      : data
                          .map(
                            (item: Partial<CustomerSelectedData>) =>
                              item.display_name
                          )
                          .join(","),
                }
              : {
                  project_contacts: data.user_id?.toString() ?? "",
                  project_contacts_names: data.display_name ?? "",
                };
          }
          return {
            project_contacts: "",
            project_contacts_names: "",
          };
        },
        name: "project_contacts",
        showNewDirectoryAddButton: false,
        options: [
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.misc_contact_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          "by_service",
        ],
        defaultSelect: defaultConfig.employee_key,
      },
      {
        type: "customer",
        label: "Sales Rep.",
        valueSetter: ({ sales_rep = "" }: Partial<ProjectFilter>) => {
          return sales_rep
            .toString()
            .split(",")
            .filter((user_id) => user_id !== "0" && user_id !== "")
            .map((user_id) => ({ user_id: Number(user_id) }));
        },
        valueGetter: (
          data:
            | Partial<CustomerSelectedData>
            | Array<Partial<CustomerSelectedData>>
            | undefined
        ) => {
          if (data) {
            return Array.isArray(data)
              ? {
                  sales_rep:
                    data
                      .map(
                        (item: Partial<CustomerSelectedData>) => item.user_id
                      )
                      .join(",") ?? "",
                  sales_rep_names:
                    data.length > 2
                      ? `${data.length} Selected`
                      : data
                          .map(
                            (item: Partial<CustomerSelectedData>) =>
                              item.display_name
                          )
                          .join(","),
                }
              : {
                  sales_rep: data.user_id?.toString() ?? "",
                  sales_rep_names: data.display_name ?? "",
                };
          }
          return {
            sales_rep: "",
            sales_rep_names: "",
          };
        },
        name: "sales_rep",
        showNewDirectoryAddButton: false,
        options: [defaultConfig.employee_key, "my_crew"],
      },
      {
        type: "customer",
        label: "Project Manager",
        valueSetter: ({ project_manager = "" }: Partial<ProjectFilter>) => {
          return project_manager
            .toString()
            .split(",")
            .filter((user_id) => user_id !== "0" && user_id !== "")
            .map((user_id) => ({ user_id: Number(user_id) }));
        },
        valueGetter: (
          data:
            | Partial<CustomerSelectedData>
            | Array<Partial<CustomerSelectedData>>
            | undefined
        ) => {
          if (data) {
            return Array.isArray(data)
              ? {
                  project_manager:
                    data
                      .map(
                        (item: Partial<CustomerSelectedData>) => item.user_id
                      )
                      .join(",") ?? "",
                  project_manager_names:
                    data.length > 2
                      ? `${data.length} Selected`
                      : data
                          .map(
                            (item: Partial<CustomerSelectedData>) =>
                              item.display_name
                          )
                          .join(","),
                }
              : {
                  project_manager: data.user_id?.toString() || "",
                  project_manager_names: data.display_name || "",
                };
          }
          return {
            project_manager: "",
            project_manager_names: "",
          };
        },
        name: "project_manager",
        showNewDirectoryAddButton: false,
        options: [defaultConfig.employee_key, defaultConfig.contractor_key],
        app_access: true,
      },
    ];
  }, [projectTypes, projectStatus, filter?.tab, kanbanView]);

  const filterCallComplete = () => {
    setIsFilterUpdated(true);
  };

  const filterComponent = useMemo(() => {
    return (
      <ModuleDashboardFilter<Partial<ProjectFilter>>
        filterItems={filterItems}
        popoverParams={{
          itemLabelClassName: "min-w-[120px]",
        }}
        title={module_name}
        moduleId={module_id}
        filter={filter}
        defaultValue={defaultModuleFilter.project_module}
        onResetClick={() => clearSearch()}
        filterCallComplete={filterCallComplete}
        beforeFilterUpdateCallback={beforeFilterUpdateCallback}
      />
    );
  }, [filterItems, defaultModuleFilter.directories, filter]);

  return filterComponent;
};

export default ProjectFilter;
