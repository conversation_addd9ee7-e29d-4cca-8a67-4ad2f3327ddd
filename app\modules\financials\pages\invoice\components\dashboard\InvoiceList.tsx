// React + ag-grid
import { useEffect, useMemo, useRef } from "react";
import { useNavigate } from "@remix-run/react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import {
  CellClickedEvent,
  type GridReadyEvent,
  type SortChangedEvent,
} from "ag-grid-community";
// Hook
import { useTranslation } from "~/hook";
import useTableGridData from "~/shared/hooks/useTableGridData";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import InvoiceListAction from "../InvoiceListAction";
// Constants, Shared & Common
import {
  escapeHtmlEntities,
  formatAmount,
  getDefaultStatuscolor,
  sanitizeString,
} from "~/helpers/helper";
import { STATUS_CODE } from "~/shared/constants";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
// Redux
import { routes } from "~/route-services/routes";
import {
  fetchDashData,
  getIVListApi,
} from "~/modules/financials/pages/invoice/redux/action/dashboardAction";
import { getGConfig, getGModuleFilters } from "~/zustand";
import { useAppIVDispatch } from "~/modules/financials/pages/invoice/redux/store";
import { GRID_BUTTON_TAB } from "../../utils/constants";

let timeout: NodeJS.Timeout;
const InvoiceList = ({ setIsAddInvoiceOpen, search }: IInvoiceListProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { datasource, gridRowParams } = useTableGridData();
  const navigate = useNavigate();
  const dispatch = useAppIVDispatch();
  const { module_access }: GConfig = getGConfig();
  const filterSrv = getGModuleFilters() as
    | Partial<IInvoiceModuleFilter>
    | undefined;
  const limit = 30;

  const filter = useMemo(() => {
    return {
      project: filterSrv?.project || "",
      customer: filterSrv?.customer || "",
      status: filterSrv?.status || "",
      billing_status: filterSrv?.billing_status || "",
      billing_status_kanban: filterSrv?.billing_status_kanban || "",
      start_date: filterSrv?.start_date,
      end_date: filterSrv?.end_date,
      tab: filterSrv?.tab || "all",
      key: filterSrv?.key,
      value: filterSrv?.value,
    };
  }, [JSON.stringify(filterSrv)]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };
  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };
  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const fetchInvoiceList = async () => {
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const { start, order_by_name, order_by_dir } = changeGridParams || {};
    const filterObj: Partial<IInvoiceModuleFilter> | undefined = !isEmpty(
      filter
    )
      ? getValuableObj(filter)
      : undefined;
    const length = changeGridParams?.length ?? limit;
    if (filterObj?.status === STATUS_CODE.ALL) {
      delete filterObj.status;
    }
    delete filterObj?.billing_status_kanban;
    delete filterObj?.billing_status_kanban_names;
    const startPagination = start ? Math.floor(start / length) : 0;

    if (!filterObj?.billing_status) {
      if (filterObj?.tab === "paid") {
        filterObj.billing_status = GRID_BUTTON_TAB.find(
          (item) => item.value === "paid"
        )?.billing_status;
      } else if (filterObj?.tab === "unpaid") {
        filterObj.billing_status = GRID_BUTTON_TAB.find(
          (item) => item.value === "unpaid"
        )?.billing_status;
      }
    }

    let dataParams: IInvoiceListParmas = {
      length: length,
      page: startPagination,
      ignore_filter: 1, // without data-base
      search: !!search ? escapeHtmlEntities(search || "") : undefined,
      filter: !isEmpty(filterObj) ? filterObj : undefined,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
    };

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_name;
    }

    try {
      gridParams?.api.hideOverlay();
      const resData = (await getIVListApi(dataParams)) as IInvoiceApiRes;
      const invoiceArr = resData?.data || [];

      // Check if we got less data than requested - indicates last page
      const isLastPage = invoiceArr.length < length;

      // Calculate total based on current page and data length
      const currentTotal = isLastPage
        ? (start ? start : 0) + invoiceArr.length
        : (start ? start : 0) + length + 1; // +1 indicates there might be more

      // Send response to grid
      gridParams?.success({
        rowData: invoiceArr,
        rowCount: currentTotal,
      });

      // Handle no records case
      if (
        (!resData?.success || invoiceArr.length === 0) &&
        startPagination === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  // use ref
  const previousValues = useRef({
    filter: JSON.stringify(filter),
    search,
  });

  // use effects
  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        if (!isEmpty(filterSrv)) {
          fetchInvoiceList();
        }
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search,
    };

    if (
      !isEqual(previousValues.current, currentValues) &&
      !isEmpty(filterSrv)
    ) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search, JSON.stringify(filter)]);

  const columnDefs = [
    {
      headerName: _t("Invoice") + " #",
      field: "prefix_company_invoice_id",
      minWidth: 130,
      maxWidth: 200,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      valueGetter: ({ data }: IInvoiceTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.prefix_company_invoice_id)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      valueGetter: ({ data }: IInvoiceTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.project_name)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Customer"),
      field: "customer_name",
      minWidth: 130,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      valueGetter: ({ data }: IInvoiceTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.customer_name)),
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Invoice Date"),
      field: "invoice_date",
      maxWidth: 135,
      minWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IInvoiceTableCellRenderer) => {
        const { invoice_date } = data;
        return invoice_date ? (
          <DateTimeCard format="date" date={invoice_date} />
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Due Date"),
      field: "due_date",
      maxWidth: 135,
      minWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IInvoiceTableCellRenderer) => {
        const { due_date } = data;
        return due_date ? (
          <DateTimeCard format="date" date={due_date} />
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 130,
      maxWidth: 130,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IInvoiceTableCellRenderer) => {
        const nValue = formatter(
          formatAmount((Number(data?.total) / 100).toFixed(2))
        ).value_with_symbol;

        return nValue ? (
          <div className="text-right">
            <Tooltip title={nValue}>
              <Typography className="table-tooltip-text">{nValue}</Typography>
            </Tooltip>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Balance"),
      field: "due_balance",
      minWidth: 130,
      maxWidth: 130,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IInvoiceTableCellRenderer) => {
        const nValue =
          Number(data?.due_balance) > 0
            ? formatter(
                formatAmount((Number(data?.due_balance) / 100).toFixed(2))
              ).value_with_symbol
            : formatter(formatAmount((0 / 100).toFixed(2))).value_with_symbol;

        return nValue ? (
          <div className="text-right">
            <Tooltip title={nValue}>
              <Typography className="table-tooltip-text">{nValue}</Typography>
            </Tooltip>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "approval_type_name",
      minWidth: 120,
      maxWidth: 120,
      headerClass: "ag-header-center",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IInvoiceTableCellRenderer) => {
        if (data?.approval_type_key) {
          const { color, textColor } = getDefaultStatuscolor(
            data?.status_color || ""
          );
          const status = HTMLEntities.decode(
            sanitizeString(data?.approval_type_name)
          );
          return status ? (
            <Tooltip title={status}>
              <div className="text-center overflow-hidden">
                <Tag
                  color={color}
                  style={{
                    color: `${textColor || ""}`,
                  }}
                  className={`${
                    textColor === "" && "!text-primary-900"
                  } mx-auto text-13 type-badge common-tag max-w-24`}
                >
                  {status}
                </Tag>
              </div>
            </Tooltip>
          ) : (
            <div className="table-tooltip-text text-center">-</div>
          );
        }
        return <></>;
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMovable: false,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellClass: "!cursor-auto",
      hide: module_access === "read_only",
      cellRenderer: ({ data }: IInvoiceTableCellRenderer) => {
        return (
          <div className="flex items-center justify-center">
            <InvoiceListAction
              isKanbanDropDown={false}
              isDetailDropDown={false}
              paramsData={{
                ...data,
                contact_id: data?.customer_contact_id,
              }}
              onActionComplete={() => {
                refreshAgGrid();
                dispatch(fetchDashData());
              }}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div
        className={`list-view-table ag-grid-cell-pointer ag-theme-alpine ${
          module_access === "read_only"
            ? "md:h-[calc(100vh-314px)] h-[calc(100vh-367px)]"
            : "lg:h-[calc(100vh-283px)] md:h-[calc(100vh-291px)] h-[calc(100vh-330px)]"
        }`}
      >
        <DynamicTable
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div>
                    <Typography
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                      onClick={() => setIsAddInvoiceOpen(true)}
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={true}
          generateOpenInNewTabUrl={(data: { invoice_id?: number }) =>
            `${routes.MANAGE_INVOICE.url}/${data?.invoice_id}`
          }
        />
      </div>
    </>
  );
};
export default InvoiceList;
