import { useEffect, useMemo, useState } from "react";
// custom hook
import { useIframe, useTranslation } from "~/hook";
// static const
import { apiRoutes, routes } from "~/route-services/routes";
import { defaultConfig } from "~/data";
// zustand
import {
  getGConfig,
  getGModuleByKey,
  getGSettings,
  useGModules,
} from "~/zustand";
import {
  getDirectaryIdByName,
  setSendEmailOpenStatus,
} from "~/components/sidebars/multi-select/customer/zustand/action";
// helpers
import { getApiDefaultParams, Number, sanitizeString } from "~/helpers/helper";
import { getApiData } from "~/helpers/axios-api-helper";
// molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
// Other
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { useNavigate, useParams } from "@remix-run/react";
import {
  ICopyWorkOrderInvoice,
  downloadPdfWorkOrderApi,
} from "../../redux/action/workorderListAction";
import { removeFirstSlash } from "~/shared/utils/helper/common";
import { setShouldWidgetsRefresh } from "../../redux/slices/workorderDashSlice";
import { useWoAppDispatch, useWoAppSelector } from "../../redux/store";
import { sendMessageKeys } from "~/components/page/$url/data";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { fetchWorkorderDetails } from "../../redux/action/workorderDetailsAction";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";

export const WorkOrderTableDropdownItems = ({
  data,
  refreshTable,
  className,
  iconClassName,
  contentClassName,
  icon,
  tooltipcontent,
  isDetailView = false,
  isCallApi = false,
}: IWorkOrderTableDropdownItemsProps) => {
  const { _t } = useTranslation();
  const [workOrderPdfViewOpen, setWorkOrderListPdfViewOpen] =
    useState<boolean>(false);
  const navigate = useNavigate();
  const { details }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const {
    module_id,
    module_key,
    module_singular_name,
    module_access,
    page_is_iframe,
  }: GConfig = getGConfig();
  const gConfig: GConfig = getGConfig();
  const [shareLinkUrl, setSharelinkUrl] = useState<string>("");
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false); // Updated state
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const { module_name }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const { getGlobalModuleByKey } = useGlobalModule();
  const [sendEmailOpen, setSendEmailOpen] = useState<boolean>(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [deleteRecordLoading, setDeleteRecordLoading] =
    useState<boolean>(false);
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const [isSumbitForApprovalLoading, setIsSumbitForApprovalLoading] =
    useState<boolean>(false);
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const dispatch = useWoAppDispatch();
  const { module_name: invoiceModuleName = "Invoice" } =
    (getGModuleByKey(defaultConfig.invoice_merge_module_key) as GModule) || {};
  const stModuleName: IModule | undefined = getGlobalModuleByKey(
    CFConfig.service_ticket_module || "Service Tickets"
  );
  const [selectedCustomerData, setSelecteCustomedData] = useState<
    TselectedContactSendMail[]
  >([]);
  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) || _t("Work Orders");
  const hasInvoiceAccess = useMemo(() => {
    const mAccess = checkModuleAccessByKey(
      defaultConfig.invoice_merge_module_key
    );

    return mAccess === "full_access" || mAccess === "own_data_access";
  }, []);

  const hasInvoiceAccessShowMessages = useMemo(() => {
    const mAccess = checkModuleAccessByKey(
      defaultConfig.invoice_merge_module_key
    );

    return mAccess === "no_access" || mAccess === "read_only";
  }, []);

  const { parentPostMessage } = useIframe();

  const dataIsActive = useMemo(
    () => data?.is_deleted?.toString() === "0",
    [data?.is_deleted]
  );

  const { id: work_order_id }: RouteParams = useParams();

  const options: CustomerEmailTab[] = [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  useEffect(() => {
    let selectedCustData = [];
    if (workOrderPdfViewOpen) {
      if (!isCallApi) {
        selectedCustData.push({
          user_id: Number(data?.billed_to || 0),
          display_name: data?.billed_to_name,
          type_key: data?.billed_to_dir_key,
          type_name: data?.billed_to_type_name,
          email: data?.billed_to_email,
          image: data?.billed_to_image,
        });
      }
      if (details?.billed_to && details?.billed_to != 0 && isCallApi) {
        selectedCustData.push({
          user_id: Number(details?.billed_to || 0),
          display_name: details?.billed_to_name,
          type_key: details?.billed_to_dir_type,
          type_name: getDirectaryIdByName(
            Number(details?.billed_to_dir_type) === 1
              ? 2
              : Number(details?.billed_to_dir_type),
            gConfig
          ),
          email: details?.billed_to_email,
          image: details?.billed_to_image,
          contact_id: details?.billed_to_contact || undefined,
        });
      }
      setSelecteCustomedData(selectedCustData as TselectedContactSendMail[]);
    }
  }, [data?.billed_to, workOrderPdfViewOpen, details]);

  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean,
    tId?: string
  ) => {
    const formData: IWorkOrderSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id:
        Number(work_order_id) === 0
          ? Number(data?.work_order_id)
          : Number(work_order_id),
      module_id: module_id,
      module_key: module_key,
      t_id: pdfTempId,
      work_order_id:
        Number(work_order_id) === 0
          ? Number(data?.work_order_id)
          : Number(work_order_id),
      action: "send",
      op: "pdf_work_order_office",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
          setSendEmailOpenStatus(false);
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const downloadPdf = async (tId: string) => {
    const res = (await downloadPdfWorkOrderApi({
      work_order_id:
        Number(work_order_id) === 0
          ? Number(data?.work_order_id)
          : Number(work_order_id),
      action: "download",
      t_id: tId,
    })) as IDownloadWorkOrderRes;

    if (res) {
      if (res.success) {
        const fileName = res?.data?.pdf_name;
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download = fileName
          ? fileName.toString()
          : res.base64_encode_pdfUrl ?? "";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const response = (await webWorkerApi({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: {
          ...tempFormData,
          send_me_copy: ccMailCopy ? 1 : 0,
          send_custom_email: 0,
        },
      })) as Omit<IApiCallResponse, "success"> & { success: boolean | string };
      if (
        (typeof response?.success === "boolean" && response?.success) ||
        (typeof response?.success !== "boolean" &&
          response?.success?.toString() === "1")
      ) {
        closeSendMailSidebar();
        setIsShareOpen(false); // Set the share link modal to close
        setSendEmailOpenStatus(false);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      console.error(
        `\n File: #header-link.tsx -> Line: #47 -> `,
        (error as Error)?.message
      );
      notification.error({
        description: "Something went wrong. Please try again.",
      });
    }
  };

  const handleDelete = () => {
    try {
      setDeleteRecordLoading(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key:
              Number(work_order_id) === 0
                ? Number(data?.work_order_id)
                : Number(work_order_id),
            module_key: module_key,
            remove_associated_data: 0,
          },
        }),
        success: (response: { message: string }) => {
          setConfirmDialogOpen(false);
          setDeleteRecordLoading(false);

          if (window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            if (Number(work_order_id) !== 0) {
              navigate(`${routes.MANAGE_WORKORDER.url}`);
              if (isDetailView) return;
            }
          }

          refreshTable();
          dispatch(setShouldWidgetsRefresh());
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {},
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleSubmitForApproval = async (
    subject: string,
    message: string,
    tId: string
  ) => {
    try {
      setIsSumbitForApprovalLoading(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "pdf_work_order_office",
          user,
          otherParams: {
            action: "send",
            work_order_id:
              Number(work_order_id) === 0
                ? Number(data?.work_order_id)
                : Number(work_order_id),
            req_from: "send_to_approval",
            send_custom_email: 0,
            custom_subject: subject,
            custom_approve_message: message,
            t_id: tId,
            send_me_copy: 0,
          },
        }),
        success: (response: { message: string }) => {
          if (Number(work_order_id) === 0) {
            navigate(`${routes.MANAGE_WORKORDER.url}/${data?.work_order_id}`);
          }
          refreshTable();
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {},
      });
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleStatus = () => {
    setDeleteRecordLoading(true);
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key:
              Number(work_order_id) === 0
                ? Number(data?.work_order_id)
                : Number(work_order_id),
            module_key: module_key,
            status: dataIsActive ? 1 : 0,
          },
        }),
        success: (response: { message: string }) => {
          refreshTable();
          dispatch(setShouldWidgetsRefresh());
          setConfirmArchiveDialogOpen(false);

          if (window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            if (Number(work_order_id) !== 0) {
              navigate(`${routes.MANAGE_WORKORDER.url}`);
            }
          }

          setDeleteRecordLoading(false);
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {},
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const handleCreateModifyInvoice = async (
    navigateCustom?: (url: string) => void
  ) => {
    const workOrderID =
      Number(work_order_id) === 0
        ? Number(data?.work_order_id)
        : Number(work_order_id);

    const res = (await ICopyWorkOrderInvoice({
      work_order_id: workOrderID,
    })) as ICopyWorkOrderInvoiceApiRes;
    if (res.success) {
      const url = `${routes.MANAGE_INVOICE.url}/${
        res?.data?.id?.toString() || ""
      }`;
      if (navigateCustom) {
        navigateCustom(url);
      } else if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
        if (window.parent && window.parent.location) {
          window.parent.location.href = url;
        }
      } else {
        navigate(url);
      }
    }

    // if (res.success) {
    //   const url = `/manage-invoice/${res?.data?.id}`;
    //   if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
    //     if (window.parent && window.parent.location) {
    //       window.parent.location.href = url;
    //     }
    //   } else {
    //     navigate(url);
    //   }
    // }
  };

  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    visible: boolean;
  }>({ x: 0, y: 0, visible: false });

  // Add context menu items
  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        handleCreateModifyInvoice((url) => {
          window.open(url, "_blank", "noopener,noreferrer");
        });
      },
    },
  ];

  const WorkOrderlistViewTableOptions = [
    {
      label: _t("View/Email PDF"),
      icon: "fa-regular fa-file-pdf",
      onClick: () => {
        setWorkOrderListPdfViewOpen(true);
      },
    },
    {
      label: _t(`Create/Modify ${invoiceModuleName}`),
      icon: "fa-regular fa-file-invoice",
      onClick: (e: { domEvent: any }) => {
        const domEvent = e.domEvent;
        const isNewTab = domEvent && (domEvent?.ctrlKey || domEvent?.metaKey);
        handleCreateModifyInvoice(
          isNewTab
            ? (url) => window.open(url, "_blank", "noopener,noreferrer")
            : undefined
        );
      },
      onAuxClick: (e: any) => {
        if (e.button === 1) {
          handleCreateModifyInvoice((url) => {
            window.open(url, "_blank", "noopener,noreferrer");
          });
        }
      },
      onContextMenu: (e: React.MouseEvent<any>) => {
        e.preventDefault();
        setContextMenu({ x: e.clientX, y: e.clientY, visible: true });
      },
      disabled: !hasInvoiceAccess,
    },
    {
      content: _t("Share Internal Link"),
      icon: "fa-regular fa-share-nodes",
      onClick: () => {
        setIsShareOpen(true); // Set to open when clicking
      },
      onlyIconView: true,
    },
    {
      label: _t(`${modulePLName} vs ${stModuleName?.plural_name}`),
      icon: "fa-regular fa-video",
      onClick: () => {
        window.open(`${defaultConfig.vimeo_website_url}399295862`);
      },
    },
    {
      content:
        data?.is_deleted?.toString() === "1" ? (
          <>
            {_t("Status: Archived")} <br />
            {_t("Click to Activate the item")}
          </>
        ) : (
          <>
            {_t("Status: Active")} <br />
            {_t("Click to Archive the item")}
          </>
        ),
      icon:
        data?.is_deleted?.toString() === "1"
          ? "fa-regular fa-regular-active"
          : "fa-regular fa-box-archive",
      onClick: () => setConfirmArchiveDialogOpen(true),
      onlyIconView: true,
    },
    {
      content: _t("Delete"),
      icon: "fa-regular fa-trash-can",
      onClick: () => setConfirmDialogOpen(true),
      disabled: allow_delete_module_items === "0" || page_is_iframe,
      onlyIconView: true,
    },
  ];
  useEffect(() => {
    if (workOrderPdfViewOpen && data?.work_order_id && isCallApi) {
      dispatch(
        fetchWorkorderDetails({
          id: data?.work_order_id.toString(),
          shouldLoading: false,
        })
      );
    }
  }, [workOrderPdfViewOpen, isCallApi]);
  return (
    <>
      <DropdownMenu
        icon={icon ? icon : "fa-regular fa-ellipsis-vertical"}
        options={WorkOrderlistViewTableOptions}
        buttonClass={
          className ? className : "active-button hover:!bg-[#0000000f] !rounded"
        }
        tooltipcontent={tooltipcontent}
        iconClassName={iconClassName && iconClassName}
        contentClassName={contentClassName && contentClassName}
        {...((hasInvoiceAccessShowMessages ||
          allow_delete_module_items === "0") && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />

      {workOrderPdfViewOpen && data && (
        <PDFFilePreview
          projectId={Number(data?.project_id ?? "")}
          moduleName={module_singular_name}
          isOpen={workOrderPdfViewOpen}
          onCloseModal={() => setWorkOrderListPdfViewOpen(false)}
          moduleId={module_id}
          op="pdf_work_order_office"
          idName="work_order_id"
          isLoading={false}
          id={
            Number(work_order_id) === 0
              ? (data?.work_order_id?.toString() as string)
              : (work_order_id as string)
          }
          options={options}
          emailSubject={data?.email_subject}
          handleEmailApiCall={emailApiCall}
          isSumbitForApproval={
            data?.work_order_status === 179 ||
            data?.work_order_status === 245 ||
            data?.work_order_status === 181
          }
          handleDownload={downloadPdf}
          isViewAttachment={false}
          submitForApprovalSubject={`Work Order #${
            data.prefix_company_work_order_id || data.company_order_id
          } (${data?.project_name})`}
          handleSubmitForApproval={handleSubmitForApproval}
          isSumbitForApprovalLoading={isSumbitForApprovalLoading}
          setPdfTempId={setPdfTempId}
          selectedCustomer={selectedCustomerData}
          isAddUserId={true}
        />
      )}
      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id:
              Number(work_order_id) === 0
                ? Number(data?.work_order_id)
                : Number(work_order_id), // Adjust the key name for your data structure
            module_key: module_key,
            module_page: removeFirstSlash(routes.MANAGE_WORKORDER.url || ""),
          }}
          onEmailLinkClick={(shareLinkData) => {
            setSendEmailOpenStatus(true); // Open the email modal
            setSendEmailOpen(true);
            setIsShareOpen(false);
            setSharelinkUrl(shareLinkData);
          }}
          onCloseModal={() => {
            setIsShareOpen(false); // Close the modal
          }}
        />
      )}

      {sendEmailOpen && (
        <SendEmailDrawer
          closeDrawer={() => {
            setSendEmailOpen(false);
            setSendEmailOpenStatus(false);
          }}
          appUsers={true}
          isViewAttachment={false}
          openSendEmailSidebar={sendEmailOpen}
          options={[defaultConfig.employee_key, defaultConfig.contractor_key]}
          singleSelecte={false}
          emailApiCall={handleEmailApiCall}
          customEmailData={{
            body: _t(
              `A link to a record within Contractor Foreman has been shared with you. <a href = ${shareLinkUrl}>View Details</a>.`
            ),
            subject: _t("Shared Link"),
          }}
          validationParams={{
            date_format,
            file_support_module_access: checkModuleAccessByKey(
              defaultConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
            save_a_copy_of_sent_pdf,
          }}
          canWrite={false}
          contactId={0}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          isLoading={deleteRecordLoading}
          withConfirmText={true}
          onAccept={() => {
            handleDelete();
          }}
          onDecline={() => setConfirmDialogOpen(false)}
          onCloseModal={() => setConfirmDialogOpen(false)}
        />
      )}

      <ConfirmModal
        isOpen={confirmArchiveDialogOpen}
        isLoading={deleteRecordLoading}
        modaltitle={_t(
          data?.is_deleted?.toString() === "1" ? "Active" : "Archive"
        )}
        description={_t(
          data?.is_deleted?.toString() === "1"
            ? "Are you sure you want to Activate this data?"
            : `Are you sure you want to Archive this item?  ${
                dataIsActive
                  ? "To view it or Activate it later, set the filter to show Archived items."
                  : ""
              }`
        )}
        modalIcon={
          dataIsActive
            ? "fa-regular fa-box-archive"
            : "fa-regular fa-regular-active"
        }
        onAccept={() => {
          handleStatus();
        }}
        onDecline={() => {
          setConfirmArchiveDialogOpen(false);
        }}
        onCloseModal={() => {
          setConfirmArchiveDialogOpen(false);
        }}
      />

      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={contextMenuItems}
        onClose={() => setContextMenu((c) => ({ ...c, visible: false }))}
      />
    </>
  );
};
