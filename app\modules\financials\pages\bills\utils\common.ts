import dayjs from "dayjs";
import { downloadDocumentApi } from "~/redux/action/downloadDocAction";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { billRoutes } from "~/route-services/bill.routes";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

export const getFileUrl = (
  todoId: number | string,
  userId: number,
  companyId: number,
  currentDateTime: dayjs.Dayjs
) =>
  `${billRoutes.pdf}?bill_id=${todoId}&tz=${dayjs().format("ZZ")}&tzid=${
    Intl.DateTimeFormat().resolvedOptions().timeZone
  }&from=panel&version=web&curr_time=${dayjs(currentDateTime).format(
    "YYYY-MM-DD HH:mm:ss"
  )}`;

export const handleEmailApiCall = async (
  tempFormData: SendEmailFormDataWithApiDefault,
  closeSendMailSidebar: () => void,
  ccMailCopy: boolean,
  id: number
) => {
  const formData = {
    ...tempFormData,
    send_me_copy: ccMailCopy ? 1 : 0,
    send_custom_email: 0,
    op: "pdf_bill",
    action: "send",
    bill_id: Number(id),
  } as ISendEmailFormTodoModule;

  try {
    const responseApi = (await sendCommonEmailApi(
      formData
    )) as ISendEmailCommonRes;
    if (responseApi?.success) {
      closeSendMailSidebar();
    }
    if (!responseApi?.success) {
      notification.error({
        description: responseApi.message,
      });
    }
  } catch (error) {
    notification.error({
      description: (error as Error).message || "",
    });
  }
  closeSendMailSidebar();
};

export const handleDownload = async (formData: IDownloadDocumentWithAction) => {
  try {
    const responseApi = (await downloadDocumentApi(
      formData
    )) as IDownloadDocumentApiRes;
    if (responseApi?.success) {
      const fileName = responseApi?.data?.pdf_name;
      const link = document.createElement("a");
      link.href = responseApi?.base64_encode_pdf ?? "";
      link.download = fileName
        ? fileName.toString()
        : responseApi?.base64_encode_pdfUrl || "";
      document.body.appendChild(link);
      link.click();
      document?.body?.removeChild(link);
    }
    if (!responseApi?.success) {
      notification.error({
        description: responseApi.message,
      });
    }
  } catch (error) {
    notification.error({
      description: (error as Error).message || "",
    });
  }
};

export const addItemObject: IAddItems = {
  text: "Terms Name & Press Enter",
  icon: "fa-regular fa-plus",
};

export const floatNumberRegex = /^\d*\.?\d{0,2}$/;

export const calculateBalanceDue = ({
  totalAmountForHeader,
  billItemsWithoutGlobalTaxAndRetainageItems,
  totalTaxRate,
  billPayment,
}: {
  totalAmountForHeader?: number;
  billItemsWithoutGlobalTaxAndRetainageItems: BillItem[];
  totalTaxRate?: number;
  billPayment?: number;
}) => {
  const baseTotal = Number(totalAmountForHeader || 0);

  const taxPart =
    ((billItemsWithoutGlobalTaxAndRetainageItems?.reduce(
      (prevData: number, currentData?: BillItem) =>
        Math.abs(prevData + Number(currentData?.total || 0)),
      0
    ) ?? 0) /
      100) *
    Math.abs(Number(totalTaxRate || 0) / 100);

  const afterTaxTotal = baseTotal + taxPart;
  const paid = Number(billPayment || 0) / 100;

  const balanceDue = afterTaxTotal - paid;

  const { formatter } = useCurrencyFormatter();
  const formatterWithTwoDigit = (value: string | number) => {
    return formatter(Number(value).toFixed(2));
  };

  return balanceDue <= 0
    ? formatterWithTwoDigit(0).value_with_symbol
    : formatterWithTwoDigit(balanceDue).value_with_symbol;
};
