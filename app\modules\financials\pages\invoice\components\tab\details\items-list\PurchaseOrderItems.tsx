// atoms
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { useAppIVDispatch, useAppIVSelector } from "../../../../redux/store";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { useMemo, useState } from "react";
import { useParams } from "@remix-run/react";
import {
  deleteTimeMaterialItems,
  updateInvoiceItem,
} from "../../../../redux/action/InvoiceItemsActions";
import {
  deleteInvoiceItem,
  updateInvoiceItems,
} from "../../../../redux/slices/InvoiceItemsSlice";
import { useGModules } from "~/zustand";
import { defaultConfig } from "~/data";
import { ValueSetterParams } from "ag-grid-community";

interface IIvPurchaseOrderItemProps {
  invoiceItem: boolean;
  setInvoiceItem: React.Dispatch<React.SetStateAction<boolean>>;
  setItemToBeUpdate: React.Dispatch<React.SetStateAction<IInvoiceItemData>>;
  setEquipmentLogItem: React.Dispatch<React.SetStateAction<boolean>>;
  isReadOnly: boolean;
}

const PurchaseOrderItems = ({
  invoiceItem,
  setInvoiceItem,
  setItemToBeUpdate,
  setEquipmentLogItem,
  isReadOnly,
}: IIvPurchaseOrderItemProps) => {
  const { _t } = useTranslation();

  const { formatter } = useCurrencyFormatter();
  const { details } = useAppIVSelector((state) => state.invoiceDetails);
  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );
  const { getGModuleByKey } = useGModules();
  const params = useParams();
  const [selectedItemToDelete, setSelectedItemToDelete] = useState<number[]>();
  const [selectedSectionToDelete, setSelectedSectionToDelete] =
    useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const dispatch = useAppIVDispatch();

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        item_equipment: "fa-regular fa-screwdriver-wrench",
        item_material: "fa-regular fa-block-brick",
        item_labour: "fa-regular fa-user-helmet-safety",
        item_sub_contractor: "fa-regular fa-file-signature",
        item_other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  const handleInvoiceItemDelete = async (
    itemIds: number[],
    sectionId: number
  ) => {
    setIsDeleting(true);
    const response = (await deleteTimeMaterialItems({
      id: Number(params.id),
      purchase_orders: itemIds,
    })) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        purchase_order_sections: invoiceItems.purchase_order_sections.filter(
          (section) => Number(section.section_id) !== sectionId
        ),
        items: invoiceItems.items.filter(
          (item) => !itemIds.some((itemId) => item.item_id === itemId)
        ),
      };
      const filtereNewItems: IInvoiceItemsListData = {
        ...newItems,
        purchase_order_sections: newItems.purchase_order_sections.filter(
          (section) => section.items.length > 0
        ),
      };
      dispatch(deleteInvoiceItem({ items: filtereNewItems }));
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    } else {
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
      notification.error({
        description: response.message || "Something went wrong!",
      });
    }
  };

  const handleApplyGlobalTaxUpdate = async (
    invoiceItem: IInvoiceItemData,
    applyGlobalTax: number
  ) => {
    const itemToBeUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: [
        {
          item_id: invoiceItem.item_id,
          subject: invoiceItem.subject,
          item_type: invoiceItem.item_type,
          apply_global_tax: applyGlobalTax,
        },
      ],
    };

    const response = (await updateInvoiceItem(
      itemToBeUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        purchase_order_sections: invoiceItems.purchase_order_sections.map(
          (section) => {
            return {
              ...section,
              items: section.items.map((item) => {
                if (item.item_id === invoiceItem.item_id) {
                  return {
                    ...item,
                    apply_global_tax: applyGlobalTax,
                  };
                } else {
                  return { ...item };
                }
              }),
            };
          }
        ),
      };
      dispatch(updateInvoiceItems({ items: newItems }));
    }

    if (!response.success) {
      notification.error({
        description: response.message || "Something went wrong",
      });
    }
  };

  const headerComponentForDeleteIcon = (props: any) => {
    const section = props.section as IInvoiceItemsListBillSectionData;

    return (
      <>
        {!isReadOnly &&
        (details?.approval_type_key === "invoice_open" ||
          details?.approval_type_key === "invoice_on_hold") ? (
          <div className="flex justify-end w-full">
            <ButtonWithTooltip
              tooltipTitle={_t("Delete")}
              tooltipPlacement="top"
              icon="fa-regular fa-trash-can"
              onClick={() => {
                const itemIDs = section.items.map((item) =>
                  Number(item.item_id)
                );
                setSelectedSectionToDelete(Number(section.section_id));
                setSelectedItemToDelete(itemIDs);
                setIsDeleteConfirmOpen(true);
              }}
            />
          </div>
        ) : (
          <></>
        )}
      </>
    );
  };

  return (
    <>
      {invoiceItems.purchase_order_sections.map((section) => {
        if (section.items.length !== 0) {
          const sectiontitle = `${
            getGModuleByKey(defaultConfig.purchase_order_module)?.plural_name ||
            "Purchase Orders"
          } - ${section.purchase_order_section_subject}`;

          let total = 0;
          section.items.map((item) => {
            total += Number(item.total) || 0;
          });
          const data = section.items;
          const sortedData = [...data].sort(
            (a, b) => Number(a?.invoice_item_no) - Number(b.invoice_item_no)
          );

          return (
            <CollapseSingleTable
              title={_t(HTMLEntities.decode(sanitizeString(sectiontitle)))}
              totalRecord={`${
                formatter(formatAmount(((Number(total) || 0) / 100).toFixed(2)))
                  .value_with_symbol
              }`}
              defaultActiveKey={section.items.length !== 0 ? 1 : 0}
              totalRecordIcon={true}
              children={
                <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-6 before:bg-gradient-to-r from-primary-500">
                  <div className="ag-theme-alpine">
                    <StaticTable
                      className="static-table"
                      columnDefs={[
                        {
                          headerName: "",
                          field: "move",
                          minWidth: 30,
                          maxWidth: 30,
                          suppressMenu: true,
                        },
                        {
                          headerName: _t("Type"),
                          field: "type",
                          maxWidth: 50,
                          minWidth: 50,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-center",
                          cellClass: "ag-cell-center",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return data.item_type_name ? (
                              <Tooltip title={_t(data.item_type_name)}>
                                <FontAwesomeIcon
                                  className="w-4 h-4 text-primary-900 mx-auto"
                                  icon={iconByItemTypeName[data.item_type_key]}
                                />
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Item Name"),
                          field: "item",
                          minWidth: 220,
                          maxWidth: 220,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return data.subject ? (
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(data.subject)
                                )}
                              >
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(data.subject)
                                  )}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Cost Code"),
                          field: "cost_code",
                          minWidth: 560,
                          flex: 2,
                          resizable: true,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const costCode = `${data?.cost_code_name ?? ""}${
                              data?.csi_code ? ` (${data?.csi_code})` : ""
                            }${
                              data?.code_is_deleted === 1 ? ` (Archived)` : ""
                            }`;

                            return (
                              <>
                                {costCode ? (
                                  <Tooltip
                                    title={HTMLEntities.decode(
                                      sanitizeString(costCode)
                                    )}
                                  >
                                    <Typography className="table-tooltip-text">
                                      {HTMLEntities.decode(
                                        sanitizeString(costCode || "-")
                                      )}
                                    </Typography>
                                  </Tooltip>
                                ) : (
                                  "-"
                                )}
                              </>
                            );
                          },
                        },
                        {
                          headerName: _t("QTY"),
                          field: "qty",
                          minWidth: 80,
                          maxWidth: 80,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                             const quantity =
                               formatter(
                                 formatAmount(Number(data?.quantity || 0), {
                                   isQuantity: true,
                                 })
                               ).value || 0;
                             return (
                               <Tooltip title={quantity}>
                                 <Typography className="table-tooltip-text">
                                   {quantity}
                                 </Typography>
                               </Tooltip>
                             );
                          },
                        },
                        {
                          headerName: _t("Cost"),
                          field: "cost",
                          minWidth: 130,
                          maxWidth: 130,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return data.unit_cost ? (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      (
                                        (Number(data.unit_cost) || 0) / 100
                                      ).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(data.unit_cost) || 0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Unit"),
                          field: "unit",
                          maxWidth: 100,
                          minWidth: 100,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          suppressMovable: false,
                          suppressMenu: true,
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return data.unit ? (
                              <Tooltip title={data.unit}>
                                <Typography className="table-tooltip-text">
                                  {data.unit || "-"}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: "MU%",
                          field: "mu",
                          maxWidth: 80,
                          minWidth: 80,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const is_markup_percentage =
                              data?.is_markup_percentage ?? "";
                            const total =
                              Number(data?.quantity ?? 0) *
                              (Number(data?.unit_cost ?? 0) / 100);
                            const markup = Number(data?.markup);

                            let markupToShow = formatter("0.00").value;

                            if (is_markup_percentage?.toString() === "1") {
                              markupToShow = formatter(
                                markup?.toString() ?? "0"
                              ).value;
                            } else {
                              if (total != 0) {
                                const markupPercentage = markup / total - 100;
                                markupToShow = markupPercentage.toFixed(2);
                                markupToShow = formatter(
                                  Number(markupToShow || 0).toString()
                                )?.value;
                              }
                            }
                            return data.markup ? (
                              <Tooltip title={markupToShow}>
                                <Typography className="table-tooltip-text">
                                  {markupToShow}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <>-</>
                            );
                          },
                        },
                        {
                          headerName: _t("Total"),
                          field: "total",
                          maxWidth: 130,
                          minWidth: 130,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const total = "$22.42";
                            return data.total ? (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      ((Number(data.total) || 0) / 100).toFixed(
                                        2
                                      )
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(data.total) || 0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Tax"),
                          field: "apply_global_tax",
                          minWidth: 50,
                          maxWidth: 50,
                          suppressMovable: false,
                          suppressMenu: true,

                          headerClass: "ag-header-center",
                          cellClass: "ag-cell-center flex justify-center",
                          editable:
                            !isReadOnly &&
                            (details?.approval_type_key === "invoice_open" ||
                              details?.approval_type_key === "invoice_on_hold"),
                          cellRenderer: "agCheckboxCellRenderer",
                          cellEditor: "agCheckboxCellEditor",
                          valueGetter: ({ data }: IIvItemsCellRenderer) => {
                            return data?.apply_global_tax == 1;
                          },
                          valueSetter: (params: ValueSetterParams) => {
                            if (params && params.node) {
                              let nVal = params.newValue;
                              const updatedData = {
                                ...params.data,
                                apply_global_tax: nVal,
                              };
                              params.node.setData(updatedData);
                              handleApplyGlobalTaxUpdate(
                                params.data,
                                nVal === true ? 1 : 0
                              );
                            }
                            return true;
                          },
                        },
                        {
                          headerName: "",
                          field: "",
                          maxWidth: 80,
                          minWidth: 80,
                          suppressMenu: true,
                          headerComponent: headerComponentForDeleteIcon,
                          headerComponentParams: {
                            section: section,
                          },
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <div className="flex items-center gap-1.5 justify-end">
                                <ButtonWithTooltip
                                  tooltipTitle={_t("View")}
                                  tooltipPlacement="top"
                                  icon="fa-solid fa-eye"
                                  onClick={() => {
                                    setItemToBeUpdate(data);
                                    setEquipmentLogItem(false);
                                    setInvoiceItem(true);
                                  }}
                                />
                              </div>
                            );
                          },
                        },
                      ]}
                      rowData={sortedData}
                      noRowsOverlayComponent={() => (
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-purchase-orders.svg`}
                        />
                      )}
                    />
                  </div>
                </div>
              }
            />
          );
        } else {
          return <></>;
        }
      })}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Section?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={() => {
            if (selectedItemToDelete && selectedSectionToDelete) {
              handleInvoiceItemDelete(
                selectedItemToDelete,
                selectedSectionToDelete
              );
            }
          }}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default PurchaseOrderItems;
