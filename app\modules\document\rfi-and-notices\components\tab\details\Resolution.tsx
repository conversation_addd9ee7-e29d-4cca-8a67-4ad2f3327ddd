import { useTranslation } from "~/hook";
import delay from "lodash/delay";
import dayjs, { Dayjs } from "dayjs";
import type { DatePickerProps } from "antd";
// molecules
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { useCallback, useEffect, useRef, useState } from "react";
import { RFIDetailsField } from "../../../utils/constasnts";
import { useParams } from "@remix-run/react";
import { getGSettings } from "~/zustand";
import { RFIFieldStatus } from "~/constants/rfi-and-notices";
import { useAppRFIDispatch, useAppRFISelector } from "../../../redux/store";
import { updateRfiDetailsApi } from "../../../redux/action/rfiDetailAction";
import { updateRFIDetails } from "../../../redux/slices/rfiDetailSlice";
import { getStatusActionForField } from "~/shared/utils/helper/common";
import debounce from "lodash/debounce";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";

export const getStatusForField = (
  loadingStatus: IFieldStatus[],
  fieldName: string
): IStatus => {
  const itemField = loadingStatus.find(
    (item: IFieldStatus) => item && item.field === fieldName
  );
  if (itemField && itemField.status) {
    return itemField.status;
  }
  return "button";
};

const Resolution = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const dispatch = useAppRFIDispatch();
  const { id: RFI_Id }: RouteParams = useParams();
  const descriptionFieldRef = useRef<HTMLInputElement>(null);
  const { date_format }: GSettings = getGSettings();
  const [inputValues, setInputValues] =
    useState<Partial<IRFIDetails>>(RFIDetailsField);
  const loadingStatusRef = useRef(RFIFieldStatus);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(RFIFieldStatus);
  const { rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: Partial<IRFIDetails>) => {
    const field = Object.keys(data)[0] as keyof IRFIDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateRfiDetailsApi({
      correspondence_id: RFI_Id,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateRFIDetails(data));
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 1000);
  };

  useEffect(() => {
    if (rfiDetail?.correspondence_id) {
      setInputValues(rfiDetail);
    }
  }, [rfiDetail]);

  const submitUpdate = useCallback(
    async (data: { [key: string]: string | number | boolean | null }) => {
      const field = Object.keys(data);
      const response = (await updateRfiDetailsApi({
        correspondence_id: RFI_Id,
        ...data,
      })) as ApiCallResponse;
      if (response.success) {
        if (field[0] === "resolution_date") {
          const values = Object.values(data)[0] as string;
          dispatch(
            updateRFIDetails({
              ...data,
              resolution_date: !!values
                ? dayjs(values).format(date_format)
                : "",
            })
          );
        } else if (field[0] === "resolve_by_date") {
          const values = Object.values(data)[0] as string;
          dispatch(
            updateRFIDetails({
              ...data,
              resolve_by_date: !!values
                ? dayjs(values).format(date_format)
                : "",
            })
          );
        } else {
          dispatch(updateRFIDetails(data));
        }
      } else {
        notification.error({
          message: "Error",
          description: response.message,
        });
      }
      return response;
    },
    []
  );

  const handleUpdate = useCallback(
    async (key: string, value: string | number | null) => {
      if (!rfiDetail) return;

      let data = {
        [key]: value,
      };

      if ((key === "resolution_date" || key === "resolve_by_date") && value) {
        let parsedDate;
        if (date_format === "DD/MM/YYYY") {
          parsedDate = dayjs(value, "DD/MM/YYYY", true);
        } else if (date_format === "MM/DD/YYYY") {
          parsedDate = dayjs(value, "MM/DD/YYYY", true);
        } else {
          parsedDate = dayjs(
            value,
            ["YYYY-MM-DD", "DD.MM.YYYY", "DD/MMM/YYYY"],
            true
          );
        }

        data = {
          [key]: parsedDate.isValid() ? parsedDate.format("YYYY-MM-DD") : null,
        };
      }

      handleChangeFieldStatus({
        field: key,
        status: "loading",
        action: "API",
      });

      const { success } = await submitUpdate(data);

      if (success) {
        handleChangeFieldStatus({
          field: key,
          status: "success",
          action: "API",
        });
      }

      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatusRef.current,
          key
        );
        handleChangeFieldStatus({
          field: key,
          status: fieldAction === "FOCUS" ? "save" : "button",
          action: fieldAction || "API",
        });
      }, 1000);
    },
    [rfiDetail]
  );

  const handleChangeDate = debounce((dateString: string) => {
    if (dateString === "") {
      setInputValues({
        ...inputValues,
        resolution_date: null,
      });
      handleUpdateField({
        resolution_date: null,
      });
      return false;
    } else {
      const isValidFormat = dayjs(
        dateString,
        CFConfig.day_js_date_format,
        true
      ).isValid();
      const selectedDate = dayjs(dateString, CFConfig.day_js_date_format, true);
      const today = dayjs().startOf("day");
      const formattedDate = selectedDate.format(CFConfig.day_js_date_format);
      if (isValidFormat) {
        const ResolutionDate = dayjs(dateString, CFConfig.day_js_date_format);
        const correspondencedate = dayjs(
          rfiDetail?.correspondence_date,
          CFConfig.day_js_date_format
        );
        if (ResolutionDate.isBefore(correspondencedate)) {
          notification.error({
            description:
              "Resolution Date must be greater than or equal to Date value.",
          });
          setInputValues((prev) => ({
            ...prev,
            resolve_by_date: rfiDetail?.resolve_by_date,
          }));

          return false;
        }
        if (selectedDate.isAfter(today)) {
          notification.error({
            description: "Resolution Date cannot be after current Date",
          });
          setInputValues((prev) => ({
            ...prev,
            resolution_date: rfiDetail?.resolution_date,
          }));

          return false;
        }
        setInputValues({
          ...inputValues,
          resolution_date: dateString.toString(),
        });
        handleUpdate("resolution_date", formattedDate);
      }
    }
  }, 100);

  const handleResolveByDateChange = debounce((dateString: string) => {
    if (dateString === "") {
      setInputValues({
        ...inputValues,
        resolve_by_date: dateString,
      });
      handleUpdateField({
        resolve_by_date: null,
      });
      return false;
    } else {
      const isValidFormat = dayjs(
        dateString,
        CFConfig.day_js_date_format,
        true
      ).isValid();
      const selectedDate = dayjs(dateString, CFConfig.day_js_date_format, true);
      const formattedDate = selectedDate.format(CFConfig.day_js_date_format);
      if (isValidFormat) {
        const ResolutionDeadlineDate = dayjs(
          dateString,
          CFConfig.day_js_date_format
        );
        const correspondencedate = dayjs(
          rfiDetail?.correspondence_date,
          CFConfig.day_js_date_format
        );

        if (ResolutionDeadlineDate.isBefore(correspondencedate)) {
          notification.error({
            description:
              "Resolution Deadline must be greater than or equal to Date value.",
          });
          setInputValues((prev) => ({
            ...prev,
            resolve_by_date: rfiDetail?.resolve_by_date,
          }));

          return false;
        }

        setInputValues({
          ...inputValues,
          resolve_by_date: dateString.toString(),
        });
        handleUpdate("resolve_by_date", formattedDate);
      }
    }
  }, 100);
  const handleInpOnChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Resolution")}
        iconProps={{
          icon: "fa-solid fa-object-intersect",
          containerClassName:
            "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
          id: "rfi_response_icon",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li>
                <DatePickerField
                  label={_t("Deadline")}
                  placeholder={_t("Select Date")}
                  labelPlacement="left"
                  name="resolve_by_date"
                  disabled={isReadOnly}
                  readOnly={isReadOnly}
                  inputReadOnly={true}
                  editInline={true}
                  iconView={true}
                  allowClear={true}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "resolve_by_date"
                  )}
                  value={
                    inputValues.resolve_by_date
                      ? displayDateFormat(
                          inputValues.resolve_by_date?.toString().trim(),
                          date_format
                        )
                      : null
                  }
                  onChange={(_, dateString) => {
                    handleResolveByDateChange(dateString as string);
                  }}
                  format={date_format}
                />
              </li>
              <li>
                <DatePickerField
                  label={_t("Date")}
                  placeholder={_t("Select Date")}
                  labelPlacement="left"
                  name="resolution_date"
                  disabled={isReadOnly}
                  readOnly={isReadOnly}
                  inputReadOnly={true}
                  editInline={true}
                  iconView={true}
                  allowClear={true}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "resolution_date"
                  )}
                  value={
                    inputValues.resolution_date
                      ? displayDateFormat(
                          inputValues.resolution_date?.toString().trim(),
                          date_format
                        )
                      : null
                  }
                  onChange={(_, dateString) =>
                    handleChangeDate(dateString as string)
                  }
                  format={date_format}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Response")}
                  placeholder={_t("Resolution or Response Details")}
                  labelPlacement="left"
                  name="resolution_notes"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  ref={descriptionFieldRef}
                  value={HTMLEntities.decode(
                    inputValues.resolution_notes ?? ""
                  )}
                  onClickStsIcon={() => {
                    if (
                      getStatusForField(loadingStatus, "resolution_notes") ===
                      "edit"
                    ) {
                      descriptionFieldRef?.current?.focus();
                    }
                  }}
                  onChange={handleInpOnChange}
                  onBlur={(e) => {
                    const value = e?.target?.value?.trim();
                    if (value !== rfiDetail.resolution_notes) {
                      handleUpdateField({ resolution_notes: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "resolution_notes",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        resolution_notes: rfiDetail.resolution_notes,
                      });
                    }
                  }}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "resolution_notes"
                  )}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "resolution_notes",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "resolution_notes",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "resolution_notes",
                      status: "button",
                      action: "ML",
                    });
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />
    </>
  );
};

export default Resolution;
