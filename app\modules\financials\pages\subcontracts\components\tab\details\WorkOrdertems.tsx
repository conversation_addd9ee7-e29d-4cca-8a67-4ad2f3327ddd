import { useEffect, useMemo, useState } from "react";
import { useParams } from "@remix-run/react";
import { getGModule<PERSON>y<PERSON><PERSON> } from "~/zustand";
// Hook Redux
import { useTranslation } from "~/hook";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import {
  useAppSCDispatch,
  useAppSCSelector,
} from "~/modules/financials/pages/subcontracts/redux/store";
import {
  deleteSCItems,
  fetchSCWorkOrder,
} from "~/modules/financials/pages/subcontracts/redux/action";
import { resetDash } from "~/modules/financials/pages/subcontracts/redux/slices";
// Constants
import { SCIcons } from "~/modules/financials/pages/subcontracts/utils/constants";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { defaultConfig } from "~/data";
import { AddSubContractItem } from "../sidebar/subContractItem";

const WorkOrdertems = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const { id: sCId }: RouteParams = useParams();
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.work_order_module
  );
  const dispatch = useAppSCDispatch();
  const { workOrderItem, isWorkOrderItemLoading }: ISCItemsInitialState =
    useAppSCSelector((state) => state.subContractsItems);
  const { details }: ISCDetailsInitialState = useAppSCSelector(
    (state) => state.subContractsDetails
  );

  // STATE
  const [selectedSCOtherItemId, setSelectedSCOtherItemId] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isActiveTable, setIsActiveTable] = useState<string[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<number>(0);
  const [isViewOnly, setIsViewOnly] = useState<boolean>(false);
  const { filter }: ISCCommonInitialState = useAppSCSelector(
    (state) => state.sCCommonData
  );
  const handleDeleteSCOtherItem = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      const deleteRes = (await deleteSCItems({
        id: Number(sCId),
        item_ids: [selectedSCOtherItemId || 0],
      })) as ISCItemsDeleteApiRes;

      if (deleteRes?.success) {
        dispatch(resetDash());
        dispatch(fetchSCWorkOrder({ id: sCId || "" }));
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  const handleTableAction = (
    params: ISCWorkOrderTableCellRenderer,
    key: string
  ) => {
    if (key === "view") {
      setIsViewOnly(true);
      setSelectedItemId(params?.data?.item_id || 0);
    } else if (key === "edit") {
      setIsViewOnly(true);
      setSelectedItemId(params?.data?.item_id || 0);
    }
  };

  const otherItemColumnDefs = [
    {
      headerName: "",
      field: "",
      minWidth: 30,
      maxWidth: 30,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
    },
    {
      headerName: _t("Type"),
      maxWidth: 50,
      minWidth: 50,
      field: "item_type",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const itemType = data?.item_type_key;
        const itemTypeName = data?.item_type_name;
        const itemTypeIcon = SCIcons[itemType as IconKey];

        return itemTypeIcon ? (
          <Tooltip title={itemTypeName}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900 mx-auto"
              icon={itemTypeIcon}
            />
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Source"),
      field: "company_order_id",
      minWidth: 130,
      maxWidth: 130,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const source = `${_t("WO")} ${data?.company_order_id || ""}`;
        return source ? (
          <Tooltip title={source}>
            <Typography className="table-tooltip-text">{source}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: 150,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const subject = data?.subject;

        return subject ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Cost Code"),
      field: "cost_code_name",
      minWidth: 130,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const costCode = `${data?.cost_code_name ?? ""}${
          data?.cost_code ? ` (${data?.cost_code})` : ""
        }${data?.code_is_deleted == 1 ? ` (Archived)` : ""}`;

        return costCode ? (
          <Tooltip title={HTMLEntities.decode(sanitizeString(costCode))}>
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(costCode))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "quantity",
      maxWidth: 80,
      minWidth: 80,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: ISCWorkOrderTableCellRenderer) => {
        const quantityUnit =
          formatter(
            formatAmount(Number(params?.data.quantity), { isQuantity: true })
          ).value || 0;

        return (
          <Tooltip title={quantityUnit}>
            <Typography className="table-tooltip-text">
              {quantityUnit}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Unit Cost"),
      field: "unit_cost",
      maxWidth: 130,
      minWidth: 130,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const nValue = Number(data?.unit_cost) / 100;
        const unitCost = formatter(formatAmount(nValue)).value_with_symbol;
        return unitCost ? (
          <Tooltip title={unitCost}>
            <Typography className="table-tooltip-text">{unitCost}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Unit"),
      field: "unit",
      maxWidth: 100,
      minWidth: 100,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const unit = data?.unit;
        return unit ? (
          <Tooltip title={unit}>
            <Typography className="table-tooltip-text">{unit}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Billed"),
      field: "billed",
      minWidth: 160,
      maxWidth: 160,
      suppressMovable: false,
      suppressMenu: true,
      // cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      hide: details.sc_multi_bill_count === "0",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const nValueBilled = Number(data.total_sc_paid_bill_amt) / 100;
        const billed = formatter(formatAmount(nValueBilled)).value_with_symbol;
        const billPer = Number(data.total_sc_paid_bill_percentage);

        return billed ? (
          <div className="flex gap-1 overflow-hidden w-full justify-end">
            <Tooltip title={billed}>
              <Typography className="table-tooltip-text !max-w-[calc(100%-63px)] block truncate">
                {billed}
              </Typography>
            </Tooltip>
            <Typography className="table-tooltip-text">
              {`(${formatAmount(billPer)}%)`}
            </Typography>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Remaining"),
      field: "remaining",
      minWidth: 160,
      maxWidth: 160,
      suppressMovable: false,
      suppressMenu: true,
      // cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      hide: details.sc_multi_bill_count === "0",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const nValueRemaining =
          (Number(data?.total) - Number(data?.total_sc_paid_bill_amt)) / 100;
        const remaining = formatter(
          formatAmount(nValueRemaining)
        ).value_with_symbol;
        const reminingPer = 100 - Number(data.total_sc_paid_bill_percentage);

        return remaining ? (
          <div className="flex gap-1 overflow-hidden w-full justify-end">
            <Tooltip title={remaining}>
              <Typography className="table-tooltip-text !max-w-[calc(100%-63px)] block truncate">
                {remaining}
              </Typography>
            </Tooltip>
            <Typography className="table-tooltip-text">
              {`(${formatAmount(reminingPer)}%)`}
            </Typography>
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMovable: false,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: ({ data }: ISCWorkOrderTableCellRenderer) => {
        const nValue = Number(data?.total) / 100;
        const total = formatter(formatAmount(nValue)).value_with_symbol;
        return total ? (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: (params: ISCWorkOrderTableCellRenderer) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                handleTableAction(params, isReadOnly ? "view" : "edit");
              }}
            />
            {!isReadOnly &&
              details?.response !== "accept" &&
              details?.response !== "closed" && (
                <ButtonWithTooltip
                  tooltipTitle={_t("Delete")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-trash-can"
                  onClick={() => {
                    setSelectedSCOtherItemId(
                      Number(params?.data?.item_id) || 0
                    );
                    setIsDeleteConfirmOpen(true);
                  }}
                />
              )}
          </div>
        );
      },
    },
  ];

  const workOrderItemlistFilter = useMemo(() => {
    const workOrderItemNew = [...workOrderItem];
    if (filter.length === 0) {
      return workOrderItemNew.sort(
        (a, b) =>
          Number(a?.sub_contract_item_no) - Number(b.sub_contract_item_no)
      );
    }

    return workOrderItemNew
      .filter(
        (item) =>
          item?.item_type_key !== undefined &&
          filter.includes(item?.item_type_key)
      )
      .sort(
        (a, b) =>
          Number(a?.sub_contract_item_no) - Number(b?.sub_contract_item_no)
      );
  }, [workOrderItem, filter]);

  useEffect(() => {
    setIsActiveTable([workOrderItem?.length > 0 ? "1" : "0"]);
  }, [workOrderItem]);

  const totalData = useMemo(() => {
    let total = 0;
    if (workOrderItemlistFilter?.length > 0) {
      workOrderItemlistFilter.forEach((entry) => {
        const amount = entry.total ? parseFloat(entry.total) : 0;
        total += amount;
      });
    }
    return {
      total: total,
    };
  }, [workOrderItemlistFilter]);

  return (
    <>
      <div className="grid gap-2.5">
        {workOrderItem?.length > 0 && (
          <CollapseSingleTable
            title={_t(
              `${
                HTMLEntities.decode(
                  sanitizeString(`${module?.module_name} Items`)
                ) || "Work Order Items"
              }`
            )}
            totalRecordIcon={true}
            activeKey={isActiveTable}
            onChange={(key: string | string[]) => {
              setIsActiveTable(Array.isArray(key) ? key : [key]);
            }}
            totalRecord={
              formatter(formatAmount(+totalData?.total / 100)).value_with_symbol
            }
            children={
              <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-5 before:bg-gradient-to-r from-primary-500">
                {isWorkOrderItemLoading &&
                workOrderItemlistFilter?.length < 1 ? (
                  <Spin className="w-full h-20 flex items-center justify-center" />
                ) : (
                  <div className="ag-theme-alpine">
                    <StaticTable
                      className="static-table"
                      columnDefs={otherItemColumnDefs}
                      rowData={workOrderItemlistFilter}
                      noRowsOverlayComponent={() => (
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                        />
                      )}
                    />
                  </div>
                )}
              </div>
            }
          />
        )}
      </div>

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteSCOtherItem}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}

      {Boolean(selectedItemId) && (
        <AddSubContractItem
          isOpen={Boolean(selectedItemId)}
          moduleName={module?.module_name || ""}
          recordId={selectedItemId}
          allRecords={workOrderItem}
          isShowNextPre={true}
          type="changeOrder"
          isViewOnly={isViewOnly}
          onClose={() => setSelectedItemId(0)}
        />
      )}
    </>
  );
};

export default WorkOrdertems;
