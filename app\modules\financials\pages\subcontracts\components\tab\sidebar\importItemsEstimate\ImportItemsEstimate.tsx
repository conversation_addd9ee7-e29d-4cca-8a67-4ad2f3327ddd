import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { GridReadyEvent, SelectionChangedEvent } from "ag-grid-community";
// Hook Redux + store
import { useTranslation } from "~/hook";
import { getGConfig, getGModule<PERSON>y<PERSON><PERSON> } from "~/zustand";
import {
  useAppSCDispatch,
  useAppSCSelector,
} from "~/modules/financials/pages/subcontracts/redux/store";
import { fetchSCEstimateItemListApi } from "~/modules/financials/pages/subcontracts/redux/action";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Spin } from "~/shared/components/atoms/spin";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import ItemsTable from "~/shared/components/molecules/itemsTable/ItemsTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { defaultConfig } from "~/data";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";

const ImportItemsEstimate = ({
  isOpen,
  onClose,
  isDataUpdating,
  estimateItemHandler,
}: IImportItemsEstimateSubProps) => {
  const { _t } = useTranslation();
  const { module_id }: GConfig = getGConfig();
  const dispatch = useAppSCDispatch();
  const { formatter } = useCurrencyFormatter();
  const moduleEst: GModule | undefined = getGModuleByKey(
    defaultConfig.estimate_module
  );
  // this is will NF once testing done it will be merge on Dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);

  const { details }: ISCDetailsInitialState = useAppSCSelector(
    (state) => state.subContractsDetails
  );
  const {
    estimateItems,
    isEstitemsLoading,
    isEstitemsFetched,
  }: ISCImportItemsInitialState = useAppSCSelector(
    (state) => state.subContractsImportItemList
  );

  const { estimateItem }: ISCItemsInitialState = useAppSCSelector(
    (state) => state.subContractsItems
  );

  const [open, setOpen] = useState<boolean>(false);
  const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);
  const [defaultselectedData, setDefaultselectedData] = useState<
    ISCEstimateItem[]
  >([]);

  const [selectedItems, setSelectedItems] = useState<ISCEstimateItem[]>([]);
  const [selectedItemsByTable, setSelectedItemsByTable] = useState({});
  const [tableKey, setTableKey] = useState(0);

  const estimatelistFilter = useMemo(() => {
    setTableKey((prevKey) => prevKey + 1);
    if (filter.length === 0) {
      return estimateItems;
    }

    return estimateItems.filter(
      (item) =>
        item?.item_type_key !== undefined &&
        filter.includes(item?.item_type_key)
    );
  }, [JSON.stringify(estimateItems), JSON.stringify(filter)]);

  useEffect(() => {
    if (
      estimatelistFilter &&
      !!estimatelistFilter.length &&
      estimateItem?.length > 0
    ) {
      const defaultselectedData = estimatelistFilter.filter((jobItem) =>
        estimateItem?.some((section) =>
          section.items.some(
            (item) =>
              item?.reference_module_item_id?.toString() ===
              jobItem?.item_id.toString()
          )
        )
      );
      setDefaultselectedData(defaultselectedData);
    }
  }, [
    JSON.stringify(estimateItem), // Serialize to avoid shallow comparison
    JSON.stringify(estimatelistFilter),
  ]);

  const handleSelectionChanged = (
    event: SelectionChangedEvent,
    sectionId: string
  ) => {
    const selectedNodes = event.api.getSelectedNodes();
    const selectedData = selectedNodes.map((node) => node.data);
    setSelectedItemsByTable((prev) => ({ ...prev, [sectionId]: selectedData }));
  };

  useEffect(() => {
    if (selectedItemsByTable) {
      const result = Object?.values(
        selectedItemsByTable
      )?.flat() as ISCEstimateItem[];
      setSelectedItems(result);
    }
  }, [selectedItemsByTable]);

  useEffect(() => {
    if (!isEstitemsFetched) {
      const params: IGetImportItemsListParams = {
        module_id: module_id,
        project_id: Number(details.project_id),
      };
      dispatch(fetchSCEstimateItemListApi(params));
    }
  }, [isOpen, isEstitemsFetched]);

  const handleSaveEstimateItems = useCallback(async () => {
    const itemsToAddEstimateItem: ISCEstimateItem[] = [];
    let itemToBeDeleteEstimateItem: ISCEstimateItem[] = [];
    let itemToBeDeleteIdEstimateItem: number[];
    selectedItems.forEach((item) => {
      if (
        !defaultselectedData.some(
          (i: ISCEstimateItem) => i.item_id === item.item_id
        )
      ) {
        itemsToAddEstimateItem.push(item);
      }
    });

    defaultselectedData.forEach((item: ISCEstimateItem) => {
      if (!selectedItems.some((i) => i.item_id === item.item_id)) {
        itemToBeDeleteEstimateItem.push(item);
      }
    });

    itemToBeDeleteIdEstimateItem =
      itemToBeDeleteEstimateItem?.map((item: ISCEstimateItem) => {
        const mainItemId = estimateItem
          .find((section) =>
            section.items.some(
              (i) =>
                i.reference_module_item_id?.toString() ===
                item.item_id?.toString()
            )
          )
          ?.items.find(
            (i) =>
              i.reference_module_item_id?.toString() ===
              item.item_id?.toString()
          )?.item_id as number;

        return mainItemId;
      }) ?? [];

    estimateItemHandler({
      itemsToAddEstimateItem,
      itemToBeDeleteIdEstimateItem,
    });
    onClose();
  }, [JSON.stringify(selectedItems), JSON.stringify(defaultselectedData)]);

  const estimateSectionData: IEstimateSectionData[] = useMemo(() => {
    if (estimatelistFilter.length < 1) {
      return [];
    }
    return Object.values(
      estimatelistFilter.reduce(
        (
          acc: { [key: string]: IEstimateSectionData },
          item: ISCEstimateItem
        ) => {
          const { section_id, section_name } = item;

          if (!acc[section_id]) {
            acc[section_id] = {
              section_id,
              section_name,
              items: [],
            };
          }
          acc[section_id].items.push(item);
          return acc;
        },
        {}
      )
    );
  }, [JSON.stringify(estimatelistFilter)]);

  const handleGridReady = () => (params: GridReadyEvent) => {
    const gridApi = params.api;
    gridApi.forEachNode((node) => {
      const { item_id } = node.data;
      if (
        [...defaultselectedData, ...selectedItems].some(
          (item) => item?.item_id == item_id
        )
      ) {
        node.setSelected(true);
      }
    });
  };

  const columnDefs = [
    {
      headerName: _t(""),
      field: "",
      minWidth: 37,
      maxWidth: 37,
      cellStyle: { textAlign: "center" },
      headerCheckboxSelection: true,
      suppressMenu: true,
      checkboxSelection: true,
    },
    {
      headerName: _t("Item Name"),
      field: "item_name",
      minWidth: 150,
      flex: 2,
      cellClass: "ag-cell-left",
      headerClass: "ag-header-left",
      suppressMenu: true,
      cellRenderer: (params: ISubContractorEstimateItemsCellRenderer) => {
        const { data } = params;
        return HTMLEntities.decode(sanitizeString(data.subject)) ? (
          <Tooltip title={HTMLEntities.decode(sanitizeString(data.subject))}>
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.subject))}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("QTY"),
      field: "qty",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: ISubContractorEstimateItemsCellRenderer) => {
        const { data } = params;
        return data.quantity ? (
          <Tooltip title={data.quantity}>
            <Typography className="table-tooltip-text">
              {data.quantity}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Cost/Unit"),
      field: "cost_unit",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: ISubContractorEstimateItemsCellRenderer) => {
        const { data } = params;
        const nValue = Number(data?.modified_unit_cost) / 100;
        const unitCost = formatter(formatAmount(nValue)).value_with_symbol;
        const wOUnit = data?.unit ? `/${data?.unit}` : "";
        return unitCost || wOUnit ? (
          <Tooltip title={`${unitCost}${wOUnit}`}>
            <Typography className="table-tooltip-text">
              {`${unitCost}${wOUnit}`}
            </Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      cellRenderer: (params: ISubContractorEstimateItemsCellRenderer) => {
        const { data } = params;
        const total = formatter(
          formatAmount(Number(data?.no_mu_total) / 100)
        ).value_with_symbol;
        return total ? (
          <Tooltip title={total}>
            <Typography className="table-tooltip-text">{total}</Typography>
          </Tooltip>
        ) : (
          <>-</>
        );
      },
    },
  ];

  const noDataTable = useMemo(() => {
    return (
      <StaticTable
        className="static-table"
        rowSelection="multiple"
        columnDefs={columnDefs}
        rowMultiSelectWithClick={true}
        suppressRowClickSelection={true}
        rowData={[]}
        noRowsOverlayComponent={() => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
          />
        )}
      />
    );
  }, [JSON.stringify(estimateSectionData)]);

  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return (
  //     JSON.stringify(selectedItems) !== JSON.stringify(defaultselectedData)
  //   );
  // }, [selectedItems, defaultselectedData]);

  // const closeConfirmationModal = () => {
  //   isOpen;
  //   setIsConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDialogOpen(false);
  //   onClose();
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     onClose();
  //   } else {
  //     setIsConfirmDialogOpen(true);
  //   }
  // };

  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={750}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-calculator"
              />
            </div>
            <div className="flex justify-between items-center w-[calc(100%-40px)] pr-2">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t(
                  `${_t("Import Items from")} ${
                    HTMLEntities.decode(
                      sanitizeString(moduleEst?.module_name)
                    ) || _t("Estimate")
                  }`
                )}
              </Header>
              <Tooltip
                title={_t(
                  "You can only add items that are in your Approved Estimate."
                )}
                placement="bottom"
              >
                <Typography className="cursor-pointer text-[#B94A48] text-sm">
                  {_t("Missing Something?")}
                </Typography>
              </Tooltip>
            </div>
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        // closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <div className="py-4">
          <div className="sidebar-body overflow-y-auto h-[calc(100vh-132px)] px-4">
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <Header level={5} className="!text-sm !mb-0 text-[#4B4B4B]">
                  {moduleEst?.module_name
                    ? `${HTMLEntities.decode(
                        sanitizeString(moduleEst?.module_name)
                      )} ${_t("Items")}`
                    : _t("Estimate Items")}
                </Header>
                <ModuleItemsFilter
                  onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                  filter={filter}
                  onChangeFilter={setFilter}
                  openFilter={open}
                />
              </div>
              <div className="grid gap-4">
                {isEstitemsLoading ? (
                  <SidebarCardBorder addGap={true}>
                    <Spin className="w-full h-20 flex items-center justify-center" />
                  </SidebarCardBorder>
                ) : estimateSectionData?.length > 0 ? (
                  estimateSectionData.map((items) => (
                    <SidebarCardBorder addGap={true} key={items?.section_id}>
                      <div className="grid gap-2">
                        <ItemsTable
                          subTitle={items?.section_name}
                          tableProps={{
                            key: `${tableKey}-${items.section_id}`,
                            rowSelection: "multiple",
                            columnDefs: columnDefs,
                            rowData:
                              items?.items?.length > 0 ? items?.items : [],
                            rowMultiSelectWithClick: true,
                            suppressRowClickSelection: true,
                            onGridReady: handleGridReady(),
                            onSelectionChanged: (event) =>
                              handleSelectionChanged(event, items.section_id),
                            noRowsOverlayComponent: () => (
                              <NoRecords
                                image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                              />
                            ),
                          }}
                        />
                      </div>
                    </SidebarCardBorder>
                  ))
                ) : (
                  <SidebarCardBorder addGap={true}>
                    <div className="p-2 common-card">
                      <div className="ag-theme-alpine">{noDataTable}</div>
                    </div>
                  </SidebarCardBorder>
                )}
              </div>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              type="primary"
              className="w-full justify-center primary-btn"
              htmlType="submit"
              onClick={() => {
                handleSaveEstimateItems();
              }}
              isLoading={isDataUpdating}
              disabled={isDataUpdating}
              buttonText={_t("Save & Close")}
            />
          </div>
        </div>
      </Drawer>
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeConfirmationModal}
        />
      )} */}
    </>
  );
};

export default ImportItemsEstimate;
