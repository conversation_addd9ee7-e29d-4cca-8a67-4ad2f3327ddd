// Atoms
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Spin } from "~/shared/components/atoms/spin";
import { Tag } from "~/shared/components/atoms/tag";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";

// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// React
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

// Hook
import { useTranslation } from "~/hook";

// Other
import { useAppSTDispatch, useAppSTSelector } from "../../../redux/store";
import { ServiceTicketItem } from "../sidebar";
import {
  deleteSTItemsApi,
  updateSTItemsApi,
} from "../../../redux/action/serviceTicketServiceItemAction";
import {
  addSTCodeCostItemDetail,
  deleteSTItemDetail,
  updateSTItemDetail,
  updateSTItemIndex,
} from "../../../redux/slices/serviceTicketServiceItemSlice";
import { useParams } from "@remix-run/react";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { costDataBase, defaultConfig } from "~/data";
import {
  formatAmount,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import debounce from "lodash/debounce";
import {
  GridApi,
  GridOptions,
  GridReadyEvent,
  IRowNode,
  ValueSetterParams,
} from "ag-grid-community";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { qtyNumberCheck } from "~/shared/utils/helper/common";
import { ColDef } from "ag-grid-community";
import { getGlobalUser } from "~/zustand/global/user/slice";

import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { unitRoutes } from "~/route-services/unit.routes";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { addUnit, getUnitList } from "~/redux/action/unitActions";

const ServiceTicketItems = () => {
  const { _t } = useTranslation();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const [open, setOpen] = useState<boolean>(false);
  const gridApiRef = useRef<GridApi | null>(null);
  const { codeCostData }: IGetCostCodeList = useAppSTSelector(
    (state) => state.costCode
  );
  const params: RouteParams = useParams();
  const { formatter } = useCurrencyFormatter();
  const gConfig: GConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const {
    default_equipment_markup_percent,
    default_labor_markup_percent,
    default_material_markup_percent,
    default_other_item_markup_percent,
    default_sub_contractor_markup_percent,
    default_undefined_markup_percent,
  } = gSettings;
  const { module_key, module_singular_name }: GConfig = gConfig;
  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useAppSTDispatch();
  const [isSTItemToViewEditable, setIsSTItemToViewEditable] =
    useState<boolean>(true);
  const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);
  const [selectedServiceItem, setSelectedServiceItem] =
    useState<ISTItemDetails | null>(null);
  const [isOpenSelectMaterial, setIsOpenSelectMaterial] =
    useState<boolean>(false);
  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );
  const { serviceItems }: ISTItemInitialState = useAppSTSelector(
    (state) => state.serviceItemsDetails
  );
  const { itemTypes }: IGetItemTypeList = useAppSTSelector(
    (state) => state.itemTypes
  );
  const [serviceTicketItem, setServiceTicketItem] = useState<boolean>(false);
  const [STInputValues, setSTInputValues] = useState<ISTItemDetails[]>([]);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [editForm, setEditForm] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [initialSubMaterial, setInitialSubMaterial] = useState<
    Partial<CIDBItemSideData>[]
  >([]);

  const gridRef = useRef<ExtendedAgGridReact<ISTItemDetails> | null>(null);
  const [selectedData, setSelectedData] = useState<ISTItemDetails>();
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
    }
  }, []);

  const ST_ITEMS_LIST_OPTIONS = [
    {
      label: `Add Item to ${module_singular_name} `,
      value: "add_item_to_service_ticket",
      key: "add_item_to_service_ticket",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "Database",
      key: "import_from_cost_items",
    },
    {
      label: `Add Manual ${module_singular_name} Item`,
      value: "service_ticket_item",
      key: "add_service_ticket_item",
    },
  ];

  const applyFilter = () => {
    if (filter.length === 0) {
      setSTInputValues(serviceItems);
    } else {
      const filteredData = serviceItems.filter(
        (item) => item?.item_type_key && filter.includes(item.item_type_key)
      );
      setSTInputValues(filteredData);
    }
    setIsLoading(false);
  };

  const handleFilterChange = debounce(() => {
    applyFilter();
  }, 300);

  useEffect(() => {
    handleFilterChange();
    return handleFilterChange.cancel;
  }, [serviceItems]);

  useEffect(() => {
    setIsLoading(true);
    handleFilterChange();
    return handleFilterChange.cancel;
  }, [filter]);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const gridOptions: GridOptions = {
    onRowDragEnd: function (event) {
      const { node, overIndex } = event as {
        node: IRowNode;
        overIndex: number;
      };
      if (!gridOptions.api || !node) return;
      const rowData: ISTItemInitialState["serviceItems"] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));
      if (node.rowIndex !== null) {
        // Step 1: Move the dragged item within `rowData`
        const movedRecord = rowData.splice(node?.rowIndex, 1)[0]; // Remove the dragged record
        rowData.splice(overIndex, 0, movedRecord); // Insert it at the new position

        // Step 2: Trigger appropriate handler based on the row index and filter
        if (
          filter.length === 0 ||
          (filter.length && serviceItems.length <= 1)
        ) {
          handleDragAndDrop(rowData); // Handle the drag-and-drop update
        } else {
          // Step 3: If additional logic is needed, move items within `serviceItems`
          let nextRecord =
            rowData[node?.rowIndex + 1] || rowData[node?.rowIndex - 1]; // Get the next or previous record

          // Step 4: Only process if there are multiple service items
          if (serviceItems.length > 1) {
            // Step 5: Find the index of the moved record and the next/previous record
            const movedRecordIndex = serviceItems.findIndex(
              (i) => i.item_id === movedRecord.item_id
            );
            const nextRecordIndex = nextRecord
              ? serviceItems.findIndex((i) => i.item_id === nextRecord.item_id)
              : serviceItems.length - 1;

            // Step 6: Move the record within `serviceItems`
            const tempServiceItems = [...serviceItems]; // Create a copy of serviceItems
            const [removedItem] = tempServiceItems.splice(movedRecordIndex, 1); // Remove the moved item
            tempServiceItems.splice(nextRecordIndex, 0, removedItem); // Insert it at the new position

            // Step 7: Handle the updated service items
            handleDragAndDrop(tempServiceItems);
          }
        }
      }
    },
  };
  useEffect(() => {
    setSTInputValues(serviceItems);
  }, [serviceItems]);

  const costItemsFromDatabase = useMemo(() => {
    const data = serviceItems
      ?.filter((item: ISTItemDetails) => Number(item?.reference_item_id) > 0)
      ?.map((item: ISTItemDetails) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_id,
          item_id: item?.item_id as string,
        };
        switch (item?.item_type_key) {
          case "item_equipment":
            temData.equipment_id = item?.reference_item_id;
            temData.item_type = "162";
            temData.type_name = _t("Equipment");
            break;
          case "item_labour":
            temData.labor_id = item?.reference_item_id;
            temData.item_type = "163";
            temData.type_name = _t("Labor");
            break;
          case "item_material":
            temData.material_id = item?.reference_item_id?.toString();
            temData.item_type = "161";
            temData.type_name = _t("Material");
            break;
          case "item_sub_contractor":
            temData.contractor_id = item?.reference_item_id;
            temData.item_type = "164";
            temData.type_name = _t("Subcontractor");
            break;
          case "item_other":
            temData.other_item_id = item?.reference_item_id;
            temData.item_type = "165";
            temData.type_name = _t("Other Items");
            break;
          default:
            break;
        }
        return temData as Partial<CIDBItemSideData>;
      });
    return data;
  }, [serviceItems]);

  const handleSTItemDelete = async (id: number) => {
    const deleteItemParams: ISTDeleteItemReqDetails = {
      service_ticket_id: Number(params?.id),
      item_id: [id],
    };
    const response = (await deleteSTItemsApi(
      deleteItemParams as ISTDeleteItemReqDetails
    )) as IServiceTicketItemApiRes;
    if (response?.success) {
      dispatch(deleteSTItemDetail({ item_ids: [id] }));
      setSelectedServiceItem(null);
      setIsDeleteConfirmOpen(false);
      setIsDeleting(false);
    } else {
      notification.error({
        description: _t(
          response?.message ||
            `Something went wrong while deleting an item with ${id}`
        ),
      });
      setIsDeleting(false);
    }
  };

  const handleDeleteItem = async () => {
    try {
      if (!isDeleting && selectedServiceItem) {
        setIsDeleting(true);
        handleSTItemDelete(Number(selectedServiceItem?.item_id));
      }
    } catch (error: unknown) {
      notification.error({
        description: _t((error as Error)?.message),
      });
    }
  };

  const serviceItemsList = useMemo(() => {
    if (STInputValues) {
      return STInputValues.filter(
        (item: ISTItemDetails) =>
          filter.length === 0 ||
          (item?.item_type_key && filter.includes(item?.item_type_key))
      );
    }
    return [];
  }, [STInputValues, filter]);

  const handleOptionClick = (option: IDropdownMenuOption) => {
    if (option.value === "service_ticket_item") {
      setServiceTicketItem(true);
      setSelectedServiceItem(null);
      setIsSTItemToViewEditable(false);
      setEditForm(false);
    }
    if (option.value === "Database") {
      setIsOpenSelectMaterial(true);
    }
  };

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api as GridApi;
  };

  const uniCostDropdownHight =
    STInputValues?.length === 1
      ? 60
      : STInputValues?.length === 2
      ? 90
      : STInputValues?.length === 3
      ? 120
      : STInputValues?.length === 4
      ? 150
      : 180;

  const handleDragAndDrop = async (
    items: ISTItemInitialState["serviceItems"]
  ) => {
    const indexedItems: ISTItemInitialState["serviceItems"] = items.map(
      (row: ISTItemDetails, index) => ({
        ...row,
        service_ticket_item_no: index + 1,
      })
    );
    let combinedData = indexedItems;

    if (serviceItemsList && serviceItemsList?.length !== combinedData?.length) {
      const newData = serviceItemsList?.filter(
        (itemB) =>
          !indexedItems?.some((itemA) => itemA.item_id === itemB.item_id)
      );

      const startIndex = indexedItems?.length;
      let maxBillItemNo = startIndex;
      newData?.forEach((obj, index) => {
        maxBillItemNo++;
        obj.service_ticket_item_no = maxBillItemNo.toString();
      });

      combinedData = [...indexedItems, ...newData];
    }

    dispatch(updateSTItemIndex(indexedItems));

    const STItem = {
      service_ticket_id: Number(params?.id),
      from: "panel",
      items: combinedData,
    };

    const formData = getValuableObj(STItem);

    const response = (await updateSTItemsApi(
      formData
    )) as IServiceTicketItemApiRes;

    // if (response?.success === false) {
    //   dispatch(updateSTItemIndex(oldItems));
    // }
  };

  const addSTItemCallBack = useCallback(
    async (formData: ISTAddUpItemReqDetails) => {
      const response = (await updateSTItemsApi(
        formData as ISTAddUpItemReqDetails
      )) as IServiceTicketItemApiRes;
      if (response?.success) {
        dispatch(addSTCodeCostItemDetail(response?.data));
      } else {
        notification.error({
          description: _t(
            response?.message || "Something went wrong while adding item"
          ),
        });
      }
    },
    [updateSTItemsApi]
  );

  const deleteSTItemCallBack = useCallback(
    async (formData: ISTDeleteItemReqDetails) => {
      const response = (await deleteSTItemsApi(
        formData as ISTDeleteItemReqDetails
      )) as IServiceTicketItemApiRes;
      if (response?.success) {
        dispatch(deleteSTItemDetail({ item_ids: formData?.item_id }));
      } else {
        notification.error({
          description: _t(
            response?.message || "Something went wrong while adding item"
          ),
        });
      }
    },
    [updateSTItemsApi]
  );

  const getUnmatchedItems = (
    selectedData: CIDBItemSideData[],
    itemList: ISTItemDetails[]
  ) => {
    const getUniqeSideData = new Set(
      selectedData.map((item) => Number(item.item_id))
    );
    const filterdItemList = itemList.filter(
      (item) => !getUniqeSideData.has(Number(item.item_id))
    );

    return filterdItemList;
  };

  const handleItemFromCostItemDatabase = async (data: CIDBItemSideData[]) => {
    const removeunmatchdata = getUnmatchedItems(data, serviceItems);
    const removedItemIds = removeunmatchdata
      .filter((jobItem) => {
        if (
          jobItem.reference_item_id &&
          Number(jobItem.reference_item_id) > 0
        ) {
          return !data.some((item) => {
            if (item?.type_name === "Material") {
              return item.material_id === jobItem.reference_item_id;
            } else if (item?.type_name === "Labor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.labor_id)
              );
            } else if (item?.type_name === "Subcontractor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.contractor_id)
              );
            } else if (item?.type_name === "Equipment") {
              return (
                Number(jobItem.reference_item_id) === Number(item.equipment_id)
              );
            } else if (item?.type_name === "Other Items") {
              return (
                Number(jobItem.reference_item_id) === Number(item.other_item_id)
              );
            } else if (item?.type_name === "Groups") {
              return Number(jobItem.reference_item_id) === Number(item.item_id);
            }
          });
        }
      })
      .map((item) => Number(item.item_id));
    const mutationPromises = [];
    if (removedItemIds.length) {
      const STDeleteItem = {
        service_ticket_id: Number(params?.id),
        item_id: removedItemIds,
      };
      mutationPromises.push(deleteSTItemCallBack(STDeleteItem));
    }

    const itemsToBeAdd = data
      .filter(
        (jobItem) =>
          !serviceItems.some((item) => {
            if (jobItem?.type_name === "Material") {
              return (
                Number(item.reference_item_id) === Number(jobItem.material_id)
              );
            } else if (jobItem?.type_name === "Labor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.labor_id)
              );
            } else if (jobItem?.type_name === "Subcontractor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.contractor_id)
              );
            } else if (jobItem?.type_name === "Equipment") {
              return (
                Number(item.reference_item_id) === Number(jobItem.equipment_id)
              );
            } else if (jobItem?.type_name === "Other Items") {
              return (
                Number(item.reference_item_id) === Number(jobItem.other_item_id)
              );
            } else if (jobItem?.type_name === "Group") {
              return Number(item.reference_item_id) === Number(jobItem.item_id);
            }
          })
      )
      .map((item) => {
        let markup = item.markup;
        if (!markup && details?.project_id) {
          const matchedItemType = itemTypes.find(
            (type) => type.type_id === item.item_type
          );
          if (matchedItemType) {
            markup = matchedItemType.mark_up || markup;
          }
        }

        if (!markup) {
          if (
            item?.type_name === "Material" &&
            default_material_markup_percent
          ) {
            markup = default_material_markup_percent;
          } else if (
            item?.type_name === "Labor" &&
            default_labor_markup_percent
          ) {
            markup = default_labor_markup_percent;
          } else if (
            item?.type_name === "Subcontractor" &&
            default_sub_contractor_markup_percent
          ) {
            markup = default_sub_contractor_markup_percent;
          } else if (
            item?.type_name === "Equipment" &&
            default_equipment_markup_percent
          ) {
            markup = default_equipment_markup_percent;
          } else if (
            item?.type_name === "Other Items" &&
            default_other_item_markup_percent
          ) {
            markup = default_other_item_markup_percent;
          } else if (default_undefined_markup_percent) {
            markup = default_undefined_markup_percent;
          }
        }

        let temData: ISTItemDetails = {
          subject: item.name,
          quantity: item.quantity || 0,
          unit: item.unit,
          unit_cost: item.unit_cost
            ? Number(item.unit_cost)
            : Number(item?.unitCost) ?? "",
          cost_code_id: item.cost_code_id ?? item?.costCodeId ?? "",
          markup: markup ?? "",
          is_markup_percentage: 1,
          total: item.total,
          assigned_to: "",
          assigned_to_contact_id: 0,
          description: item.notes ?? "", // this fix as per PHP site
          internal_notes: item.internalNotes
            ? item.internalNotes
            : item.internal_notes ?? "",
          item_type: item?.item_type?.toString() || "",
          add_item_to_database: 1,
        };
        if (item?.type_name === "Material") {
          temData.reference_item_id = item?.material_id?.toString();
        } else if (item?.type_name === "Labor") {
          temData.reference_item_id = item?.labor_id?.toString();
        } else if (item?.type_name === "Subcontractor") {
          temData.reference_item_id = item?.contractor_id?.toString();
        } else if (item?.type_name === "Equipment") {
          temData.reference_item_id = item?.equipment_id?.toString();
        } else if (item?.type_name === "Other Items") {
          temData.reference_item_id = item?.other_item_id?.toString();
        } else if (item?.type_name === "Group") {
          temData.reference_item_id =
            item?.equipment_id?.toString() ||
            item?.material_id?.toString() ||
            item?.labor_id?.toString() ||
            item?.contractor_id?.toString() ||
            item?.other_item_id?.toString() ||
            item?.item_id?.toString();
        }
        return temData;
      });

    if (itemsToBeAdd.length) {
      const STItem = {
        service_ticket_id: Number(params?.id),
        from: "panel",
        is_single_item: 0,
        items: itemsToBeAdd,
      };

      const formData = getValuableObj(STItem);
      mutationPromises.push(addSTItemCallBack(formData));
    }

    await Promise.all(mutationPromises);
  };

  const handleInLineEditing = async (newValue: ISTItemDetails) => {
    const STItem = {
      service_ticket_id: Number(params?.id),
      is_single_item: 1,
      items: [
        {
          ...newValue,
        },
      ],
    };
    const newItems = serviceItems.map((item) => {
      if (Number(item.item_id) === Number(newValue.item_id)) {
        return { ...item, ...newValue };
      } else {
        return { ...item };
      }
    });
    dispatch(updateSTItemIndex(newItems));

    const formData = getValuableObj(STItem);

    const response = (await updateSTItemsApi(
      formData
    )) as IServiceTicketItemApiRes;
    if (response?.success === true) {
      const newItem = { ...STItem.items[0], ...response.data[0] };
      dispatch(updateSTItemDetail(newItem));
    } else {
      if (response.statusCode === 403) {
        notification.error({
          description: response.message,
        });
      }
      dispatch(updateSTItemIndex({ serviceItems }));
    }
  };

  const getTotalAmount = useCallback(
    (data: ISTItemDetails, qty: string, unit_cost: string | number) => {
      const markup_amount = Number(data.markup || 0);
      const is_markup_percentage = Number(data.is_markup_percentage);

      const total: number = Number(qty) * Number(unit_cost);
      let mainTotal: number = 0;

      if (is_markup_percentage === 1) {
        const markup = (total * markup_amount) / 100;
        mainTotal = Number(markup) + Number(total);
      } else {
        mainTotal = markup_amount;
      }
      return mainTotal;
    },
    []
  );

  const totalAmountForHeader = useMemo(() => {
    return serviceItemsList.reduce((acc: number, item: ISTItemDetails) => {
      const singleItemTotal = Number(item.total) ?? 0;
      return acc + singleItemTotal / 100;
    }, 0);
  }, [serviceItemsList]);

  const totalProfitAndPercentageForHeader: {
    profit: number;
    profitPercentage: number;
  } = useMemo(() => {
    let markupTotal = 0;
    let markupItemTotalWithoutTax = 0;

    serviceItemsList.forEach((item) => {
      const quantity = Number(item.quantity) || 0;
      const unitCost = Number(item.unit_cost || 0) / 100;
      const markup = (item.markup || "0") as string;
      const isMarkupPercentage =
        item.is_markup_percentage?.toString() as string;

      if (quantity !== 0 && unitCost > 0 && markup !== "") {
        const markupPercentage = parseInt(markup) / 100;

        if (isMarkupPercentage !== "") {
          if (markupPercentage !== 0) {
            markupItemTotalWithoutTax += quantity * unitCost;
          }

          if (parseInt(isMarkupPercentage) === 0 && markupPercentage !== 0) {
            markupTotal += markupPercentage - unitCost * quantity;
          } else {
            markupTotal += unitCost * quantity * markupPercentage;
          }
        }
      }
    });

    const markupPercentageTotal =
      100 * (markupTotal / markupItemTotalWithoutTax);
    const roundedMarkupPercentage = isNaN(markupPercentageTotal)
      ? 0
      : Math.round(markupPercentageTotal);

    return { profit: markupTotal, profitPercentage: roundedMarkupPercentage };
  }, [serviceItemsList]);

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        Equipment: "fa-regular fa-screwdriver-wrench",
        Material: "fa-regular fa-block-brick",
        Labor: "fa-regular fa-user-helmet-safety",
        Subcontractor: "fa-regular fa-file-signature",
        Other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  useEffect(() => {
    if (serviceItems?.length) {
      const jobsiteDataArr: any = [];

      serviceItems.forEach((data: any) => {
        jobsiteDataArr.push({
          name: data.subject || "",
          reference_item_id: data.reference_item_id || "",
          item_id: data.item_id || "",
          material_id: String(data.reference_item_id || ""),
          item_type: String(data.item_type || ""),
          type_name: data.item_type_name || "",
        });
      });
      setInitialSubMaterial(jobsiteDataArr);
    } else {
      setInitialSubMaterial([]);
    }
  }, [serviceItems]);
  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t(`${module_singular_name} Items`)}
        iconProps={{
          icon: "fa-solid fa-clipboard-user",
          containerClassName:
            "bg-[linear-gradient(180deg,#1CB5E01a_0%,#0086AB1a_100%)]",
          id: "service_tickets_items_icon",
          colors: ["#1CB5E0", "#0086AB"],
        }}
        headerRightButton={
          <div className="flex items-center gap-2 flex-wrap">
            <div>
              <Tag
                color="#0F732B1d"
                className="!text-[#0F732B] font-medium mx-auto !text-sm type-badge common-tag"
              >
                {_t("Profit")}:{" "}
                {
                  formatter(
                    formatAmount(totalProfitAndPercentageForHeader.profit || 0)
                  ).value_with_symbol
                }{" "}
                ({totalProfitAndPercentageForHeader.profitPercentage || 0}
                %)
              </Tag>
            </div>
            <div className="flex items-center gap-1.5 bg-blue-100 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
              <FontAwesomeIcon
                className="w-[17px] h-[17px]"
                icon="fa-solid fa-money-check-dollar"
              />
              <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90">
                {
                  formatter(formatAmount(totalAmountForHeader || 0))
                    .value_with_symbol
                }
              </Typography>
            </div>
            {!isReadOnly && (
              <div className="w-fit">
                <DropdownMenu
                  options={ST_ITEMS_LIST_OPTIONS.map((item) => {
                    return {
                      ...item,
                      onClick: () => handleOptionClick(item),
                    };
                  })}
                  buttonClass="w-fit h-auto"
                  contentClassName="add-items-drop-down"
                  placement="bottomRight"
                >
                  <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
                    <Typography className="text-primary-900 text-sm">
                      {_t("Add Item to") + " " + module_singular_name}
                    </Typography>
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900"
                      icon="fa-regular fa-chevron-down"
                    />
                  </div>
                </DropdownMenu>
              </div>
            )}
            <div className="md:relative absolute top-0 right-0">
              <ModuleItemsFilter
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                filter={filter}
                onChangeFilter={setFilter}
                openFilter={open}
              />
            </div>
          </div>
        }
        children={
          <div className="pt-2">
            <div className="ag-theme-alpine">
              {isLoading ? (
                <Spin className="w-full h-[180px] flex items-center justify-center" />
              ) : (
                <StaticTable
                  ref={gridRef}
                  className="static-table"
                  gridOptions={gridOptions}
                  stopEditingWhenCellsLoseFocus={true}
                  columnDefs={[
                    {
                      headerName: "",
                      minWidth: isReadOnly ? 0 : 30,
                      maxWidth: isReadOnly ? 0 : 30,
                      rowDrag: !isReadOnly,
                      field: "move",
                      cellClass:
                        "ag-cell-center ag-move-cell custom-move-icon-set",
                      cellRenderer: () => {
                        return (
                          <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
                            <FontAwesomeIcon
                              className="w-4 h-4 text-[#4b5a76]"
                              icon="fa-solid fa-grip-dots"
                            />
                          </div>
                        );
                      },
                    },
                    {
                      headerName: _t("Type"),
                      minWidth: 50,
                      maxWidth: 50,
                      field: "item_type_name",
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-center",
                      cellClass: "ag-cell-center",
                      cellStyle: { textAlign: "center" },
                      cellRenderer: (params: { data: ISTItemDetails }) => {
                        const { data } = params;
                        return data.item_type_name ? (
                          <>
                            {Object.keys(iconByItemTypeName).map(
                              (key, index) => {
                                if (data.item_type_name === key) {
                                  return (
                                    <Tooltip
                                      title={data.item_type_name}
                                      key={index}
                                    >
                                      <FontAwesomeIcon
                                        className="w-4 h-4 text-primary-900 mx-auto"
                                        icon={iconByItemTypeName[key]}
                                      />
                                    </Tooltip>
                                  );
                                }
                              }
                            )}
                          </>
                        ) : (
                          "-"
                        );
                      },
                    },
                    {
                      headerName: _t("Item Name"),
                      field: "subject",
                      minWidth: 150,
                      flex: 2,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      resizable: true,
                      suppressMovable: false,

                      suppressMenu: true,
                      cellRenderer: (row: { data: ISTItemDetails }) => {
                        const itemName = HTMLEntities.decode(
                          sanitizeString(row?.data?.subject)
                        );
                        return itemName ? (
                          <Tooltip title={itemName}>
                            <Typography className="table-tooltip-text">
                              {itemName}
                            </Typography>
                          </Tooltip>
                        ) : (
                          "-"
                        );
                      },
                      editable: () =>
                        gConfig?.module_read_only ? false : true,
                      valueGetter: (params: { data: ISTItemDetails }) => {
                        return HTMLEntities.decode(
                          sanitizeString(params?.data?.subject)
                        );
                      },
                      valueSetter: (params: {
                        data: ISTItemDetails;
                        newValue: string;
                      }) => {
                        if (params.newValue) {
                          const valuesToBeUpdate: ISTItemDetails = {
                            item_id: params?.data?.item_id,
                            subject: replaceDOMParams(
                              sanitizeString(params.newValue)
                            ),
                          };
                          handleInLineEditing(valuesToBeUpdate);
                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("Cost Code"),
                      field: "cost_code_name",
                      minWidth: 180,
                      flex: 2,
                      resizable: true,
                      suppressMovable: false,
                      suppressMenu: true,
                      cellEditor: "agRichSelectCellEditor",
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      cellRenderer: (params: { data: ISTItemDetails }) => {
                        const { data } = params;
                        let costCode = `${data?.cost_code_name ?? ""}${
                          data?.csi_code ? ` (${data?.csi_code})` : ""
                        }${data?.is_deleted === 1 ? ` (Archived)` : ""}`;
                        if (data?.cost_code_name?.includes(")")) {
                          costCode = `${data?.cost_code_name ?? ""}${
                            data?.is_deleted === 1 ? ` (Archived)` : ""
                          }`;
                        }

                        return (
                          <>
                            {costCode !== null && costCode ? (
                              <Tooltip title={costCode}>
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(costCode)
                                  )}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <div className="text-left">-</div>
                            )}
                          </>
                        );
                      },
                      editable: (params: CostItemTableCellRenderer) =>
                        gConfig?.module_read_only ? false : true,
                      cellEditorParams: {
                        values: codeCostData?.map(
                          (item: ICostCode) =>
                            `${item?.cost_code_name || "-"}` +
                            `${item?.csi_code ? ` (${item?.csi_code})` : ""}`
                        ),
                        filterList: true,
                        searchType: "matchAny",
                        allowTyping: true,
                        valueListMaxHeight: uniCostDropdownHight,
                      },
                      valueGetter: (params: { data: ISTItemDetails }) => {
                        return params?.data?.cost_code_name;
                      },
                      valueSetter: (params: {
                        data: ISTItemDetails;
                        newValue: string;
                      }) => {
                        if (params.newValue) {
                          const newCostCodeName = HTMLEntities.decode(
                            sanitizeString(params.newValue)
                          ).trim();
                          const selectedItem = codeCostData.find(
                            (item) =>
                              `${item?.cost_code_name || "-"}${
                                item?.csi_code ? ` (${item?.csi_code})` : ""
                              }` === newCostCodeName
                          );

                          const valuesToBeUpdate: ISTItemDetails = {
                            item_id: params?.data?.item_id,
                            cost_code_id: selectedItem?.code_id || "",
                            cost_code_name:
                              newCostCodeName === "Select Cost Code"
                                ? ""
                                : newCostCodeName,
                          };

                          handleInLineEditing(valuesToBeUpdate);

                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("QTY"),
                      field: "quantity",
                      minWidth: 80,
                      maxWidth: 80,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",

                      cellEditor: "agNumberCellEditor",
                      suppressKeyboardEvent,
                      cellRenderer: (params: { data: ISTItemDetails }) => {
                        const { data } = params;
                        const quantity =
                          formatter(
                            formatAmount(Number(data.quantity), {
                              isQuantity: true,
                            })
                          ).value || 0;

                        return (
                          <>
                            {/* {data.quantity ? ( */}
                            <Tooltip title={quantity}>
                              <Typography className="table-tooltip-text">
                                {quantity}
                              </Typography>
                            </Tooltip>
                            {/* ) : (
                              <div>-</div>
                            )} */}
                          </>
                        );
                      },
                      editable: () =>
                        gConfig?.module_read_only ? false : true,
                      valueGetter: (params: { data: ISTItemDetails }) => {
                        return params?.data?.quantity || 0;
                      },
                      valueSetter: (params: ValueSetterParams) => {
                        if (params && params.node) {
                          let nVal = params.newValue;
                          if (Number(nVal) < 0) {
                            notification.error({
                              message: "Notification Title",
                              description:
                                "Negative values are not allowed in Quantity field",
                            });
                            return false;
                          }
                          const updatedTotal: number = getTotalAmount(
                            params.data,
                            nVal,
                            params.data?.unit_cost as string | number
                          );

                          if (!nVal || nVal == null) {
                            const updatedData = {
                              ...params.data,
                              quantity: "",
                            };
                            params.node.setData(updatedData);
                            const valuesToBeUpdate: IWorkorderDetailsItem = {
                              item_id: params?.data?.item_id,
                              quantity: nVal,
                              total: updatedTotal,
                              order_item_no: params.data.order_item_no,
                            };
                            handleInLineEditing(valuesToBeUpdate);
                            return false;
                          }
                          const checkNum = qtyNumberCheck(nVal);
                          const integerPartLength = nVal
                            .toString()
                            .split(".")[0].length;

                          if (integerPartLength > 6 || !checkNum) {
                            notification.error({
                              description:
                                "Quantity should be less than or equal to 6 digits.",
                            });
                            return false;
                          }
                          if (!floatWithNegativeRegex.test(nVal)) {
                            notification.error({
                              description: _t(
                                "Decimal part should be less than or equal to 2 digits."
                              ),
                            });
                            return false;
                          }
                          if (nVal.toString().includes(".")) {
                            nVal = nVal.toFixed(2);
                          }

                          const updatedData = {
                            ...params.data,
                            quantity: nVal,
                          };
                          params.node.setData(updatedData);
                          const valuesToBeUpdate: IWorkorderDetailsItem = {
                            item_id: params?.data?.item_id,
                            quantity: nVal,
                            total: updatedTotal,
                            order_item_no: params.data.order_item_no,
                          };
                          handleInLineEditing(valuesToBeUpdate);
                        }

                        return true;
                      },
                    },
                    {
                      headerName: _t("Unit Cost"),
                      field: "unit_cost",
                      minWidth: 130,
                      maxWidth: 130,
                      suppressMovable: false,
                      suppressMenu: true,

                      suppressKeyboardEvent,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      cellEditor: "agNumberCellEditor",
                      cellRenderer: (params: { data: ISTItemDetails }) => {
                        const { data } = params;
                        return (
                          <>
                            {data.unit_cost ? (
                              <Tooltip
                                title={`${
                                  formatter(
                                    formatAmount(Number(data?.unit_cost) / 100)
                                  ).value_with_symbol
                                }`}
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        Number(data?.unit_cost) / 100
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            ) : (
                              <div>
                                {
                                  formatter(formatAmount("0.00"))
                                    .value_with_symbol
                                }
                              </div>
                            )}
                          </>
                        );
                      },
                      editable: () =>
                        gConfig?.module_read_only ? false : true,
                      valueGetter: (params: { data: ISTItemDetails }) => {
                        return Number(params?.data?.unit_cost) / 100;
                      },
                      valueSetter: (params: {
                        data: ISTItemDetails;
                        newValue: number | string;
                      }) => {
                        let newUnitCost = params.newValue
                          ? params.newValue.toString()
                          : "";
                        if (Number(params.newValue) < 0) {
                          notification.error({
                            description: _t(
                              "Negative values are not allowed for Unit Cost."
                            ),
                          });
                          return false;
                        }
                        const [integerPart, decimalPart] =
                          newUnitCost.split(".");
                        const fullStr = BigInt(
                          Math.floor(Number(newUnitCost))
                        ).toString();

                        if (fullStr.length > 10 || fullStr[0] === "-") {
                          notification.error({
                            description: _t(
                              "Unit cost should be less than 10 digits"
                            ),
                          });
                          return false;
                        }

                        if (decimalPart && decimalPart.length > 2) {
                          notification.error({
                            description: _t(
                              "Decimal part should be less than or equal to 2 digits"
                            ),
                          });
                          return false;
                        }

                        if (!isNaN(Number(newUnitCost))) {
                          if (
                            Number(newUnitCost) ===
                            Number(params.data.unit_cost) / 100
                          ) {
                            return false;
                          }

                          const updatedTotal: number = getTotalAmount(
                            params.data,
                            params.data.quantity as string,
                            newUnitCost as string | number
                          );

                          const valuesToBeUpdate: ISTItemDetails = {
                            item_id: params?.data?.item_id,
                            unit_cost: Number(newUnitCost) * 100,
                            total: updatedTotal * 100,
                            service_ticket_item_no:
                              params.data.service_ticket_item_no,
                          };
                          handleInLineEditing(valuesToBeUpdate);
                          return true;
                        }

                        return false;
                      },
                    },
                    {
                      headerName: _t("Unit"),
                      field: "unit",
                      minWidth: 100,
                      maxWidth: 100,
                      headerClass: "ag-header-left",
                      cellClass: "ag-cell-left",
                      suppressMovable: false,
                      suppressMenu: true,
                      ...(window.ENV.ENABLE_UNIT_DROPDOWN && {
                        suppressKeyboardEvent: (params) => {
                          if (params.event.key === "Enter") {
                            params.event.preventDefault();
                            return true; // Block Ag-Grid's default behavior
                          }
                          return false;
                        },
                        cellEditorParams: {
                          values: units,
                          onKeyDown: (
                            e: React.KeyboardEvent<HTMLInputElement>,
                            data: ISTItemDetails
                          ) => {
                            if (e.key === "Enter") {
                              const value = e?.currentTarget?.value?.trim();
                              const newType = onEnterSelectSearchValue(
                                e,
                                units?.map((unit) => ({
                                  label: unit?.name,
                                  value: "",
                                })) || []
                              );
                              if (newType) {
                                setNewUnitName(newType);
                                setSelectedData(data);
                              } else if (value) {
                                notification.error({
                                  description:
                                    "Records already exist, no new records were added.",
                                });
                              }
                            }
                          },
                        },
                        cellEditor: UnitCellEditor<ISTItemDetails>,
                      }),
                      cellRenderer: (params: { data: ISTItemDetails }) => {
                        const { data } = params;
                        return (
                          <>
                            {data.unit !== "" ? (
                              <Tooltip title={data.unit}>
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(data.unit)
                                  )}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <div>-</div>
                            )}
                          </>
                        );
                      },
                      editable: () =>
                        gConfig?.module_read_only ? false : true,
                      valueGetter: (params: { data: ISTItemDetails }) => {
                        return params?.data?.unit;
                      },
                      valueSetter: (
                        params: ValueSetterParams<ISTItemDetails>
                      ) => {
                        const newUnit = window.ENV.ENABLE_UNIT_DROPDOWN
                          ? (params?.newValue?.name?.trim() || "")?.toString()
                          : (params?.newValue || "")?.toString();

                        const [integerPart] = newUnit.split(".");

                        if (integerPart.length > 15) {
                          notification.error({
                            description: _t(
                              "Unit should be less than 15 characters"
                            ),
                          });
                          return false;
                        }

                        const valuesToBeUpdate: ISTItemDetails = {
                          item_id: params?.data?.item_id,
                          unit: newUnit,
                        };

                        handleInLineEditing(valuesToBeUpdate);
                        return false;
                      },
                    },
                    {
                      headerName: _t("Total"),
                      field: "total",
                      minWidth: 130,
                      maxWidth: 130,
                      suppressMovable: false,
                      suppressMenu: true,
                      headerClass: "ag-header-right",
                      cellClass: "ag-cell-right",
                      cellRenderer: (params: { data: ISTItemDetails }) => {
                        const { data } = params;
                        return (
                          <>
                            <Tooltip
                              title={
                                data.total === "0"
                                  ? formatter(formatAmount("0.00"))
                                      .value_with_symbol
                                  : `${
                                      formatter(
                                        formatAmount(Number(data?.total) / 100)
                                      ).value_with_symbol
                                    }`
                              }
                            >
                              <Typography className="table-tooltip-text">
                                {data.total === "0"
                                  ? formatter(formatAmount("0.00"))
                                      .value_with_symbol
                                  : `${
                                      formatter(
                                        formatAmount(
                                          (Number(data?.total) / 100).toFixed(2)
                                        )
                                      ).value_with_symbol
                                    }`}
                              </Typography>
                            </Tooltip>
                          </>
                        );
                      },
                    },
                    {
                      headerName: "",
                      field: "",
                      minWidth: 80,
                      maxWidth: 80,
                      headerClass: "ag-header-center",
                      cellRenderer: (row: { data: ISTItemDetails }) => {
                        return (
                          <div className="flex items-center gap-1.5 justify-center">
                            <ButtonWithTooltip
                              tooltipTitle={_t("View")}
                              tooltipPlacement="top"
                              icon="fa-solid fa-eye"
                              onClick={() => {
                                setSelectedServiceItem(row?.data);
                                setServiceTicketItem(true);
                                setIsSTItemToViewEditable(isReadOnly);
                                setEditForm(true);
                              }}
                            />
                            {!isReadOnly && (
                              <ButtonWithTooltip
                                tooltipTitle={_t("Delete")}
                                disabled={isReadOnly}
                                tooltipPlacement="top"
                                icon="fa-regular fa-trash-can"
                                onClick={() => {
                                  setSelectedServiceItem(row?.data);
                                  setIsDeleteConfirmOpen(true);
                                }}
                              />
                            )}
                          </div>
                        );
                      },
                    },
                  ]}
                  onGridReady={onGridReady}
                  rowData={serviceItemsList}
                  rowDragManaged={true}
                  noRowsOverlayComponent={() => (
                    <NoRecords
                      image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                    />
                  )}
                />
              )}
            </div>
          </div>
        }
      />
      {serviceTicketItem && (
        <ServiceTicketItem
          serviceTicketItem={serviceTicketItem}
          setServiceTicketItem={setServiceTicketItem}
          editForm={editForm}
          formData={selectedServiceItem}
          isViewOnly={isSTItemToViewEditable}
          setSelectedServiceItem={setSelectedServiceItem}
          handleUpdateForm={(isCloseForm: boolean) => {
            setEditForm(isCloseForm);
          }}
          serviceItemsList={serviceItemsList}
        />
      )}
      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={handleDeleteItem}
          onDecline={() => {
            setSelectedServiceItem(null);
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setSelectedServiceItem(null);
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}

      {isOpenSelectMaterial && (
        <CidbItemDrawer
          closeDrawer={() => {
            setIsOpenSelectMaterial(false);
          }}
          options={costDataBase}
          singleSelecte={false}
          addItem={(data) => {
            // setSubMaterial(data as ICIDBMaterial[]);
            // handleSaveMaterial(data as ICIDBMaterial[]);
            handleItemFromCostItemDatabase(data as CIDBItemSideData[]);
          }}
          itemTypes={itemTypes}
          openSendEmailSidebar={isOpenSelectMaterial}
          data={costItemsFromDatabase as Partial<CIDBItemSideData>[]}
          initialSubMaterial={initialSubMaterial as CIDBItemSideData[]}
          cidbModuleVIseIdAndValue={{
            [defaultConfig.material_key]: {
              id: defaultConfig.material_teb_id,
              value: defaultConfig.material_key,
            },
            [defaultConfig.equipment_key]: {
              id: defaultConfig.equipment_teb_id,
              value: defaultConfig.equipment_key,
            },
            [defaultConfig.labor_key]: {
              id: defaultConfig.labor_teb_id,
              value: defaultConfig.labor_key,
            },
            [defaultConfig.subcontractor_key]: {
              id: defaultConfig.subcontractor_teb_id,
              value: defaultConfig.subcontractor_key,
            },
            [defaultConfig.other_items_key]: {
              id: defaultConfig.other_items_teb_id,
              value: defaultConfig.other_items_key,
            },
          }}
        />
      )}
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.item_id === selectedData?.item_id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode?.setData({
                      ...currentRowNode.data,
                      unit: newUnitName,
                    });
                    try {
                      const STItem = {
                        service_ticket_id: Number(params?.id),
                        is_single_item: 1,
                        items: [
                          {
                            item_id: oldData?.item_id,
                            unit: newUnitName,
                          },
                        ],
                      };

                      const newItems = serviceItems.map((item) => {
                        if (Number(item.item_id) === Number(oldData.item_id)) {
                          return { ...item, ...oldData };
                        } else {
                          return { ...item };
                        }
                      });

                      dispatch(updateSTItemIndex(newItems));

                      const formData = getValuableObj(STItem);

                      const response = (await updateSTItemsApi(
                        formData
                      )) as IServiceTicketItemApiRes;
                      if (response?.success === true) {
                        const newItem = {
                          ...STItem.items[0],
                          ...response.data[0],
                        };
                        dispatch(updateSTItemDetail(newItem));
                      } else {
                        if (response.statusCode === 403) {
                          notification.error({
                            description: response.message,
                          });
                        }
                        currentRowNode?.setData(oldData);
                        dispatch(updateSTItemIndex({ serviceItems }));
                      }
                    } catch (error) {
                      currentRowNode?.setData(oldData);
                      dispatch(updateSTItemIndex({ serviceItems }));
                    }
                  }
                }
                const existingColDefs = api.getColumnDefs();
                if (!existingColDefs) return;

                const updatedColDefs = existingColDefs.map((col) =>
                  "field" in col && col.field === "unit"
                    ? {
                        ...col,
                        filterParams: {
                          values:
                            newUnits.map((unit) => ({
                              label: unit.name?.toString(),
                              value: unit.name?.toString(),
                            })) ?? [],
                        },
                        cellEditorParams: {
                          ...col.cellEditorParams,
                          values: newUnits,
                        },
                      }
                    : col
                );

                api.setColumnDefs(updatedColDefs);

                // Ensure the grid header re-renders
                api.refreshHeader();
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
};

export default ServiceTicketItems;
