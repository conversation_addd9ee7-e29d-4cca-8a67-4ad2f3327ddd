import { Outlet, redirect, useLocation, useNavigate } from "@remix-run/react";
import { getCommonSidebarCollapse, setCommonSidebarCollapse } from "~/zustand";
// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// ModuleSidebar
import { ModuleSidebar } from "~/shared/components/moduleSidebar";
import { useEffect, useState } from "react";
// FontAwesome File
import { MIDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/mleso-items/regular";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import {
  MLESOITEMS_SELECTOPTION,
  MLESOITEMS_SELECTOPTION_TAB_KEYS,
} from "~/modules/settings/costItemsDatabase/utils/constants";
import { MIDashboardSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/mleso-items/solid";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { routes } from "~/route-services/routes";

// Fort Awesome Library Add icons
MIDashboardRegularIconAdd();
MIDashboardSolidIconAdd();

export const loader = async ({ request }: TLoaderFunctionArgs) => {
  const url = new URL(request.url);
  const tab = url.pathname.split("/").pop();
  console.log(`\n File: #_layout.tsx -> Line: #27 ->  `, url.search);
  if ("/" + tab === url.pathname) {
    return redirect(
      url.pathname +
        "/" +
        MLESOITEMS_SELECTOPTION_TAB_KEYS.all_items +
        url.search
    );
  }

  return {};
};

const ManageMlesoItemsCom = () => {
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { checkGlobalModulePermission, getGlobalModuleById } =
    useGlobalModule();
  const [selectedOption, setSelectedOption] = useState<string>(
    MLESOITEMS_SELECTOPTION_TAB_KEYS.all_items
  );

  useEffect(() => {
    if (pathname) {
      const tab = pathname.split("/").pop();
      if (window.location.hash) {
        let tabString = "";
        let searchParamsKey = "";
        switch (window.location.hash) {
          case "#materialItemDiv":
            searchParamsKey = "materialId";
            tabString = MLESOITEMS_SELECTOPTION_TAB_KEYS.material;
            break;
          case "#equipmentItemDiv":
            searchParamsKey = "equipmentId";
            tabString = MLESOITEMS_SELECTOPTION_TAB_KEYS.equipment;
            break;
          case "#subContractorDiv":
            searchParamsKey = "subContractorId";
            tabString = MLESOITEMS_SELECTOPTION_TAB_KEYS.sub_contractor;
            break;
          case "#laborItemDiv":
            searchParamsKey = "laborId";
            tabString = MLESOITEMS_SELECTOPTION_TAB_KEYS.labor;
            break;
          case "#otherItemDiv":
            searchParamsKey = "otherItemId";
            tabString = MLESOITEMS_SELECTOPTION_TAB_KEYS.other;
            break;
        }
        if (tabString) {
          setSelectedOption(tabString);
          let searchParams = "";
          const id = new URLSearchParams(window.location.search).get("id");
          if (id) {
            searchParams = `?${searchParamsKey}=${id}`;
          }
          navigate(
            routes.MANAGE_MLESO_ITEMS.url + "/" + tabString + searchParams
          );
          return;
        }
      }
      if (tab && "/" + tab !== pathname) setSelectedOption(tab);
    }
  }, [pathname]);

  const module = getCurrentMenuModule();
  const { module_access, name } = module || {};

  useEffect(() => {
    if (
      !(module_access && !["no_access", "disabled"].includes(module_access)) &&
      name
    ) {
      notification.error({
        description: "You have no access to " + name,
      });
    }
  }, [module_access, name]);

  const moduleOptions: ICIDBTabOption[] = MLESOITEMS_SELECTOPTION.map(
    (item) => {
      if ("moduleTabId" in item) {
        const module = item.moduleTabId
          ? getGlobalModuleById(item.moduleTabId)
          : null;
        return {
          ...item,
          access: item.moduleTabId
            ? checkGlobalModulePermission(item.moduleTabId)
            : "no_access",
          module,
        };
      }

      return { ...item, access: "full_access" };
    }
  );

  const currentOption: ICIDBTabOption | undefined = moduleOptions.find(
    (option) => option.value === selectedOption
  );

  useEffect(() => {
    if (currentOption?.access === "no_access") {
      navigate(
        routes.MANAGE_MLESO_ITEMS.url +
          "/" +
          MLESOITEMS_SELECTOPTION_TAB_KEYS.all_items
      );
    }
  }, [currentOption]);

  return (
    <div
      className={`flex overflow-hidden ${
        !window.ENV.PAGE_IS_IFRAME
          ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
          : "h-screen"
      }`}
    >
      {!sidebarCollapse && (
        <div
          className={`sm:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
            sidebarCollapse
              ? "w-0 h-0"
              : window.ENV.PAGE_IS_IFRAME
              ? "w-full h-full"
              : "w-full h-[calc(100dvh-112px)]"
          }`}
          onClick={() => setCommonSidebarCollapse(true)}
        ></div>
      )}
      <ModuleSidebar
        sidebarCollapse={sidebarCollapse}
        onSidebarCollapse={setCommonSidebarCollapse}
        selectOptions={moduleOptions.filter(
          (item) => item.access !== "no_access"
        )}
        onSelectedOption={(value: string) => {
          navigate(value);
        }}
        selectedOption={selectedOption}
        allowBack={false}
        suppressInitialResize={true}
      />
      <div
        className={`ease-in-out duration-300 p-4 w-full overflow-y-auto ${
          sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
        }`}
      >
        <ReadOnlyPermissionMsg
          className="p-4 pt-0"
          view={
            module_access !== "full_access" ||
            currentOption?.access === "read_only"
          }
        />
        <div
          className={`px-2 common-card ${
            module_access !== "full_access" ||
            currentOption?.access === "read_only"
              ? "h-[calc(100dvh-214px)]"
              : "pb-1"
          }`}
        >
          {module_access &&
            !["no_access", "disabled"].includes(module_access) &&
            currentOption?.access !== "no_access" && (
              <Outlet
                context={{
                  options: moduleOptions,
                  currentOption,
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};

const ManageMlesoItemsTab = () => {
  return <ManageMlesoItemsCom />;
};
export default ManageMlesoItemsTab;

export { ErrorBoundary };
