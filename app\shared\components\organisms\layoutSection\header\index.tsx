import { memo, useEffect, useMemo, useRef, useState } from "react";
import { Link, useLoaderData, useSearchParams } from "@remix-run/react";
import { ClientOnly } from "remix-utils/client-only";

// Zustand
import {
  getGlobalAdminSettings,
  getGlobalCompanySettings,
} from "~/zustand/global/settings/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import {
  getGlobalFavoriteModules,
  useGlobalMenuModule,
} from "~/zustand/global/menuModules/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";

// shared
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Button } from "~/shared/components/atoms/button";
import { Breadcrumb } from "~/shared/components/atoms/breadCrumb";
import { Typography } from "~/shared/components/atoms/typography";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { Popover } from "~/shared/components/atoms/popover";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Others
import { apiRoutes, routes } from "~/route-services/routes";
import { loader } from "~/routes-flat/_module";
import SelectProject from "~/components/sidebars/select-project";
import { useTranslation } from "~/hook";
import HeaderMenu from "./header-menu";
import HeaderLinkSide from "~/components/sidebars/header-link/header-link";
import { getConvertedLink, sanitizeString } from "~/helpers/helper";
import { useAppDispatch } from "~/redux/store";
import { setOpenSendEmailSidebar } from "~/redux/slices/sendEmailSlice";
import { useZohoScript } from "~/hook/zoho";
import UserMenu from "./user-menu";
import MakeSuggestionClient from "./make-suggestion.client";
import HeaderSettingIcon from "./header-setting-icon";
import { setGlobalProject } from "~/zustand/global/config/action";
import { initialGlobalData } from "~/zustand/global/constants";

const MainHeader = () => {
  const { complies_flag } = useLoaderData<typeof loader>();

  const favoriteModules: IInitialGlobalData["menu"]["modifiedMenuModules"] =
    getGlobalFavoriteModules();
  const adminSettings: IInitialGlobalData["settings"]["admin_settings"] =
    getGlobalAdminSettings();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { user_id } = user || {};
  const companySettings: IInitialGlobalData["settings"]["company_settings"] =
    getGlobalCompanySettings();
  const { header_logo, company_name = "" } = companySettings || {};

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    module_key: current_module_key,
    name: current_module_name = "",
    is_enabled: current_is_enabled,
    vimeo_url = "",
    kb_url_name = "",
  } = currentMenuModule || {};

  const { checkGlobalMenuModulePermissionByKey } = useGlobalMenuModule();

  const [menuOpen, setMenuOpen] = useState<boolean>(false);
  const headerMenuButtonRef = useRef<HTMLButtonElement | null>(null);
  const [sidebarName, setSidebarName] = useState<string>("");
  const [compliesFlag, setCompliesFlag] = useState<string>("");
  const [companiesDrop, setCompaniesDrop] = useState<boolean>(false);
  const [companiesData, setCompaniesData] = useState<
    GetAssociatedEmailsForCompanyResponse["associate_emails"]
  >([]);
  const [searchParams] = useSearchParams();

  const destination = searchParams.get("from");
  let { _t } = useTranslation();

  useEffect(() => {
    getGCompaniesApi();
  }, []);

  const dispatch = useAppDispatch();

  const { openChat } = useZohoScript();

  useEffect(() => {
    const fetchData = async () => {
      const response = (await complies_flag) || "";
      setCompliesFlag(response);
    };
    fetchData();
  }, [complies_flag]);

  const vimeo = useMemo(() => {
    let tempVimeo: IVimeo = { target: "_blank" };
    if (current_is_enabled) {
      if (kb_url_name?.trim()) {
        tempVimeo.href = `https://kb.contractorforeman.com/${kb_url_name}/?kb_view=popup`;
      } else if (
        typeof current_module_key !== "undefined" &&
        ["index", "", "wepay_checkout", "stripe_checkout"].includes(
          current_module_key
        )
      ) {
        tempVimeo.href = `${CFConfig.vimeo_website_url}*********`;
      } else if (vimeo_url) {
        tempVimeo.href = `${CFConfig.vimeo_website_url}${vimeo_url}`;
      } else {
        tempVimeo.href = "#";
      }

      if (
        typeof window !== "undefined" &&
        ["beta", "dev", "localhost", "production"].includes(
          window.ENV.PANEL_TYPE || ""
        )
      ) {
        if (
          typeof current_module_key !== "undefined" &&
          [
            "index",
            "",
            "quickbook_log",
            "training_videos",
            "test_purchase_orders",
          ].includes(current_module_key)
        ) {
          tempVimeo.href = kb_url_name?.trim()
            ? `https://kb.contractorforeman.com/${kb_url_name}/?kb_view=popup`
            : tempVimeo.href || `${CFConfig.vimeo_website_url}*********`;
          tempVimeo.target = "_blank";
        } else if (!vimeo_url && !kb_url_name?.trim()) {
          tempVimeo.href = "#";
        }

        if (kb_url_name?.trim()) {
          tempVimeo.onClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
            console.log(event, "Regular click event triggered");
            const paramsKBVideo = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=1600,height=800,left=100,top=100`;
            window.open(
              `https://kb.contractorforeman.com/${kb_url_name}/?kb_view=popup`,
              current_module_key || "Dashboard",
              paramsKBVideo
            );
          };
        }
      }
    }
    return tempVimeo;
  }, [current_is_enabled, current_module_key, vimeo_url, kb_url_name]);

  const getGCompaniesApi = () => {
    try {
      const fetchData = async () => {
        const response = (await webWorkerApi({
          url: apiRoutes.GET_ASSOCIATED_EMAILS_FOR_COMPANY.url,
          method: "post",
          data: {
            getItem: "companies_data",
          },
        })) as GetAssociatedEmailsForCompanyResponse;
        if (response.success && user_id) {
          setCompaniesData(
            Object.values(response.associate_emails)?.filter(
              (type: GCompanyData) => type?.user_id !== user_id.toString()
            ) ?? []
          );
        } else {
          notification.error({
            description: response.message,
          });
        }
      };
      fetchData();
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const autoLogin = async (value: String) => {
    try {
      const response = (await webWorkerApi({
        url: apiRoutes.AUTO_LOGIN_USER.url,
        method: "post",
        data: {
          auth_token: value,
        },
      })) as AutoLoginUserApiResponse;
      if (response) {
        if (response.success) {
          setCompaniesDrop(false);
          window.location.reload();
        } else {
          notification.error({
            description: response.message,
          });
        }
      }
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  useEffect(() => {
    if (
      checkGlobalMenuModulePermissionByKey(CFConfig.project_module) ===
      "no_access"
    ) {
      setGlobalProject(initialGlobalData.config.global_project);
    }
  }, []);
  return (
    <>
      <header className="header fixed z-[1001] w-full top-0 text-sm">
        <div className="bg-white dark:bg-dark-800">
          <div className="flex items-center justify-between">
            <ul className="flex items-center md:h-[66px] h-12 md:w-auto w-full">
              <li className="flex h-full md:border-r border-[#c4c4c480] dark:border-white/10 md:w-auto w-full md:order-1 order-2">
                <Link
                  to={routes.DASHBOARD.url}
                  className="logo px-2.5 m-auto logo"
                  onClick={(e) => {
                    e.preventDefault();
                    window.location.href = window.ENV.PANEL_URL + "index.php";
                  }}
                >
                  <img
                    src={
                      header_logo ||
                      "https://cdn.contractorforeman.net/assets/images/logo.svg"
                    }
                    alt=""
                    className="xl:min-w-[180px] xl:w-[180px] md:min-w-[120px] md:w-[120px] md:max-h-[60px] max-h-[40px] object-contain"
                  />
                </Link>
              </li>
              {checkGlobalMenuModulePermissionByKey(CFConfig.project_module) !==
                "no_access" && (
                <li className="header-list-menu flex h-full md:border-r border-[#c4c4c480] dark:border-white/10 cursor-pointer md:order-2 order-3">
                  <SelectProject />
                </li>
              )}
              <li
                className="header-list-menu flex md:w-auto w-[100px] h-full md:border-r border-[#c4c4c480] dark:border-white/10 cursor-pointer md:order-3 order-1"
                ref={(ref) => {
                  headerMenuButtonRef.current = ref?.querySelector(
                    "button"
                  ) as HTMLButtonElement;
                }}
              >
                <Button
                  className={`px-3 py-3 !h-full !leading-[normal] !border-0 group/menu rounded-none outline-none active:!bg-primary-200/10 hover:!bg-primary-8 normal-case font-normal focus-visible:outline-none focus-within:outline-none dark:!bg-dark-800 dark:active:!bg-dark-900 ${
                    menuOpen ? "!bg-primary-8" : ""
                  }`}
                  onClick={() => setMenuOpen((prev: boolean) => !prev)}
                >
                  <div className="md:flex gap-0.5 fw-600 xl:min-w-[150px] xl:w-auto md:min-w-[130px] md:w-[130px] items-center justify-between hidden">
                    <div className="text-left max-w-[calc(100%-16px)] overflow-hidden">
                      <Typography className="text-[11px] text-[#666] dark:text-white/50 font-medium uppercase">
                        {_t("Menu")}
                      </Typography>
                      <div className="font-medium text-black dark:text-[#dcdcdd] !text-sm truncate">
                        {current_is_enabled
                          ? HTMLEntities.decode(_t(current_module_name)) ||
                            _t("Dashboard")
                          : _t(routes.ACCESS_DENIED.title)}
                      </div>
                    </div>
                    <FontAwesomeIcon
                      className={`text-black dark:text-[#dcdcdd] w-4 h-4 transition-all duration-300 ease-in-out group-hover/menu:rotate-180 ${
                        menuOpen ? "rotate-180" : ""
                      }`}
                      icon="fa-regular fa-chevron-down"
                    />
                  </div>
                  <div className="md:hidden block px-2 md:pt-0 pt-0.5">
                    <FontAwesomeIcon
                      className="w-5 h-5 text-primary-900 dark:text-[#dcdcdd]"
                      icon="fa-regular fa-bars"
                    />
                    <a href="" target="_top"></a>
                  </div>
                </Button>
              </li>
              {Boolean(favoriteModules?.length) && (
                <li className="md:flex hidden items-center favlink px-2.5 md:order-4">
                  <Tooltip title={_t("Favorites")}>
                    <FontAwesomeIcon
                      className="!text-deep-orange-500 w-[13.5px] h-3"
                      icon="fa-solid fa-star"
                    />
                  </Tooltip>
                  <ul className="all-menu-shortcut-ul ml-[5px] flex flex-wrap max-h-11 overflow-hidden">
                    {favoriteModules.map((module, key) => {
                      const href = !module.web_page.includes(
                        "javascript:void(0)"
                      )
                        ? getConvertedLink({
                            url: module.web_page,
                            moduleKey: module.module_key,
                          })
                        : "#";

                      const onClick: React.MouseEventHandler<
                        HTMLAnchorElement
                      > = (e) => {
                        if (module.module_key === CFConfig.send_email_module) {
                          e.stopPropagation();
                          if (module.can_write) {
                            setSidebarName(module.module_key);
                            dispatch(setOpenSendEmailSidebar(true));
                          } else {
                            notification.error({
                              description:
                                "You don’t have access to Send Email.",
                            });
                          }
                        }
                        setMenuOpen(false);
                      };

                      return (
                        <li className="mr-2.5 last:mr-0" key={key}>
                          <Link
                            to={href}
                            onClick={onClick}
                            className="stop-all-ajax-call !text-black dark:text-white/90 relative mr-0 after:absolute after:left-0 after:bottom-0 after:w-0 after:hover:w-full after:h-px after:bg-primary-900 after:ease-in after:duration-300 dark:after:bg-white/90"
                          >
                            {HTMLEntities.decode(_t(module.name))}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                </li>
              )}
            </ul>
            <ul className="flex items-center md:h-[66px] h-12 whitespace-nowrap md:justify-normal justify-end">
              <li className="w-auto h-full px-2.5 training-info border-r border-[#c4c4c480] dark:border-white/10 text-xs lg:block hidden">
                <div className="flex flex-col h-full justify-center">
                  <div
                    className="flex items-center gap-0.5 training-session-header text-[11px] text-primary-gray-80 dark:text-white/50"
                    dangerouslySetInnerHTML={{
                      __html: sanitizeString(
                        adminSettings?.training_session_header || ""
                      ),
                    }}
                  />
                  <div
                    className="flex items-center gap-0.5 training-session-top text-[11px] text-primary-gray-80 dark:text-[#dcdcdd]"
                    dangerouslySetInnerHTML={{
                      __html: sanitizeString(
                        adminSettings?.training_session_top || ""
                      ),
                    }}
                  />
                  <div
                    className="flex items-center gap-0.5 training-session-bottom text-[11px] text-primary-gray-80 dark:text-[#dcdcdd]"
                    dangerouslySetInnerHTML={{
                      __html: sanitizeString(
                        adminSettings?.training_session_bottom || ""
                      ),
                    }}
                  />
                </div>
              </li>
              <li className="border-r h-full border-[#c4c4c480] dark:border-white/10 w-[62px] lg:flex hidden">
                <Tooltip title={_t("Available 7am-8pm EST")} placement="bottom">
                  <Button
                    onClick={openChat}
                    className="h-full border-none !leading-[normal] relative m-auto p-0 hover:!text-primary-900"
                  >
                    <div className="text-center">
                      <div className="relative w-fit m-auto">
                        <FontAwesomeIcon
                          className="text-2xl dark:text-[#dcdcdd] w-8"
                          icon="fa-regular fa-comments"
                        />
                        <div className="absolute top-0.5 right-[7px] rounded-full h-1.5 w-1.5 bg-[#ff1818] animate-pulse-custom"></div>
                      </div>
                      <small className="fw-normal text-xs dark:text-[#dcdcdd] mt-0.5 block">
                        {_t("Live Chat")}
                      </small>
                    </div>
                  </Button>
                </Tooltip>
              </li>
              <li className="dropdown profile-dropdown">
                <UserMenu />
              </li>
            </ul>
          </div>
        </div>
        {destination !== "android" || destination !== "ios" || !destination ? (
          <div className="bg-primary-900 dark:bg-dark-900 md:py-2.5 py-1.5 h-auto md:h-[46px]">
            <div className="flex items-center justify-center w-full">
              <div className="md:w-1/3 w-2/3 px-4">
                <Breadcrumb
                  className="menu-breadcrumb"
                  items={[
                    {
                      title: (
                        <a
                          href={routes.DASHBOARD.url}
                          onClick={(e) => {
                            if (e.ctrlKey || e.metaKey || e.button === 1) {
                              return;
                            }
                            e.preventDefault();
                            window.location.href =
                              window.ENV.PANEL_URL + "index.php";
                          }}
                        >
                          <Tooltip
                            title={_t("Dashboard")}
                            placement="right"
                            className="after:left-6"
                          >
                            <FontAwesomeIcon
                              icon="fa-duotone fa-solid fa-home"
                              className="w-3.5 h-3.5 pt-[5px] !text-white dark:text-[#dcdcdd] home-icon"
                              fill="#fff"
                            />
                          </Tooltip>
                        </a>
                      ),
                    },
                    {
                      title: (
                        <Typography
                          color="white"
                          className={`font-normal !mb-0 tracking-wide cursor-default lg:text-base text-sm !text-white dark:!text-[#dcdcdd]`}
                        >
                          {current_is_enabled
                            ? HTMLEntities.decode(_t(current_module_name)) ||
                              _t("Dashboard")
                            : _t(routes.ACCESS_DENIED.title)}
                        </Typography>
                      ),
                    },
                  ]}
                />
              </div>
              <div className="md:w-2/3 w-2/4 company-change justify-center items-center md:flex hidden">
                {compliesFlag === "1" ? (
                  <Popover
                    open={companiesDrop}
                    onOpenChange={(newOpen) => {
                      setCompaniesDrop(newOpen);
                    }}
                    placement="bottom"
                    content={
                      <div className="md:w-[500px] w-[calc(100vh-300px)] py-1 project-select">
                        {companiesData.map(
                          (
                            {
                              auth_token,
                              company_name,
                              header_logo,
                            }: GCompanyData,
                            key: number
                          ) => (
                            <div
                              className="flex items-center py-2 px-3 border-b border-[#1F2F3] last:border-b-0 cursor-pointer hover:bg-primary-gray-20"
                              key={key}
                              onClick={() => autoLogin(auth_token)}
                            >
                              <AvatarProfile
                                user={{
                                  name: HTMLEntities.decode(company_name),
                                  image: header_logo,
                                }}
                                className="w-7 h-7"
                                iconClassName="text-xs font-semibold"
                              />
                              <div className="w-[calc(100%-50px)]">
                                <Tooltip
                                  title={HTMLEntities.decode(company_name)}
                                >
                                  <div className="!max-w-fit ">
                                    <Typography className="!mb-0 pl-2 !text-primary-900 !text-sm font-normal text-ellipsis whitespace-nowrap overflow-hidden w-full">
                                      {HTMLEntities.decode(company_name)}
                                    </Typography>
                                  </div>
                                </Tooltip>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    }
                    trigger="click"
                  >
                    <div
                      className="flex items-center gap-2 cursor-pointer px-1"
                      onClick={() => {
                        setCompaniesDrop(!companiesDrop);
                        !companiesDrop && getGCompaniesApi();
                      }}
                    >
                      {company_name.trim() && (
                        <Typography className="text-white font-semibold lg:text-base text-sm">
                          {HTMLEntities.decode(company_name)}
                        </Typography>
                      )}
                      <FontAwesomeIcon
                        icon="fa-regular fa-angle-down"
                        className="text-base w-4 h-4 pt-1 !text-white"
                      />
                    </div>
                  </Popover>
                ) : (
                  company_name.trim() && (
                    <Header
                      level={5}
                      className="lg:!text-[17px] !text-[15px] !mb-0 font-medium tracking-wide leading-[26px] !text-white dark:!text-[#dcdcdd] text-center"
                    >
                      {HTMLEntities.decode(company_name)}
                    </Header>
                  )
                )}
              </div>
              <div className="w-1/3">
                <ul className="breadcrumbs flex items-center justify-end md:px-4 px-2 text-[#C3C3C3] leading-5">
                  {/* Notes: This Code is responsive so do not remove this */}
                  {/* <li
                  className={`flex md:hidden ${
                    compliesFlag === "1" &&
                    "after:inline-block after:align-middle after:content-['/'] after:mx-0.5 after:text-[20px] mr-2"
                  } `}
                >
                  {compliesFlag === "1" && (
                    <Popover
                      open={companiesDrop}
                      onOpenChange={handleVisibleChange}
                      placement="bottom"
                      content={
                        <div className="md:w-[500px] w-[calc(100vh-300px)] py-1 project-select">
                          {companiesData.map(
                            (
                              {
                                auth_token,
                                company_name,
                                header_logo,
                              }: GCompanyData,
                              key: number
                            ) => (
                              <div
                                className="flex items-center py-2 px-3 border-b border-[#1F2F3] last:border-b-0 cursor-pointer hover:bg-primary-gray-20"
                                key={key}
                                onClick={() => autoLogin(auth_token)}
                              >
                                <AvatarProfile
                                  user={{
                                    name: HTMLEntities.decode(company_name),
                                    image: header_logo,
                                  }}
                                  className="w-7 h-7 text-xs"
                                />
                                <div className="w-[calc(100%-50px)]">
                                  <Tooltip
                                    title={HTMLEntities.decode(company_name)}
                                  >
                                    <div className="!max-w-fit ">
                                      <Typography className="!mb-0 pl-2 !text-primary-900 !text-sm font-normal text-ellipsis whitespace-nowrap overflow-hidden w-full">
                                        {HTMLEntities.decode(company_name)}
                                      </Typography>
                                    </div>
                                  </Tooltip>
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      }
                      trigger="click"
                    >
                      <div
                        className="flex items-center gap-2 cursor-pointer px-1"
                        onClick={() => setCompaniesDrop(!companiesDrop)}
                      >
                        <FontAwesomeIcon
                          icon="fa-regular fa-swap-arrows"
                          className="text-base w-3.5 h-3.5 !text-[#C3C3C3] hover:text-white"
                          onClick={() => getGCompaniesApi()}
                        />
                      </div>
                    </Popover>
                  )}
                </li> */}
                  {/* Note: This logic need when setting module developed */}
                  {/* {current_module_key === "app_setting" && <SearchSetting />} */}
                  <li className="flex items-center">
                    <div className="header-icon-div">
                      <a
                        className="mr-2 cursor-pointer"
                        href={vimeo.href || "#"}
                        target={vimeo.target || "_blank"}
                        rel="noopener noreferrer"
                        onClick={(
                          event: React.MouseEvent<HTMLAnchorElement>
                        ) => {
                          if (
                            event.ctrlKey ||
                            event.metaKey ||
                            event.button === 1
                          ) {
                            if (vimeo.href && vimeo.href !== "#") {
                              if (kb_url_name?.trim()) {
                                window.open(
                                  `https://kb.contractorforeman.com/${kb_url_name}/?kb_view=popup`,
                                  "_blank"
                                );
                                event.preventDefault();
                              }
                              return;
                            }
                            event.preventDefault();
                            return;
                          }
                          if (kb_url_name?.trim() && vimeo.onClick) {
                            event.preventDefault();
                            vimeo.onClick(event);
                          }
                        }}
                        onAuxClick={(
                          event: React.MouseEvent<HTMLAnchorElement>
                        ) => {
                          if (
                            event.button === 1 &&
                            vimeo.href &&
                            vimeo.href !== "#"
                          ) {
                            event.preventDefault();
                            if (kb_url_name?.trim()) {
                              window.open(
                                `https://kb.contractorforeman.com/${kb_url_name}/?kb_view=popup`,
                                "_blank"
                              );
                            } else {
                              window.open(vimeo.href, "_blank");
                            }
                          }
                        }}
                        onContextMenu={(
                          event: React.MouseEvent<HTMLAnchorElement>
                        ) => {
                          if (!vimeo.href || vimeo.href === "#") {
                            event.preventDefault();
                          }
                        }}
                        aria-label={_t("Watch a 'How To' Video")}
                      >
                        <Tooltip title={_t("Watch a 'How To' Video")}>
                          <FontAwesomeIcon
                            icon="fa-solid fa-video-camera"
                            className="text-base w-3.5 h-3.5 !text-[#C3C3C3] hover:!text-white"
                          />
                        </Tooltip>
                      </a>
                    </div>
                  </li>
                  <li className="flex items-center before:inline-block before:align-middle before:content-['/'] before:mx-0.5 before:text-[20px]">
                    <ClientOnly>{() => <MakeSuggestionClient />}</ClientOnly>
                  </li>
                  {checkGlobalMenuModulePermissionByKey(
                    CFConfig.app_setting_module
                  ) !== "no_access" &&
                    checkGlobalMenuModulePermissionByKey(
                      CFConfig.app_setting_module
                    ) !== "read_only" && <HeaderSettingIcon />}
                </ul>
              </div>
            </div>
          </div>
        ) : (
          <></>
        )}
      </header>
      <HeaderLinkSide sidebarName={sidebarName} />
      {(destination !== "android" || destination !== "ios" || !destination) && (
        <HeaderMenu
          setSidebarName={setSidebarName}
          menuOpen={menuOpen}
          setMenuOpen={setMenuOpen}
          headerMenuButtonRef={headerMenuButtonRef}
        />
      )}
    </>
  );
};

export default memo(MainHeader);
