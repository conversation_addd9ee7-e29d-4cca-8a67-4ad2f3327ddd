import { useTranslation } from "~/hook";
import * as Yup from "yup";
import delay from "lodash/delay";

// Fortawesome
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Header } from "~/shared/components/atoms/header";
import { Typography } from "~/shared/components/atoms/typography";
import { Link } from "~/shared/components/atoms/link";
import { Popover } from "~/shared/components/atoms/popover";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { InputField } from "~/shared/components/molecules/inputField";
import { GoogleAutoCompleteInput } from "~/shared/components/molecules/googleAutoCompleteInput";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";

import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getGModuleDashboard, getGSettings } from "~/zustand";
import { Number, replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { useFormik } from "formik";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { useAppProDispatch, useAppProSelector } from "../../redux/store";

import { customDataTypesByKey } from "~/utils/constasnts";
import { addCustomData } from "~/redux/action/customDataAction";
import { addCustomProjectType } from "../../redux/slices/proDashWidgetsSlice";
import { defaultConfig } from "~/data";

import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import {
  COPY_TEMPLATES_MODULE,
  copyTemplatesModuleOption,
  initialLatLong,
} from "../../utils/constants";
import {
  AddProjectApi,
  getProjectTemplatesListApi,
} from "../../redux/action/addProjectAction";
import { Form, useNavigate } from "@remix-run/react";
import { getCompanyAddressApi } from "../../redux/action/proDashAction";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
import { routes } from "~/route-services/routes";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { debounce } from "lodash";

const AddProject = ({
  setAddProjectOpen,
  addProjectOpen,
}: IAddProjectProps) => {
  const { _t } = useTranslation();
  const currentModule = getCurrentMenuModule();
  const {
    module_id = 0,
    singular_name = "",
    module_key = "",
  } = currentModule || {};

  const appSettings = getGlobalAppSettings();

  const {
    is_custom_project_id = "",
    custom_project_id = "",
    project_prefix = "",
    need_project_prefix = "",
  } = appSettings || {};

  const { date_format }: GSettings = getGSettings();

  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const navigate = useNavigate();
  const { projectTypes } = useAppProSelector((state) => state.proDashboard);
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();

  const [contactAddress, setContactAddress] = useState<boolean>(false);

  const [companyAddress, setCompanyAddress] = useState<ICompanyAddressData>({
    street: "",
    zip_code: "",
    city: "",
    state: "",
    latitude: 0,
    longitude: 0,
  });

  const addressInfoRef = useRef<HTMLDivElement | null>(null);
  const [locationLatLong, setLocationLatLong] =
    useState<ILatLongItf>(initialLatLong);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [IsOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();
  const [projectTemplates, setProjectTemplates] =
    useState<IProjectTemplate[]>();
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerEmail[]>();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const dispatch = useAppProDispatch();

  const projectStatus = gModuleDashboard.module_setting?.module_status?.map(
    (item: ModuleStatus & { is_status?: string }) => {
      if (item?.is_status === "1" && item.name && item.key) {
        return {
          label: item.name,
          value: item.key,
        };
      }
    }
  );

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();

  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
    } as IRequestCustomFieldForSidebar
  );

  const initValues: IAddProjectData = {
    customer_id: "",
    project_id: "",
    project_name: "",
    project_type: "",
    default_tax_rate_id: 0,
    project_status: "pending",
    access_to_custom_fields: 0,
    address1: "",
    address2: "",
    city: "",
    state: "",
    zip: "",
    type: "",
    project_template_id: "",
    copy_templates_module: COPY_TEMPLATES_MODULE,
  };

  const initialValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const baseValidationSchema = {
    project_id: Yup.string().required("This field is required."),
    project_name: Yup.string().required("This field is required."),
    project_type: Yup.string().required("This field is required."),
    customer_id: Yup.string().required("This field is required."),
  };

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const validationSchema = Yup.object().shape({
    ...baseValidationSchema,
    ...(componentList.length && !isNoAccessCustomField
      ? { custom_fields: Yup.object().shape(dynamicValidationSchema) }
      : {}),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      let isCustomFieldValid = true;
      if (componentList.length && !isNoAccessCustomField) {
        for (let index = 0; index < componentList.length; index++) {
          const value =
            formik?.values?.custom_fields?.[componentList[index].name];
          const multiple = componentList[index].multiple;
          const typeComponent = componentList[index].type;
          if (multiple || typeComponent === "checkbox-group") {
            if (!value?.length) {
              isCustomFieldValid = false;
              break;
            }
          } else if (!value) {
            isCustomFieldValid = false;
            break;
          }
        }
      }

      if (!formik.isValid || !isCustomFieldValid) return;

      const formData = {
        ...formik.values,
        custom_fields:
          formik.values.custom_fields && !isNoAccessCustomField
            ? formatCustomFieldForRequest(
                formik.values.custom_fields,
                componentList,
                date_format
              ).custom_fields
            : undefined,
        access_to_custom_fields:
          componentList.length && !isNoAccessCustomField ? 1 : 0,
      };
      try {
        const response = (await AddProjectApi(
          getValuableObj(formData)
        )) as IAddProjectApiRes;
        if (response.success) {
          navigate(
            `${routes.MANAGE_PROJECT.url}/${response?.data?.inserted_id}`
          );
        } else {
          notification.error({
            description: response?.message || "Something went wrong!",
          });
        }
      } catch (error) {
        notification.error({
          description: (error as Error)?.message || "Something went wrong!",
        });
      }
    },
  });

  const {
    handleSubmit,
    handleChange,
    setFieldValue,
    values,
    errors,
    touched,
    validateForm,
  } = formik;

  const handleCloseDrawer = () => {
    setAddProjectOpen(false);
    setSelectedCustomer([]);
    formik.resetForm();
  };

  const projectTypesOptions = useMemo(() => {
    if (projectTypes) {
      return projectTypes
        .filter((item) => item.item_type === "188")
        .map((item) => {
          return {
            label: HTMLEntities.decode(sanitizeString(item.name)) ?? "",
            value: item.item_id,
          };
        });
    } else {
      return [];
    }
  }, [projectTypes]);

  const addItemObject: IAddItems = {
    text: "Add Name: Type Name & Press Enter",
    icon: "fa-regular fa-plus",
  };

  const fetchProjectTemplate = async () => {
    const response =
      (await getProjectTemplatesListApi()) as IProjectTemplatesListApiRes;
    if (response.success) {
      setProjectTemplates(response.data);
    } else {
      notification.error({
        description: response.message,
      });
    }
  };

  const getCompanyAddress = async () => {
    const response = (await getCompanyAddressApi()) as ICompanyAddressApiRes;
    if (response.success) {
      setCompanyAddress(response.data);
    } else {
      notification.error({
        description: response.message,
      });
    }
  };

  useEffect(() => {
    getCompanyAddress();
    fetchProjectTemplate();
  }, []);

  useEffect(() => {
    if (Number(is_custom_project_id) === 2 && last_primary_id) {
      formik.setValues({
        ...formik.values,
        // project_id: (need_project_prefix == 1
        //   ? project_prefix + Number(need_to_increment) + Number(last_primary_id)
        //   : Number(need_to_increment) + Number(last_primary_id)
        // ).toString(),
        project_id: last_primary_id.toString(),
      });
    } else if (Number(is_custom_project_id) === 0) {
      formik.setValues({
        ...formik.values,
        project_id:
          need_project_prefix == 1
            ? project_prefix + custom_project_id.toString()
            : custom_project_id.toString(),
      });
    } else {
      formik.setValues({
        ...formik.values,
        project_id: "",
      });
    }
  }, [
    is_custom_project_id,
    need_to_increment,
    need_project_prefix,
    project_prefix,
    last_primary_id,
    addProjectOpen,
  ]);

  useEffect(() => {
    if (Number(is_custom_project_id) == 2 && addProjectOpen) {
      setModuleAutoIncrementId(module_id, module_key);
    }
  }, [is_custom_project_id, addProjectOpen]);

  const handleAddProjectType = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        projectTypesOptions || []
      );
      if (newType) {
        setCustomDataAdd({
          itemType: customDataTypesByKey.projectTypeKeyID,
          name: HTMLEntities.encode(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addCustomProjectType(cDataRes?.data));
        delay(() => {
          setFieldValue("project_type", cDataRes?.data?.item_id);
          formik.setFieldError("project_type", "");
        }, 500);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  const handleCustomerSelection = (data: CustomerEmail[]) => {
    if (data) {
      setSelectedCustomer(data);
      if (data[0]) {
        const customer = data[0];

        setFieldValue("customer_id", customer.user_id);
        if (
          (customer.address1 && customer.address1 !== "") ||
          (customer.address2 && customer.address2 !== "") ||
          (customer.city && customer.city !== "") ||
          (customer.state && customer.state !== "") ||
          (customer.zip && customer.zip !== "")
        ) {
          setContactAddress(true);
        }
      } else {
        setFieldValue("address1", "");
        setFieldValue("address2", "");
        setFieldValue("city", "");
        setFieldValue("state", "");
        setFieldValue("zip", "");
        setFieldValue("customer_id", "");
      }
    } else {
      setFieldValue("customer_id", "");
      setSelectedCustomer([]);
    }
  };

  const customerAddress = useMemo(() => {
    if (selectedCustomer && selectedCustomer[0]) {
      const customer = selectedCustomer[0];
      return {
        street1: customer.address1 ?? "",
        street2: customer.address2 ?? "",
        cityStZip: [
          customer.city ?? "",
          customer.state ?? "",
          customer.zip ?? "",
        ].join(", "),
      };
    } else {
      return {
        street1: "",
        street2: "",
        cityStZip: "",
      };
    }
  }, [selectedCustomer]);

  const googleMapAddress = useMemo(() => {
    return (
      values.address1 !== "" ||
      values.address2 !== "" ||
      values.city !== "" ||
      values.state !== "" ||
      values.zip !== ""
    );
  }, [values]);

  const copyToClipboard = () => {
    setFieldValue("address1", customerAddress.street1);
    setFieldValue("address2", customerAddress.street2);
    setFieldValue("city", customerAddress.cityStZip.split(", ")[0]);
    setFieldValue("state", customerAddress.cityStZip.split(", ")[1]);
    setFieldValue("zip", customerAddress.cityStZip.split(", ")[2]);
    setContactAddress(false);
  };

  const contactAddressChange = (newOpen: boolean) => {
    setContactAddress(newOpen);
  };

  const handleSelectedLocation = (
    googleMapsPlaces: google.maps.places.PlaceResult | null
  ) => {
    const place = googleMapsPlaces as unknown as PlaceDetails;
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });

    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const address1 = `${streetNumber}${getAddressComponent(place, "route")}`;
    const address2 = "";
    const city =
      getAddressComponent(place, "locality") ||
      getAddressComponent(place, "sublocality");
    const getState = getAddressComponent(place, "administrative_area_level_1");
    const zip = getAddressComponent(place, "postal_code");

    formik.setValues({
      ...formik.values,
      city,
      zip,
      state: getState,
      address1,
      address2,
    });
  };

  const handleSelectedLocationOnMap = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });
    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const updateValues: IProjectCustomerAddress = {
      street1: `${streetNumber}${getAddressComponent(place, "route")}`,
      street2: "",
      city: getAddressComponent(place, "locality"),
      state: getAddressComponent(place, "administrative_area_level_1"),
      zip: getAddressComponent(place, "postal_code"),
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    };

    formik.setValues({
      ...formik.values,
      city: updateValues.city,
      zip: updateValues.zip,
      state: updateValues.state,
      address1: updateValues.street1,
      address2: updateValues.street2,
    });
  };
  const handleInputChange = debounce((e) => {
    formik.setValues({ ...formik.values, [e.target.name]: e.target.value });
  }, 5000);
  const handleMarkerMoved = useCallback((lat: number, lng: number) => {
    if (!window.google || !window.google.maps) return;

    const geocoder = new window.google.maps.Geocoder();

    geocoder.geocode({ location: { lat, lng } }, (results, status) => {
      if (status === "OK" && results && results[0]) {
        const addressComponents = results[0].address_components;

        const getComponent = (type: string): string =>
          addressComponents.find((c) => c.types.includes(type))?.long_name ||
          "";

        const city =
          getComponent("locality") ||
          getComponent("sublocality") ||
          getComponent("administrative_area_level_2") ||
          getComponent("administrative_area_level_1") ||
          getComponent("country") ||
          "";

        const state = getComponent("administrative_area_level_1") || "";
        const zip = getComponent("postal_code") || "";
        const streetNumber = getComponent("street_number");
        const route = getComponent("route");

        const address1 =
          [streetNumber, route].filter(Boolean).join(" ") ||
          results[0].formatted_address ||
          "";

        formik.setFieldValue("address1", address1, false);
        formik.setFieldValue("address2", "", false);
        formik.setFieldValue("city", city, false);
        formik.setFieldValue("state", state, false);
        formik.setFieldValue("zip", zip, false);
        formik.setFieldValue("latitude", lat, false);
        formik.setFieldValue("longitude", lng, false);
      } else {
        console.warn("Geocoder failed due to: " + status);
      }
    });
  }, []);

  return (
    <>
      <div
        className={`fixed h-screen top-0 right-0 z-[1002] bg-white shadow-[0_5px_15px] shadow-black/50 ease-in-out duration-300 ${
          addProjectOpen ? "translate-x-0 w-full" : "translate-x-full w-0"
        }`}
      >
        <div className="md:w-[718px] w-full h-screen ml-auto overflow-hidden shadow-[0_5px_15px] shadow-black/50 relative z-20">
          <div className="py-2.5 px-4 border-gray-200 dark:border-white/10 border-b flex justify-between items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
                <FontAwesomeIcon
                  className="w-4 h-4"
                  icon="fa-regular fa-house"
                />
              </div>
              <Header
                level={5}
                className="!text-[17px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t(
                  `Add ${
                    replaceDOMParams(sanitizeString(singular_name)) ?? "Project"
                  } Details`
                )}
              </Header>
            </div>
            <CloseButton
              onClick={() => {
                setAddProjectOpen(false);
                handleCloseDrawer();
              }}
            />
          </div>
          <Form method="post" onSubmit={handleSubmit} noValidate>
            <div className="overflow-y-auto p-4 h-[calc(100vh-122px)] flex flex-col gap-4">
              <SidebarCardBorder addGap={true} className="overflow-visible">
                <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                  <div className="w-full">
                    <InputField
                      disabled={is_custom_project_id == 0}
                      isRequired={true}
                      label={_t("Project ID")}
                      name="project_id"
                      id="project_id"
                      maxLength={15}
                      value={
                        is_custom_project_id == 0
                          ? "Save To View"
                          : values?.project_id
                      }
                      labelPlacement="top"
                      errorMessage={touched.project_id ? errors.project_id : ""}
                      autoComplete="off"
                      onChange={(e) => {
                        handleChange(e);
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      isRequired={true}
                      label={_t("Project Name")}
                      name="project_name"
                      id="project_name"
                      value={values?.project_name}
                      labelPlacement="top"
                      errorMessage={
                        touched.project_name ? errors.project_name : ""
                      }
                      autoComplete="off"
                      onChange={(e) => {
                        setFieldValue(
                          "project_name",
                          e.target.value.trimStart()
                        );
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                  <div className="w-full">
                    <SelectField
                      label={_t("Project Status")}
                      labelPlacement="top"
                      name="project_status"
                      id="project_status"
                      value={values?.project_status}
                      options={
                        projectStatus
                          ? projectStatus.filter((item) => item !== undefined)
                          : []
                      }
                      onChange={(value) => {
                        setFieldValue("project_status", value);
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={_t("Project Type")}
                      name="project_type"
                      id="project_type"
                      labelPlacement="top"
                      isRequired={true}
                      showSearch
                      addItem={addItemObject}
                      options={projectTypesOptions}
                      onInputKeyDown={(e) => handleAddProjectType(e)}
                      errorMessage={
                        touched.project_type && !isConfirmDialogOpen
                          ? errors.project_type
                          : ""
                      }
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      value={
                        HTMLEntities.decode(
                          sanitizeString(values.project_type)
                        ) ?? ""
                      }
                      onChange={(value) => {
                        setFieldValue("project_type", value);
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>
                <div className="w-full relative">
                  <ButtonField
                    label={_t("Customer")}
                    name="customer_id"
                    id="customer_id"
                    labelPlacement="top"
                    statusProps={{ status: "button" }}
                    required={true}
                    value={
                      selectedCustomer && selectedCustomer[0]
                        ? selectedCustomer[0].display_name
                        : ""
                    }
                    onClick={() => setIsOpenSelectCustomer(true)}
                    avatarProps={
                      selectedCustomer && selectedCustomer[0]
                        ? {
                            user: {
                              name: HTMLEntities.decode(
                                sanitizeString(selectedCustomer[0].display_name)
                              ),
                              image: selectedCustomer[0].image || "",
                            },
                          }
                        : undefined
                    }
                    errorMessage={touched.customer_id ? errors.customer_id : ""}
                    addonBefore={
                      selectedCustomer && selectedCustomer[0] ? (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={async (e) => {
                              e.stopPropagation();
                              await setcontactId(
                                selectedCustomer?.[0]?.user_id as number
                              );
                              setIsContactDetails(true);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={
                              selectedCustomer[0]?.user_id?.toString() || ""
                            }
                            directoryTypeKey={
                              selectedCustomer[0]
                                ? getDirectaryKeyById(
                                    selectedCustomer[0].type === 1
                                      ? 2
                                      : Number(selectedCustomer[0].type),
                                    undefined
                                  )
                                : ""
                            }
                          />
                        </div>
                      ) : null
                    }
                  />
                  <Popover
                    content={
                      <div className="min-w-[272px]">
                        <Typography className="block text-sm py-2 px-3.5 bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                          {_t("Copy Customer Address")}
                        </Typography>
                        <div className="py-2 px-3.5">
                          <Typography className="mb-2">
                            {_t("Copy Customer Address to Project Address?")}
                          </Typography>
                          <Typography className="block text-13">
                            <Typography className="font-medium text-13">
                              {_t("Street")}
                              {": "}
                            </Typography>
                            {customerAddress.street1}
                          </Typography>
                          <Typography className="block text-13">
                            <Typography className="font-medium text-13">
                              {_t("Street")}
                              {" 2: "}
                            </Typography>
                            {customerAddress.street2}
                          </Typography>
                          <Typography className="block text-13">
                            <Typography className="font-medium text-13">
                              {_t("City")}/{_t("ST")}/{_t("Zip")}
                              {": "}
                            </Typography>
                            {customerAddress.cityStZip}
                          </Typography>
                          <div className="flex gap-2 justify-center mt-3">
                            <Button
                              type="primary"
                              className="w-fit"
                              onClick={copyToClipboard}
                            >
                              {_t("Yes")}
                            </Button>
                            <Button onClick={() => setContactAddress(false)}>
                              {_t("No")}
                            </Button>
                          </div>
                        </div>
                      </div>
                    }
                    placement="bottom"
                    trigger="click"
                    open={contactAddress}
                    onOpenChange={contactAddressChange}
                  >
                    <Button
                      type="primary"
                      className="w-full h-0 p-0 border-0 absolute"
                    ></Button>
                  </Popover>
                </div>
                <div className="w-full">
                  <GoogleAutoCompleteInput
                    label={_t("Street")}
                    labelPlacement="top"
                    onSelectValue={(
                      item: google.maps.places.PlaceResult | null
                    ) => handleSelectedLocation(item)}
                    onSetValue={(val) => {
                      setFieldValue("address1", val.trim());
                    }}
                    name="address1"
                    value={values.address1}
                    addonAfterIcon={true}
                    addonAfter={
                      <Tooltip title={_t("Location")}>
                        <div>
                          <Link href={"#"} className="!leading-3" title={""}>
                            <FontAwesomeIcon
                              className="w-3.5 h-3.5 text-primary-900 dark:text-[#dcdcdd] cursor-pointer"
                              icon="fa-regular fa-location-dot"
                            />
                          </Link>
                        </div>
                      </Tooltip>
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Street 2")}
                    name="address2"
                    id="address2"
                    value={values?.address2}
                    labelPlacement="top"
                    autoComplete="off"
                    onChange={(e) => {
                      setFieldValue("address2", e.target.value.trimStart());
                    }}
                    onBlur={formik.handleBlur}
                  />
                </div>
                <div className="grid md:grid-cols-3 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("City")}
                      name="city"
                      id="city"
                      value={values?.city}
                      labelPlacement="top"
                      autoComplete="off"
                      onChange={(e) => {
                        setFieldValue("city", e.target.value.trimStart());
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("State")}
                      name="state"
                      id="state"
                      value={values?.state}
                      labelPlacement="top"
                      autoComplete="off"
                      onChange={(e) => {
                        setFieldValue("state", e.target.value.trimStart());
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Zip/Postal Code")}
                      name="zip"
                      id="zip"
                      maxLength={11}
                      value={values?.zip}
                      labelPlacement="top"
                      autoComplete="off"
                      onChange={(e) => {
                        setFieldValue("zip", e.target.value.trimStart());
                      }}
                      onBlur={formik.handleBlur}
                    />
                  </div>
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Use Project Templates")}
                    name="project_template_id"
                    id="project_template_id"
                    labelPlacement="top"
                    showSearch
                    informationProps={{
                      icon: "fa-regular fa-circle-info",
                      message: _t(
                        "Tip: Save a Project as a template for future use by clicking on Project > Details > Project Configuration and selecting Save as Template or Complated projects."
                      ),
                    }}
                    options={
                      projectTemplates
                        ? projectTemplates.map((template) => {
                            return {
                              label: `${
                                Number(template?.save_as_template) === 1
                                  ? "(Template)"
                                  : ""
                              } ${template.project_name} (${
                                template.project_id
                              })`,
                              value: template.id?.toString(),
                            };
                          })
                        : []
                    }
                    value={values.project_template_id}
                    onChange={(value) => {
                      setFieldValue("project_template_id", value);
                      setFieldValue(
                        "copy_templates_module",
                        COPY_TEMPLATES_MODULE
                      );
                    }}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    onBlur={formik.handleBlur}
                  />
                </div>
                {values.project_template_id !== "" && (
                  <div className="w-full">
                    <InlineField
                      label={_t(
                        "Select the items that should be copied to the new Project."
                      )}
                      labelPlacement="top"
                      field={
                        <CheckboxGroupList
                          formInputClassName="!p-0 mt-2"
                          name="copy_templates_module"
                          view="row"
                          onChange={(value) => {
                            setFieldValue("copy_templates_module", value);
                          }}
                          className="gap-y-1 grid sm:grid-cols-3 grid-cols-2 w-full"
                          value={values.copy_templates_module}
                          options={copyTemplatesModuleOption}
                        />
                      }
                    />
                  </div>
                )}
              </SidebarCardBorder>

              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>

            <div className="p-5 flex items-center justify-center w-full">
              <PrimaryButton
                type="primary"
                className="w-full justify-center primary-btn"
                htmlType="submit"
                buttonText={_t(
                  `Create ${
                    replaceDOMParams(sanitizeString(singular_name)) ?? "Project"
                  }`
                )}
                onClick={() => {
                  validateForm(values);
                  setIsSubmit(true);
                }}
                isLoading={formik.isSubmitting}
                disabled={formik.isSubmitting || loadingCustomField}
              ></PrimaryButton>
            </div>
          </Form>
        </div>
        <div className="absolute w-full h-full top-0 right-0 left-0 bottom-0 md:flex justify-start bg-white z-10 hidden">
          <div className="relative w-[calc(100%-718px)] h-full bg-[#e5e3df] ">
            <GoogleMap
              ref={addressInfoRef}
              cssStyle={{ height: "100vh" }}
              addressInfo={{}}
              mapAddress={{
                address1: googleMapAddress
                  ? values.address1
                  : companyAddress.street,
                address2: googleMapAddress ? values.address2 : "",
                city: googleMapAddress ? values.city : companyAddress.city,
                state: googleMapAddress ? values.state : companyAddress.state,
                zip: googleMapAddress ? values.zip : companyAddress.zip_code,
              }}
              isEditable={true}
              title={[
                values.address1 !== ""
                  ? values.address1
                  : companyAddress.street,
                values.address2 !== "" ? values.address2 : "",
                values.city !== "" ? values.city : companyAddress.city,
                values.state !== "" ? values.state : companyAddress.state,
                values.zip !== "" ? values.zip : companyAddress.zip_code,
              ]
                .filter((value) => !!value)
                .join(", ")}
              defaultCenter={true}
              temperature_scale={0}
              handleSelectedLocation={handleSelectedLocationOnMap}
              handleInputChange={handleInputChange}
              handleMarkerMoved={handleMarkerMoved}
              isDraggable={true}
            />
          </div>
        </div>
      </div>

      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setIsConfirmDialogOpen(false)}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => setIsConfirmDialogOpen(false)}
        />
      )}

      {IsOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={IsOpenSelectCustomer}
          closeDrawer={() => {
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            handleCustomerSelection(data as CustomerEmail[]);
          }}
          options={[defaultConfig.customer_key]}
          selectedCustomer={selectedCustomer}
          groupCheckBox={true}
        />
      )}

      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => setIsContactDetails(false)}
          contactId={contactId}
          additional_contact_id={0}
          isShowMap={true}
        />
      )}
    </>
  );
};

export default memo(AddProject);
