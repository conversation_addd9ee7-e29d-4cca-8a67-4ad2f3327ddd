import { ICellRendererParams } from "ag-grid-community";
declare global {
  interface IApproveEstimateItemTableProps {
    setBudgetItem: React.Dispatch<React.SetStateAction<boolean>>;
    setSOVItemsToBeView: React.Dispatch<
      React.SetStateAction<ISOVBudgetItemsData>
    >;
    callSummaryApi: Function;
    handleDeleteClick: (id: number) => void;
  }

  interface ISOVItemsTableProps {
    items: ISOVBudgetItemsData[];
    setBudgetItem: React.Dispatch<React.SetStateAction<boolean>>;
    setSOVItemsToBeView: React.Dispatch<
      React.SetStateAction<ISOVBudgetItemsData>
    >;
    noRecordImage: string;
    isRowSortable?: boolean;
    isReadOnly: boolean;
    isEstimateSection?: boolean;
    gridKey?: string;
    handleDeleteClick?: (id: number) => void;
  }
  interface ISOVItemsTanleCellRenderer
    extends Omit<ICellRendererParams, "data"> {
    data: ISOVBudgetItemsData;
  }
  interface TCostByTypeChartParams {
    series: string[];
    seriesIndex: number;
    dataPointIndex: number;
    w: ChartFunW;
  }

  type TChartOption = {
    key: string;
    label: string;
    colors: string[];
  };

  type TOtherItemsTableProps = {
    callSummaryApi: Function;
    callProjectSOVItemsApi?: Function;
  };

  interface TOtherItemsCellRenderer extends Omit<ICellRendererParams, "data"> {
    data: ISOVBudgetItemsData;
  }

  type TItemTableRender = {
    details: Partial<IProjectDetails>;
    otherItems: ISOVBudgetItemsData[];
    callSummaryApi: Function;
    isPercentageBilling: boolean;
    setEditProjectItem: React.Dispatch<React.SetStateAction<boolean>>;
    setItemToEdit: (data: Partial<ISOVBudgetItemsData> | undefined) => void;
    setDeleteItemId: React.Dispatch<React.SetStateAction<number>>;
    gridKey: string;
    computeTotalRow: () => void;
  };

  interface ICellRendererProps {
    formatter: TCurrencyFormatterHandler;
    isPercentageBilling: boolean;
    isReadOnly?: boolean;
    setEditProjectItem: TItemTableRender["setEditProjectItem"];
    setItemToEdit: TItemTableRender["setItemToEdit"];
    setDeleteItemId: TItemTableRender["setDeleteItemId"];
    _t: (key: str) => string;
  }

  type BaseCellRendererProps<Extras extends Partial<ICellRendererProps> = {}> =
    TOtherItemsCellRenderer & Extras;
}
