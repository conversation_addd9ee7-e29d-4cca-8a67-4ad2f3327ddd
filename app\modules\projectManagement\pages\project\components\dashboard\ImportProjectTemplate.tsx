import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useTranslation } from "~/hook";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { Number, sanitizeString } from "~/helpers/helper";
import { SelectField } from "~/shared/components/molecules/selectField";
import { useModuleAccess } from "../../hook/useModuleAcces";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { getProjectTemplatesListApi } from "../../redux/action/addProjectAction";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { InlineField } from "~/shared/components/molecules/inlineField";
import {
  COPY_TEMPLATES_MODULE,
  copyTemplatesModuleOption,
} from "../../utils/constants";
import {
  copyProjectDataApi,
  fetchProjectDetailsApi,
} from "../../redux/action/projectDetailsAction";
import { useParams } from "@remix-run/react";
import { useAppProDispatch } from "../../redux/store";

const ImportProjectTemplate: React.FC<
  Pick<TImportMicroProFileProps, "isOpen" | "onClose"> & { id: string | number }
> = ({ isOpen, onClose, id }) => {
  const { _t } = useTranslation();
  const [projectTemplates, setProjectTemplates] =
    useState<IProjectTemplate[]>();
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | number>(
    ""
  );
  const [selectedModules, setSelectedModules] = useState<string[]>(
    COPY_TEMPLATES_MODULE
  );

  const loadingBool = useBoolean();
  const { isReadOnly } = useModuleAccess();
  const params: RouteParams = useParams();

  const dispatch = useAppProDispatch();

  const fetchProjectTemplate = useCallback(async () => {
    const response =
      (await getProjectTemplatesListApi()) as IProjectTemplatesListApiRes;
    if (response.success) {
      setProjectTemplates(response.data);
    } else {
      notification.error({
        description: response.message,
      });
    }
  }, []);

  useEffect(() => {
    fetchProjectTemplate();
  }, []);

  const projectTemplatesOpts = useMemo(() => {
    return (
      projectTemplates?.map((template) => {
        return {
          label: HTMLEntities.decode(
            sanitizeString(
              `${
                Number(template?.save_as_template) === 1 ? "(Template)" : ""
              } ${template.project_name} (${template.project_id})`
            )
          ),
          value: template.id?.toString(),
        };
      }) ?? []
    );
  }, [projectTemplates]);

  const importTemplateHandler = useCallback(async () => {
    if (!selectedTemplateId || selectedModules.length === 0) {
      notification.error({
        description: "Please select template or modules to import.",
      });
      return;
    }

    loadingBool.onTrue();

    const copyDataRes = (await copyProjectDataApi({
      copy_templates_module: selectedModules,
      project_template_id: Number(selectedTemplateId),
      is_from_action_menu: true,
      project_id: Number(params?.id) === 0 ? Number(id) : Number(params.id),
    })) as DefaultResponse;

    if (!copyDataRes.success) {
      notification.error({
        description:
          copyDataRes.message ?? "Failed to import project templates",
      });
    } else {
      dispatch(
        fetchProjectDetailsApi({
          project_id: Number(params?.id),
          record_type: "project",
        })
      );

      onClose();
    }
    loadingBool.onFalse();
  }, [selectedTemplateId, selectedModules, params?.id]);

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="600px"
      onCloseModal={onClose}
      modalBodyClass="p-0"
      header={{
        title: _t("Import Project Template"),
        icon: (
          <FontAwesomeIcon
            className="w-3.5 h-3.5"
            icon="fa-regular fa-file-import"
          />
        ),
        closeIcon: true,
      }}
    >
      <div className="p-4">
        <div className="modal-body overflow-y-auto max-h-[calc(100vh-200px)]">
          <SelectField
            label={_t("Use Project Templates")}
            value={selectedTemplateId}
            name="project_id"
            labelPlacement="top"
            disabled={isReadOnly}
            readOnly={isReadOnly}
            isRequired={true}
            informationProps={{
              icon: "fa-regular fa-circle-info",
              message: _t(
                "Tip: Save a Project as a template for future use by clicking on Project > Details > Project Configuration and selecting Save as Template or Complated projects."
              ),
            }}
            options={projectTemplatesOpts}
            showSearch
            filterOption={(input, option) =>
              filterOptionBySubstring(input, option?.label as string)
            }
            onChange={(val) => {
              setSelectedTemplateId(val?.toString());
            }}
          />
          <div className="mt-4">
            {selectedTemplateId !== "" && (
              <div className="w-full">
                <InlineField
                  label={_t(
                    "Select the items that should be copied to the new Project."
                  )}
                  labelPlacement="top"
                  field={
                    <CheckboxGroupList
                      formInputClassName="!p-0 mt-2"
                      name="copy_templates_module"
                      view="row"
                      onChange={(value) => {
                        setSelectedModules(value);
                      }}
                      className="gap-y-1 grid sm:grid-cols-3 grid-cols-2 w-full"
                      value={selectedModules}
                      options={copyTemplatesModuleOption}
                    />
                  }
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-center mt-5">
          <PrimaryButton
            type="primary"
            htmlType="button"
            onClick={importTemplateHandler}
            isLoading={loadingBool.bool}
            disabled={loadingBool.bool}
            buttonText={_t("Import")}
            className="!w-[150px]"
          />
        </div>
      </div>
    </CommonModal>
  );
};

export default ImportProjectTemplate;
