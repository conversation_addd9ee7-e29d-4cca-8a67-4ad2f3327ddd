import { useIframe, useTranslation } from "~/hook";
import { FormikValues, useFormik } from "formik";
import * as yup from "yup";
// atom
import { Drawer } from "~/shared/components/atoms/drawer";
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { ErrorMessage } from "~/shared/components/molecules/errorMessage";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { CustomFieldSkeleton } from "~/shared/components/molecules/customFieldSkeleton";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

import { useEffect, useMemo, useState } from "react";
import { defaultConfig } from "~/data";

import {
  addExpCustomData,
  createExpense,
  fetchAccountData,
  fetchDirDetails,
} from "../../redux/action/addExpensesAction";
import { useAppEXSelector } from "../../redux/store";
import {
  getGConfig,
  getGProject,
  getGSettings,
  useExistingProjects,
} from "~/zustand";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import {
  filterOptionBySubstring,
  onKeyDownCurrency,
  onKeyDownNumber,
} from "~/shared/utils/helper/common";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import {
  escapeHtmlEntities,
  removeQueryParams,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";

import { fetchDefaultVendor, formAddExpenseSchema } from "./utils";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { NavigateFunction, useNavigate } from "@remix-run/react";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { expenseTypes } from "./constants";
import delay from "lodash/delay";
import {
  addExpCustomDataAct,
  setCallListApi,
} from "../../redux/slices/addExpensesSlice";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
import { sendMessageKeys } from "~/components/page/$url/data";

const AddExpenses = ({
  addExpenses,
  setAddExpenses,
  dispatch,
  defaultProjectId,
  action,
  openedUsingBtn,
  setOpenedUsingBtn,
  vendorId,
}: IAddExpensesProps) => {
  const { _t } = useTranslation();
  const navigate: NavigateFunction = useNavigate();
  const gConfig: GConfig = getGConfig();
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  let { getExistingUsers } = useExistingCustomers();
  const gSettings = getGSettings();
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [isSelectVendorOpen, setIsSelectVendorOpen] = useState<boolean>(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<
    TselectedContactSendMail[]
  >([]);
  const [isExpenseAmount, setIsExpenseAmount] = useState(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [zeroAmountError, setZeroAmountError] = useState(false);
  // this is will NF once testing done it will be merge on dev
  // const [drawerConfirmDialogOpen, setDrawerConfirmDialogOpen] =
  //   useState<boolean>(false);
  const [clearAccount, setClearAccount] = useState<boolean>(false);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const {
    categoryData,
    accountData,
    expenseCreditAccountId,
    expenseAccountId,
    details,
  }: {
    categoryData: ICustomData[];
    accountData: ICustomData[];
    expenseCreditAccountId: string;
    expenseAccountId: string;
    details: IDirDetails;
  } = useAppEXSelector((state) => state.addExpenses);
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { parentPostMessage } = useIframe();
  const {
    quickbook_sync,
    date_format,
    quickbook_desktop_sync,
    is_expense_billable,
  } = appSettings || {};
  const quickbookSync = useMemo(
    () =>
      Boolean(Number(quickbook_sync)) ||
      Boolean(Number(quickbook_desktop_sync)),
    [quickbook_sync, quickbook_desktop_sync]
  );

  const defaultIsExpenseBillable = useMemo(
    () =>
      !vendorId ? true : quickbookSync ? Boolean(is_expense_billable) : false,
    [is_expense_billable, quickbookSync, vendorId]
  );

  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { default_expense_account } = user || {};
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const {
    initValues,
    validateArray,
  }: { initValues: IExpenseFormState; validateArray: string[] } =
    formAddExpenseSchema(isNoAccessCustomField);
  const { project_id }: GProject = getGProject();

  const { inputFormatter, unformatted } = useCurrencyFormatter();

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();

  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue },
    {
      moduleId: module_id,
    }
  );
  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
    useExistingProjects("project");

  const handleAmount = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    formik.setFieldValue(name, value);
  };

  const handleSubmit = async (
    values: IExpenseFormState,
    { resetForm }: FormikValues
  ) => {
    setIsLoading(true);
    let isCustomFieldValid = true;

    if (!values?.expense_name.trim()) {
      formik.setFieldError("expense_name", _t("This field is required."));
      setIsLoading(false);
      return;
    } else {
      formik.setFieldValue("expense_name", values?.expense_name.trim());
    }

    if (!values.amount || values.amount == 0) {
      notification.error({
        description: "Total amount must be greater than 0.",
      });
      setIsSubmit(false);
      setIsLoading(false);
      return;
    }

    if (componentList.length && !isNoAccessCustomField) {
      for (let index = 0; index < componentList.length; index++) {
        const value =
          "custom_fields" in formik.values
            ? formik.values.custom_fields?.[componentList[index].name]
            : [];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (!value?.length) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    if (!isCustomFieldValid) {
      setIsLoading(false);
      return;
    }

    const formData = {
      ...formik.values,
      amount: formik.values.amount ? formik.values.amount * 100 : "",
      expense_name: values?.expense_name.trim(),
      custom_fields:
        "custom_fields" in formik.values &&
        formik.values.custom_fields &&
        !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
    };

    const res: Partial<IResponse<IExpenseAddResponse>> = await createExpense(
      formData
    );
    setSelectedProject([]);
    setSelectedVendor([]);
    resetForm();
    setIsLoading(false);
    if (res.success) {
      EventLogger.log(
        EVENT_LOGGER_NAME.expenses + EVENT_LOGGER_ACTION.added,
        1
      );
      if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
        parentPostMessage(sendMessageKeys?.modal_change, {
          open: false,
        });
      } else if (res.data && res.data.expense_id) {
        navigate(`${res.data.expense_id}`);
      }
      setAddExpenses(false);
      setOpenedUsingBtn(false);
    } else {
      notification.error({ description: res.message });
      setAddExpenses(false);
      setOpenedUsingBtn(false);
    }
    dispatch(setCallListApi());
  };

  const staticValidationSchema = validateArray.reduce((acc, fieldName) => {
    const isQuickbookDisabled =
      quickbook_desktop_sync.toString() === "0" &&
      quickbook_sync.toString() === "0";

    const optionalWhenQBDisabled = ["category", "paid_by", "directory_id"];

    if (isQuickbookDisabled && optionalWhenQBDisabled.includes(fieldName)) {
      acc[fieldName] = yup
        .string()
        .nullable()
        .notRequired() as yup.StringSchema<string | undefined>;
    } else {
      if (["expense_name", "paid_by", "project_id"].includes(fieldName)) {
        acc[fieldName] = yup.string().required("This field is required.");
      } else if (fieldName === "amount") {
        acc[fieldName] = yup
          .number()
          .typeError("This field is required.")
          .required("This field is required.");
      } else {
        acc[fieldName] = yup
          .number()
          .positive("Enter a valid number")
          .required("This field is required.");
      }
    }

    return acc;
  }, {} as Record<string, yup.StringSchema | yup.NumberSchema>);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = yup
        .array()
        .of(yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, yup.StringSchema | yup.AnySchema>);

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? yup.object().shape({
          ...staticValidationSchema,
          custom_fields: yup.object().shape(dynamicValidationSchema),
        })
      : yup.object().shape({
          ...staticValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const formik = useFormik({
    initialValues: {
      ...initialFormValues,
      is_billable: !vendorId ? true : details?.billed_to,
    },
    enableReinitialize: true,
    onSubmit: handleSubmit,
    validationSchema,
  });
  const [initialValuesState, setInitialValuesState] = useState<any>(
    formik.values
  );

  useEffect(() => {
    formik.setFieldValue(
      "is_billable",
      action
        ? !vendorId
          ? defaultIsExpenseBillable
          : details.is_billable
        : defaultIsExpenseBillable
    );
  }, [defaultIsExpenseBillable, details]);

  const { errors } = formik;
  // category dropdown Options
  const categoryOptions = useMemo(
    () =>
      categoryData.length
        ? categoryData.map((item) => {
            return {
              label: `${HTMLEntities.decode(sanitizeString(item.name))} ${
                item?.qb_account_type ? `(${item.qb_account_type})` : ""
              }`,
              value: item.item_id,
            };
          })
        : [],
    [categoryData]
  );

  // account dropdown Options
  const accountOptions = useMemo(() => {
    const defaultAccData = [
      { label: "Employee", value: "emp" },
      { label: "Company", value: "cmp_card" },
    ];
    if (accountData.length) {
      const accData = accountData.map((item) => {
        return {
          label: `${HTMLEntities.decode(sanitizeString(item.name))} ${
            item?.qb_account_type ? `(${item.qb_account_type})` : ""
          }`,
          value: item.item_id,
        };
      });
      return defaultAccData.concat(accData);
    } else {
      return defaultAccData;
    }
  }, [accountData]);

  useEffect(() => {
    formik.setFieldValue("paid_by", default_expense_account);
  }, []);
  useEffect(() => {
    if (!clearAccount) {
      if (default_expense_account && !formik.values.paid_by) {
        formik.setFieldValue("paid_by", default_expense_account);
      }
    }
  }, [default_expense_account, formik.values.paid_by, formik]);

  const fetchDefaultProject = async (id: string) => {
    const projects = await getExistingProjectsWithApi(id);
    const projectName = projects && projects[0] && projects[0].project_name;
    const projectId = Number(id) || projects[0]?.key;

    if (projectId && projectName) {
      setSelectedProject([
        {
          id: projectId,
          project_name: projectName,
        },
      ]);
    } else {
      setSelectedProject([]);
    }
  };

  const setDefaultVendor = async (id: number) => {
    const vendordata = await fetchDefaultVendor(
      id,
      getExistingUsers,
      gConfig,
      user
    );
    if (vendordata && Array.isArray(vendordata) && vendordata.length) {
      setSelectedVendor(vendordata);
    }
  };
  useEffect(() => {
    dispatch(fetchDirDetails({ id: vendorId }));
  }, []);
  useEffect(() => {
    if (selectedProject && selectedProject.length) {
      formik.setFieldValue(
        "project_id",
        selectedProject[0].id || selectedProject[0]?.key
      );
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        project_id: selectedProject[0].id || selectedProject[0]?.key,
      }));
    } else {
      formik.setFieldValue("project_id", "");
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        project_id: "",
      }));
    }
    if (selectedVendor && selectedVendor.length) {
      formik.setFieldValue("directory_id", selectedVendor[0].user_id);
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        directory_id: selectedVendor[0].user_id,
      }));
    } else {
      formik.setFieldValue("directory_id", null);
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        directory_id: null,
      }));
    }
  }, [selectedProject, selectedVendor]);
  useEffect(() => {
    if (!formik.values?.project_id) {
      formik.setFieldValue("project_id", selectedProject[0]?.id);
    }
  }, [formik.values?.project_id]);

  useEffect(() => {
    if (!formik.values?.directory_id) {
      formik.setFieldValue("directory_id", selectedVendor[0]?.user_id);
    }
  }, [formik.values?.directory_id]);

  // useEffect(() => {
  //   if (accountOptions && accountOptions.length) {
  //     const accountData = accountOptions.filter((item) => {
  //       return String(expenseAccountId) === String(item?.value);
  //     });
  //     formik.setFieldValue(
  //       "paid_by",
  //       accountData && accountData.length ? accountData[0].value : undefined
  //     );
  //     setInitialValuesState((prevState: any) => ({
  //       ...prevState,
  //       paid_by:
  //         accountData && accountData.length ? accountData[0].value : undefined,
  //     }));
  //   }
  // }, [accountOptions]);
  //Both UseEffect Commented for intarnal https://app.clickup.com/t/86cywbjrm
  // useEffect(() => {
  //   if (categoryOptions && categoryOptions.length) {
  //     const categoryData = categoryOptions.filter((item) => {
  //       return String(expenseCreditAccountId) === String(item?.value);
  //     });
  //     formik.setFieldValue(
  //       "category",
  //       categoryData && categoryData.length
  //         ? Number(categoryData[0].value)
  //         : undefined
  //     );
  //     setInitialValuesState((prevState: any) => ({
  //       ...prevState,
  //       category:
  //         categoryData && categoryData.length
  //           ? Number(categoryData[0].value)
  //           : undefined,
  //     }));
  //   }
  // }, [categoryOptions]);

  useEffect(() => {
    if (!openedUsingBtn) {
      if (defaultProjectId) {
        fetchDefaultProject(defaultProjectId.toString());
      } else if (!selectedProject.length && project_id) {
        fetchDefaultProject(project_id.toString());
      }
      if (vendorId) {
        setDefaultVendor(Number(vendorId));
      }
    } else if (!selectedProject.length && project_id) {
      fetchDefaultProject(project_id.toString());
    }
  }, [defaultProjectId, project_id, action]);

  useEffect(() => {
    dispatch(
      fetchAccountData({
        types: [170, 171],
        moduleId: 12,
      })
    );
  }, []);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: number
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      if (itemType == expenseTypes?.expenseCategoryTypeId) {
        const newType = onEnterSelectSearchValue(event, categoryOptions || []);
        if (newType) {
          setCustomDataAdd({
            itemType: expenseTypes?.expenseCategoryTypeId,
            name: escapeHtmlEntities(newType),
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      } else {
        const newType = onEnterSelectSearchValue(event, accountOptions || []);
        if (newType) {
          setCustomDataAdd({
            itemType: expenseTypes?.expensePaidThroughTypeId,
            name: escapeHtmlEntities(newType),
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      }
    }
  };

  const handleAddCustomData = async () => {
    if (customDataAdd?.name?.includes("\\")) {
      const availableOption = customDataAdd?.name.split("\\")[0];
      let isLabelAvailable = false;
      if (customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId) {
        isLabelAvailable = categoryOptions?.some(
          (type: Option) =>
            type?.label.toLowerCase().trim() ===
            availableOption.toLowerCase().trim()
        );
      } else {
        isLabelAvailable = accountOptions?.some(
          (type: Option) =>
            type?.label.toLowerCase().trim() ===
            availableOption.toLowerCase().trim()
        );
      }
      if (isLabelAvailable) {
        notification.error({
          description: "Records already exist - no new records were added.",
        });
        if (customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId) {
          formik.setFieldValue("category", formik.values.category);
        } else {
          formik.setFieldValue("paid_by", formik.values.paid_by);
        }
        setIsConfirmDialogOpen(false);
        return;
      }
    }
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addExpCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addExpCustomDataAct(cDataRes?.data));
        delay(() => {
          if (customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId) {
            formik.setFieldValue("category", cDataRes?.data?.item_id);
          } else {
            formik.setFieldValue("paid_by", cDataRes?.data?.item_id);
          }
        }, 500);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(formik.values) !== JSON.stringify(initialValuesState);
  // }, [formik.values, initialValuesState]);

  // const closeDrawerConfirmationModal = () => {
  //   addExpenses;
  //   setDrawerConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setDrawerConfirmDialogOpen(false);
  //   setAddExpenses(false);
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     removeQueryParams();
  //     setAddExpenses(false);
  //     setIsSubmit(false);
  //     setOpenedUsingBtn(false);
  //   } else {
  //     setDrawerConfirmDialogOpen(true);
  //   }
  // };
  return (
    <>
      <Drawer
        open={addExpenses}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-money-bill"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Add ${
                  replaceDOMParams(sanitizeString(module_singular_name)) ??
                  "Expense"
                }`
              )}
            </Header>
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        // closeIcon={<CloseButton onClick={() => handleCloseDrawer()} />}
        closeIcon={
          !window.ENV.PAGE_IS_IFRAME ? (
            <CloseButton
              onClick={() => {
                removeQueryParams();
                setAddExpenses(false);
                setIsSubmit(false);
                setOpenedUsingBtn(false);
              }}
            />
          ) : null
        }
      >
        <form className="py-4" onSubmit={formik.handleSubmit}>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project_id"
                    labelPlacement="top"
                    onClick={() => setIsSelectProjectOpen(true)}
                    required={true}
                    value={
                      selectedProject.length &&
                      HTMLEntities.decode(
                        sanitizeString(selectedProject[0].project_name)
                      )
                    }
                    errorMessage={
                      formik.touched.project_id && !formik.values.project_id
                        ? _t("This field is required.")
                        : isSubmit && !formik.values.project_id
                        ? _t("This field is required.")
                        : ""
                    }
                    addonBefore={
                      selectedProject.length &&
                      !isNaN(Number(selectedProject[0]?.id)) &&
                      selectedProject[0]?.id ? (
                        <ProjectFieldRedirectionIcon
                          projectId={`${
                            selectedProject[0].id || selectedProject[0]?.key
                          }`}
                        />
                      ) : null
                    }
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputNumberField
                      label={_t("Total")}
                      isRequired={true}
                      name="amount"
                      labelClass="dark:text-white/90"
                      placeholder={inputFormatter("0.00").value}
                      labelPlacement="top"
                      value={formik.values.amount ? formik.values.amount : ""}
                      formatter={(value, info) => {
                        const inputValue = info.input.trim();
                        const cleanValue = unformatted(inputValue);
                        const [integerPart] = cleanValue.split(".");
                        if (integerPart.length > 10) {
                          return inputFormatter(formik?.values?.amount).value;
                        }
                        const valueToFormat =
                          inputValue !== "0" && inputValue.length > 0
                            ? unformatted(inputValue)
                            : String(value);

                        return isExpenseAmount
                          ? inputFormatter(valueToFormat).value
                          : !!value
                          ? inputFormatter(Number(value)?.toFixed(2)).value
                          : "";
                      }}
                      onChange={(inputValue) => {
                        formik.setFieldValue("amount", inputValue);
                      }}
                      parser={(value) => {
                        const inputValue = value
                          ? unformatted(value.toString())
                          : "";
                        const [integerPart, decimalPart = ""] =
                          inputValue.split(".");
                        if (integerPart.length > 10) {
                          return formik.values.amount;
                        }
                        return inputValue;
                      }}
                      onKeyDown={(event) => {
                        if (!event.target.value || event.target.value == 0) {
                          formik.setFieldError(
                            "amount",
                            _t("This field is required.")
                          );
                          formik.setFieldValue("amount", "");
                        }
                        onKeyDownCurrency(event, {
                          integerDigits: 10,
                          decimalDigits: 2,
                          unformatted,
                          allowNegative: true,
                          decimalSeparator: inputFormatter().decimal_separator,
                        });
                      }}
                      onFocus={() => {
                        setIsExpenseAmount(true);
                      }}
                      onBlur={() => {
                        setIsExpenseAmount(false);
                      }}
                      prefix={inputFormatter().currency_symbol}
                      errorMessage={
                        formik.touched.amount && errors.amount
                          ? errors.amount
                          : ""
                      }
                    />
                  </div>
                  <div className="w-full overflow-hidden">
                    <ButtonField
                      label={_t("Vendor")}
                      labelPlacement="top"
                      required={
                        quickbook_desktop_sync.toString() === "1" ||
                        quickbook_sync.toString() === "1"
                      }
                      onClick={() => {
                        setIsSelectVendorOpen(true);
                      }}
                      value={
                        selectedVendor.length &&
                        HTMLEntities.decode(
                          sanitizeString(selectedVendor[0].display_name)
                        )
                      }
                      errorMessage={
                        quickbook_desktop_sync.toString() === "1" ||
                        quickbook_sync.toString() === "1"
                          ? isSubmit && !formik.values.directory_id
                            ? errors.directory_id
                            : formik.touched.directory_id && errors.directory_id
                            ? errors.directory_id
                            : ""
                          : ""
                      }
                      addonBefore={
                        selectedVendor.length ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsOpenContactDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              className="!w-5 !h-5"
                              directoryId={
                                selectedVendor[0].user_id?.toString() || ""
                              }
                              directoryTypeKey={
                                selectedVendor[0].type_key?.toString() || ""
                              }
                            />
                          </div>
                        ) : null
                      }
                    />
                  </div>
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Expense Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="expense_name"
                    value={HTMLEntities.decode(
                      sanitizeString(formik.values.expense_name)
                    )}
                    onChange={formik.handleChange}
                    errorMessage={
                      isSubmit && !formik.values.expense_name.trim()
                        ? _t("This field is required.")
                        : formik.touched.expense_name && errors.expense_name
                        ? errors.expense_name
                        : ""
                    }
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={
                        quickbook_desktop_sync.toString() === "0"
                          ? _t("Bank Account")
                          : _t("Account")
                      }
                      labelPlacement="top"
                      options={accountOptions}
                      isRequired={
                        quickbook_desktop_sync.toString() === "1" ||
                        quickbook_sync.toString() === "1"
                      }
                      iconView={true}
                      showSearch
                      allowClear={true}
                      name="paid_by"
                      value={accountOptions.filter(
                        (item) =>
                          String(formik.values.paid_by) === String(item?.value)
                      )}
                      onChange={(val) => {
                        formik.setFieldValue("paid_by", val || "");
                      }}
                      onClear={() => {
                        setClearAccount(true);
                        formik.setFieldValue("paid_by", "");
                      }}
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      errorMessage={
                        formik?.touched?.paid_by ? formik.errors.paid_by : ""
                      }
                      addItem={addItemObject}
                      onInputKeyDown={(e) =>
                        handlekeyDown(e, expenseTypes.expensePaidThroughTypeId)
                      }
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={
                        quickbook_desktop_sync.toString() === "0"
                          ? _t("Category")
                          : _t("Bank Account")
                      }
                      labelPlacement="top"
                      options={categoryOptions}
                      isRequired={
                        quickbook_desktop_sync.toString() === "1" ||
                        quickbook_sync.toString() === "1"
                      }
                      iconView={true}
                      showSearch
                      allowClear={true}
                      name="category"
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      value={categoryOptions.filter((item) => {
                        return (
                          String(formik.values.category) === String(item?.value)
                        );
                      })}
                      onChange={(val) => {
                        formik.setFieldValue("category", val || "");
                      }}
                      errorMessage={
                        formik?.touched?.category ? formik.errors.category : ""
                      }
                      addItem={addItemObject}
                      onInputKeyDown={(e) =>
                        handlekeyDown(e, expenseTypes?.expenseCategoryTypeId)
                      }
                    />
                  </div>
                  <div className="w-full">
                    <CheckBox
                      className="gap-1.5 w-fit"
                      name="is_billable"
                      checked={formik.values?.is_billable ? true : false}
                      onChange={(e) => {
                        formik.setFieldValue(
                          "is_billable",
                          e.target.checked ? 1 : 0
                        );
                      }}
                    >
                      {_t("Billable")}
                    </CheckBox>
                  </div>
                </div>
              </SidebarCardBorder>
              {!isNoAccessCustomField && loadingCustomField && (
                <CustomFieldSkeleton />
              )}
              {/* ----- custom component start ----- */}
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
              {/* ----- custom component end ----- */}
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              buttonText={_t(
                `Create ${
                  replaceDOMParams(sanitizeString(module_singular_name)) ??
                  "Expense"
                }`
              )}
              disabled={isLoading || loadingCustomField}
              isLoading={isLoading}
            />
          </div>
        </form>
      </Drawer>
      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            setSelectedProject(data);
          }}
          isRequired={false}
          genericProjects="project"
          category="expense_location"
          module_key={module_key}
        />
      )}
      {isSelectVendorOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setIsSelectVendorOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={isSelectVendorOpen}
          options={[defaultConfig.vendor_key, "by_service", "my_project"]}
          setCustomer={(data) => {
            setSelectedVendor(data);
            if (data?.length > 0) {
              if (CFConfig.vendor_key == data?.[0]?.type_key) {
                formik.setFieldValue(
                  "is_billable",
                  "billed_to" in data?.[0] && data?.[0]?.billed_to ? 1 : 0
                );
              }
            } else {
              formik.setFieldValue("is_billable", defaultIsExpenseBillable);
            }
          }}
          selectedCustomer={selectedVendor}
          groupCheckBox={false}
          projectId={
            selectedProject.length > 0
              ? Number(selectedProject[0].id) || Number(selectedProject[0]?.key)
              : 0
          }
          additionalContactDetails={0} // as per PHP, we will not select additional contact from here
        />
      )}
      {isOpenContactDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetails}
          contactId={Number(selectedVendor[0]?.user_id) || undefined}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          additional_contact_id={0}
        />
      )}
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${replaceDOMParams(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {drawerConfirmDialogOpen && (
        <ConfirmModal
          isOpen={drawerConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeDrawerConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeDrawerConfirmationModal}
        />
      )} */}
    </>
  );
};

export default AddExpenses;
