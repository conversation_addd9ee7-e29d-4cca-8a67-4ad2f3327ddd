import { useMemo } from "react";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { USER_PLANE_IDS } from "../utils/constants";
import {
  getGlobalAppSettings,
  getGlobalCompanySettings,
} from "~/zustand/global/settings/slice";
import { useAppProSelector } from "../redux/store";

export const useModuleAccess = () => {
  const { details }: IProjectDetailsInitialState = useAppProSelector(
    (state) => state.proDetails
  );

  const { checkGlobalModulePermissionByKey, getGlobalModuleByKey } =
    useGlobalModule();

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const companySettings = getGlobalCompanySettings();

  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();

  const { module_group_id = -1 } = companySettings || {};

  const { module_access } = currentModule || {};

  const { disable_client_when_project_completed } = appSettings || {};

  const clientAccessPortal = getGlobalModuleByKey(
    CFConfig.client_portal_module
  );

  const procurementModule = getGlobalModuleByKey(CFConfig.procurement_module);

  const ganttModule = getGlobalModuleByKey(
    CFConfig.project_schedule_gantt_module
  );

  const projectReportModule = getGlobalModuleByKey(
    CFConfig.project_report_module
  );

  const restrictedPlanIds: number[] = useMemo(() => {
    return [
      USER_PLANE_IDS.basic_v5,
      USER_PLANE_IDS.standard_v5,
      USER_PLANE_IDS.plus_v5,
    ];
  }, []);

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const hasEnoughAccessofProjectFinanceTabModule = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.project_finance_tab_module
    );

    return ["full_access", "own_data_access", "read_only"].includes(access);
  }, [CFConfig.project_finance_tab_module]);

  const projectContractAmountModuleAccess = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.project_contract_amount_module
    );

    return ["full_access", "own_data_access"].includes(access);
  }, [CFConfig.project_contract_amount_module]);

  const projectBudgetAmountModuleAccess = useMemo(() => {
    const access: TModuleAccessStatus = checkGlobalModulePermissionByKey(
      CFConfig.project_budget_amount_module
    );

    return ["full_access", "own_data_access"].includes(access);
  }, [CFConfig.project_contract_amount_module]);

  const enoughProcurementAccess = useMemo(() => {
    return (
      procurementModule?.can_read === "1" && procurementModule?.can_write == "1"
    );
  }, [procurementModule?.can_read, details?.enable_procurement_tab]);

  const enoughGanttModuleAccess = useMemo(() => {
    return (
      ganttModule?.can_read === "1" &&
      ![...restrictedPlanIds].slice(0, 2).includes(module_group_id)
    );
  }, [ganttModule?.can_read, restrictedPlanIds, module_group_id]);

  const enoughClientModuleAccess = useMemo(() => {
    return (
      clientAccessPortal?.can_read === "1" &&
      !restrictedPlanIds.includes(module_group_id) &&
      !(
        disable_client_when_project_completed === 1 &&
        details?.project_status === "completed"
      )
    );
  }, [
    clientAccessPortal?.can_read,
    restrictedPlanIds,
    module_group_id,
    disable_client_when_project_completed,
    details?.project_status,
  ]);

  const enoughReportModuleAccess = useMemo(() => {
    return (
      projectReportModule?.can_read === "1" &&
      ![...restrictedPlanIds].slice(0, 2).includes(module_group_id)
    );
  }, [projectReportModule?.can_read, restrictedPlanIds, module_group_id]);

  const enoughDocumentTabAccess = useMemo(() => {
    const projectDocumentTab = getGlobalModuleByKey(
      CFConfig.project_document_tab
    );
    return (
      projectDocumentTab?.can_read === "1" ||
      projectDocumentTab?.can_write === "1"
    );
  }, []);

  const enoughTabFileAccess = useMemo(() => {
    const projectFileTab = getGlobalModuleByKey(CFConfig.project_file_module);
    return (
      projectFileTab?.can_read === "1" || projectFileTab?.can_write === "1"
    );
  }, []);

  const enoughTabScheduleAccess = useMemo(() => {
    const projectScheduleTab = getGlobalModuleByKey(
      CFConfig.project_budget_tab
    );
    return (
      projectScheduleTab?.can_read === "1" ||
      projectScheduleTab?.can_write === "1"
    );
  }, []);

  return {
    hasEnoughAccessofProjectFinanceTabModule,
    projectContractAmountModuleAccess,
    projectBudgetAmountModuleAccess,
    module_access,
    isReadOnly,
    enoughProcurementAccess,
    enoughGanttModuleAccess,
    enoughClientModuleAccess,
    enoughReportModuleAccess,
    enoughDocumentTabAccess,
    enoughTabFileAccess,
    enoughTabScheduleAccess,
  };
};
