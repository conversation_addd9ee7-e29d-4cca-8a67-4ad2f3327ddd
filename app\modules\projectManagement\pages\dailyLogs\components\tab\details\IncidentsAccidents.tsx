import { useEffect, useRef, useState } from "react";
import { useParams } from "@remix-run/react";
import type {
  <PERSON>rid<PERSON><PERSON>,
  GridReadyEvent,
  SortChangedEvent,
} from "ag-grid-community";

// Hooks And Redux
import { getDLIncidentsAccidentsApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { useAppDLDispatch } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { useTranslation } from "~/hook";
import { getGModuleByKey } from "~/zustand";
import { defaultConfig } from "~/data";
import useTableGridData from "~/shared/hooks/useTableGridData";

// atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
// molecules
import { AddButton } from "~/shared/components/molecules/addButton";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// Other
import { IncidentsAdd } from "~/modules/projectManagement/pages/dailyLogs/components/tab/sidebar/incidents";
import Iframe from "~/components/page/$url/iframe";
import { sanitizeString } from "~/helpers/helper";

const IncidentsAccidents = ({
  isClickOnReload,
  isReadOnly,
}: {
  isClickOnReload: number;
  isReadOnly: boolean;
}) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.incident_module
  );
  const { id }: RouteParams = useParams();
  const gridRef = useRef<GridApi | null>(null);
  const [selectedItem, setSelectedItem] = useState<Partial<IDLIncidents>>({});
  const [isViewOpen, setIsViewOpen] = useState<boolean>(false);
  const [isOpenAddInc, setIsOpenAddInc] = useState<boolean>(false);

  const { datasource, gridRowParams } = useTableGridData();

  const fetchDirList = async () => {
    let gridData: { rowCount?: number; rowData: IDLIncidents[] } = {
      rowData: [],
    };

    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const limit = changeGridParams?.length ?? 0;

    let dataParams: IDLListParmas = {
      limit,
      page: changeGridParams?.start
        ? Math.floor(changeGridParams.start / limit)
        : 0,
      dailyLogId: id,
    };

    if (changeGridParams?.order_by_name) {
      dataParams.order_by_name = changeGridParams.order_by_name;
    }
    if (changeGridParams?.order_by_dir) {
      dataParams.order_by_dir = changeGridParams.order_by_dir;
    }

    try {
      if (gridParams) {
        gridParams.api.hideOverlay();
      }
      const resData = (await getDLIncidentsAccidentsApi(
        dataParams
      )) as IDLIncidentsApiRes;
      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.incidentsData?.length < limit) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData.data.incidentsData.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data?.incidentsData ?? [] };
      gridParams?.success(gridData);

      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.page === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  useEffect(() => {
    let timeout: NodeJS.Timeout | null = null;
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      if (gridRowParams) {
        fetchDirList();
      }
    }, 400);
    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [gridRowParams]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    if (isClickOnReload) {
      refreshAgGrid();
    }
  }, [isClickOnReload]);

  const columnDefs = [
    {
      headerName: _t("Type"),
      field: "txn_type",
      minWidth: 120,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IDLIncidentsTableCellRenderer) => {
        const incidentType =
          data?.txn_type === "employee_writeup"
            ? "Employee Write-Up"
            : "Incident";
        return (
          <Tooltip title={incidentType}>
            <Typography className="table-tooltip-text">
              {_t(incidentType)}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Classification"),
      field: "incident_type_name",
      minWidth: 180,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IDLIncidentsTableCellRenderer) => {
        const incidentsType = HTMLEntities.decode(
          sanitizeString(data?.incident_type_name)
        );
        return incidentsType ? (
          <Tooltip title={incidentsType}>
            <Typography className="table-tooltip-text">
              {incidentsType}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("People Involved"),
      field: "emp_involved",
      maxWidth: 130,
      minWidth: 130,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IDLIncidentsTableCellRenderer) => {
        const { emp_involved, emp_involved_image } = data;
        let assigneeList: string[] = [];
        let assigneeImages: string[] = [];
        const assignee_names = emp_involved?.split(",") || [];
        const formattedAssignee = emp_involved?.replace(/,+/g, "").trim();
        const assignee_images = emp_involved_image?.split(",") || [];

        assignee_names.forEach((name: string, index: number) => {
          if (name.trim() !== "") {
            assigneeList.push(name.trim());
            assigneeImages.push(assignee_images[index]?.trim() || "");
          }
        });

        return (
          <>
            {emp_involved !== null &&
            formattedAssignee !== "" &&
            emp_involved !== undefined ? (
              <AvatarGroup
                max={{
                  count: 1,
                  style: {
                    color: "#223558",
                    backgroundColor: "#ECF1F9",
                  },
                }}
                size={24}
                className="flex justify-center"
                prefixCls="multi-avatar-scroll"
              >
                {assigneeList.length > 0 &&
                  assigneeImages.length > 0 &&
                  assigneeList.map((item: string, index: number) => {
                    const dName = HTMLEntities.decode(sanitizeString(item));
                    const userImg = assigneeImages[index];

                    return (
                      <div
                        key={index}
                        className={`flex items-center ${
                          index === 0 ? "" : "gap-2 py-0.5 px-1"
                        }`}
                      >
                        {index === 0 ? (
                          <Tooltip title={dName} placement="top">
                            <div>
                              <AvatarProfile
                                user={{
                                  name: dName,
                                  image: userImg,
                                }}
                                iconClassName="text-[11px] font-semibold"
                              />
                            </div>
                          </Tooltip>
                        ) : (
                          <div className="p-1 flex items-center gap-1">
                            <AvatarProfile
                              user={{
                                name: dName,
                                image: userImg,
                              }}
                              style={{
                                backgroundColor: "#ECF1F9",
                              }}
                              iconClassName="text-[11px] font-semibold"
                            />
                            <Typography className="">{_t(dName)}</Typography>
                          </div>
                        )}
                      </div>
                    );
                  })}
              </AvatarGroup>
            ) : (
              "-"
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Description"),
      field: "description",
      minWidth: 180,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IDLIncidentsTableCellRenderer) => {
        const incidentsDec = HTMLEntities.decode(
          sanitizeString(data?.description)
        );
        return incidentsDec ? (
          <Tooltip title={incidentsDec}>
            <Typography className="table-tooltip-text">
              {incidentsDec}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellRenderer: ({ data }: IDLIncidentsTableCellRenderer) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={() => {
                setIsViewOpen(true);
                setSelectedItem(data);
              }}
            />
          </div>
        );
      },
    },
  ];

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridRef.current = gridParams.api;
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const handleAddRecord = async (newRecord: Partial<IDLIncidents>) => {
    if (gridRowParams && gridRef != null) {
      const currentData: IDLIncidents[] = [];
      gridRef?.current?.forEachNode((node) => currentData.push(node.data));
      const gridParams = gridRowParams?.gridParams;
      gridParams.api.hideOverlay();
      gridParams.success({
        rowData: [...currentData, newRecord],
      });
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Incidents & Accidents")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-triangle-exclamation",
          containerClassName:
            "bg-[linear-gradient(180deg,#f275941a_0%,#85489e1a_100%)]",
          id: "incidents_accidents_triangle_exclamation",
          colors: ["#F27594", "#85489E"],
        }}
        headerRightButton={
          <AddButton
            disabled={isReadOnly}
            onClick={() => {
              if (isReadOnly) {
                return;
              }
              setIsOpenAddInc(true);
            }}
          >
            {module?.module_name || _t("Incident")}
          </AddButton>
        }
        children={
          <div className="pt-2">
            <div className="ag-theme-alpine">
              <DynamicTable
                columnDefs={columnDefs}
                onGridReady={onGridReady}
                onSortChanged={onSortChanged}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-incidents.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {isOpenAddInc && (
        <IncidentsAdd
          isOpen={isOpenAddInc}
          isViewOnly={isReadOnly}
          onClose={setIsOpenAddInc}
          onAddedRec={(data) => {
            handleAddRecord(data);
            // refreshAgGrid();
            setIsOpenAddInc(false);
          }}
        />
      )}
      {isViewOpen && (
        <CommonModal
          isOpen={isViewOpen}
          widthSize="98%"
          draggable={false}
          onCloseModal={() => {
            setSelectedItem({});
            setIsViewOpen(false);
            refreshAgGrid();
          }}
          modalBodyClass="p-0"
          header={{
            title: "",
            closeIcon: true,
          }}
        >
          <div className="p-5">
            {selectedItem?.incident_id ? (
              <Iframe
                path="/manage_incidents_employee_writeup"
                onIframeClose={() => {
                  refreshAgGrid();
                  setIsViewOpen(false);
                  dispatch(resetDash());
                }}
                otherParams={`iframecall=1&id=${selectedItem.incident_id}`}
              />
            ) : (
              "Something went wrong!"
            )}
          </div>
        </CommonModal>
      )}
    </>
  );
};

export default IncidentsAccidents;
