import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
import { getGModuleByKey } from "~/zustand";
import { defaultConfig } from "~/data";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { fetchDashData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";

// atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";

const RecentInspections = () => {
  const { _t } = useTranslation();
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.inspection_module
  );
  const dispatch = useAppDLDispatch();
  const {
    isDashLoading,
    recent_inspections,
    recent_inspections_last_refresh_time,
  }: IDailyLogIntlState = useAppDLSelector((state) => state.dashboard);
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IRecentInspections[]>([]);
  useEffect(() => {
    if (!isCashLoading && recent_inspections) {
      setRowData(recent_inspections);
    }
  }, [recent_inspections, isCashLoading]);

  const columnDefs = [
    {
      headerName: _t("Date"),
      maxWidth: 135,
      minWidth: 135,
      field: "inspection_date",
      suppressMenu: true,
      cellRenderer: (params: IDLInspectionTableCellRenderer) => {
        return params?.data?.inspection_date ? (
          <DateTimeCard format="date" date={params?.data?.inspection_date} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ data }: IDLInspectionTableCellRenderer) => {
        const projectName = HTMLEntities.decode(
          sanitizeString(data?.project_name)
        );
        return projectName ? (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "status",
      maxWidth: 100,
      minWidth: 100,
      headerClass: "ag-header-center",
      suppressMenu: true,
      cellRenderer: (params: IDLInspectionTableCellRenderer) => {
        const { inspection_status_name, default_color } = params.data;
        const { color, textColor } = getDefaultStatuscolor(default_color || "");
        return inspection_status_name ? (
          <Tooltip title={inspection_status_name}>
            <div className="text-center overflow-hidden">
              {inspection_status_name && (
                <Tag
                  color={color}
                  style={{
                    color: `${textColor || ""}`,
                  }}
                  className={`${
                    textColor === "" && "!text-primary-900"
                  } mx-auto text-13 type-badge common-tag`}
                >
                  {inspection_status_name}
                </Tag>
              )}
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const handleRefreshWidget = async () => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refreshType: "recent_inspections" }));
    setIsCashLoading(false);
  };
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-inspections.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={module?.plural_name || ""}
        showRefreshIcon={true}
        refreshIconTooltip={recent_inspections_last_refresh_time}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isCashLoading}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            key={isDashLoading ? "loading" : "loaded"}
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            noRowsOverlayComponent={
              isDashLoading || isCashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};

export default RecentInspections;
