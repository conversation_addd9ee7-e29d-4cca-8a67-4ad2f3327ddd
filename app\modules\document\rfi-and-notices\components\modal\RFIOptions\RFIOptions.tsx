import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
import { Spin, type RadioChangeEvent } from "antd";

// Components
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";

const RFIOptions = ({
  isOpen,
  onCloseModal,
  onClickAdd,
  value,
  setValue,
  moduleName,
  availableRFIOptions,
}: IRFIOptionsProps) => {
  const { _t } = useTranslation();
  const [showCloseIcon, setShowCloseIcon] = useState<boolean>(false);
  const isDashLoading = false;

  useEffect(() => {
    if (window && window?.location?.href) {
      const currentUrl = window.location.href;
      setShowCloseIcon(currentUrl.includes("iframecall=1"));
    }
  }, []);

  useEffect(() => {
    if (!value && availableRFIOptions.length > 0) {
      setValue(availableRFIOptions[0].value);
    }
  }, [availableRFIOptions, value, setValue]);

  return (
    <>
      <CommonModal
        isOpen={isOpen}
        widthSize="525px"
        onCloseModal={onCloseModal}
        modalBodyClass="p-0"
        header={{
          title: _t(`${moduleName} Options`),
          icon: (
            <FontAwesomeIcon className="w-4 h-4" icon="fa-regular fa-qrcode" />
          ),
          closeIcon: !showCloseIcon,
        }}
      >
        <div className="py-4">
          <div className="modal-body grid gap-5 overflow-y-auto max-h-[calc(100vh-200px)] px-6">
            {isDashLoading ? (
              <Spin className="w-full h-[90px] flex items-center justify-center" />
            ) : (
              <RadioGroupList
                formInputClassName="!p-0"
                className="gap-3"
                onChange={(e: RadioChangeEvent) => {
                  setValue(e.target.value);
                }}
                value={value}
                options={availableRFIOptions}
              />
            )}
          </div>

          {!isDashLoading && !availableRFIOptions?.length && (
            <div className="text-center !text-red-600 text-[15px] font-semibold">
              No Options Found !
            </div>
          )}

          <div className="modal-footer px-4 pt-4 text-center">
            <PrimaryButton
              className="w-fit"
              htmlType="submit"
              onClick={() => onClickAdd(value)}
              buttonText={_t(`Create`)}
              disabled={!availableRFIOptions?.length}
            />
          </div>
        </div>
      </CommonModal>
    </>
  );
};

export default RFIOptions;
