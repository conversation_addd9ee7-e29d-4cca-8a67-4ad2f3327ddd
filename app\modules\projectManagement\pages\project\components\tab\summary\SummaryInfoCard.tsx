import { useMemo } from "react";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";
import { SkeletonInput } from "~/shared/components/atoms/skeleton";
// Molecules
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useDateFormatter, useTranslation } from "~/hook";
// Other
import SummaryInfoCardList from "./SummaryInfoCardList";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { Number, sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";

const SummaryInfoCard = ({ details, isLoading }: IProjectSummaryProps) => {
  const { _t } = useTranslation();
  const dateFormat = useDateFormatter();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { date_format } = appSettings || {};
  const { formatter } = useCurrencyFormatter();

  function bothDateAreValid(
    date1: string | undefined,
    date2: string | undefined
  ) {
    if (!date1 || date1 === "00/00/0000" || !date2 || date2 === "00/00/0000") {
      return false;
    }
    return true; // Both dates are valid
  }

  const startEndDateNode = useMemo(() => {
    return `${
      (details.start_date &&
        details.start_date !== "00/00/0000" &&
        displayDateFormat(
          details?.start_date,
          CFConfig.day_js_date_format
        )?.format(CFConfig.day_js_date_format)) ||
      ""
    }

    ${bothDateAreValid(details.start_date, details.end_date) ? " - " : ""}

    ${
      (details.end_date &&
        details.end_date !== "00/00/0000" &&
        displayDateFormat(
          details?.end_date,
          CFConfig.day_js_date_format
        )?.format(CFConfig.day_js_date_format)) ||
      ""
    }`;
  }, [details]);

  const siteManagerName = useMemo(() => {
    return _t(
      HTMLEntities.decode(
        sanitizeString(details?.site_manager_name?.trim() ?? "")
      )
    );
  }, [details?.site_manager_name]);

  const projectManagerName = useMemo(() => {
    return _t(
      HTMLEntities.decode(
        sanitizeString(details?.project_manager_name?.trim() ?? "")
      )
    );
  }, [details?.project_manager_name]);

  return (
    <div className="grid 2xl:flex lg:grid-cols-3 md:grid-cols-2 gap-2.5">
      <SummaryInfoCardList
        title={_t("Gross Profit")}
        iconProps={{
          icon: "fa-solid fa-chart-mixed",
          containerClassName:
            "bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)] !h-10 !w-10 !min-w-10",
          id: "gross_profit_summary_icon",
          colors: ["#ADD100", "#7B920A"],
        }}
        iconClassName="w-5 h-5"
        contantWidth="w-[calc(100%-47px)]"
        children={
          <div className="flex items-center justify-between flex-wrap">
            <Tooltip
              title={_t("Invoiced to Date Minus Total Actual Costs")}
              placement="top"
            >
              <div>
                <Typography className="text-sm font-semibold text-primary-900 dark:text-white/90">
                  {details?.billing_vs_actual?.amount_invoiced !== null &&
                  Number(details?.billing_vs_actual?.amount_invoiced) === 0
                    ? "(0.00%)"
                    : `(${(
                        (Number(details?.billing_vs_actual?.gross_profit) /
                          Number(details?.billing_vs_actual?.amount_invoiced)) *
                        100
                      ).toFixed(2)}%)`}
                </Typography>
              </div>
            </Tooltip>
            <Tooltip
              title={_t("Invoiced to Date Minus Total Actual Costs")}
              placement="top"
            >
              <div>
                <Typography className="text-sm font-semibold text-[#198754] dark:text-white/90">
                  {
                    formatter(
                      (
                        Number(details?.billing_vs_actual?.gross_profit) / 100
                      ).toFixed(2)
                    ).value_with_symbol
                  }
                </Typography>
              </div>
            </Tooltip>
          </div>
        }
      />
      <SummaryInfoCardList
        title={_t("Site Manager")}
        iconProps={{
          icon: "fa-solid fa-user",
          containerClassName:
            "bg-[linear-gradient(180deg,#9BC3FF1a_0%,#418CFD1a_100%)] !h-10 !w-10 !min-w-10",
          id: "site_manager_summary_icon",
          colors: ["#9BC3FF", "#418CFD"],
        }}
        iconClassName="w-5 h-5"
        contantWidth="w-[calc(100%-47px)]"
        children={
          isLoading ? (
            <SkeletonInput className="!w-[150px] !h-[15px]" />
          ) : details.site_manager_name ? (
            <Tooltip title={siteManagerName} placement="top">
              <TypographyLink
                target="_blank"
                href={`${routes.MANAGE_DIRECTORY.url}/${details.site_manager_id}`}
                className="font-semibold !text-primary-900 dark:text-white/90 !text-sm hover:!text-orange-500 truncate block w-fit max-w-full"
              >
                <div className="flex items-center gap-2 pl-px">
                  <AvatarProfile
                    user={{
                      name: siteManagerName,
                      image: details.site_manager_image || "",
                    }}
                    className="m-auto"
                    iconClassName="text-[11px] font-semibold"
                  />
                  {siteManagerName}
                </div>
              </TypographyLink>
            </Tooltip>
          ) : (
            <div>
              <Typography className="text-primary-900 text-sm font-semibold">
                -
              </Typography>
            </div>
          )
        }
      />
      <SummaryInfoCardList
        title={_t("Project Manager")}
        iconProps={{
          icon: "fa-solid fa-user-tie",
          containerClassName:
            "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)] !h-10 !w-10 !min-w-10",
          id: "project_manager_summary_icon",
          colors: ["#50EBFD", "#5996E9"],
        }}
        iconClassName="w-5 h-5"
        contantWidth="w-[calc(100%-47px)]"
        children={
          isLoading ? (
            <SkeletonInput className="!w-[150px] !h-[15px]" />
          ) : details.project_manager_name !== "" ? (
            <Tooltip title={projectManagerName} placement="top">
              <TypographyLink
                target="_blank"
                href={`${routes.MANAGE_DIRECTORY.url}/${details.project_manager_id}`}
                className="font-semibold !text-primary-900 dark:text-white/90 !text-sm hover:!text-orange-500 truncate block w-fit max-w-full"
              >
                <div className="flex items-center gap-2 pl-px">
                  <AvatarProfile
                    user={{
                      name: projectManagerName,
                      image: details.project_manager_image || "",
                    }}
                    className="m-auto"
                    iconClassName="text-[11px] font-semibold"
                  />
                  {projectManagerName}
                </div>
              </TypographyLink>
            </Tooltip>
          ) : (
            <div>
              <Typography className="text-primary-900 text-sm font-semibold">
                -
              </Typography>
            </div>
          )
        }
      />
      <SummaryInfoCardList
        title={_t("Start/End Date")}
        iconProps={{
          icon: "fa-solid fa-calendar",
          containerClassName:
            "bg-[linear-gradient(180deg,#C285FF1a_0%,#962EFF1a_100%)] !h-10 !w-10 !min-w-10",
          id: "start_end_summary_icon",
          colors: ["#C285FF", "#962EFF"],
        }}
        iconClassName="w-5 h-5"
        contantWidth="w-[calc(100%-47px)]"
        children={
          isLoading ? (
            <SkeletonInput className="!w-[150px] !h-[15px]" />
          ) : (
            <Tooltip title={startEndDateNode?.trim()} placement="top">
              <Typography className="text-primary-900 text-sm font-semibold w-fit max-w-full truncate block">
                {startEndDateNode}

                {/* If value not exists, showing - */}
                {(!details.start_date || details.start_date === "00/00/0000") &&
                  (!details.end_date || details.end_date === "00/00/0000") &&
                  "-"}
              </Typography>
            </Tooltip>
          )
        }
      />
      <SummaryInfoCardList
        title={_t("Schedule Completed")}
        iconProps={{
          icon: "fa-solid fa-calendar-days",
          containerClassName:
            "bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)] !h-10 !w-10 !min-w-10",
          id: "schedule_completed_summary_icon",
          colors: ["#ADD100", "#7B920A"],
        }}
        iconClassName="w-5 h-5"
        contantWidth="w-[calc(100%-47px)]"
        children={
          isLoading ? (
            <SkeletonInput className="!w-[150px] !h-[15px]" />
          ) : (
            <Typography className="text-primary-900 text-sm font-semibold">
              {!Number(details?.progress ?? 0) || details?.progress === "0.00"
                ? "0"
                : Math.round(Number(details?.progress) * 100)}
              %
            </Typography>
          )
        }
      />
    </div>
  );
};

export default SummaryInfoCard;
