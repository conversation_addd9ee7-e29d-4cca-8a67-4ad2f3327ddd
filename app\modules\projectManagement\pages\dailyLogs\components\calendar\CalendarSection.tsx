import { useNavigate } from "@remix-run/react";
import type { CalendarProps } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";

// Hooks
import { routes } from "~/route-services/routes";
import { STATUS_CODE } from "~/shared/constants";

// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Calendar } from "~/shared/components/atoms/calendar";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// Hook
import { useTranslation } from "~/hook";
import { getGModuleFilters } from "~/zustand";
import { useEffect, useRef, useState } from "react";
import { getDLCalendarEvtAPI } from "../../redux/action";

dayjs.extend(updateLocale);
dayjs.updateLocale("en", {
  weekdaysMin: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"], // 3-char day names
});

const CalendarSection = ({ calendarView, filterDate }: ICalSecEventProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentMonth, setCurrentMonth] = useState<Dayjs>(dayjs());
  const [events, setEvents] = useState<TCalSecEvents>({});
  const filter = getGModuleFilters() as
    | Partial<DailyLogModuleFilter>
    | undefined;
  const prevFilterRef = useRef<IPrevFilterRef>({
    filter: undefined,
    filterDate: undefined,
  });

  const getListData = (value: Dayjs) => {
    const dateStr = value.format("YYYY-MM-DD");
    return events[dateStr] || [];
  };

  const dateCellRender = (value: Dayjs) => {
    const listData = getListData(value);
    return (
      <ul className="events">
        {listData.map((item) => (
          <li
            key={item.logId}
            onClick={() =>
              navigate(`${routes.MANAGE_DAILYLOG.url}/${item.logId}`)
            }
          >
            <Tooltip
              overlayStyle={{ maxWidth: "100%" }}
              title={
                <div className="grid text-left">
                  {item.project_name && (
                    <Typography className="text-white text-xs">
                      {_t("Project")}: {item.project_name}
                    </Typography>
                  )}
                  {item.customer_name && (
                    <Typography className="text-white text-xs">
                      {_t("Created By")}: {item.customer_name}
                    </Typography>
                  )}
                </div>
              }
            >
              <Tag
                color={`${item?.color || "#1796b0"}`}
                style={{
                  color: `${item?.textColor || "#fff"}`,
                }}
                className="cursor-pointer mb-2.5 truncate block"
              >
                {item.title}
              </Tag>
            </Tooltip>
          </li>
        ))}
      </ul>
    );
  };

  const cellRender: CalendarProps<Dayjs>["cellRender"] = (current, info) => {
    if (info.type === "date") return dateCellRender(current);
    return info.originNode;
  };

  // Can't find appropriate appropriate type for headerRender
  const headerRender: CalendarProps<Dayjs>["headerRender"] = ({
    value,
    onChange,
  }: any) => {
    const prevMonth = () => {
      const newDate = value.subtract(1, "month");
      setCurrentMonth(newDate);
      onChange(newDate);
    };

    const nextMonth = () => {
      const newDate = value.add(1, "month");
      setCurrentMonth(newDate);
      onChange(newDate);
    };

    // ===================
    const start = 0;
    const end = 12;
    const monthOptions: IOption[] = [];
    let current = value.clone();
    const localeData = value?.localeData();
    const months = [];
    for (let i = 0; i < 12; i++) {
      current = current.month(i);
      // months.push(localeData.monthsShort(current));
      months.push(localeData.months(current));
    }
    for (let i = start; i < end; i++) {
      monthOptions.push({ label: months[i], value: i.toString() });
    }
    const selectedMonth = value.month();
    const selectedYear = value.year();
    const yearOptions: IOption[] = [];
    for (let i = selectedYear - 15; i < selectedYear + 15; i += 1) {
      yearOptions.push({ label: i, value: i.toString() });
    }

    return (
      <div className="relative sm:px-4 pb-4">
        <div className="flex sm:justify-center gap-2">
          <SelectField
            label=""
            popupClassName="!w-[114px]"
            className="bg-[#EBF1F9] disabled:opacity-50 header-dropdown-select pl-3 !h-7 rounded"
            fieldClassName="before:hidden"
            formInputClassName="w-[114px]"
            placement="bottomRight"
            labelPlacement="left"
            options={monthOptions}
            value={selectedMonth?.toString()}
            onChange={(newMonth) => {
              const now = value.clone().month(Number(newMonth));
              onChange(now);
            }}
          />

          <SelectField
            label=""
            popupClassName="!w-[90px]"
            className="bg-[#EBF1F9] disabled:opacity-50 header-dropdown-select pl-3 !h-7 rounded"
            fieldClassName="before:hidden"
            formInputClassName="w-[90px]"
            placement="bottomRight"
            labelPlacement="left"
            options={yearOptions}
            value={selectedYear?.toString()}
            onChange={(newYear) => {
              const now = value.clone().year(Number(newYear));
              onChange(now);
            }}
          />
        </div>

        <div className="absolute flex items-center gap-1 right-0 sm:top-1 top-0">
          {isLoading ? (
            <FontAwesomeIcon
              className="w-5 h-5 mr-1 fa-spin"
              icon="fa-duotone fa-solid fa-spinner-third"
            />
          ) : (
            ""
          )}
          <ButtonWithTooltip
            tooltipTitle={_t("Previous")}
            tooltipPlacement="top"
            icon="fa-regular fa-chevron-left"
            iconClassName="h-5 w-5"
            className="!w-7 !h-7"
            onClick={prevMonth}
            disabled={isLoading}
          />
          <ButtonWithTooltip
            tooltipTitle={_t("Next")}
            tooltipPlacement="top"
            icon="fa-regular fa-chevron-right"
            iconClassName="h-5 w-5"
            className="!w-7 !h-7"
            onClick={nextMonth}
            disabled={isLoading}
          />
        </div>
      </div>
    );
  };

  const fetchCalEventList = async (date: Dayjs) => {
    try {
      setIsLoading(true);

      // Check if filterDate exists, and format startDate and endDate
      const updatedFilter = {
        ...filter,
        module_status:
          filter?.status == STATUS_CODE.ALL
            ? undefined
            : filter?.status
            ? filter?.status
            : STATUS_CODE.ACTIVE,
        status: undefined,
        start_date: filterDate?.startDate || filter?.start_date || "",
        end_date: filterDate?.endDate || filter?.end_date || "",
      };

      const dataParams = {
        year: date.year().toString(),
        view: "week",
        start_date_range: date.startOf("month").format("YYYY-MM-DD HH:mm:ss"),
        filter: updatedFilter ? [JSON.stringify(updatedFilter)] : undefined,
      };

      const resData = (await getDLCalendarEvtAPI(
        dataParams
      )) as IDLCalendarEvtRes;

      if (resData.success) {
        const resDataArr = resData?.data?.length ? resData.data : [];
        const eventsDataOne = resDataArr.reduce<IDLCalendarEventsData>(
          (acc, event) => {
            if (event.start_date) {
              const dateStr = event?.start_date
                ? event.start_date.split(" ")[0]
                : "";
              if (!acc[dateStr]) {
                acc[dateStr] = [];
              }
              acc[dateStr].push({
                title: event.title,
                logId: event.log_id,
                project_name: event.project_name || "",
                customer_name: event.customer_name || "",
                color: event.color || "",
                textColor: event.textColor || "",
              });
            }
            return acc;
          },
          {}
        );
        setEvents(eventsDataOne);
      }
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    const isFilterChanged =
      JSON.stringify(filter) !== JSON.stringify(prevFilterRef.current?.filter);
    const isFilterDateChanged =
      filterDate?.startDate !== prevFilterRef.current?.filterDate?.startDate ||
      filterDate?.endDate !== prevFilterRef.current?.filterDate?.endDate;

    if (calendarView && (isFilterChanged || isFilterDateChanged)) {
      fetchCalEventList(currentMonth);
      prevFilterRef.current = {
        filter,
        filterDate,
      };
    }
  }, [currentMonth, calendarView, filter, filterDate]);

  return (
    <Calendar
      headerRender={headerRender}
      mode="month"
      cellRender={cellRender}
    />
  );
};
export default CalendarSection;

// ! How To Use
{
  /* <div className="w-full  min-h-[250px]">
<CalendarSection />
</div> */
}
