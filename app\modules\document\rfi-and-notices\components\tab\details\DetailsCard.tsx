import { useTranslation } from "~/hook";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import type { DatePickerProps } from "antd";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useCallback, useEffect, useRef, useState } from "react";
import { useAppRFIDispatch, useAppRFISelector } from "../../../redux/store";
import { RFIDetailsField } from "../../../utils/constasnts";
import { defaultConfig } from "~/data";
import { RFIFieldStatus } from "~/constants/rfi-and-notices";
import {
  filterOptionBySubstring,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { updateRFIDetails } from "../../../redux/slices/rfiDetailSlice";
import {
  getRfiDetail,
  updateRfiDetailsApi,
} from "../../../redux/action/rfiDetailAction";
import { useParams } from "@remix-run/react";
import delay from "lodash/delay";
import dayjs, { Dayjs } from "dayjs";
import { getGConfig, getGSettings } from "~/zustand";
import { sanitizeString } from "~/helpers/helper";
import { getGlobalTypes } from "~/zustand/global/types/slice";
import { onKeyDownCurrency } from "~/shared/utils/helper/common";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import debounce from "lodash/debounce";

export const getStatusForField = (
  loadingStatus: IFieldStatus[],
  fieldName: string
): IStatus => {
  const itemField = loadingStatus.find(
    (item: IFieldStatus) => item && item.field === fieldName
  );
  if (itemField && itemField.status) {
    return itemField.status;
  }
  return "button";
};

const DetailsCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const { date_format }: GSettings = getGSettings();
  const gConfig: GConfig = getGConfig();
  const { tab, id: RFI_Id }: RouteParams = useParams();
  const dispatch = useAppRFIDispatch();
  const [inputValues, setInputValues] =
    useState<Partial<IRFIDetails>>(RFIDetailsField);
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const [isDateTimeVisible, setDateTimeVisible] = useState<boolean>(false);
  const dtDivRef = useRef<HTMLLIElement>(null);
  const [customerFieldType, setCustomerFieldType] = useState<string>("");
  const [customerOptions, setCustomerOptions] = useState<CustomerEmailTab[]>(
    []
  );
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [selectedContactId, setSelectedContactId] = useState<number | null>(
    null
  );
  const [selectedAdiContactId, setSelectedAdiContactId] = useState<
    number | null
  >(null);
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const loadingStatusRef = useRef(RFIFieldStatus);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(RFIFieldStatus);
  const { rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );
  const [fixedDirType, setFixedDirType] = useState<number | null>(null);
  const typesData = getGlobalTypes();
  const dateTimeVisibleSelectRef = useRef<HTMLLIElement>(null);
  const referralSourceData =
    typesData
      ?.filter((item) => item.type === "correspondence_schedule_notice")
      ?.map((item) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.type_id,
      })) || [];

  const referralComplianceSourceData =
    typesData
      ?.filter((item) => item.type === "correspondence_compliance_notice")
      ?.map((item) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.type_id.toString(),
      })) || [];

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: Partial<IRFIDetails>) => {
    const field = Object.keys(data)[0] as keyof IRFIDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateRfiDetailsApi({
      correspondence_id: RFI_Id,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      // dispatch(updateRFIDetails(data)); // temp-solution
      dispatch(
        getRfiDetail({
          correspondence_id: RFI_Id || "",
          add_event: true,
        })
      );
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 1000);
  };

  const submitUpdate = useCallback(
    async (data: { [key: string]: string | number | boolean | null }) => {
      const field = Object.keys(data);
      const response = (await updateRfiDetailsApi({
        correspondence_id: RFI_Id,
        ...data,
      })) as ApiCallResponse;
      if (response.success) {
        if (field[0] === "correspondence_date") {
          const values = Object.values(data)[0] as string;
          dispatch(
            updateRFIDetails({
              ...data,
              correspondence_date: !!values
                ? dayjs(values).format(date_format)
                : "",
            })
          );
        } else {
          dispatch(updateRFIDetails(data));
        }
      } else {
        notification.error({
          message: "Error",
          description: response.message,
        });
      }
      return response;
    },
    []
  );

  const handleUpdate = useCallback(
    async (key: string, value: string | number | null) => {
      if (!rfiDetail) return;
      let data = {
        [key]: value,
      };
      if (key === "correspondence_date" && value) {
        let parsedDate;
        if (date_format === "DD/MM/YYYY") {
          parsedDate = dayjs(value, "DD/MM/YYYY", true);
        } else if (date_format === "MM/DD/YYYY") {
          parsedDate = dayjs(value, "MM/DD/YYYY", true);
        } else {
          parsedDate = dayjs(
            value,
            ["YYYY-MM-DD", "DD.MM.YYYY", "DD/MMM/YYYY"],
            true
          );
        }

        data = {
          [key]: parsedDate.isValid() ? parsedDate.format("YYYY-MM-DD") : null,
        };
      }
      handleChangeFieldStatus({
        field: key,
        status: "loading",
        action: "API",
      });
      const { success } = await submitUpdate(data);
      if (success) {
        handleChangeFieldStatus({
          field: key,
          status: "success",
          action: "API",
        });
      }

      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatusRef.current,
          key
        );
        handleChangeFieldStatus({
          field: key,
          status: "FOCUS" === fieldAction ? "save" : "button",
          action: fieldAction || "API",
        });
        setDateTimeVisible(false);
      }, 1000);
    },
    [rfiDetail]
  );

  const selectedCustomerList = (): TselectedContactSendMail[] => {
    let selectedCustomer: TselectedContactSendMail[] = [];
    let userFrom = {
      user_id: rfiDetail.user_from,
      display_name: rfiDetail.from_username,
      contact_id: rfiDetail.contact_id,
      type_name: rfiDetail.from_type_name ?? "",
      image: rfiDetail.from_image ?? "",
      type_key: rfiDetail.from_type_key ?? "",
      parent_type_key: rfiDetail.parent_type_key ?? "",
    };

    if (customerFieldType == "from") {
      selectedCustomer =
        inputValues.user_from && inputValues.from_username
          ? ([userFrom] as TselectedContactSendMail[])
          : [];
    } else if (customerFieldType == "cc") {
      selectedCustomer =
        inputValues.user_cc_details && inputValues.user_cc_details?.length > 0
          ? (inputValues.user_cc_details as TselectedContactSendMail[])
          : [];
    } else {
      selectedCustomer =
        inputValues.user_details && inputValues.user_details?.length > 0
          ? (inputValues.user_details as TselectedContactSendMail[])
          : [];
    }
    return selectedCustomer;
  };

  const handleContact = (data: TselectedContactSendMail[]) => {
    const userIdsString =
      data.length > 0 ? data.map((item) => item?.user_id).join(",") : "";
    const userToIdsString =
      data.length > 0 ? data.map((item) => item?.contact_id).join(",") : "";
    const suppliersTo = data
      ?.map((user) => {
        if (user?.contact_id == 0) {
          return user?.user_id?.toString();
        } else {
          return `${user?.user_id}|${user?.contact_id}`;
        }
      })
      ?.join(",");

    const existingUserCC = inputValues.user_cc
      ? inputValues?.user_cc?.toString()?.split(",").filter(Boolean)
      : [];
    const mergedUserCC = [...existingUserCC];
    const mergedUserCCString = mergedUserCC.join(",");
    if (customerFieldType === "from") {
      handleUpdateField({
        user_from: userIdsString || "",
        user_cc: mergedUserCCString || "",
      });
      // setInputValues({
      //   ...inputValues,
      //   user_from: data[0].user_id,
      //   from_username: data[0].display_name,
      //   contact_id: data[0]?.contact_id?.toString(),
      //   from_type_name: data[0].type_name,
      //   from_image: data[0].image,
      //   from_type_key: data[0].type_key,
      //   parent_type_key: data[0].parent_type_key,
      // });
    } else if (customerFieldType === "cc") {
      handleUpdateField({
        user_cc: suppliersTo || "",
        user_contact_id: userToIdsString
      });
      // setInputValues({
      //   ...inputValues,
      //   user_cc_details: data?.map((record) => ({
      //     user_id: record?.user_id,
      //     display_name: record?.display_name,
      //     contact_id: record?.contact_id,
      //     type_name: record?.type_name,
      //     image: record?.image,
      //     type_key: record?.type_key,
      //     parent_type_key: record?.parent_type_key,
      //   })),
      // });
    } else if (customerFieldType === "to") {
      if (
        userIdsString &&
        userIdsString.split(",").filter((id) => id.trim() !== "").length > 0
      ) {
        handleUpdateField({
          user_to: suppliersTo,
          user_cc: mergedUserCCString || "",
          user_contact_id: userToIdsString,
        });

        // setInputValues({
        //   ...inputValues,
        //   user_details: data?.map((record) => ({
        //     user_id: record?.user_id,
        //     display_name: record?.display_name,
        //     contact_id: record?.contact_id,
        //     type_name: record?.type_name,
        //     image: record?.image,
        //     type_key: record?.type_key,
        //     parent_type_key: record?.parent_type_key,
        //   })),
        // });
      } else {
        notification.error({
          description: _t(
            "To field is required. you have select at least one."
          ),
        });
      }
    }
  };

  useEffect(() => {
    if (rfiDetail?.correspondence_id) {
      setInputValues(rfiDetail);
    }
  }, [rfiDetail, dispatch]);

  useEffect(() => {
    const dirType = Number(inputValues?.user_details?.[0]?.dir_type);
    if (dirType === 1 || (fixedDirType === 2 && dirType === 0)) {
      setFixedDirType(2);
    } else {
      setFixedDirType(dirType);
    }
  }, [inputValues?.user_details?.[0]?.dir_type]);

  const handleChangeDate = debounce((dateString: string) => {
    if (dateString === "") {
      setInputValues({
        ...inputValues,
        correspondence_date: dateString,
      });
      handleUpdateField({
        correspondence_date: "",
      });
      return false;
    } else {
      const isValidFormat = dayjs(
        dateString,
        CFConfig.day_js_date_format,
        true
      ).isValid();
      const newValue = dateString
        ? backendDateFormat(dateString.toString(), CFConfig.day_js_date_format)
        : null;
      if (isValidFormat) {
        const ResolutionvalidationDate = dayjs(
          dateString,
          CFConfig.day_js_date_format
        );
        const ResolutionDate = dayjs(
          rfiDetail?.resolution_date,
          CFConfig.day_js_date_format
        );
        const ResolutionDeadlineDate = dayjs(
          rfiDetail?.resolve_by_date,
          CFConfig.day_js_date_format
        );
        const ResponseNeededDate = dayjs(
          rfiDetail?.response_needed_by,
          CFConfig.day_js_date_format
        );
        const ResponsereceivedDate = dayjs(
          rfiDetail?.response_received,
          CFConfig.day_js_date_format
        );
        const existingUserCC = inputValues.user_cc
          ? inputValues?.user_cc?.toString()?.split(",").filter(Boolean)
          : [];
        const mergedUserCC = [...existingUserCC];
        const mergedUserCCString = mergedUserCC.join(",");
        if (ResolutionvalidationDate > ResolutionDate) {
          notification.error({
            description:
              "Resolution date must be equal to or after the Date value.",
          });

          setInputValues({
            ...inputValues,
            correspondence_date: rfiDetail?.correspondence_date,
          });

          return false;
        }
        if (ResolutionvalidationDate > ResolutionDeadlineDate) {
          notification.error({
            description:
              "Resolution Deadline must be equal to or after the Date value.",
          });

          setInputValues({
            ...inputValues,
            correspondence_date: rfiDetail?.correspondence_date,
          });

          return false;
        }
        if (ResolutionvalidationDate > ResponseNeededDate) {
          notification.error({
            description:
              "Response Needed by must be greater than or equal to Date value.",
          });

          setInputValues({
            ...inputValues,
            correspondence_date: rfiDetail?.correspondence_date,
          });

          return false;
        }
        if (ResolutionvalidationDate > ResponsereceivedDate) {
          notification.error({
            description:
              "Response Received must be equal to or after the Date value.",
          });

          setInputValues({
            ...inputValues,
            correspondence_date: rfiDetail?.correspondence_date,
          });

          return false;
        }
        setInputValues({
          ...inputValues,
          correspondence_date: dateString.toString(),
        });
        handleUpdateField({
          correspondence_date: newValue,
          user_cc: mergedUserCCString || "",
        });
      }
    }
  }, 100);

  const disableAfterToday: DatePickerProps["disabledDate"] = (
    current: Dayjs
  ) => {
    return current && current > dayjs().endOf("day");
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "contractor_details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li>
                <InputField
                  label={_t("Type")}
                  labelPlacement="left"
                  placeholder={_t("Type")}
                  editInline={false}
                  iconView={true}
                  required={false}
                  readOnly={isReadOnly}
                  disabled
                  value={inputValues.correspondence_type}
                  onChange={() => {}}
                />
              </li>
              <li
                className={`overflow-hidden flex ${
                  !isDateTimeVisible ? "hidden" : ""
                }`}
                // ref={dtDivRef}
              >
                <InlineField
                  label={_t("Date/Time")}
                  labelPlacement="left"
                  field={
                    <div className="grid grid-cols-2 gap-1 items-center w-full">
                      <DatePickerField
                        label=""
                        placeholder={_t("Select Date")}
                        labelPlacement="left"
                        name="correspondence_date"
                        disabled={isReadOnly}
                        readOnly={isReadOnly}
                        editInline={true}
                        disabledDate={
                          rfiDetail.correspondence_key === "correspondence_rfi"
                            ? disableAfterToday
                            : undefined
                        }
                        iconView={true}
                        allowClear={
                          rfiDetail.correspondence_key ===
                          "correspondence_schedule_notice"
                            ? true
                            : false
                        }
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "correspondence_date"
                        )}
                        value={displayDateFormat(
                          inputValues.correspondence_date?.toString().trim(),
                          date_format
                        )}
                        onChange={(_, dateString) =>
                          handleChangeDate(dateString as string)
                        }
                        format={date_format}
                        inputReadOnly={true}
                      />
                      <TimePickerField
                        label=""
                        placeholder="00:00"
                        labelPlacement="left"
                        id="inspection_time"
                        name="inspection_time"
                        disabled={isReadOnly}
                        readOnly={isReadOnly}
                        editInline={true}
                        iconView={true}
                        format="hh:mm A"
                        allowClear={
                          rfiDetail.correspondence_key ===
                          "correspondence_schedule_notice"
                            ? true
                            : false
                        }
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "correspondence_time"
                        )}
                        value={
                          inputValues.correspondence_time?.trim()
                            ? dayjs(
                                inputValues.correspondence_time.trim(),
                                "hh:mm A"
                              )
                            : null
                        }
                        onChange={(_, time) => {
                          if (typeof time === "string" && time) {
                            handleUpdate("correspondence_time", time as string);
                            setInputValues({
                              ...inputValues,
                              correspondence_time: time as string,
                            });
                          } else {
                            handleUpdate("correspondence_time", null);
                            setInputValues({
                              ...inputValues,
                              correspondence_time: null,
                            });
                          }
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li
                className={`overflow-hidden ${
                  isDateTimeVisible ? "hidden" : ""
                }`}
                ref={dateTimeVisibleSelectRef}
              >
                <InlineField
                  label={_t("Date/Time")}
                  labelPlacement="left"
                  field={
                    <div className="relative w-full group/edit">
                      <InputField
                        labelPlacement="left"
                        placeholder={_t("Select Date")}
                        editInline={true}
                        iconView={true}
                        required={false}
                        readOnly={isReadOnly}
                        readOnlyClassName="!h-[34px]"
                        value={
                          inputValues?.correspondence_date
                            ? inputValues.correspondence_time
                              ? `${inputValues.correspondence_date} - ${inputValues.correspondence_time}`
                              : inputValues.correspondence_date
                            : inputValues?.correspondence_time
                            ? `${inputValues.correspondence_time}`
                            : "-"
                        }
                        onFocus={() => {
                          setDateTimeVisible(true);
                        }}
                        onChange={() => {}}
                      />
                      {inputValues.correspondence_date &&
                      inputValues.correspondence_time &&
                      !isReadOnly &&
                      !["loading", "success", "error"].includes(
                        getStatusForField(loadingStatus, "correspondence_date")
                      ) ? (
                        <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                          {rfiDetail.correspondence_key ===
                            "correspondence_schedule_notice" && (
                            <FontAwesomeIcon
                              icon="fa-solid fa-circle-xmark"
                              className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                              onClick={() => {
                                handleUpdate("correspondence_date", "");
                                handleUpdate("correspondence_time", null);
                                setInputValues((prev) => ({
                                  ...prev,
                                  correspondence_date: "",
                                  correspondence_time: null,
                                }));
                              }}
                            />
                          )}
                        </div>
                      ) : null}
                    </div>
                  }
                />
              </li>
              {rfiDetail.correspondence_key ===
                "correspondence_complience_notice" && (
                <li>
                  <SelectField
                    label={_t("Compliance Type")}
                    placeholder={_t("Select Compliance Type")}
                    name="notice_type"
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    disabled={isReadOnly}
                    readOnly={isReadOnly}
                    value={
                      referralComplianceSourceData.find(
                        (opt) => Number(opt.value) === inputValues?.notice_type
                      )?.value || ""
                    }
                    options={
                      referralComplianceSourceData.map((type) => ({
                        label: type?.label,
                        value: type?.value,
                      })) ?? []
                    }
                    onChange={async (value: string | string[]) => {
                      let newValue = 0;

                      if (!Array.isArray(value)) {
                        newValue = Number(value);
                      }

                      setInputValues({
                        ...inputValues,
                        notice_type: newValue,
                      });

                      if (newValue !== Number(rfiDetail.notice_type)) {
                        handleUpdateField({ notice_type: newValue });
                      } else {
                        handleChangeFieldStatus({
                          field: "notice_type",
                          status: "button",
                          action: "BLUR",
                        });

                        setInputValues({
                          ...inputValues,
                          notice_type: Number(rfiDetail.notice_type),
                        });
                      }
                    }}
                    fixStatus={getStatusForField(loadingStatus, "notice_type")}
                  />
                </li>
              )}
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("To")}
                  name="user_to"
                  placeholder={_t("Select/View Contact")}
                  labelPlacement="left"
                  readOnlyClassName="sm:block flex"
                  editInline={true}
                  iconView={true}
                  required={false}
                  value={
                    inputValues.user_details && inputValues.user_details.length
                      ? inputValues.user_details.length > 1
                        ? inputValues.user_details.length + " Selected"
                        : HTMLEntities.decode(
                            sanitizeString(
                              inputValues.user_details
                                .map((item) => item?.display_name)
                                .join(", ")
                            )
                          )
                      : ""
                  }
                  avatarProps={
                    inputValues.user_details?.length == 1
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(
                                inputValues.user_details[0]?.display_name
                              )
                            ),
                            image: inputValues.user_details[0]?.image,
                          },
                        }
                      : undefined
                  }
                  readOnly={isReadOnly}
                  statusProps={
                    inputValues.user_details &&
                    inputValues.user_details.length > 0
                      ? {
                          status: getStatusForField(loadingStatus, "user_to"),
                        }
                      : {}
                  }
                  onClick={() => {
                    setCustomerFieldType("to");
                    setCustomerOptions([
                      defaultConfig.employee_key,
                      "my_crew",
                      defaultConfig.customer_key,
                      defaultConfig.contractor_key,
                      defaultConfig.vendor_key,
                      defaultConfig.misc_contact_key,
                      "by_service",
                      "my_project",
                    ]);
                    setIsOpenSelectCustomer(true);
                  }}
                  rightIcon={
                    <>
                      {!!inputValues.user_details &&
                      inputValues.user_details.length === 1 ? (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={async () => {
                              setContactDetailDialogOpen(true);
                              setSelectedContactId(null);
                              setSelectedContactId(
                                Number(inputValues?.user_details?.[0]?.user_id)
                              );
                              setSelectedAdiContactId(
                                inputValues?.user_details?.[0]
                                  ?.user_contact_id as number
                              );
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={
                              inputValues?.user_details[0]?.user_id?.toString() as string
                            }
                            directoryTypeKey={
                              fixedDirType
                                ? getDirectaryKeyById(fixedDirType, gConfig)
                                : ""
                            }
                          />
                        </div>
                      ) : (
                        <AvatarIconPopover
                          placement="bottom"
                          assignedTo={
                            inputValues?.user_details as IAssignedToUsers[]
                          }
                          setSelectedUserId={(data) => {
                            setSelectedContactId(data?.id);
                            setSelectedAdiContactId(data?.contactId || 0);
                          }}
                          setIsOpenContactDetails={setContactDetailDialogOpen}
                          redirectionIcon={true}
                        />
                      )}
                    </>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("From")}
                  placeholder={_t("Request From")}
                  name="user_from"
                  labelPlacement="left"
                  readOnlyClassName="sm:block flex"
                  editInline={true}
                  required={false}
                  iconView={true}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "user_from"),
                  }}
                  value={inputValues.from_username ?? ""}
                  avatarProps={
                    inputValues.from_username
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(inputValues.from_username)
                            ),
                            image: inputValues.from_image,
                          },
                        }
                      : undefined
                  }
                  onClick={() => {
                    setCustomerFieldType("from");
                    setCustomerOptions([
                      defaultConfig.employee_key,
                      "my_crew",
                      "my_project",
                    ]);
                    setIsOpenSelectCustomer(true);
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "user_from") === "loading"
                  }
                  readOnly={isReadOnly}
                  rightIcon={
                    <>
                      {inputValues.from_username && inputValues.user_from && (
                        <div className="flex gap-1 items-center">
                          <ContactDetailsButton
                            onClick={async () => {
                              setSelectedContactId(
                                Number(inputValues?.user_from || 0)
                              );
                              setSelectedAdiContactId(
                                Number(inputValues.contact_id || "")
                              );
                              setContactDetailDialogOpen(true);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={inputValues?.user_from.toString()}
                            directoryTypeKey={getDirectaryKeyById(
                              Number(
                                inputValues?.user_details?.[0].dir_type
                              ) === 1
                                ? 2
                                : Number(
                                    inputValues?.user_details?.[0].dir_type
                                  ),
                              gConfig
                            )}
                          />
                        </div>
                      )}
                    </>
                  }
                />
              </li>
              {rfiDetail.correspondence_key === "correspondence_rfi" && (
                <li className="overflow-hidden">
                  <ButtonField
                    label={_t("CC")}
                    name="user_cc"
                    placeholder={_t("Select/View Contact")}
                    labelPlacement="left"
                    readOnlyClassName="sm:block flex"
                    editInline={true}
                    iconView={true}
                    required={false}
                    value={
                      inputValues.user_cc_details &&
                      inputValues.user_cc_details.length
                        ? inputValues.user_cc_details.length > 1
                          ? inputValues.user_cc_details.length + " Selected"
                          : HTMLEntities.decode(
                              sanitizeString(
                                inputValues.user_cc_details
                                  .map((item) => item?.display_name)
                                  .join(", ")
                              )
                            )
                        : ""
                    }
                    avatarProps={
                      inputValues.user_cc_details?.length == 1
                        ? {
                            user: {
                              name: HTMLEntities.decode(
                                sanitizeString(
                                  inputValues.user_cc_details[0]?.display_name
                                )
                              ),
                              image: inputValues.user_cc_details[0]?.image,
                            },
                          }
                        : undefined
                    }
                    readOnly={isReadOnly}
                    statusProps={
                      inputValues.user_cc_details &&
                      inputValues.user_cc_details.length > 0
                        ? {
                            status: getStatusForField(loadingStatus, "user_cc"),
                          }
                        : {}
                    }
                    onClick={() => {
                      setCustomerFieldType("cc");
                      setCustomerOptions([
                        defaultConfig.employee_key,
                        "my_crew",
                        defaultConfig.customer_key,
                        defaultConfig.contractor_key,
                        defaultConfig.vendor_key,
                        defaultConfig.misc_contact_key,
                        "by_service",
                        "my_project",
                      ]);
                      setIsOpenSelectCustomer(true);
                    }}
                    rightIcon={
                      <>
                        {inputValues.user_cc_details &&
                          inputValues.user_cc_details.length === 1 && (
                            <div className="flex gap-1 items-center">
                              <ContactDetailsButton
                                onClick={async () => {
                                  setContactDetailDialogOpen(true);
                                  setSelectedContactId(null);
                                  setSelectedContactId(
                                    Number(
                                      inputValues?.user_cc_details?.[0]?.user_id
                                    )
                                  );
                                  setSelectedAdiContactId(
                                    inputValues?.user_cc_details?.[0]
                                      ?.user_contact_id as number
                                  );
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                directoryId={
                                  inputValues?.user_cc_details[0]?.user_id?.toString() as string
                                }
                                directoryTypeKey={
                                  fixedDirType
                                    ? getDirectaryKeyById(fixedDirType, gConfig)
                                    : ""
                                }
                              />
                            </div>
                          )}

                        {inputValues?.user_cc_details &&
                          inputValues?.user_cc_details?.length > 1 && (
                            <AvatarIconPopover
                              placement="bottom"
                              redirectionIcon={true}
                              assignedTo={
                                inputValues?.user_cc_details as IAssignedToUsers[]
                              }
                              setSelectedUserId={(data) => {
                                setSelectedContactId(data?.id);
                                setSelectedAdiContactId(data?.contactId || 0);
                              }}
                              setIsOpenContactDetails={
                                setContactDetailDialogOpen
                              }
                            />
                          )}
                      </>
                    }
                  />
                </li>
              )}
              {rfiDetail.correspondence_key ===
                "correspondence_schedule_notice" && (
                <>
                  <li>
                    <SelectField
                      label={_t("Notice Type")}
                      placeholder={_t("Select Notice Type")}
                      name="notice_type"
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      showSearch={false}
                      disabled={isReadOnly}
                      readOnly={isReadOnly}
                      value={
                        referralSourceData.find(
                          (opt) =>
                            Number(opt.value) === inputValues?.notice_type
                        )?.value || ""
                      }
                      options={
                        referralSourceData.map((type) => ({
                          label: type?.label,
                          value: type?.value,
                        })) ?? []
                      }
                      onChange={async (value: string | string[]) => {
                        let newValue = 0;

                        if (!Array.isArray(value)) {
                          newValue = Number(value);
                        }

                        setInputValues({
                          ...inputValues,
                          notice_type: newValue,
                        });

                        if (newValue !== Number(rfiDetail.notice_type)) {
                          handleUpdateField({ notice_type: newValue });
                        } else {
                          handleChangeFieldStatus({
                            field: "notice_type",
                            status: "button",
                            action: "BLUR",
                          });

                          setInputValues({
                            ...inputValues,
                            notice_type: Number(rfiDetail.notice_type),
                          });
                        }
                      }}
                      onSelect={(e) => {
                        const value = e.toString();
                        if (
                          rfiDetail &&
                          String(value) !== String(rfiDetail.notice_type)
                        ) {
                          handleUpdateField({
                            notice_type: value ? value : "",
                          });
                        } else {
                          handleChangeFieldStatus({
                            field: "notice_type",
                            status: "button",
                            action: "BLUR",
                          });
                        }
                      }}
                      fixStatus={getStatusForField(
                        loadingStatus,
                        "notice_type"
                      )}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "notice_type",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                    />
                  </li>
                  <li className="overflow-hidden">
                    <InputNumberField
                      label={"# " + _t("of Days Delayed")}
                      labelPlacement="left"
                      name="days_delay"
                      placeholder={_t("Days Delayed")}
                      editInline={true}
                      iconView={true}
                      required={false}
                      readOnly={isReadOnly}
                      value={inputValues.days_delay}
                      onChange={(value) => {
                        setInputValues({
                          ...inputValues,
                          days_delay: value ? Number(value) : null,
                        });
                      }}
                      onBlur={(e) => {
                        const value = e?.target?.value
                          ? e?.target?.value
                          : null;
                        if (value !== rfiDetail.days_delay) {
                          handleUpdateField({
                            days_delay: value ? Number(value.trim()) : null,
                          });
                          setInputValues({
                            ...inputValues,
                            days_delay: value ? Number(value.trim()) : null,
                          });
                        } else {
                          handleChangeFieldStatus({
                            field: "days_delay",
                            status: "button",
                            action: "BLUR",
                          });
                          setInputValues({
                            ...inputValues,
                            days_delay: rfiDetail.days_delay,
                          });
                        }
                      }}
                      fixStatus={getStatusForField(loadingStatus, "days_delay")}
                      formatter={(value, info) => {
                        return inputFormatter(String(value)).value;
                      }}
                      parser={(value) => {
                        if (!value) return "";
                        const inputValue = unformatted(value.toString());
                        return inputValue;
                      }}
                      onFocus={() =>
                        handleChangeFieldStatus({
                          field: "days_delay",
                          status: "save",
                          action: "FOCUS",
                        })
                      }
                      onMouseEnter={() => {
                        handleChangeFieldStatus({
                          field: "days_delay",
                          status: "edit",
                          action: "ME",
                        });
                      }}
                      onMouseLeaveDiv={() => {
                        handleChangeFieldStatus({
                          field: "days_delay",
                          status: "button",
                          action: "ML",
                        });
                      }}
                      onKeyDown={(event) =>
                        onKeyDownCurrency(event, {
                          integerDigits: 3,
                          decimalDigits: 1,
                          unformatted,
                          decimalSeparator: inputFormatter().decimal_separator,
                        })
                      }
                    />
                  </li>
                </>
              )}
            </ul>
          </div>
        }
      />

      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          projectId={rfiDetail?.project_id}
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            setIsOpenSelectCustomer(false);
            setCustomerFieldType("");
            setCustomerOptions([]);
          }}
          singleSelecte={customerFieldType === "from" ? true : false}
          options={customerOptions?.length > 0 ? customerOptions : []}
          setCustomer={(data) => {
            handleContact(
              data.length ? (data as TselectedContactSendMail[]) : []
            );
          }}
          selectedCustomer={selectedCustomerList()}
          groupCheckBox={true}
          additionalContactDetails={customerFieldType === "from" ? 0 : 1}
        />
      )}

      {contactDetailDialogOpen && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => {
            setSelectedAdiContactId(null);
            setContactDetailDialogOpen(false);
          }}
          contactId={Number(selectedContactId)}
          readOnly={isReadOnly}
          additional_contact_id={Number(selectedAdiContactId || "")} // as per PHP additional contact will not select from here
        />
      )}
    </>
  );
};

export default DetailsCard;
