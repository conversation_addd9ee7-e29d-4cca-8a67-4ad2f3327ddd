import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Image } from "~/shared/components/atoms/image";
import { Button } from "~/shared/components/atoms/button";
import { Typography } from "~/shared/components/atoms/typography";
import { ColorPicker } from "~/shared/components/atoms/colorPicker";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { InputField } from "~/shared/components/molecules/inputField";

// Antd
import { ColorPickerProps, Tooltip } from "antd";
import { DefineGeofenceLocation } from "../../sidebar";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { getStatusForField } from "~/shared/utils/helper/common";
import { useAppProSelector } from "../../../redux/store";
import { twMerge } from "tailwind-merge";
import { GetProp } from "antd/lib";
import { Number } from "~/helpers/helper";
import { isValidFloatNum, isValidInteger } from "../../utils/common-util";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { CF_PLANS } from "~/data/pages";

type Color = GetProp<ColorPickerProps, "value">;

const LABOR_RATE_OPTIONS: TLaborRateOptions[] = [
  {
    label: "Wage (Hr)",
    value: "project_enable_labor_markup_wage_rate",
  },
  {
    label: "Billing Rate",
    value: "project_enable_labor_markup_billing_rate",
  },
  {
    label: "Burden Rate",
    value: "project_enable_labor_markup_burden_rate",
  },
];

const ProjectConfiguration = () => {
  const { _t } = useTranslation();

  const MARKUP_FIELDS: TMarkupFields[] = [
    {
      label: `${_t("Material") + " %"}`,
      key: "project_default_material_markup_percent",
    },
    {
      label: `${_t("Labor") + " %"}`,
      key: "project_default_labor_markup_percent",
      isLaborRate: true,
    },
    {
      label: `${_t("Equipment") + " %"}`,
      key: "project_default_equipment_markup_percent",
    },
    {
      label: `${_t("Sub Contractor") + " %"}`,
      key: "project_default_sub_contractor_markup_percent",
    },
    {
      label: `${_t("Other Items") + " %"}`,
      key: "project_default_other_item_markup_percent",
    },
    {
      label: `${_t("Undefined") + " %"}`,
      key: "project_default_undefined_markup_percent",
    },
  ];

  const [confugurationFieldOpen, setConfugurationFieldOpen] =
    useState<boolean>(false);
  const [geofenceLocationOpen, setGeofenceLocationOpen] =
    useState<boolean>(false);

  const { loadingStatus, details } = useAppProSelector(
    (state) => state.proDetails
  );

  const [projectColorVal, setProjectColorVal] = useState<Color>(
    details.project_color || ""
  );
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const {
    wepay_activated,
    stripe_activated = "0",
    module_group_id = "0",
  } = appSettings || {};

  const {
    isReadOnly,
    handleUpdateField,
    updateDataInStore,
    onChangeInputField,
    updateInputFieldOnBlur,
    inputVals,
    onBlurUpdateFieldStatus,
    onFocusUpdateFieldStatus,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
  } = useProjectDetail();

  useEffect(() => {
    setProjectColorVal(details.project_color || "");
  }, [details.project_color]);

  const proColor = useMemo<string>(() => {
    if (typeof projectColorVal === "string") {
      return projectColorVal;
    }

    // Ensure `projectColorVal` is not an array before calling toHexString()
    if (Array.isArray(projectColorVal)) {
      return (projectColorVal[0]?.color as string) || "#000";
    }

    return projectColorVal?.toHexString?.() || "#000";
  }, [projectColorVal]);

  const handleCheckBoxChange = async (
    e: CheckboxChangeEvent,
    field: string
  ) => {
    const newval = e.target.checked ? 1 : 0;
    await handleUpdateField({
      data_to_send_in_api: {
        [field]: newval,
      },
    });
  };

  const loadingFields = useMemo(() => {
    const view_in_timecard =
      getStatusForField(loadingStatus, "view_in_timecard") === "loading";
    const allow_online_payment =
      getStatusForField(loadingStatus, "allow_online_payment") === "loading";
    const view_in_geofence =
      getStatusForField(loadingStatus, "view_in_geofence") === "loading";
    const save_as_template =
      getStatusForField(loadingStatus, "save_as_template") === "loading";
    const project_color =
      getStatusForField(loadingStatus, "project_color") === "loading";
    const project_enable_labor_markup_wage_rate =
      getStatusForField(
        loadingStatus,
        "project_enable_labor_markup_wage_rate"
      ) === "loading";
    const project_enable_labor_markup_billing_rate =
      getStatusForField(
        loadingStatus,
        "project_enable_labor_markup_billing_rate"
      ) === "loading";
    const project_enable_labor_markup_burden_rate =
      getStatusForField(
        loadingStatus,
        "project_enable_labor_markup_burden_rate"
      ) === "loading";

    return {
      view_in_timecard,
      allow_online_payment,
      view_in_geofence,
      save_as_template,
      project_color,
      project_enable_labor_markup_wage_rate,
      project_enable_labor_markup_billing_rate,
      project_enable_labor_markup_burden_rate,
    };
  }, [loadingStatus]);

  const isGeoForceInView = useMemo(() => {
    return details.view_in_timecard?.toString() === "1";
  }, [details.view_in_timecard]);

  const isMapViewVisible = useMemo(() => {
    return details.view_in_geofence?.toString() === "1";
  }, [details.view_in_geofence]);

  const handleMarkupUpdateOnBlur = (
    value: string,
    field: keyof IProjectDetails
  ) => {
    const inputValue = Number(value);

    if (inputValue !== Number((details[field] as string) ?? 0)) {
      updateInputFieldOnBlur({
        field,
        value: inputValue || "",
      });
    } else {
      updateDataInStore({
        [field]: details[field],
      });
      onBlurUpdateFieldStatus({
        field,
      });
    }
  };

  const [keyToCheckForLoading, setKeyToCheckForLoading] = useState<string>("");

  const onChangeLaborRate = (value: TLabourValueKey[]) => {
    const keys: Array<TLabourValueKey> = LABOR_RATE_OPTIONS.map((o) => o.value);

    const updatedVals = keys.reduce((acc, key) => {
      const isSelected = value.includes(key);
      const newValue = isSelected ? 1 : 0;

      if (newValue !== details[key]) {
        acc[key] = newValue;
      }

      return acc;
    }, {} as Record<TLabourValueKey, number>);

    setKeyToCheckForLoading(Object.keys(updatedVals)[0]);
    handleUpdateField({
      data_to_send_in_api: {
        ...updatedVals,
      },
    });
  };

  const laborRateCheckBoxVal = useMemo(() => {
    const keys: Array<TLabourValueKey> = LABOR_RATE_OPTIONS.map((o) => o.value);
    return keys.filter((k) => details[k] === 1);
  }, [details]);

  const updateGeoImagecallback = useCallback((img_url: string) => {
    handleUpdateField({
      data_to_send_in_api: {
        geo_image: img_url,
      },
    });
  }, []);

  const allowOnlinePaymentIsVisible = useMemo(() => {
    return (
      Number(stripe_activated) === 1 &&
      (module_group_id === CF_PLANS.default_planID ||
        module_group_id === CF_PLANS.plus_v1_to_v4_planID ||
        module_group_id === CF_PLANS.pro_v1_to_v4_planID ||
        module_group_id === CF_PLANS.enterprise_v1_to_v4_planID ||
        module_group_id === CF_PLANS.unlimited_v1_to_v4_planID ||
        module_group_id === CF_PLANS.standard_v5_planID ||
        module_group_id === CF_PLANS.plus_v5_planID ||
        module_group_id === CF_PLANS.pro_v5_planID ||
        module_group_id === CF_PLANS.unlimited_v5_planID ||
        module_group_id === CF_PLANS.basic_v5 ||
        module_group_id === CF_PLANS.basic_v1_to_v4)
    );
  }, [CF_PLANS, wepay_activated, stripe_activated, module_group_id]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Project Configuration")}
        headerProps={{
          containerClassName: "!flex-row !items-center",
        }}
        iconProps={{
          icon: "fa-solid fa-screwdriver-wrench",
          containerClassName: `bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)]`,
          id: "project_configuration",
          colors: ["#ADD100", "#7B920A"],
        }}
        hideBorder={!confugurationFieldOpen}
        headerRightButton={
          <ButtonWithTooltip
            icon={
              confugurationFieldOpen
                ? "fa-regular fa-chevron-up"
                : "fa-regular fa-chevron-down"
            }
            tooltipTitle={
              confugurationFieldOpen ? _t("Collapse") : _t("Expand")
            }
            tooltipPlacement="top"
            iconClassName="w-3.5 h-3.5"
            onClick={() => setConfugurationFieldOpen((prev) => !prev)}
          />
        }
        children={
          <div className={`${confugurationFieldOpen ? "pt-2" : "hidden"}`}>
            <ul className="grid xl:grid-cols-2 gap-y-2">
              <li>
                <CustomCheckBox
                  className="gap-1.5"
                  name="view_in_timecard"
                  checked={Boolean(details.view_in_timecard)}
                  onChange={(e) => handleCheckBoxChange(e, "view_in_timecard")}
                  disabled={isReadOnly || loadingFields.view_in_timecard}
                  loadingProps={{
                    isLoading: loadingFields.view_in_timecard,
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("View in Time Card")}
                </CustomCheckBox>
              </li>
              {allowOnlinePaymentIsVisible && (
                <li>
                  <CustomCheckBox
                    className="gap-1.5"
                    name="allow_online_payment"
                    checked={Boolean(details.allow_online_payment)}
                    onChange={(e) =>
                      handleCheckBoxChange(e, "allow_online_payment")
                    }
                    disabled={isReadOnly || loadingFields.allow_online_payment}
                    loadingProps={{
                      isLoading: loadingFields.allow_online_payment,
                      className: "bg-[#ffffff]",
                    }}
                  >
                    {_t("Allow Online Payment")}
                    <Tooltip
                      title={_t(
                        "Unless Online Payments has been disabled in Invoice > Settings, the selection made here overwrites settings made directly on the individual Invoice."
                      )}
                      placement="top"
                    >
                      <FontAwesomeIcon
                        className="ml-1 w-[13px] h-[13px] text-primary-900"
                        icon="fa-regular fa-info-circle"
                      />
                    </Tooltip>
                  </CustomCheckBox>
                </li>
              )}
              {isGeoForceInView && (
                <li className="relative">
                  <CustomCheckBox
                    className="gap-1.5"
                    name="view_in_geofence"
                    checked={Boolean(details.view_in_geofence)}
                    onChange={(e) =>
                      handleCheckBoxChange(e, "view_in_geofence")
                    }
                    disabled={isReadOnly || loadingFields.view_in_geofence}
                    loadingProps={{
                      isLoading: loadingFields.view_in_geofence,
                      className: "bg-[#ffffff]",
                    }}
                  >
                    {_t("Set Geofence Area (for Time Card Clock-In)")}
                  </CustomCheckBox>
                </li>
              )}
              <li>
                <div className="flex items-center gap-1.5 relative">
                  {loadingFields.project_color ? (
                    <div
                      className={`w-[18px] h-[18px] flex items-center rounded-[5px] bg-[#ffffff]`}
                    >
                      <FontAwesomeIcon
                        className={twMerge(
                          `h-[18px] w-[18px] text-primary-900 fa-spin`
                        )}
                        icon="fa-duotone fa-solid fa-spinner-third"
                      />
                    </div>
                  ) : (
                    <ColorPicker
                      className="p-0 !min-w-4 !min-h-4 !h-4 border-none"
                      size="small"
                      value={projectColorVal || "#000000"}
                      disabled={isReadOnly}
                      disabledAlpha={true}
                      onChange={setProjectColorVal}
                      onOpenChange={(isOpen) => {
                        if (!isOpen && proColor !== details?.project_color) {
                          handleUpdateField({
                            data_to_send_in_api: {
                              project_color: proColor,
                            },
                          });
                        }
                      }}
                    />
                  )}
                  <FieldLabel labelClass="!text-primary-900 !font-normal">
                    {_t("Project Color")}
                  </FieldLabel>
                </div>
              </li>
              <li>
                <CustomCheckBox
                  className="gap-1.5"
                  name="save_as_template"
                  checked={Boolean(details.save_as_template)}
                  onChange={(e) => handleCheckBoxChange(e, "save_as_template")}
                  disabled={isReadOnly || loadingFields.save_as_template}
                  loadingProps={{
                    isLoading: loadingFields.save_as_template,
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("Save as Template")}
                </CustomCheckBox>
              </li>
            </ul>
            {isMapViewVisible && (
              <>
                <div className="grid xl:grid-cols-2 2xl:gap-3 gap-2 mt-2">
                  <div className="min-h-[250px]">
                    <Image
                      src={
                        details.geo_image ||
                        window.ENV.CDN_URL + "images/default-location-maps.png"
                      }
                      rootClassName="w-full h-full rounded-xl overflow-hidden relative"
                      className="absolute !h-full top-0 left-0 object-cover cursor-pointer"
                      alt="Geo location Image"
                      preview={false}
                      onClick={() => {
                        if (isReadOnly) return;
                        setGeofenceLocationOpen(true);
                      }}
                    />
                  </div>
                  <ul className="flex flex-col gap-1">
                    <li>
                      <Button
                        type="default"
                        className="hover:!text-[#000000e0] !border-[#d9d9d9]"
                        onClick={() => {
                          if (isReadOnly) return;
                          setGeofenceLocationOpen(true);
                        }}
                      >
                        <FontAwesomeIcon
                          icon="fa-regular fa-location-dot"
                          className="w-3.5 h-3.5"
                        />
                        {_t("Define Location")}
                      </Button>
                    </li>
                    <li>
                      <InputField
                        label={_t("Radius (Meters)")}
                        placeholder={_t("00")}
                        labelPlacement="left"
                        labelClass="2xl:min-w-[155px] 2xl:max-w-[155px] 2xl:w-[155px] sm:min-w-[126px] sm:max-w-[126px] sm:w-[126px]"
                        editInline={true}
                        iconView={true}
                        name="geo_radius"
                        readOnly={isReadOnly}
                        value={inputVals.geo_radius}
                        informationProps={{
                          icon: "fa-regular fa-info-circle",
                          message: _t(
                            "The minimum and maximum radius limitation is based on Time Card module settings under GPS Tracking."
                          ),
                        }}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "geo_radius"
                        )}
                        onChange={(e) => {
                          const val = e.target.value?.trimStart();

                          if (val === "" || isValidInteger(val)) {
                            onChangeInputField({
                              field: "geo_radius",
                              value: val,
                            });
                          }
                        }}
                        onMouseEnter={() => {
                          onMouseEnterUpdateFieldStatus({
                            field: "geo_radius",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "geo_radius",
                          });
                        }}
                        onFocus={() => {
                          onFocusUpdateFieldStatus({
                            field: "geo_radius",
                          });
                        }}
                        onBlur={({
                          target: { value },
                        }: React.FocusEvent<HTMLInputElement>) => {
                          const inputValue = Number(value);

                          if (inputValue < 50 || inputValue > 2000) {
                            updateDataInStore({
                              geo_radius: details.geo_radius,
                            });

                            notification.error({
                              key: "geo_radius_validation_error",
                              description: "Enter a radius between 50 and 2000",
                            });
                            return;
                          }

                          if (inputValue !== Number(details.geo_radius ?? 0)) {
                            updateInputFieldOnBlur({
                              field: "geo_radius",
                              value: inputValue || "",
                            });
                          } else {
                            updateDataInStore({
                              geo_radius: details.geo_radius,
                            });
                            onBlurUpdateFieldStatus({
                              field: "geo_radius",
                            });
                          }
                        }}
                        // onBlur={({
                        //   target: { value },
                        // }: React.FocusEvent<HTMLInputElement>) => {
                        //   const inputValue =
                        //     Number(value) < 50
                        //       ? 50
                        //       : Number(value) > 2000
                        //       ? 2000
                        //       : Number(value);

                        //   if (inputValue !== Number(details.geo_radius ?? 0)) {
                        //     updateInputFieldOnBlur({
                        //       field: "geo_radius",
                        //       value: inputValue || "",
                        //     });
                        //   } else {
                        //     updateDataInStore({
                        //       geo_radius: details.geo_radius,
                        //     });
                        //     onBlurUpdateFieldStatus({
                        //       field: "geo_radius",
                        //     });
                        //   }
                        // }}
                      />
                    </li>
                    <li>
                      <InputField
                        label={_t("Latitude")}
                        autoComplete="off"
                        placeholder="0"
                        labelClass="shrink-0 2xl:min-w-[155px] 2xl:max-w-[155px] 2xl:w-[155px] sm:min-w-[126px] sm:max-w-[126px] sm:w-[126px]"
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        name="latitude"
                        readOnly={isReadOnly}
                        value={inputVals.latitude}
                        fixStatus={getStatusForField(loadingStatus, "latitude")}
                        onChange={(e) => {
                          const val = e.target.value?.trimStart();

                          if (val === "" || isValidFloatNum(val)) {
                            onChangeInputField({
                              field: "latitude",
                              value: val,
                            });
                          }
                        }}
                        onMouseEnter={() => {
                          onMouseEnterUpdateFieldStatus({
                            field: "latitude",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "latitude",
                          });
                        }}
                        onFocus={() => {
                          onFocusUpdateFieldStatus({
                            field: "latitude",
                          });
                        }}
                        onBlur={({
                          target: { value },
                        }: React.FocusEvent<HTMLInputElement>) => {
                          const inputValue = Number(value);

                          if (inputValue !== Number(details.latitude ?? 0)) {
                            updateInputFieldOnBlur({
                              field: "latitude",
                              value: inputValue || "",
                            });
                          } else {
                            updateDataInStore({
                              latitude: details.latitude,
                            });
                            onBlurUpdateFieldStatus({
                              field: "latitude",
                            });
                          }
                        }}
                      />
                    </li>
                    <li>
                      <InputField
                        label={_t("Longitude")}
                        autoComplete="off"
                        placeholder="0"
                        labelClass="shrink-0 2xl:min-w-[155px] 2xl:max-w-[155px] 2xl:w-[155px] sm:min-w-[126px] sm:max-w-[126px] sm:w-[126px]"
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        name="longitude"
                        readOnly={isReadOnly}
                        value={inputVals.longitude}
                        content={inputVals.longitude}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "longitude"
                        )}
                        onChange={(e) => {
                          const val = e.target.value?.trimStart();

                          if (val === "" || isValidFloatNum(val)) {
                            onChangeInputField({
                              field: "longitude",
                              value: val,
                            });
                          }
                        }}
                        onMouseEnter={() => {
                          onMouseEnterUpdateFieldStatus({
                            field: "longitude",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          onMouseLeaveUpdateFieldStatus({
                            field: "longitude",
                          });
                        }}
                        onFocus={() => {
                          onFocusUpdateFieldStatus({
                            field: "longitude",
                          });
                        }}
                        onBlur={({
                          target: { value },
                        }: React.FocusEvent<HTMLInputElement>) => {
                          const inputValue = Number(value);

                          if (inputValue !== Number(details.longitude ?? 0)) {
                            updateInputFieldOnBlur({
                              field: "longitude",
                              value: inputValue || "",
                            });
                          } else {
                            updateDataInStore({
                              longitude: details.longitude,
                            });
                            onBlurUpdateFieldStatus({
                              field: "longitude",
                            });
                          }
                        }}
                      />
                    </li>
                  </ul>
                </div>
              </>
            )}
            <Typography className="text-[#343a40e3] text-13 my-2 block">
              {_t(
                "Items associated with this Project will automatically have the specified markup applied. You can define the global default within Settings > Financial."
              )}
            </Typography>
            <ul className="flex flex-col gap-1">
              {MARKUP_FIELDS.map((m) => {
                return (
                  <li
                    key={m.key}
                    className={
                      m.isLaborRate
                        ? "flex gap-2 xl:flex-row flex-col items-start"
                        : ""
                    }
                  >
                    <InputField
                      key={m.key}
                      label={m.label}
                      placeholder="0"
                      labelPlacement="left"
                      labelClass={`sm:w-[125px] sm:max-w-[125px] sm:min-w-[125px] ${
                        m.isLaborRate ? "shrink-0" : ""
                      }`}
                      formInputClassName="max-w-[250px] min-w-[250px]"
                      fieldClassName="max-w-[100px]"
                      editInline={true}
                      iconView={true}
                      name={m.key}
                      id={m.key}
                      maxLength={3}
                      readOnly={isReadOnly}
                      value={inputVals[m.key] as string}
                      content={(inputVals[m.key] as string) || ""}
                      fixStatus={getStatusForField(loadingStatus, m.key)}
                      onChange={(e) => {
                        const val = e.target.value?.trimStart();

                        if (val === "" || isValidInteger(val)) {
                          onChangeInputField({
                            field: m.key,
                            value: val,
                          });
                        }
                      }}
                      onMouseEnter={() => {
                        onMouseEnterUpdateFieldStatus({
                          field: m.key,
                        });
                      }}
                      onMouseLeaveDiv={() => {
                        onMouseLeaveUpdateFieldStatus({
                          field: m.key,
                        });
                      }}
                      onFocus={() => {
                        onFocusUpdateFieldStatus({
                          field: m.key,
                        });
                      }}
                      onBlur={({
                        target: { value },
                      }: React.FocusEvent<HTMLInputElement>) => {
                        handleMarkupUpdateOnBlur(value, m.key);
                      }}
                    />
                    {m.isLaborRate && (
                      <InlineField
                        label={_t("Apply to the following labor rates:")}
                        labelPlacement="top"
                        labelClass="!text-13 !font-medium"
                        formInputClassName="ml-2"
                        field={
                          <div className="flex gap-x-2 gap-y-1 flex-wrap">
                            {LABOR_RATE_OPTIONS.map((o, idx) => {
                              return (
                                <CustomCheckBox
                                  key={idx}
                                  className="gap-1.5"
                                  name={o.value}
                                  checked={Boolean(details[o.value])}
                                  onChange={(e) =>
                                    handleCheckBoxChange(e, o.value)
                                  }
                                  disabled={
                                    isReadOnly ||
                                    loadingFields[o.value] ||
                                    inputVals[m.key] === ""
                                  }
                                  loadingProps={{
                                    isLoading: loadingFields[o.value],
                                    className: "bg-[#ffffff]",
                                  }}
                                >
                                  {_t(o.label)}
                                </CustomCheckBox>
                              );
                            })}
                          </div>
                        }
                      />
                    )}
                  </li>
                );
              })}
            </ul>
          </div>
        }
      />
      {geofenceLocationOpen && (
        <DefineGeofenceLocation
          setGeofenceLocationOpen={setGeofenceLocationOpen}
          geofenceLocationOpen={geofenceLocationOpen}
          updateGeoImagecallback={updateGeoImagecallback}
        />
      )}
    </>
  );
};

export default ProjectConfiguration;
