import { useEffect, useMemo, useRef, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import {
  CellClickedEvent,
  type GridApi,
  type GridReadyEvent,
} from "ag-grid-community";
import { useInAppDispatch, useInAppSelector } from "../../redux/store";
import { fetchInspectionsDashboardApi } from "../../redux/action/inspectionDashAction";
import { sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { useNavigate } from "@remix-run/react";
import { getGConfig } from "~/zustand";

const UpcomingDeliveries = () => {
  const { _t } = useTranslation();
  const { module_name }: GConfig = getGConfig();
  const navigate = useNavigate();
  const dispatch = useInAppDispatch();
  const gridApiRef = useRef<GridApi | null>(null);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };
  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IUpcomingInspectionsData[]>([]);
  const { get_upcoming_inspections, isDashboardLoading } = useInAppSelector(
    (state) => state.inspectionsDashboard
  );

  const hasData = useMemo(() => {
    return (
      get_upcoming_inspections && get_upcoming_inspections?.data.length > 0
    );
  }, [get_upcoming_inspections]);

  useEffect(() => {
    if (!isCashLoading && get_upcoming_inspections?.data) {
      setRowData(get_upcoming_inspections?.data);
    }
  }, [get_upcoming_inspections, isCashLoading]);

  const handleClickRefresh = async () => {
    setisCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchInspectionsDashboardApi({
        refresh_type: "get_upcoming_inspections",
      })
    );
    setisCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Date"),
      field: "date",
      minWidth: hasData ? 220 : 100,
      maxWidth: hasData ? 220 : 100,
      suppressMenu: true,
      cellRenderer: (params: { data: IUpcomingInspectionsData }) => {
        const { data } = params;
        return data.inspection_date || data.inspection_time ? (
          <DateTimeCard
            format="datetime"
            date={data.inspection_date}
            time={data.inspection_time}
          />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: hasData ? 130 : 100,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IUpcomingInspectionsData }) => {
        const { data } = params;
        return data.project_name ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.project_name))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.project_name))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "type",
      minWidth: hasData ? 130 : 80,
      maxWidth: hasData ? 130 : 80,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: { data: IUpcomingInspectionsData }) => {
        const { data } = params;
        return data.inspection_type ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.inspection_type))}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.inspection_type))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noRowsOverlay = () => (
    <StaticTableRowLoading columnDefs={columnDefs} limit={6} />
  );

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-upcoming-deliveries.svg`}
    />
  );

  return (
    <>
      <DashboardCardHeader
        title={_t(`Upcoming ${module_name}`)}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={get_upcoming_inspections?.last_refres_time}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px] ag-grid-cell-pointer">
          <StaticTable
            className="static-table"
            defaultColDef={{
              flex: 1,
            }}
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            key={isDashboardLoading ? "loading" : "loaded"}
            onCellClicked={(params: CellClickedEvent) => {
              const column = params.column;
              if (
                column.getColDef().field !== "" &&
                column.getColDef().field !== "email"
              ) {
                navigate(
                  `${routes.MANAGE_INSPECTION.url}/${params?.data?.inspection_id}`
                );
              }
            }}
            noRowsOverlayComponent={
              isCashLoading || isDashboardLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};
export default UpcomingDeliveries;
