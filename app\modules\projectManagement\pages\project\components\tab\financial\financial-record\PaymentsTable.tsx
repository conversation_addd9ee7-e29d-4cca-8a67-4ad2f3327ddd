import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "@remix-run/react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { PaymentFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/paymentFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../../redux/store";
import {
  getDefaultStatuscolor,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { routes } from "~/route-services/routes";
import { sendMessageKeys } from "~/components/page/$url/data";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { getGConfig } from "~/zustand";

const PaymentsTable = (props: TableProps) => {
  const { authorization }: GConfig = getGConfig();
  const { fetchAllProjectFinancialModules, dataLimit, formattedTotalAmount } =
    props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { formatter } = useCurrencyFormatter();
  const { getGlobalModuleByKey } = useGlobalModule();
  const PaymentModule = getGlobalModuleByKey(CFConfig.payment_module);
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const { financialData } = useAppProSelector((state) => state.proFinancial);
  const payments = financialData?.payments ?? [];
  const [selectedId, setSelectedId] = useState<number>(0);
  const navigate = useNavigate();
  const [allPayments, setAllPayments] = useState<IProjectPaymentsData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const displayedPayments = isShowingMore
    ? allPayments
    : allPayments?.slice(0, dataLimit);

  const totalCount = Number(
    financialData?.payments_count?.[0]?.number_of_payment ?? 0
  );
  const totalAmount = Number(financialData?.payments_count?.[0]?.amount ?? 0);
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  const [collapse, setCollapse] = useState<string[]>([]);

  useEffect(() => {
    if (isInitialLoad) {
      setAllPayments(payments);
      setIsInitialLoad(false);
      return;
    }

    const updatedMap = new Map(payments?.map((p) => [p?.payment_id, p]));

    const mergedPayments = allPayments?.map((existing) => {
      const updated = updatedMap?.get(existing?.payment_id);
      return updated ? updated : existing;
    });

    const existingIds = new Set(allPayments?.map((p) => p?.payment_id));
    const newPayments = payments?.filter(
      (p) => !existingIds?.has(p?.payment_id)
    );

    const nextAll = [...mergedPayments, ...newPayments];

    const hasChanged =
      nextAll?.length !== allPayments?.length ||
      nextAll?.some(
        (p, i) => JSON.stringify(p) !== JSON.stringify(allPayments[i])
      );

    if (hasChanged) {
      setAllPayments(nextAll);
    }
  }, [payments, isInitialLoad]);

  useEffect(() => {
    if (
      (props?.openTable && props.openTable === "payments") ||
      (totalCount && isInitialLoad)
    ) {
      setCollapse(["1"]);
    }
  }, [props?.openTable, props?.refresh, totalCount, isInitialLoad]);

  const handleShowMore = () => {
    if (allPayments.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectFinancialModules(true, ["payments"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: "Invoice #",
      field: "prefix_company_invoice_id",
      minWidth: 130,
      maxWidth: 130,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const invId = `Inv. #${HTMLEntities.decode(sanitizeString(value))}`;
        return value ? (
          <Tooltip title={invId}>
            <Typography className="table-tooltip-text">{invId}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "payment_date",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: ({ value }: { value: string }) =>
        value ? <DateTimeCard format="date" date={value} /> : "-",
    },
    {
      headerName: "Type",
      field: "payment_type_name",
      minWidth: 320,
      flex: 1,
      resizable: true,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const payment_type = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={payment_type}>
            <Typography className="table-tooltip-text">
              {payment_type}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "approval_status_name",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: { data: Payments }) => {
        const status = data.approval_status_name;
        const { color, textColor } = getDefaultStatuscolor(
          data.status_color || ""
        );

        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <div className="table-tooltip-text text-center">-</div>
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "amount",
      minWidth: 240,
      maxWidth: 240,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ value }: { value: string }) => {
        const formateValue = formatter(
          Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0.00"
        ).value_with_symbol;

        return value ? (
          <Tooltip title={formateValue}>
            <Typography className="table-tooltip-text">
              {formateValue}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "payment_id",
      maxWidth: 70,
      minWidth: 70,
      cellRenderer: ({ data }: { data: Payments }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const newURL = new URL(
                  routes.MANAGE_PAYMENT.url +
                    "/" +
                    (data?.payment_id?.toString() || ""),
                  window.location.origin
                );
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <PaymentFieldRedirectionIcon
              iconClassName="!w-3.5 !h-3.5"
              paymentId={data.payment_id}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t(PaymentModule?.plural_name ?? "Payments")}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        totalRecord={formattedTotalAmount(totalAmount)}
        total={totalCount?.toString()}
        totalRecordIcon={true}
        addButton={_t(PaymentModule?.module_name ?? "Payment")}
        onClickAdd={async () => {
          window.open(
            `${routes.MANAGE_PAYMENT.url}?action=new&project=${id}`,
            "_self"
          );

          // if (!id) {
          //   return;
          // }
          // let tempAuthorization = authorization;
          // const isExpired = isExpiredAuthorization();
          // if (isExpired) {
          //   const response = (await webWorkerApi({
          //     url: "/api/auth/token",
          //   })) as IGetTokenFromNode;
          //   if (response.success) {
          //     tempAuthorization = response.data.accessToken;
          //     setAuthorizationExpired(response.data.accessTokenExpired);
          //   }
          // }
          // const newURL = new URL(
          //   routes.MANAGE_PAYMENT.url + "/",
          //   window.location.origin
          // );
          // newURL.searchParams.set("authorize_token", tempAuthorization);
          // newURL.searchParams.set("iframecall", "1");
          // newURL.searchParams.set("from_remix", "1");
          // newURL.searchParams.set("action", "new");
          // newURL.searchParams.set("project", id?.toString());
          // setIframeData({
          //   addUrl: newURL.toString(),
          //   title: String(id),
          // });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
      >
        <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
          <div className="ag-theme-alpine">
            <StaticTable
              className="static-table"
              columnDefs={columnDefs}
              rowData={displayedPayments}
              noRowsOverlayComponent={() => (
                <NoRecords
                  image={`${window.ENV.CDN_URL}assets/images/no-records-payments-table.svg`}
                />
              )}
            />
          </div>
        </div>
      </CollapseSingleTable>

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            fetchAllProjectFinancialModules(false, ["payments", "counts"]);
            setIframeData({ url: "", title: "", addUrl: "" });
            handleShowLess();
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.modal_change) {
              // reload data
              fetchAllProjectFinancialModules(false, ["payments", "counts"]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default PaymentsTable;
