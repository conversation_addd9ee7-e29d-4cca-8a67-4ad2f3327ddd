import { useMemo } from "react";
import isEmpty from "lodash/isEmpty";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { SelectField } from "~/shared/components/molecules/selectField";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

// Other
import { useAppDLSelector } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { useTranslation } from "~/hook";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { sanitizeString } from "~/helpers/helper";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";

const AddDeliveredItem = ({
  isOpen,
  onCloseModal,
  onSavematerialItem,
  isLoading,
  itemType,
}: IAddDeliveredItemProps) => {
  const { _t } = useTranslation();
  const { materialDelivery, equipmentDelivery }: IDLCommonInitialState =
    useAppDLSelector((state) => state.dLCommonData);

  const materialDeliveryList: ICommonOpItemDeliveryList[] = useMemo(() => {
    if (itemType == "material") {
      return materialDelivery?.map((item: ICommonOpItemDelivery) => ({
        label:
          HTMLEntities.decode(
            sanitizeString(
              `${item.subject} ${
                !isEmpty(item.prefix_company_purchase_order_id) &&
                `(PO #${item.prefix_company_purchase_order_id})`
              }`
            )
          ) || "",
        value: item?.item_id?.toString() || "",
      }));
    }
    return equipmentDelivery?.map((item: ICommonOpItemDelivery) => ({
      label:
        HTMLEntities.decode(
          sanitizeString(
            `${item.subject} ${
              !isEmpty(item.prefix_company_purchase_order_id) &&
              `(PO #${item.prefix_company_purchase_order_id})`
            }`
          )
        ) || "",
      value: item?.item_id?.toString() || "",
    }));
  }, [materialDelivery, equipmentDelivery, itemType]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        materialDeliveryList || []
      );
      if (newType) {
        onSavematerialItem({ subject: newType || "" });
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="400px"
      onCloseModal={onCloseModal}
      modalBodyClass="p-0"
      header={{
        title: _t("Delivered Item"),
        icon: (
          <FontAwesomeIcon
            className="w-3.5 h-3.5"
            icon="fa-regular fa-box-circle-check"
          />
        ),
        closeIcon: true,
      }}
    >
      <div className="py-4">
        {isLoading ? (
          <Spin className="w-full h-14 flex items-center justify-center" />
        ) : (
          <div className="modal-body grid gap-5 overflow-y-auto max-h-[calc(100vh-200px)] px-4">
            <div className="w-full">
              <SelectField
                label={_t("Select Item")}
                labelPlacement="top"
                showSearch
                allowClear={true}
                options={materialDeliveryList}
                addItem={addItemObject}
                onInputKeyDown={(e) => handlekeyDown(e)}
                onChange={(value: string | string[]) => {
                  if (typeof value === "string") {
                    const newData =
                      itemType == "material"
                        ? materialDelivery.find(
                            (item) => item?.item_id?.toString() == value
                          )
                        : equipmentDelivery.find(
                            (item) => item?.item_id?.toString() == value
                          );
                    onSavematerialItem(newData as ICommonOpItemDelivery);
                  }
                }}
                filterOption={(input, option) =>
                  filterOptionBySubstring(input, option?.label as string)
                }
              />
            </div>
          </div>
        )}
      </div>
    </CommonModal>
  );
};

export default AddDeliveredItem;
