import { useEffect, useState } from "react";

// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import IframeModal from "~/shared/components/molecules/iframeModal/IframeModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { RFIFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/rfiFieldRedirectionIcon";
// Other
import { useParams } from "@remix-run/react";
import { useAppProSelector } from "../../../redux/store";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { getGConfig } from "~/zustand";
import { sendMessageKeys } from "~/components/page/$url/data";

const RfiNoticesTable = (props: DocumentTableProps) => {
  const { fetchAllProjectDocumentsModules, dataLimit } = props;
  const { _t } = useTranslation();
  const { id } = useParams();
  const { getGlobalModuleByKey } = useGlobalModule();
  const correspondModule = getGlobalModuleByKey(CFConfig.correspond_module);
  const { documentsData } = useAppProSelector((state) => state.proDashDocument);
  const rfiNotices = documentsData?.correspondences ?? [];
  const [isShowingMore, setIsShowingMore] = useState<boolean>(false);
  const [allRfiNotices, setAllRfiNotices] = useState(rfiNotices);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [collapse, setCollapse] = useState<string[]>([]);
  const displayedRfiNotices = isShowingMore
    ? allRfiNotices
    : allRfiNotices.slice(0, dataLimit);
  const totalCount = Number(
    documentsData?.correspondences_count?.[0]?.number_of_correspondence ?? 0
  );
  const { authorization }: GConfig = getGConfig();
  const [iframeData, setIframeData] = useState<{
    url?: string;
    title: string;
    addUrl?: string;
  }>({ url: "", title: "", addUrl: "" });

  useEffect(() => {
    if (isInitialLoad || !isShowingMore) {
      setAllRfiNotices(rfiNotices);
      setIsInitialLoad(false);
    } else {
      const newRfiNotices = rfiNotices.filter(
        (item) =>
          !allRfiNotices.some(
            (p) => p.correspondence_id === item.correspondence_id
          )
      );
      if (newRfiNotices.length > 0) {
        setAllRfiNotices((prev) => [...prev, ...newRfiNotices]);
      } else {
        setAllRfiNotices(rfiNotices);
      }
    }
  }, [rfiNotices, isInitialLoad]);

  useEffect(() => {
    if (rfiNotices.length) {
      setCollapse(["1"]);
    }
  }, [rfiNotices]);

  const handleShowMore = () => {
    if (allRfiNotices.length === totalCount) {
      setIsShowingMore(true);
    } else {
      setIsShowingMore(true);
      fetchAllProjectDocumentsModules(true, ["correspondences", "counts"]);
    }
  };

  const handleShowLess = () => {
    setIsShowingMore(false);
  };

  const columnDefs = [
    {
      headerName: _t("Type"),
      field: "type_name",
      minWidth: 135,
      flex: 2,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ({ value }: { value: string }) => {
        const type_name = HTMLEntities.decode(sanitizeString(value));
        return value ? (
          <Tooltip title={type_name}>
            <Typography className="table-tooltip-text">{type_name}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "correspondence_date",
      maxWidth: 270,
      minWidth: 270,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: string }) => {
        return value ? <DateTimeCard format="date" date={value} /> : "-";
      },
    },
    {
      headerName: "Status",
      maxWidth: 120,
      minWidth: 120,
      field: "status_name",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-center-aligned-cell",
      cellRenderer: ({ data }: { data: RfiNoticesData }) => {
        const status = data?.status_name;
        const correspondencKey = data?.correspondence_key;
        let color = "";
        let textColor = "";
        const hexToRgb = (hex: string) => {
          const bigint = parseInt(hex?.replace("#", ""), 16);
          const r = (bigint >> 16) & 255;
          const g = (bigint >> 8) & 255;
          const b = bigint & 255;
          return `${r}, ${g}, ${b}`;
        };

        if (correspondencKey === "correspondence_rfi_draft") {
          color = "#223558";
        } else if (correspondencKey === "correspondence_rfi_pending") {
          color = "#00807f";
        } else if (correspondencKey === "correspondence_rfi_outstanding") {
          color = "#019de0";
        } else if (correspondencKey === "correspondence_rfi_reply") {
          color = "#ffa500";
        } else if (correspondencKey === "correspondence_rfi_closed") {
          color = "#343a40";
        } else if (correspondencKey === "correspondence_schedule_draft") {
          color = "#223558";
        } else if (correspondencKey === "correspondence_schedule_open") {
          color = "#2e5e56";
        } else if (correspondencKey === "correspondence_schedule_closed") {
          color = "#343a40";
        } else if (correspondencKey === "correspondence_compliance_draft") {
          color = "#223558";
        } else if (correspondencKey === "correspondence_compliance_open") {
          color = "#2e5e56";
        } else if (correspondencKey === "correspondence_compliance_closed") {
          color = "#343a40";
        }

        if (color) {
          textColor = `rgba(${hexToRgb(color)}, 0.18)`;
        }

        return status ? (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  backgroundColor: textColor,
                  color: color,
                }}
                className="mx-auto text-13 type-badge common-tag max-w-24"
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <Typography className="table-tooltip-text">-</Typography>
        );
      },
    },
    {
      headerName: "",
      field: "correspondence_id",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      cellRenderer: ({ value }: { value: number }) => {
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("View")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                let tempAuthorization = authorization;
                const isExpired = isExpiredAuthorization();
                if (isExpired) {
                  const response = (await webWorkerApi({
                    url: "/api/auth/token",
                  })) as IGetTokenFromNode;
                  if (response.success) {
                    tempAuthorization = response.data.accessToken;
                    setAuthorizationExpired(response.data.accessTokenExpired);
                  }
                }
                const pathname = "manage_rfi_and_notices.php";
                const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);
                newURL.searchParams.set("id", value?.toString());
                newURL.searchParams.set("authorize_token", tempAuthorization);
                newURL.searchParams.set("iframecall", "1");
                newURL.searchParams.set("from_remix", "1");
                setIframeData({
                  url: newURL.toString(),
                  title: String(id),
                });
              }}
            />
            <div className="w-6">
              <RFIFieldRedirectionIcon
                iconClassName="!w-3.5 !h-3.5"
                rfiId={value}
              />
            </div>
          </div>
        );
      },
    },
  ];
  return (
    <>
      <CollapseSingleTable
        title={_t(correspondModule?.plural_name || "RFI & Notice")}
        addButton={correspondModule?.module_name || "RFI & Notice"}
        defaultActiveKey={totalCount ? ["1"] : []}
        activeKey={collapse}
        onChange={setCollapse}
        addButtonDisabled={correspondModule?.can_write !== "1"}
        onClickAdd={async () => {
          if (!id) {
            return;
          }
          let tempAuthorization = authorization;
          const isExpired = isExpiredAuthorization();
          if (isExpired) {
            const response = (await webWorkerApi({
              url: "/api/auth/token",
            })) as IGetTokenFromNode;
            if (response.success) {
              tempAuthorization = response.data.accessToken;
              setAuthorizationExpired(response.data.accessTokenExpired);
            }
          }
          const pathname = "manage_rfi_and_notices.php";
          const newURL = new URL("/" + pathname, window.ENV.PANEL_URL);

          newURL.searchParams.set("authorize_token", tempAuthorization);
          newURL.searchParams.set("iframecall", "1");
          newURL.searchParams.set("from_remix", "1");
          newURL.searchParams.set("action", "new");
          newURL.searchParams.set("project", id?.toString());
          setIframeData({
            addUrl: newURL.toString(),
            title: String(id),
          });
        }}
        rightsideContant={
          dataLimit < totalCount && (
            <ButtonWithTooltip
              tooltipTitle={isShowingMore ? _t("Show Less") : _t("Show More")}
              tooltipPlacement="top"
              icon={
                isShowingMore
                  ? "fa-regular fa-arrow-up"
                  : "fa-regular fa-arrow-down"
              }
              className="hover:!bg-primary-900/20"
              onClick={isShowingMore ? handleShowLess : handleShowMore}
            />
          )
        }
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={columnDefs}
                rowData={displayedRfiNotices}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-rfi-notice.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />

      {(iframeData?.url || iframeData?.addUrl) && (
        <IframeModal
          isOpen={iframeData?.url || iframeData?.addUrl ? true : false}
          widthSize="100vw"
          onCloseModal={() => {
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
          modalBodyClass="p-0"
          header={{
            // icon,
            closeIcon: true,
          }}
          iframeProps={{
            src: !iframeData.url ? iframeData?.addUrl : iframeData?.url,
            id: iframeData.title,
          }}
          messageListener={(key, data) => {
            if (key === sendMessageKeys.iframe_change) {
              // reload data
              fetchAllProjectDocumentsModules(false, [
                "correspondences",
                "counts",
              ]);
              handleShowLess();
            }
            setIframeData({ url: "", title: "", addUrl: "" });
          }}
        />
      )}
    </>
  );
};

export default RfiNoticesTable;
