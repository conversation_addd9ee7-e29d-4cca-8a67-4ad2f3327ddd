import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useSearchParams } from "@remix-run/react";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import * as Yup from "yup";
import { FormikValues, useFormik } from "formik";
import isEmpty from "lodash/isEmpty";

// Hooks
import { sendMessageKeys } from "~/components/page/$url/data";
import { useIframe, useTranslation } from "~/hook";
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import { addDailyLogAPI } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import {
  resetDash,
  setDailyLogListIdsAct,
} from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";

import {
  backendDateFormat,
  backendTimeFormat,
  displayDateFormat,
  displayTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";

// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";

// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

// Other
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { DatePickerProps } from "antd";

dayjs.extend(utc);
dayjs.extend(timezone);
const AddDailyLog = ({ isOpen, onClose }: IAddDailyLogProps) => {
  const { project_id, project_name }: GProject = getGProject();
  const { _t } = useTranslation();
  const { parentPostMessage } = useIframe();
  const [searchParams] = useSearchParams();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id } = user || {};
  const { date_format }: GSettings = getGSettings();
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const dispatch = useAppDLDispatch();

  const isCalledProApiRef = useRef<string | null>(null);

  const [isIframe, setIsIframe] = useState<boolean>(true);
  const [submittingFrm, setSubmittingFrm] = useState<boolean>(false);
  const [projectChange, setProjectChange] = useState<boolean>(false);
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);
  useEffect(() => {
    if (window && window?.location?.href) {
      const currentUrl = window.location.href;
      setIsIframe(currentUrl.includes("iframecall=1"));
    }
  }, []);

  const navigate = useNavigate();
  const { jobStatus }: IStatusListDataInitialState = useAppDLSelector(
    (state) => state.statusListData
  );
  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
    } as IRequestCustomFieldForSidebar
  );
  const [isSelectProOpen, setIsSelectProOpen] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<
    IProject | undefined | null
  >();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const validateArray = ["projectId", "arrivalDate", "arrivalTime"];

  const staticValidationSchema = validateArray.reduce((acc, fieldName) => {
    if (fieldName === "arrivalDate") {
      acc[fieldName] = Yup.date()
        .transform((_, originalValue) => {
          return dayjs(originalValue, date_format).toDate();
        })
        .typeError("Please enter a valid date")
        .required("This field is required.");
      return acc;
    } else {
      acc[fieldName] = Yup.string().required("This field is required.");
      return acc;
    }
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const logStatusList: IDirTypeOption[] = useMemo(
    () =>
      jobStatus
        .filter((item) => item.type_id === 174 || item.name === "In Progress")
        .map((item) => ({
          label: replaceDOMParams(
            sanitizeString(item.display_name || item.name)
          ),
          value: item.type_id.toString(),
        })),
    [jobStatus]
  );

  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  const initialValues: IAddDailyLogInitialValues = {
    projectId: "",
    arrivalDate: currentDateTime.format(date_format),
    arrivalTime: currentDateTime.format("HH:mm A"),
    logStatus: logStatusList[0]?.value,
    accessToCustomFields: 0,
    custom_fields: {},
  };

  const [initialValuesState, setInitialValuesState] =
    useState<IAddDailyLogInitialValues>(initialValues);

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...staticValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...staticValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initialValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initialValues;

  const handleSubmitAddDailyLog = async ({ setSubmitting }: FormikValues) => {
    let isCustomFieldValid = true;
    const formVal = { ...formik?.values };
    if (componentList.length && !isNoAccessCustomField) {
      for (let index = 0; index < componentList.length; index++) {
        const value = formVal?.custom_fields?.[componentList[index].name];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (!value?.length) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    if (!formik.isValid || !isCustomFieldValid) return;

    if (!!formVal?.arrivalDate) {
      formVal.arrivalDate = backendDateFormat(
        formVal?.arrivalDate,
        date_format
      );
    }
    if (!!formVal?.arrivalTime) {
      formVal.arrivalTime = backendTimeFormat(formVal?.arrivalTime);
    }

    const formData = {
      ...formVal,
      logStatus: logStatusList[0]?.value,
      customFields:
        formVal.custom_fields && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formVal.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
      accessToCustomFields:
        componentList.length && !isNoAccessCustomField ? 1 : 0,
    };
    delete formData.custom_fields;
    setSubmittingFrm(true);
    try {
      const responseApi = (await addDailyLogAPI(
        getValuableObj(formData)
      )) as IAddDailyLogRes;
      if (responseApi?.success) {
        EventLogger.log(
          EVENT_LOGGER_NAME.daily_logs + EVENT_LOGGER_ACTION.added,
          1
        );
        if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
          setSubmittingFrm(false);
        } else {
          dispatch(resetDash());
          dispatch(setDailyLogListIdsAct([]));
          navigate(`${responseApi.data?.dailyLogId}`);
        }
      } else {
        setSubmittingFrm(false);
      }
    } catch (error) {
      setSubmittingFrm(false);
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: handleSubmitAddDailyLog,
  });
  const { handleSubmit, setFieldValue, isSubmitting, values, touched, errors } =
    formik;

  useEffect(() => {
    if (
      searchParams.get("action")?.trim() === "new" &&
      searchParams.get("project") &&
      isCalledProApiRef.current !== searchParams.get("project")
    ) {
      isCalledProApiRef.current = searchParams.get("project");

      (async () => {
        try {
          const proResApi = (await getProjectDetails({
            start: 0,
            limit: 1,
            projects: searchParams.get("project") || "",
            need_all_projects: 0,
            global_call: true,
            is_completed: true,
            filter: { status: "0" },
          })) as IProjectDetailsRes;

          const queryPro = proResApi?.data?.projects[0];
          setSelectedProject({
            id: Number(queryPro?.id),
            project_name: queryPro?.project_name,
          });
          setFieldValue("projectId", queryPro?.id || "");
        } catch (e) {}
      })();
    }

    if (
      project_id &&
      project_id != "0" &&
      isEmpty(searchParams.get("project"))
    ) {
      setSelectedProject({
        id: Number(project_id),
        project_name,
      });
      setInitialValuesState((prevState: IAddDailyLogInitialValues) => ({
        ...prevState,
        projectId: project_id,
      }));
    }
  }, [
    project_id,
    searchParams.get("action"),
    searchParams.get("project"),
    componentList,
    isNoAccessCustomField,
  ]);

  useEffect(() => {
    if (projectChange) {
      setInitialValuesState((prevState: IAddDailyLogInitialValues) => ({
        ...prevState,
        projectId:
          project_id && Number(project_id) !== 0 ? Number(project_id) : "",
      }));
    }
  }, [projectChange]);

  useEffect(() => {
    if (componentList) {
      setInitialValuesState((prevState: IAddDailyLogInitialValues) => ({
        ...prevState,
        custom_fields: componentList.reduce((acc, item) => {
          acc[item.name] = item?.value ?? "";
          return acc;
        }, {} as ICustomFieldInitValue),
      }));
    } else {
      setInitialValuesState((prevState: IAddDailyLogInitialValues) => ({
        ...prevState,
        custom_fields: {},
      }));
    }
  }, [componentList]);

  useEffect(() => {
    setFieldValue("projectId", selectedProject?.id || "");
  }, [selectedProject, componentList, isNoAccessCustomField]);

  const disableAfterToday: DatePickerProps["disabledDate"] = (
    current: Dayjs
  ) => {
    return current && current > dayjs().endOf("day");
  };

  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-calendar-check"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Add ${
                  replaceDOMParams(sanitizeString(module_singular_name)) ??
                  "Daily Log"
                }`
              )}
            </Header>
          </div>
        }
        closeIcon={
          !isIframe && (
            <CloseButton
              onClick={() => {
                onClose();
                setIsSubmit(false);
              }}
            />
          )
        }
      >
        <form
          noValidate
          method="post"
          className="py-4"
          onSubmit={handleSubmit}
          onKeyDown={(e) => {
            if (
              e.key === "Enter" &&
              (e.target as HTMLElement).tagName !== "TEXTAREA"
            ) {
              e.preventDefault();
            }
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="projectId"
                    labelPlacement="top"
                    required={true}
                    addonBefore={
                      !isEmpty(selectedProject?.project_name) && (
                        <ProjectFieldRedirectionIcon
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          projectId={selectedProject?.id?.toString() || ""}
                        />
                      )
                    }
                    value={HTMLEntities.decode(
                      sanitizeString(selectedProject?.project_name)
                    )}
                    onClick={() => {
                      setIsSelectProOpen(true);
                    }}
                    errorMessage={touched.projectId ? errors.projectId : ""}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <DatePickerField
                      label={_t("Arrival Date")}
                      labelPlacement="top"
                      placeholder=""
                      isRequired={true}
                      format={date_format}
                      disabledDate={disableAfterToday}
                      value={displayDateFormat(
                        values?.arrivalDate?.trim(),
                        date_format
                      )}
                      onChange={(_, dateString) => {
                        setFieldValue("arrivalDate", dateString);
                      }}
                      errorMessage={
                        touched?.arrivalDate ? errors?.arrivalDate : ""
                      }
                    />
                  </div>
                  <div className="w-full">
                    <TimePickerField
                      label={_t("Arrival Time")}
                      labelPlacement="top"
                      placeholder=""
                      isRequired={true}
                      format="hh:mm A"
                      value={displayTimeFormat(values?.arrivalTime?.trim())}
                      onChange={(_, val) => {
                        setFieldValue("arrivalTime", val);
                      }}
                      errorMessage={
                        touched?.arrivalTime ? errors?.arrivalTime : ""
                      }
                    />
                  </div>
                </div>
              </SidebarCardBorder>

              {/* ----- custom component start ----- */}
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
              {/* ----- custom component end ----- */}
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              buttonText={_t(
                `Create ${
                  replaceDOMParams(sanitizeString(module_singular_name)) ??
                  "Daily Log"
                }`
              )}
              disabled={submittingFrm || loadingCustomField}
              isLoading={submittingFrm}
            />
          </div>
        </form>
      </Drawer>

      {isSelectProOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProOpen}
          module_key={module_key}
          setOpen={setIsSelectProOpen}
          selectedProjects={selectedProject ? [selectedProject] : []}
          onProjectSelected={(data) => {
            setProjectChange(true);
            setSelectedProject(data.length ? data[0] : null);
            setFieldValue("projectId", data.length ? data[0]?.id : "");
          }}
        />
      )}
    </>
  );
};

export default AddDailyLog;
