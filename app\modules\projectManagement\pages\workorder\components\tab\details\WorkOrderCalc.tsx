import { useParams } from "@remix-run/react";
import { useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CalcList } from "~/shared/components/molecules/calcList";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import SelectTaxRate from "~/shared/components/molecules/selectTaxRate/SelectTaxRate";
// Hook
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import delay from "lodash/delay";
import { useWoAppDispatch, useWoAppSelector } from "../../../redux/store";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { cleanTaxValue } from "~/modules/financials/pages/changeOrder/utils/helpers";
import {
  updateWorkorderDetail,
  updateWOTaxes,
} from "../../../redux/slices/workorderDetailsSlice";
import {
  labourHoursUnits,
  workorderDetailsField,
  workorderfieldStatus,
} from "../../../utils/constasnts";
import {
  fetchWorkorderDetails,
  updateWorkOrderDetailApi,
} from "../../../redux/action/workorderDetailsAction";
import { getStatusForField } from "~/shared/utils/helper/common";

interface IWorkOrderTotals {
  profit: number;
  total: number;
  profitPercentage: number;
  grossTotal: number;
  taxedItemTotal: number;
  totalTaxAmount: number;
  hours: number;
  markup: number;
}

interface TaxRecord {
  label: string;
  value: string;
  taxRate: number;
}

const WorkOrderCalc = ({
  filteredItems,
}: {
  filteredItems: IWorkorderDetailsItem[];
}) => {
  const { _t } = useTranslation();
  const gSettings = getGSettings();
  const gConfig: GConfig = getGConfig();
  const { module_key }: GConfig = getGConfig();
  const { formatter } = useCurrencyFormatter();
  const { items, details, taxes }: IWorkorderInitialState = useWoAppSelector(
    (state) => state.WorkorderDetails
  );
  const { checkModuleAccessByKey } = useGModules();
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  const params = useParams();
  const [inputValues, setInputValues] = useState<IWorkorderDetails>(
    workorderDetailsField
  );
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(workorderfieldStatus);
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<boolean>(false);

  const [selectedTaxRates, setSelectedTaxRates] = useState<TaxRecord[]>(
    taxes.map((tax) => {
      return {
        label: `${tax.tax_name} (${Math.round(Number(tax.tax_rate) || 0)}%)`,
        value: tax.tax_id.toString(),
        taxRate: Number(tax.tax_rate),
      };
    })
  );
  const dispatch = useWoAppDispatch();
  const totalProfitAndPercentageForHeader: {
    profit: number;
    profitPercentage: number;
  } = useMemo(() => {
    let markupTotal = 0;
    let markupItemTotalWithoutTax = 0;

    filteredItems.forEach((item) => {
      const quantity = Number(item.quantity) || 0;
      const unitCost = Number(item.unit_cost || 0) / 100;
      const markup = (item.markup || "0") as string;
      const isMarkupPercentage =
        item.is_markup_percentage?.toString() as string;

      if (quantity !== 0 && unitCost > 0 && markup !== "") {
        const markupPercentage = parseInt(markup) / 100;

        if (isMarkupPercentage !== "") {
          if (markupPercentage !== 0) {
            markupItemTotalWithoutTax += quantity * unitCost;
          }

          if (parseInt(isMarkupPercentage) === 0 && markupPercentage !== 0) {
            markupTotal += markupPercentage - unitCost * quantity;
          } else {
            markupTotal += unitCost * quantity * markupPercentage;
          }
        }
      }
    });

    const markupPercentageTotal =
      100 * (markupTotal / markupItemTotalWithoutTax);
    const roundedMarkupPercentage = isNaN(markupPercentageTotal)
      ? 0
      : Math.round(markupPercentageTotal);

    return { profit: markupTotal, profitPercentage: roundedMarkupPercentage };
  }, [filteredItems]);

  const shouldAddItemsDropDownHide = useMemo(() => {
    const ddHideStatuses: string[] = ["182", "180", "324"];
    if (
      ddHideStatuses.includes(details.work_order_status?.toString() as string)
    ) {
      return true;
    }
    return false;
  }, [details]);

  const readOnlyMode = useMemo(() => {
    return gConfig?.module_read_only || shouldAddItemsDropDownHide;
  }, [gConfig, shouldAddItemsDropDownHide]);

  const itemTotalCalculator = (item: IWorkorderDetails) => {
    const { unit_cost, markup, quantity, is_markup_percentage } = item;
    if (is_markup_percentage !== 1) {
      if (!!Number(quantity) && !!unit_cost) {
        if (Number(markup) === 0) {
          return Math.round(Number(unit_cost) * Number(quantity));
        }
        return Math.round(Number(markup));
      } else {
        if (Number(markup) === 0) {
          return 0;
        }
        return 0;
      }
    }
    const markupAmount =
      Number(unit_cost) * (Number(markup) / 100) * Number(quantity);
    let totalFloat = 0;
    if (quantity !== 0) {
      totalFloat = Number(unit_cost) * Number(quantity) + Number(markupAmount);
    } else {
      totalFloat = Number(unit_cost) * Number(quantity);
    }
    return Math.round(totalFloat);
  };

  const roundToTwo = (num: number): number => {
    return Math.round((num + Number.EPSILON) * 100) / 100;
  };

  const {
    profit,
    total,
    profitPercentage,
    grossTotal,
    taxedItemTotal,
    totalTaxAmount,
    hours,
    markup,
  } = useMemo((): IWorkOrderTotals => {
    let totalUnit = 0,
      totalProfit = 0,
      totalCost = 0,
      taxedItemTotal = 0,
      totalMarkup = 0,
      markupItemTotalTax = 0,
      markupAmount = 0;

    const everyQtyOrUnitCostZero = filteredItems.every(
      ({ quantity, unit_cost }) =>
        [quantity, unit_cost].some((value) => Number(value) === 0)
    );

    filteredItems.map((item) => {
      const unitCost = Number(item.unit_cost);
      const quantity = Number(item.quantity) || 0;
      const markupAmounts = Number(item.markup) || 0;

      const markupAmountData = item.is_markup_percentage
        ? undefined
        : (markupAmounts - unitCost * quantity).toString();

      if (!!unitCost && !!quantity) {
        if (item.is_markup_percentage) {
          markupAmount = unitCost * (Number(item.markup) / 100) * quantity;
        } else {
          markupAmount = Number(markupAmountData);
        }
      } else {
        markupAmount = 0;
      }
      totalCost = everyQtyOrUnitCostZero
        ? 0
        : totalCost + itemTotalCalculator(item);
      if (Number(item.markup) > 0) {
        markupItemTotalTax += Number(item.quantity) * Number(item.unit_cost);
      }
      if (Number(item.markup) > 0) {
        totalUnit = totalUnit + (unitCost * quantity - markupAmount);
        totalProfit = totalProfit + markupAmount;
        totalMarkup = totalMarkup + markupAmount;
      }
      if (item.apply_global_tax) {
        taxedItemTotal = everyQtyOrUnitCostZero
          ? 0
          : taxedItemTotal + (unitCost * quantity + markupAmount);
      }
    });
    const markupPercentage = (100 * totalProfit) / markupItemTotalTax;

    const totalTaxRate = taxes
      .filter((tax) => !tax.is_reversible)
      .reduce((acc, tax) => acc + Number(tax.tax_rate), 0);

    const totalTaxAmount = everyQtyOrUnitCostZero
      ? 0
      : (taxedItemTotal * totalTaxRate) / 100;

    const hours = filteredItems
      .filter(
        (item) =>
          item.item_type_name === "Labor" && labourHoursUnits?.[item?.unit]
      )
      .reduce((acc, item) => acc + Number(item.quantity), 0);

    const EstimatedCost = totalCost - totalProfit;
    const markup = (totalMarkup * 100) / EstimatedCost;

    const denominator = totalCost - totalProfit;
    return {
      profit: everyQtyOrUnitCostZero ? 0 : totalProfit,
      profitPercentage:
        totalUnit === 0 || everyQtyOrUnitCostZero
          ? 0
          : Math.round((100 * totalProfit) / totalCost),
      total: everyQtyOrUnitCostZero ? 0 : totalCost,
      grossTotal: roundToTwo(totalTaxAmount / 100) * 100 + totalCost,
      totalTaxAmount,
      taxedItemTotal,
      hours,
      markup: isNaN(markup) || everyQtyOrUnitCostZero ? 0 : Math.round(markup),
    };
  }, [items, filteredItems, taxes]);

  const calculateProfitMarginPercentage = (
    total: number,
    markup: number
  ): number => {
    if (isNaN(total) || isNaN(markup) || total <= 0) {
      //
      return 0;
    }

    const profitMargin = (markup / total) * 100;
    return parseFloat(profitMargin.toFixed(0));
  };

  const estimated_cost = useMemo(
    () => total / 100 - profit / 100,
    [total, profit, items]
  );

  const taxName = useMemo(() => {
    return gSettings?.tax_format === "gst" ? "GST" : "Tax";
  }, [gSettings?.tax_format]);

  const reversibleTaxes = useMemo(
    () => taxes.filter((tax) => tax.is_reversible),
    [taxes]
  );

  const selectedTaxId = useMemo(() => {
    return selectedTaxRates.filter((item) => item).map((tax) => tax.value)[0];
  }, [selectedTaxRates]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleUpdateField = async (data: IWorkorderDetails) => {
    const field = Object.keys(data)[0] as keyof IWorkorderDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateWorkOrderDetailApi({
      id: params.id,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateWorkorderDetail(data));
      dispatch(fetchWorkorderDetails({ id: params.id, shouldLoading: false }));
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 1000);
  };

  const handleTaxUpdate = (updatedTaxes: TaxRecord[]) => {
    const taxes = updatedTaxes.filter((item) => item);
    setSelectedTaxRates(taxes);
    if (taxes.length > 0) {
      const taxId = taxes.map((tax) => tax.value)[0];
      handleUpdateField({
        tax_id: taxId,
      });
      dispatch(updateWOTaxes(JSON.parse(JSON.stringify(taxes))));
    } else {
      handleUpdateField({
        tax_id: "",
      });
      dispatch(updateWOTaxes([]));
    }
  };

  return (
    <div className="md:grid md:grid-cols-2 flex flex-col-reverse gap-2.5">
      <div className="flex flex-col gap-[15px] py-[15px] px-5 ml-auto common-card w-full">
        <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
          <CalcList
            label={_t("Estimated Cost")}
            value={formatter(formatAmount(estimated_cost)).value_with_symbol}
          />
          <li>
            <ul className="md:py-0 py-0.5 relative">
              <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                <FontAwesomeIcon
                  className="w-3 h-3 text-primary-900 dark:text-white"
                  icon="fa-regular fa-plus"
                />
              </li>
            </ul>
          </li>
          <CalcList
            label={_t("Profit")}
            valueClassName="text-[#008000]"
            value={`${
              formatter(formatAmount(profit / 100)).value_with_symbol
            } (${Math.round(calculateProfitMarginPercentage(total, profit))}%)`}
          />
        </ul>

        <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
          <CalcList
            label={_t("Sub Total")}
            labelClassName="font-semibold"
            value={`${formatter(formatAmount(total / 100)).value_with_symbol}`}
          />
          <li>
            <ul className="md:py-0 py-0.5 relative">
              <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                <FontAwesomeIcon
                  className="w-3 h-3 text-primary-900 dark:text-white"
                  icon="fa-regular fa-plus"
                />
              </li>
            </ul>
          </li>
          <CalcList
            label={_t(`Tax`)}
            valueClassName="text-[#008000]"
            value={
              formatter(formatAmount(totalTaxAmount / 100)).value_with_symbol
            }
          />
        </ul>

        <ul className="border-t border-dashed border-[#ddd] relative">
          <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
            <FontAwesomeIcon
              className="w-3 h-3 text-primary-900 dark:text-white"
              icon="fa-regular fa-equals"
            />
          </li>
        </ul>
        <ul className="sm:py-[5px] py-2 px-[15px] rounded relative bg-primary-100 dark:bg-dark-900 w-full">
          <CalcList
            label={_t("Grand Total")}
            labelClassName="font-semibold"
            value={formatter(formatAmount(grossTotal / 100)).value_with_symbol}
          />
        </ul>

        <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
          <CalcList label={_t("Hours")} value={hours} />
          <CalcList label={_t("Markup")} value={`${markup}%`} />
        </ul>
      </div>
      <div className="flex flex-col gap-[15px] py-[15px] px-5 ml-auto common-card w-full">
        <div className="">
          <FieldLabel labelClass="pb-2.5 !w-full block !text-[13px]">
            {_t(`${taxName}/Amount`)}
          </FieldLabel>
          <ul className="grid gap-2.5">
            <li className="w-full">
              <div className="w-full">
                <SelectTaxRate
                  value={selectedTaxId}
                  placeholder={`${_t(`Select ${taxName}`)}`}
                  readOnly={isReadOnly}
                  editInline={true}
                  iconView={true}
                  labelPlacement="left"
                  fixStatus={getStatusForField(loadingStatus, "tax_id")}
                  disabled={readOnlyMode}
                  formInputClassName="bg-[#F8F8F8] tax-select-filed"
                  className="!text-[13px]"
                  allowClear
                  onChange={(_value, options) => {
                    const selectedTaxes = Array.isArray(options)
                      ? options
                      : [options];
                    handleTaxUpdate(selectedTaxes);
                  }}
                  onMouseLeave={() => {
                    handleChangeFieldStatus({
                      field: "tax_id",
                      status: "button",
                      action: "ML",
                    });
                  }}
                />
              </div>
            </li>
          </ul>
        </div>
        <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
          <CalcList
            label={_t("Sub Total")}
            valueClassName="text-[#008000]"
            value={formatter(formatAmount(total / 100)).value_with_symbol}
          />
          {taxes.map((tax) => (
            <>
              <li>
                <ul className="md:py-0 py-0.5 relative">
                  <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
              </li>
              <CalcList
                label={_t(
                  `${taxName}: ${HTMLEntities.decode(
                    sanitizeString(
                      `${tax.tax_name} (${cleanTaxValue(tax.tax_rate)}%)`
                    )
                  )}`
                )}
                valueClassName="text-[#008000]"
                value={`${
                  formatter(
                    formatAmount(
                      (Number(tax.tax_rate) * taxedItemTotal) / 100 / 100
                    )
                  ).value_with_symbol
                }`}
              />
            </>
          ))}
        </ul>

        {!!reversibleTaxes.length && (
          <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
            {reversibleTaxes.map((tax) => (
              <CalcList
                label={_t(
                  `Reversal ${taxName}: ${HTMLEntities.decode(
                    sanitizeString(
                      `${tax.tax_name} (${cleanTaxValue(tax.tax_rate)}%)`
                    )
                  )}`
                )}
                valueClassName="text-[#E25A32]"
                value={
                  formatter(
                    formatAmount(
                      (Number(tax.tax_rate) * taxedItemTotal) / 100 / 100
                    )
                  ).value_with_symbol
                }
              />
            ))}
            <div className="h-5 w-5 rounded-full bg-white absolute shadow-[0px_5px_10px_rgba(0,_0,_0,_0.15)] leading-5 text-center bottom-auto -top-[14px] left-0 right-0 mx-auto">
              <FontAwesomeIcon
                className="w-3 h-3 text-primary-900"
                icon="fa-regular fa-minus"
              />
            </div>
          </ul>
        )}

        <ul className="sm:py-[5px] py-2 px-[15px] rounded relative bg-primary-100 dark:bg-dark-900 w-full">
          <CalcList
            label={_t("Total")}
            labelClassName="font-semibold"
            value={formatter(formatAmount(grossTotal / 100)).value_with_symbol}
          />
        </ul>
        {/* <CustomCheckBox
          className="gap-1.5 w-fit ml-auto font-medium"
          defaultChecked={!!details.count_in_contract_amount}
          loadingProps={{
            isLoading: isLoadingCheckBox,
            className: "bg-[#ffffff]",
          }}
          disabled={module_access == "read_only"}
          onChange={(e) => {
            handleCountContractCheck(e.target.checked ? 1 : 0);
          }}
        >
          {_t("No Cost " + getCOModuleName({ gConfig, isRequest: false }))}
        </CustomCheckBox> */}
      </div>
    </div>
  );
};

export default WorkOrderCalc;
