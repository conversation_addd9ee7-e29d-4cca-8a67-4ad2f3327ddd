import { useMemo, useRef, useState } from "react";
import { useTranslation } from "~/hook";

// molecules
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import {
  filterOptionBySubstring,
  getStatusForField,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useAppEXDispatch, useAppEXSelector } from "../../../redux/store";
import { SelectField } from "~/shared/components/molecules/selectField";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { expenseTypes } from "../../sidebar/constants";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { InlineField } from "~/shared/components/molecules/inlineField";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
import { defaultConfig } from "~/data";
import { InputCurrencyField } from "~/shared/components/molecules/inputCurrencyField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { addExpCustomDataAct } from "../../../redux/slices/addExpensesSlice";
import { addExpCustomData } from "../../../redux/action/addExpensesAction";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

const FinancialsCard = ({
  inputValues,
  isReadOnly = false,
  loadingStatus,
  setInputValues,
  handleChangeFieldStatus,
  handleUpdateField,
}: IFinancialsCardProps) => {
  const { _t } = useTranslation();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { qb_country, quickbook_sync, quickbook_desktop_sync } =
    appSettings || {};
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const inputBlurStatus = useRef(false);
  const { expenseDetail, isExpenseDetailLoading }: IExpensesDetailState =
    useAppEXSelector((state) => state.expenseDetail);
  const { categoryData, accountData }: IExpensesAddState = useAppEXSelector(
    (state) => state.addExpenses
  );
  const { codeCostData }: IGetCostCodeList = useAppEXSelector(
    (state) => state.costCode
  );
  const dispatch = useAppEXDispatch();
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const requiredFieldExceptionFilter = (fieldName: string, field: string) => {
    notification.error({ description: `${fieldName} is a required field` });
    if (field === "amount") {
      setInputValues(
        { ...inputValues, [field]: expenseDetail?.[field] / 100 },
        () => {
          inputBlurStatus.current = true;
        }
      );
    } else {
      setInputValues({ ...inputValues, [field]: expenseDetail?.[field] });
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const handleAddCustomData = async () => {
    if (customDataAdd?.name?.includes("\\")) {
      const availableOption = customDataAdd?.name.split("\\")[0];
      let isLabelAvailable = false;
      if (customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId) {
        isLabelAvailable = categoryOptions?.some(
          (type: Option) =>
            type?.label.toLowerCase().trim() ===
            availableOption.toLowerCase().trim()
        );
      } else {
        isLabelAvailable = accountOptions?.some(
          (type: Option) =>
            type?.label.toLowerCase().trim() ===
            availableOption.toLowerCase().trim()
        );
      }
      if (isLabelAvailable) {
        notification.error({
          description: "Records already exist - no new records were added.",
        });
        if (customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId) {
          setInputValues((prev: IExpenseDetails) => ({
            ...prev,
            category: prev.category,
          }));
        } else {
          setInputValues((prev: IExpenseDetails) => ({
            ...prev,
            paid_by: prev.paid_by,
          }));
        }
        setIsConfirmDialogOpen(false);
        return;
      }
    }
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addExpCustomData({
        itemType: customDataAdd?.itemType,
        name: HTMLEntities.encode(customDataAdd?.name),
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        const itemId = cDataRes?.data?.item_id;
        handleUpdateField({
          [customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId
            ? "category"
            : "paid_by"]: itemId ? itemId : "",
        });
        dispatch(addExpCustomDataAct(cDataRes?.data));
        delay(() => {
          if (customDataAdd?.itemType == expenseTypes?.expenseCategoryTypeId) {
            setInputValues((prev: IExpenseDetails) => ({
              ...prev,
              category: cDataRes?.data?.item_id,
            }));
          } else {
            setInputValues((prev: IExpenseDetails) => ({
              ...prev,
              paid_by: cDataRes?.data?.item_id,
            }));
          }
        }, 500);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };
  // account dropdown Options
  const accountOptions = useMemo(() => {
    const defaultAccData = [
      { label: "Employee", value: "emp" },
      { label: "Company", value: "cmp_card" },
    ];
    if (accountData.length) {
      const accData = accountData.map((item) => {
        return {
          label: `${HTMLEntities.decode(sanitizeString(item.name))} ${
            item?.qb_account_type ? `(${item.qb_account_type})` : ""
          }`,
          value: item.item_id,
        };
      });
      return defaultAccData.concat(accData);
    } else {
      return defaultAccData;
    }
  }, [accountData]);

  // category dropdown Options
  const categoryOptions = useMemo(
    () =>
      categoryData.length
        ? categoryData.map((item) => {
            return {
              label: `${HTMLEntities.decode(sanitizeString(item.name))} ${
                item?.qb_account_type ? `(${item.qb_account_type})` : ""
              }`,
              value: item.item_id,
            };
          })
        : [],
    [categoryData]
  );

  // cost code dropdown Options
  const costCodeOptions = useMemo(
    () =>
      codeCostData.length
        ? codeCostData.map((item) => {
            return {
              label: `${item.cost_code_name} ${
                item?.csi_code && item?.csi_code !== null
                  ? `(${item?.csi_code})`
                  : ""
              }`,
              value: item.code_id,
            };
          })
        : [],
    [codeCostData]
  );

  const taxAmounts: ITaxAmount[] | undefined = useAppEXSelector(
    (state) => state.expenseDetail.taxData.data
  );

  const taxSelectOptions: {
    label: string;
    value: string;
  }[] = useMemo(() => {
    let tempTaxAmount: ITaxAmount[] | undefined = taxAmounts;
    return (
      tempTaxAmount
        ?.map((taxAmount: ITaxAmount) => ({
          label: `${taxAmount.tax_name} (${+taxAmount.tax_rate}%) ${
            taxAmount.is_deleted?.toString() === "1" ? "(Archived)" : ""
          }`,
          value: taxAmount.tax_id.toString(),
        }))
        ?.sort((a: { label: string }, b: { label: string }) => {
          if (a.label < b.label) return -1;
          if (a.label > b.label) return 1;
          return 0;
        }) ?? []
    );
  }, [taxAmounts, expenseDetail]);

  const currentTaxObject: ITaxAmount | null | undefined = useMemo(() => {
    if (isEmpty(taxAmounts) || !inputValues.expense_id) {
      return null;
    }
    let currentTaxObj = taxAmounts?.find(
      (taxAmount: ITaxAmount) =>
        taxAmount?.tax_id?.toString() === String(inputValues.tax_id)
    );

    if (!currentTaxObj) {
      currentTaxObj = taxAmounts?.find(
        (taxAmount: ITaxAmount) =>
          taxAmount?.tax_id?.toString() === defaultConfig.defaultTaxId
      );
    }

    return currentTaxObj;
  }, [inputValues.tax_id, inputValues.expense_id, JSON.stringify(taxAmounts)]);

  const calculateBillTotal = useMemo(() => {
    if (!currentTaxObject) {
      return "";
    }
    const tax = Number(currentTaxObject?.tax_rate);
    const amount = Number(inputValues?.amount);
    let tax_amount = 0;
    if (tax > 0) {
      tax_amount = parseFloat(
        (amount - (amount * 100) / (tax + 100)).toFixed(2)
      );
    }

    return tax_amount;
  }, [currentTaxObject?.tax_id, inputValues?.amount]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: string
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      if (itemType == expenseTypes?.expenseCategoryTypeId.toString()) {
        const newType = onEnterSelectSearchValue(event, categoryOptions || []);
        if (newType) {
          setCustomDataAdd({
            itemType: expenseTypes?.expenseCategoryTypeId.toString(),
            name: HTMLEntities.decode(newType),
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      } else {
        const newType = onEnterSelectSearchValue(event, accountOptions || []);
        if (newType) {
          setCustomDataAdd({
            itemType: expenseTypes.expensePaidThroughTypeId.toString(),
            name: HTMLEntities.decode(newType),
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      }
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Financials")}
        iconProps={{
          icon: "fa-solid fa-file-invoice-dollar",
          containerClassName:
            "bg-[linear-gradient(180deg,#B974FF1a_0%,#8308FF1a_100%)]",
          id: "financials_details_icon",
          colors: ["#B974FF", "#8308FF"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li className="overflow-hidden">
                <InputNumberField
                  label={
                    (qb_country === "CA" || qb_country === "AU") &&
                    quickbook_sync?.toString() === "1"
                      ? _t("Total (w/Tax)")
                      : _t("Total")
                  }
                  placeholder={inputFormatter("0.00").value}
                  value={inputValues.amount ? inputValues.amount : ""}
                  name="amount"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "amount")}
                  labelClass="dark:text-white/90"
                  formatter={(value, info) => {
                    if (inputBlurStatus.current) {
                      inputBlurStatus.current = false;
                      info.input = "";

                      return inputFormatter(info.input).value;
                    }

                    const inputValue = info.input.trim();

                    const valueToFormat =
                      inputValue.length > 0
                        ? unformatted(inputValue)
                        : value
                        ? Number(value).toFixed(2)
                        : String(value);

                    return inputFormatter(valueToFormat).value;
                  }}
                  onChange={(value) => {
                    if (value) {
                      setInputValues({
                        ...inputValues,
                        amount: value,
                      });
                    }
                  }}
                  parser={(value) => {
                    if (!value) return "";
                    const inputValue = unformatted(value.toString());
                    const [integerPart] = inputValue.split(".");

                    if (integerPart.length > 10) {
                      return integerPart.slice(0, 10);
                    }
                    return inputValue;
                  }}
                  onKeyDown={(event) =>
                    onKeyDownCurrency(event, {
                      integerDigits: 10,
                      decimalDigits: 2,
                      unformatted,
                      allowNegative: true,
                      decimalSeparator: inputFormatter().decimal_separator,
                    })
                  }
                  prefix={inputFormatter().currency_symbol}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "amount",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "amount",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "amount",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    inputBlurStatus.current = true;
                    const value = e.target.value?.trim();
                    const numericValue = Number(unformatted(value));

                    if (!value) {
                      requiredFieldExceptionFilter("Total", "amount");
                      setInputValues((prev: Number) => ({
                        ...prev,
                        amount: Number(expenseDetail.amount) / 100,
                      }));
                      return;
                    }

                    const rawUnformatted = unformatted(value);
                    const [integerPart] = rawUnformatted.split(".");
                    if (integerPart.length > 10) {
                      handleChangeFieldStatus({
                        field: "permit_fees",
                        status: "button",
                        action: "BLUR",
                      });
                      return;
                    }

                    if (numericValue === 0) {
                      notification.error({
                        description: "Total amount must be greater than 0",
                      });
                      setInputValues((prev: Number) => ({
                        ...prev,
                        amount: Number(expenseDetail.amount) / 100,
                      }));
                      return;
                    }
                    if (numericValue * 100 !== Number(expenseDetail.amount)) {
                      handleUpdateField({
                        amount: escape(numericValue.toString()),
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "amount",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        amount: numericValue,
                      });
                    }
                  }}
                />
              </li>
              <li>
                <SelectField
                  name="paid_by"
                  label={
                    quickbook_desktop_sync?.toString() === "1"
                      ? _t("Account")
                      : _t("Bank Account")
                  }
                  placeholder={_t(
                    `Select ${
                      quickbook_desktop_sync?.toString() === "1"
                        ? "Account"
                        : "Bank Account"
                    }`
                  )}
                  labelPlacement="left"
                  value={
                    inputValues.paid_by
                      ? accountOptions.filter((item) => {
                          return (
                            String(inputValues.paid_by) === String(item?.value)
                          );
                        })
                      : []
                  }
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  options={accountOptions}
                  allowClear={true}
                  showSearch
                  fixStatus={getStatusForField(loadingStatus, "paid_by")}
                  disabled={
                    getStatusForField(loadingStatus, "paid_by") === "loading"
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    if (
                      (quickbook_desktop_sync?.toString() === "1" ||
                        quickbook_sync?.toString() === "1") &&
                      value == ""
                    ) {
                      notification.error({
                        description: _t(
                          `${
                            quickbook_desktop_sync?.toString() === "1"
                              ? "Account"
                              : "Bank Account"
                          } is is a required field`
                        ),
                      });
                      return;
                    }
                    if (typeof value === "number") {
                      setInputValues({
                        ...inputValues,
                        paid_by: value,
                      });
                    }
                  }}
                  onInputKeyDown={(e) =>
                    handlekeyDown(
                      e,
                      expenseTypes.expensePaidThroughTypeId.toString()
                    )
                  }
                  onSelect={(e) => {
                    const value = e.toString();
                    if (
                      expenseDetail &&
                      String(value) !== String(expenseDetail.paid_by)
                    ) {
                      handleUpdateField({
                        paid_by: value ? value : "",
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "paid_by",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                  }}
                  onClear={() => {
                    if (
                      quickbook_desktop_sync?.toString() === "1" ||
                      quickbook_sync?.toString() === "1"
                    ) {
                      notification.error({
                        description: _t(
                          `${
                            quickbook_desktop_sync?.toString() === "1"
                              ? "Account"
                              : "Bank Account"
                          } is a required field`
                        ),
                      });
                    } else {
                      setInputValues({
                        ...inputValues,
                        paid_by: "",
                      });
                      handleUpdateField({
                        paid_by: "",
                      });
                    }
                  }}
                  addItem={addItemObject}
                />
              </li>
              <li>
                <SelectField
                  name="category"
                  label={_t(
                    quickbook_desktop_sync?.toString() === "1"
                      ? "Bank Account"
                      : "Category"
                  )}
                  placeholder={_t(
                    `Select ${
                      quickbook_desktop_sync?.toString() === "1"
                        ? "Bank Account"
                        : "Category"
                    }`
                  )}
                  value={
                    inputValues.category
                      ? categoryOptions.filter((item) => {
                          return (
                            String(inputValues.category) === String(item.value)
                          );
                        })
                      : []
                  }
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  options={categoryOptions}
                  allowClear={true}
                  showSearch
                  addItem={addItemObject}
                  fixStatus={getStatusForField(loadingStatus, "category")}
                  disabled={
                    getStatusForField(loadingStatus, "category") === "loading"
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    if (
                      (quickbook_desktop_sync?.toString() === "1" ||
                        quickbook_sync?.toString() === "1") &&
                      value == ""
                    ) {
                      notification.error({
                        description: _t(
                          `${
                            quickbook_desktop_sync?.toString() === "1"
                              ? "Bank Account"
                              : "Category"
                          } is a required field`
                        ),
                      });
                      return;
                    }
                    if (typeof value === "number") {
                      setInputValues({
                        ...inputValues,
                        category: value,
                      });
                    }
                  }}
                  onInputKeyDown={(e) =>
                    handlekeyDown(
                      e,
                      expenseTypes?.expenseCategoryTypeId.toString()
                    )
                  }
                  onSelect={(e) => {
                    const value = e.toString();
                    if (
                      expenseDetail &&
                      String(value) !== String(expenseDetail.category)
                    ) {
                      handleUpdateField({
                        category: value ? value : "",
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "category",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                  }}
                  onClear={() => {
                    if (
                      quickbook_desktop_sync?.toString() === "1" ||
                      quickbook_sync?.toString() === "1"
                    ) {
                      notification.error({
                        description: _t(
                          `${
                            quickbook_desktop_sync?.toString() === "1"
                              ? "Bank Account"
                              : "Category"
                          } is a required field`
                        ),
                      });
                    } else {
                      setInputValues({
                        ...inputValues,
                        category: "",
                      });
                      handleUpdateField({
                        category: "",
                      });
                    }
                  }}
                />
              </li>
              <li>
                <SelectField
                  showSearch={true}
                  label={_t("Cost Code")}
                  placeholder={_t("Select Cost Code")}
                  name="cost_code_id"
                  labelPlacement="left"
                  value={
                    inputValues.cost_code_id
                      ? costCodeOptions.filter((item) => {
                          return (
                            String(inputValues.cost_code_id) ===
                            String(item.value)
                          );
                        })
                      : []
                  }
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  options={costCodeOptions}
                  allowClear
                  fixStatus={getStatusForField(loadingStatus, "cost_code_id")}
                  disabled={
                    getStatusForField(loadingStatus, "cost_code_id") ===
                    "loading"
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(value: string | string[]) => {
                    if (value) {
                      setInputValues({
                        ...inputValues,
                        cost_code_id: Number(value),
                      });
                    }
                  }}
                  onSelect={(e) => {
                    const value = e.toString();
                    if (
                      expenseDetail &&
                      String(value) !== String(expenseDetail.cost_code_id)
                    ) {
                      handleUpdateField({
                        cost_code_id: value ? value : "",
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "cost_code_id",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                  }}
                  onClear={() => {
                    handleUpdateField({
                      cost_code_id: "",
                    });
                  }}
                />
              </li>
              <li>
                {(qb_country === "CA" || qb_country === "AU") &&
                  quickbook_sync?.toString() === "1" && (
                    <InlineField
                      label={
                        qb_country === "CA" || qb_country === "AU"
                          ? _t("Taxes/Amount")
                          : _t("Amount")
                      }
                      labelPlacement="left"
                      field={
                        <div
                          className={`grid w-full ${
                            qb_country === "CA" || qb_country === "AU"
                              ? "grid-cols-2"
                              : ""
                          }`}
                        >
                          {qb_country === "CA" || qb_country === "AU" ? (
                            <div className="w-full">
                              <SelectField
                                name="tax_id"
                                labelPlacement="left"
                                value={currentTaxObject?.tax_id.toString()}
                                editInline={true}
                                iconView={true}
                                readOnly={isReadOnly}
                                options={taxSelectOptions}
                                allowClear
                                fixStatus={getStatusForField(
                                  loadingStatus,
                                  "tax_id"
                                )}
                                disabled={
                                  getStatusForField(loadingStatus, "tax_id") ===
                                  "loading"
                                }
                                filterOption={(input, option) =>
                                  filterOptionBySubstring(
                                    input,
                                    option?.label as string
                                  )
                                }
                                onChange={(value) => {
                                  setInputValues({
                                    ...inputValues,
                                    tax_id: value,
                                  });
                                }}
                                onBlur={() => {
                                  if (
                                    expenseDetail &&
                                    inputValues.tax_id !== expenseDetail.tax_id
                                  ) {
                                    handleUpdateField({
                                      tax_id:
                                        inputValues && inputValues.tax_id
                                          ? inputValues.tax_id
                                          : "",
                                    });
                                  } else {
                                    handleChangeFieldStatus({
                                      field: "tax_id",
                                      status: "button",
                                      action: "BLUR",
                                    });
                                  }
                                }}
                                onClear={() => {
                                  handleUpdateField({
                                    item_type: "",
                                  });
                                }}
                              />
                            </div>
                          ) : (
                            ""
                          )}
                          <div className="w-full">
                            <InputCurrencyField
                              name="amount"
                              placeholder="0.00"
                              labelPlacement="left"
                              editInline={true}
                              iconView={true}
                              onChange={() => {}}
                              value={calculateBillTotal}
                              disabled={true}
                              readOnly={isReadOnly}
                            />
                          </div>
                        </div>
                      }
                    />
                  )}
              </li>
            </ul>
          </div>
        }
      />
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
    </>
  );
};

export default FinancialsCard;
