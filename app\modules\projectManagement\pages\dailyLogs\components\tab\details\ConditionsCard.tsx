import { useEffect, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import delay from "lodash/delay";

// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Switch } from "~/shared/components/atoms/switch";

// molecules
import { InlineField } from "~/shared/components/molecules/inlineField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";

// helper and hooks
import {
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { useTranslation } from "~/hook";

// redux slice, store and action
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { updateDLDetailApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { updateDLWeatherDetail } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dLDetailsSlice";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";

// constants
import { DLWeatherDetailsField, fieldStatus } from "../../../utils/constasnts";

const ConditionsCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const loadingStatusRef = useRef(fieldStatus);
  const decInpRef = useRef<HTMLInputElement>(null);
  const scheInpRef = useRef<HTMLInputElement>(null);
  const switchRefWD = useRef<HTMLDivElement>(null);
  const switchRefSD = useRef<HTMLDivElement>(null);

  const dispatch = useAppDLDispatch();
  const { weatherDetails, isDetailLoading }: IDLDetailsInitialState =
    useAppDLSelector((state) => state.dailyLogDetails);
  const [inputValues, setInputValues] = useState<IDLWeatherUpDetails>(
    DLWeatherDetailsField
  );
  const [isConditions, setISConditions] = useState({
    anyWeatherDelay: false,
    anyScheduleDelay: false,
  });
  const preSwitchBlurWD = useRef(false);
  const preSwitchBlurSD = useRef(false);

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);

  useEffect(() => {
    setISConditions({
      anyWeatherDelay: weatherDetails?.anyWeatherDelay === 1,
      anyScheduleDelay: weatherDetails?.anyScheduleDelay === 1,
    });
    setInputValues(weatherDetails);
  }, [weatherDetails.logId]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: IDLDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDLWeatherUpDetails;
    setInputValues({ ...inputValues, ...data });
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateDLDetailApi({
      logId: weatherDetails?.logId || "",
      ...data,
    })) as IDLDetailsUpdateApiRes;

    if (updateRes?.success) {
      dispatch(resetDash());
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateDLWeatherDetail(data));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: weatherDetails[field] });
      setISConditions({ ...isConditions, [field]: weatherDetails[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChange = (data: boolean, type: string) => {
    if (type === "anyWeatherDelay") {
      setISConditions((prev) => ({
        ...prev,
        anyWeatherDelay: data,
      }));
      if (!data || !isEmpty(inputValues?.weatherDelayNotes))
        handleUpdateField({
          anyWeatherDelay: data ? 1 : 0,
        });
    } else {
      setISConditions((prev) => ({
        ...prev,
        anyScheduleDelay: data,
      }));
      if (!data || !isEmpty(inputValues?.scheduleDelayNotes))
        handleUpdateField({
          anyScheduleDelay: data ? 1 : 0,
        });
    }
  };

  const handleSwitchMouseDown = (type: string) => {
    if (type === "anyWeatherDelay") {
      preSwitchBlurWD.current = true;
      setTimeout(() => {
        preSwitchBlurWD.current = false;
      }, 1000);
    } else {
      preSwitchBlurSD.current = true;
      setTimeout(() => {
        preSwitchBlurSD.current = false;
      }, 1000);
    }
  };

  return (
    <CrudCommonCard
      headerTitle={_t("Delays")}
      iconProps={{
        icon: "fa-solid fa-ballot-check",
        containerClassName:
          "bg-[linear-gradient(180deg,#50ebfd1a_0%,#5996e91a_100%)]",
        id: "condition_square_list",
        colors: ["#50EBFD", "#5996E9"],
      }}
      children={
        <div className="pt-2">
          {isDetailLoading ? (
            <Spin className="w-full h-[75px] flex items-center justify-center" />
          ) : (
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <InlineField
                  label={_t("Weather Delays")}
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  labelPlacement="left"
                  field={
                    <div className="flex gap-2 w-full">
                      <div
                        ref={switchRefWD}
                        onMouseDown={() => {
                          handleSwitchMouseDown("anyWeatherDelay");
                        }}
                      >
                        <Switch
                          className="cf-switch success mt-2"
                          value={isConditions.anyWeatherDelay}
                          disabled={isReadOnly}
                          onChange={(data) => {
                            handleChange(data as boolean, "anyWeatherDelay");
                          }}
                        />
                      </div>
                      <TextAreaField
                        ref={decInpRef}
                        placeholder={_t("Write Note about Weather Delay")}
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        readOnly={isReadOnly || !isConditions.anyWeatherDelay}
                        value={
                          (isConditions.anyWeatherDelay &&
                            inputValues?.weatherDelayNotes) ||
                          ""
                        }
                        name="weatherDelayNotes"
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "weatherDelayNotes"
                        )}
                        onChange={handleInpOnChange}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "weatherDelayNotes",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "weatherDelayNotes",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onFocus={() =>
                          handleChangeFieldStatus({
                            field: "weatherDelayNotes",
                            status: "save",
                            action: "FOCUS",
                          })
                        }
                        onBlur={(e) => {
                          const value = e?.target?.value.trim();
                          if (preSwitchBlurWD.current) {
                            preSwitchBlurWD.current = false; // Reset after switch is clicked
                            return;
                          }
                          if (value !== weatherDetails?.weatherDelayNotes) {
                            if (value === "") {
                              notification.error({
                                description: _t(
                                  `Weather delays cannot be empty.`
                                ),
                              });
                              setInputValues({
                                ...inputValues,
                                weatherDelayNotes:
                                  weatherDetails?.weatherDelayNotes,
                              });
                              return false;
                            }
                            handleUpdateField({
                              weatherDelayNotes: value,
                              anyWeatherDelay: value ? 1 : 0,
                            });
                          } else {
                            handleChangeFieldStatus({
                              field: "weatherDelayNotes",
                              status: "button",
                              action: "BLUR",
                            });
                            setInputValues({
                              ...inputValues,
                              weatherDelayNotes:
                                weatherDetails.weatherDelayNotes,
                            });
                          }
                        }}
                        onClickStsIcon={() => {
                          if (
                            getStatusForField(
                              loadingStatus,
                              "weatherDelayNotes"
                            ) === "edit"
                          ) {
                            decInpRef.current?.focus();
                          }
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li>
                <InlineField
                  label={_t("Schedule Delays")}
                  labelClass="sm:w-[140px] sm:max-w-[140px]"
                  labelPlacement="left"
                  field={
                    <div className="flex gap-2 w-full">
                      <div
                        ref={switchRefSD}
                        onMouseDown={() => {
                          handleSwitchMouseDown("anyScheduleDelay");
                        }}
                      >
                        <Switch
                          className="cf-switch success mt-2"
                          value={isConditions.anyScheduleDelay}
                          disabled={isReadOnly}
                          onChange={(data) => {
                            handleChange(data as boolean, "anyScheduleDelay");
                          }}
                        />
                      </div>
                      <TextAreaField
                        placeholder={_t("Write Note about Schedule Delay")}
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        readOnly={isReadOnly || !isConditions.anyScheduleDelay}
                        ref={scheInpRef}
                        value={
                          (isConditions.anyScheduleDelay &&
                            inputValues?.scheduleDelayNotes) ||
                          ""
                        }
                        name="scheduleDelayNotes"
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "scheduleDelayNotes"
                        )}
                        onChange={handleInpOnChange}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "scheduleDelayNotes",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "scheduleDelayNotes",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onFocus={() =>
                          handleChangeFieldStatus({
                            field: "scheduleDelayNotes",
                            status: "save",
                            action: "FOCUS",
                          })
                        }
                        onBlur={(e) => {
                          const value = e?.target?.value.trim();
                          if (preSwitchBlurSD.current) {
                            preSwitchBlurSD.current = false; // Reset after switch is clicked
                            return;
                          }
                          if (value !== weatherDetails?.scheduleDelayNotes) {
                            if (value === "") {
                              notification.error({
                                description: _t(
                                  `Schedule delays cannot be empty.`
                                ),
                              });
                              setInputValues({
                                ...inputValues,
                                scheduleDelayNotes:
                                  weatherDetails?.scheduleDelayNotes,
                              });
                              return false;
                            }
                            handleUpdateField({
                              scheduleDelayNotes: value,
                              anyScheduleDelay: value ? 1 : 0,
                            });
                          } else {
                            handleChangeFieldStatus({
                              field: "scheduleDelayNotes",
                              status: "button",
                              action: "BLUR",
                            });
                            setInputValues({
                              ...inputValues,
                              scheduleDelayNotes:
                                weatherDetails.scheduleDelayNotes,
                            });
                          }
                        }}
                        onClickStsIcon={() => {
                          if (
                            getStatusForField(
                              loadingStatus,
                              "scheduleDelayNotes"
                            ) === "edit"
                          ) {
                            scheInpRef.current?.focus();
                          }
                        }}
                      />
                    </div>
                  }
                />
              </li>
            </ul>
          )}
        </div>
      }
    />
  );
};

export default ConditionsCard;
