import { memo, useCallback, useEffect, useRef, useState } from "react";

// Fort Awesome
import { faInfoCircle } from "@fortawesome/pro-regular-svg-icons/faInfoCircle";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// atoms
import { But<PERSON> } from "~/shared/components/atoms/button";
import { Typography } from "~/shared/components/atoms/typography";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Progress } from "~/shared/components/atoms/progress";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Search } from "~/shared/components/atoms/search";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { InfiniteScroll } from "~/shared/components/molecules/InfiniteScroll/InfiniteScroll";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// Zustand
import {
  getGlobalProject,
  getGlobalProjectActionType,
} from "~/zustand/global/config/slice";
import {
  GLOBAL_PROJECT_ACTION_OPTIONS,
  GLOBAL_PROJECT_ACTIONS,
} from "~/zustand/global/config/constants";
import { apiRoutes, routes } from "~/route-services/routes";
import {
  setGlobalProject,
  setGlobalProjectActionType,
} from "~/zustand/global/config/action";

// Lodash
import debounce from "lodash/debounce";

// Other
import { Number, sanitizeString } from "~/helpers/helper";
import NoDataFound from "~/components/common/no-data-found";
import { useTranslation } from "~/hook";
import ProjectListLoader from "./project-list-loader";
import { initialGlobalData } from "~/zustand/global/constants";
import { NavigateFunction, useNavigate } from "@remix-run/react";

const SelectProject = () => {
  let { _t } = useTranslation();
  const navigate: NavigateFunction = useNavigate();
  const selectedProjectDevRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLDivElement | null>(null);

  const [isFilterClear, setIsFilterClear] = useState<boolean>(false);

  const [selectProjectOpen, setSelectProjectOpen] = useState<boolean>(false);
  const [withComplete, setWithComplete] = useState<boolean>(false);
  const [searchProject, setSearchProject] = useState<string>("");
  const actionTypeRef = useRef<string | "">("");
  const [projects, setProjects] = useState<IGlobalProjectListModifiedData[]>(
    []
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [hasSingleProjectFetched, setHasSingleProjectFetched] =
    useState<boolean>(false);

  const {
    project_name,
    project_id,
  }: IInitialGlobalData["config"]["global_project"] = getGlobalProject();
  const actionType: IInitialGlobalData["config"]["global_project_action_type"] =
    getGlobalProjectActionType();

  useEffect(() => {
    if (actionType) {
      actionTypeRef.current = actionType;
    }
  }, [actionType]);

  const isDefaultProject =
    actionType === GLOBAL_PROJECT_ACTIONS.default_project.value;

  const setProjectsList = async ({
    add_new_data = true,
    start = 0,
    limit = 20,
    search,
    is_completed = false,
    is_checked = false,
  }: ISetProjectsListParams | undefined = {}) => {
    try {
      setIsLoading(true);
      const apiParams = await getWebWorkerApiParams<ISetProjectsParams>({
        otherParams: {
          ...(search ? { search: HTMLEntities.encode(search) } : {}),
          start,
          limit,
          is_completed,
          record_type: "project",
          need_all_projects: 0,
          top_project_id: project_id,
          global_call: true,
          filter: { status: "0" },
        },
      });

      const { success, message, data } = (await webWorkerApi({
        url: apiRoutes.GET_PROJECTS.url,
        method: "post",
        data: apiParams,
      })) as IGetProjectsApiResponse;

      if (success) {
        if (data) {
          if (data.global_project) {
            setGlobalProject({
              ...data.global_project,
              project_id: data.global_project.project_id.toString(),
            });
            // 86cycrkgq
            if (!start && !is_completed && !search?.trim() && !is_checked) {
              setGlobalProjectActionType(data.global_project.project_selection);
            }
          }
          const modifiedProjects = (): IGlobalProjectListModifiedData[] => {
            return data.projects.map((project) => {
              data.module_status.find(
                (moduleStatus) =>
                  moduleStatus.key === project.project_status_key
              );
              return {
                ...project,
                project_status_color: data.module_status.find(
                  (moduleStatus) =>
                    moduleStatus.key === project.project_status_key
                )?.status_color,
                project_type_color: data.module_status.find(
                  (moduleStatus) =>
                    moduleStatus.key === project.project_type_key
                )?.status_color,
              };
            });
          };
          setProjects((prev) => {
            const newProjects = modifiedProjects();
            if (add_new_data) {
              // Filter out projects that already exist in the current array
              const filteredNewProjects = newProjects.filter(
                (newProject) =>
                  !prev.some(
                    (existingProject) =>
                      existingProject.id.toString() === newProject.id.toString()
                  )
              );
              return [...prev, ...filteredNewProjects];
            } else {
              return newProjects;
            }
          });

          if (data.projects.length < limit) {
            setHasMore(false);
          }
        }

        setIsLoading(false);
        return;
      }
      notification.error({
        description: message || "Something went wrong!",
      });
      setHasMore(false);
      setIsLoading(false);
      return;
    } catch (error) {
      notification.error({
        description: (error as Error).message,
      });
    }
    setHasMore(false);
    setIsLoading(false);
  };

  const handleInputChange = useCallback(
    debounce(({ search, is_completed }: Partial<ISetProjectsListParams>) => {
      setProjectsList({
        add_new_data: false,
        is_completed,
        search,
      });
    }, 300),
    []
  );

  useEffect(() => {
    const onClick = (e: MouseEvent) => {
      if (buttonRef?.current?.contains(e.target as Node)) {
        if (
          isDefaultProject &&
          project_id.trim() &&
          project_id.trim() !== "0" &&
          selectedProjectDevRef?.current?.contains(e.target as Node)
        ) {
          navigate(`${routes.MANAGE_PROJECT.url}/${project_id}`);
        } else {
          setSelectProjectOpen((prev: boolean) => !prev);
          if (isFilterClear) {
            setProjectsList({
              add_new_data: false,
            });
            setIsFilterClear(false);
          }
        }
      }
    };
    window.addEventListener("click", onClick);
    return () => {
      window.removeEventListener("click", onClick);
    };
  }, [
    buttonRef,
    selectedProjectDevRef,
    isDefaultProject,
    project_id,
    isFilterClear,
  ]);

  const updateGlobalProject = async (
    project: IGlobalProjectListModifiedData
  ) => {
    try {
      const currentProjectId = project.id;
      const currentAction =
        project_id.toString() !== project.id.toString() ? "select" : "deselect";
      const apiParams = await getWebWorkerApiParams({
        otherParams: {
          project_id: currentProjectId,
          project_selection: actionTypeRef.current || actionType,
          action: currentAction,
        },
      });

      const {
        success,
        message,
        data: global_project,
      } = (await webWorkerApi({
        url: apiRoutes.UPDATE_GLOBAL_PROJECT.url,
        method: "post",
        data: apiParams,
      })) as IUpdateGlobalProjectNewApiResponse;
      if (success) {
        setGlobalProject({
          ...initialGlobalData.config.global_project,
          ...global_project,
          project_name: global_project.project_name || "",
          ...(currentAction === "select"
            ? { project_id: global_project.project_id?.toString() }
            : {}),
          view_in_timecard: currentProjectId
            ? project.view_in_timecard.toString()
            : "",
        });
        return;
      }
      notification.error({
        description: message || "Something went wrong!",
      });
      return;
    } catch (error) {
      notification.error({
        description: (error as Error).message,
      });
    }
  };

  const loadMoreProjects = () => {
    setProjectsList({
      start: projects.length,
    });
  };

  useEffect(() => {
    if (selectProjectOpen) {
      const onClick = (e: MouseEvent) => {
        const headerTab = document.getElementsByTagName("header")?.[0] as Node;
        const globalSelectProjectButton = document.getElementById(
          "global_select_project"
        ) as Node;
        if (
          headerTab?.contains(e.target as Node) &&
          !globalSelectProjectButton?.contains(e.target as Node)
        ) {
          setSelectProjectOpen(false);
        }
      };
      window.addEventListener("click", onClick);
      return () => {
        window.removeEventListener("click", onClick);
      };
    }
  }, [selectProjectOpen]);

  useEffect(() => {
    setProjectsList({
      add_new_data: false,
    });
  }, []);

  useEffect(() => {
    if (project_id && projects.length > 0 && !hasSingleProjectFetched) {
      const selectedData = projects.find((i) => i?.id.toString() == project_id);
      setHasSingleProjectFetched(true);
      if (selectedData) return;
      setProjectsList({
        add_new_data: true,
        limit: 1,
      });
    }
  }, [project_id, projects]);

  const closeProjectSidebar = () => {
    setSelectProjectOpen(!selectProjectOpen);
    if (Boolean(searchProject.trim()) || withComplete) {
      setSearchProject("");
      setWithComplete(false);
      setProjects([]);
      setIsFilterClear(true);
    }
  };

  return (
    <>
      <Button
        id="global_select_project"
        className={`!p-0 !h-full !leading-[normal] !border-0 rounded-none group/menu hover:!bg-primary-8 outline-none dark:!bg-dark-800 dark:active:!bg-dark-900 active:!bg-primary-200/10 hover:!bg-inherit normal-case font-normal focus-visible:outline-none focus-within:outline-none ${
          selectProjectOpen ? "!bg-primary-8" : ""
        }`}
      >
        <div ref={buttonRef} className="px-3 py-1.5 flex w-full h-full">
          <div className="md:flex xl:min-w-[175px] xl:max-w-[175px] md:min-w-[130px] md:max-w-[130px] items-center justify-between hidden">
            <div className="text-left max-w-[calc(100%-16px)] overflow-hidden">
              {isDefaultProject && (
                <Typography className="text-[11px] text-[#666] font-semibold uppercase">
                  {_t("DEFAULT PROJECT")}
                  <Tooltip
                    title={_t(
                      "When a Default Project is selected, you will only see items that pertain to the selected project.  To change the default project click on the down arrow to select/deselect a project."
                    )}
                    placement="bottom"
                  >
                    <FontAwesomeIcon
                      className="ml-1 w-3 h-3 text-primary-900"
                      icon={faInfoCircle}
                    />
                  </Tooltip>
                </Typography>
              )}
              <div
                className="font-medium text-black dark:text-white !text-sm truncate"
                ref={selectedProjectDevRef}
              >
                {isDefaultProject ? (
                  <Typography>
                    {project_name.trim()
                      ? HTMLEntities.decode(sanitizeString(project_name))
                      : _t("Select a Default Project")}
                  </Typography>
                ) : (
                  _t("Select a Project")
                )}
              </div>
            </div>
            <FontAwesomeIcon
              className={`text-black dark:text-white w-4 h-4 transition-all duration-300 ease-in-out group-hover/menu:rotate-180 ${
                selectProjectOpen ? "rotate-180" : ""
              }`}
              icon="fa-regular fa-chevron-down"
            />
          </div>
          <div className="md:hidden flex items-center md:pt-0 pt-0.5">
            <FontAwesomeIcon
              className="text-primary-900 w-5 h-5 dark:text-white/90"
              icon="fa-regular fa-ballot-check"
            />
          </div>
        </div>
      </Button>

      <Drawer
        open={selectProjectOpen}
        placement="left"
        rootClassName="drawer-open md:top-[66px] top-12"
        width={400}
        maskClosable={true}
        onClose={closeProjectSidebar}
        classNames={{
          header: "bg-primary-900 dark:bg-dark-900 !px-4 !pb-2.5 !pt-[11px]",
          body: "!p-0 !overflow-hidden ",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 bg-transparent text-white w-5 h-5">
              <img src="https://cdn.contractorforeman.net/assets/images/list-check-solid.svg" />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !mb-0 dark:!text-white/90 !font-normal !text-white !text-base dark:text-[#dcdcdd] select-project-header"
            >
              {_t(`Select Project`)}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton
            className="bg-transparent hover:!bg-[#1c2537] w-6 h-6 max-w-[24px] max-h-[24px]"
            iconClassName="!text-white group-hover/cancle:!text-white"
            onClick={closeProjectSidebar}
          />
        }
      >
        <div className="sidebar-body dark:bg-dark-800 h-full">
          <div className="border-b border-gray-300 dark:border-white/10 py-2 px-4">
            <div className="w-full">
              <Search
                placeholder={_t("Search Projects")}
                variant="borderless"
                className="search-input-borderless relative dark:bg-white/5 rounded-md border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 dark:before:bg-[#696b6e]"
                allowClear={true}
                value={searchProject}
                onChange={(e) => {
                  setSearchProject(e.target.value);
                  handleInputChange({
                    search: e.target.value,
                    is_completed: withComplete,
                  });
                }}
              />
            </div>
            <div className="flex justify-between items-center mt-2">
              <CheckBox
                className="gap-1.5 text-xs text-primary-900 checkbox-padding-remove font-medium dark:text-white/90"
                children={_t("Show Completed")}
                checked={withComplete}
                disabled={isLoading}
                onChange={(e) => {
                  setWithComplete(e.target.checked);
                  setProjectsList({
                    add_new_data: false,
                    is_completed: e.target.checked,
                    search: searchProject,
                    is_checked: true,
                  });
                }}
              />
              <SelectField
                labelPlacement="left"
                label={_t("Action") + ":"}
                formInputClassName="!w-fit"
                fieldClassName="before:hidden"
                labelClass="dark:text-[#dcdcdd] pr-1 !text-xs !w-fit "
                className="!text-xs !h-7 !border-0 select-project-dropdown"
                popupClassName="!w-[170px] select-project-popup"
                placement="bottomRight"
                options={GLOBAL_PROJECT_ACTION_OPTIONS}
                value={actionType}
                onChange={(value) => {
                  const tempActionType = Array.isArray(value)
                    ? ""
                    : value || "";
                  actionTypeRef.current = tempActionType;
                  setGlobalProjectActionType(tempActionType);
                  if (
                    tempActionType ===
                    GLOBAL_PROJECT_ACTIONS.detail_project.value
                  ) {
                    const selectedGlobalProject = projects.find(
                      (ele) => ele.id?.toString() === project_id
                    );
                    if (selectedGlobalProject) {
                      updateGlobalProject(selectedGlobalProject);
                    }
                  } else {
                    setGlobalProject({
                      project_id: "0",
                      project_name: "",
                      view_in_timecard: "",
                      project_selection: tempActionType,
                    });
                  }
                }}
              />
            </div>
          </div>
          <div
            className={`md:h-[calc(100vh-200px)] h-[calc(100vh-184px)] overflow-x-hidden ${
              isLoading ? "" : "overflow-y-auto "
            }`}
          >
            {isLoading && projects.length === 0 ? (
              <ProjectListLoader />
            ) : projects.length === 0 ? (
              <NoDataFound />
            ) : (
              <InfiniteScroll
                loadMore={loadMoreProjects}
                hasMore={hasMore}
                isLoading={isLoading}
                loadingComponent={<ProjectListLoader length={1} />}
              >
                {projects.map((project, key) => (
                  <div key={key}>
                    <ProjectItem
                      onClick={() => {
                        setSelectProjectOpen(!selectProjectOpen);
                        if (isDefaultProject) {
                          updateGlobalProject(project);
                        }
                        if (Boolean(searchProject.trim()) || withComplete) {
                          setSearchProject("");
                          setWithComplete(false);
                          setProjects([]);
                          setIsFilterClear(true);
                        }
                      }}
                      project={project}
                      active={project.id.toString() === project_id.toString()}
                      href={
                        !isDefaultProject
                          ? `${routes.MANAGE_PROJECT.url}/${project?.id}`
                          : undefined
                      }
                    />
                  </div>
                ))}
              </InfiniteScroll>
            )}
          </div>
        </div>
      </Drawer>
    </>
  );
};

const ProjectItem = ({ onClick, project, active, href }: IProjectItem) => {
  const {
    project_color = "",
    project_name = "",
    project_id = 0,
    project_status_name = "",
    project_type_name = "",
    customer_name = "",
    project_status_color = "",
    project_type_color = "",
    progress = "",
  } = project;
  const title = `${
    HTMLEntities.decode(sanitizeString(project_name)) ?? ""
  } (${project_id})`;

  let { _t } = useTranslation();
  return (
    <TypographyLink
      href={href}
      onClick={onClick}
      className="w-full h-full !py-1.5 !px-4 flex items-center justify-between cursor-pointer !border-b !border-solid !border-gray-300 hover:bg-primary-gray-20 dark:hover:bg-dark-900 dark:border-white/10 project"
      style={{
        background: active ? "#ebf1f9" : "",
      }}
    >
      <div className="flex items-center w-[calc(100%-40px)]">
        <div className="truncate w-full">
          <div className="flex items-center">
            <div
              className="mr-1.5 w-2.5 h-2.5 bg-primary-900 rounded-full"
              style={{ background: project_color }}
            />
            <Tooltip
              title={title}
              className="max-w-xs after:left-10"
              placement="top"
            >
              <Typography className="truncate block !max-w-[calc(100%-16px)] !text-primary-900 dark:!text-white/90 font-semibold !text-13">
                {title}
              </Typography>
            </Tooltip>
          </div>
          <div className="flex items-center mt-1">
            <div className="flex items-center w-full">
              <ProjectStatus
                statusColor={project_status_color}
                statusName={HTMLEntities.decode(
                  sanitizeString(project_status_name)
                )}
              />
              <ProjectStatus
                statusColor={project_type_color}
                statusName={HTMLEntities.decode(
                  sanitizeString(project_type_name)
                )}
                className=" bg-[#5816442e] text-[#581644]"
              />
            </div>
          </div>
          <div className="flex items-center mt-1 text-primary-900 dark:text-[#dcdcdd]">
            <div className="flex items-center mr-2 max-w-full overflow-hidden leading-4">
              {customer_name ? (
                <Tooltip
                  title={HTMLEntities.decode(sanitizeString(customer_name))}
                  className="max-w-xs mt-0.5"
                  placement="top"
                >
                  <Typography className="text-xs font-normal truncate block dark:text-white/90">
                    {HTMLEntities.decode(sanitizeString(customer_name))}
                  </Typography>
                </Tooltip>
              ) : (
                <Typography className="text-xs font-normal truncate block italic pr-1">
                  {_t("No customer selected")}
                </Typography>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="project-percent">
        <Progress
          size={30}
          type="circle"
          trailColor={"#00800080"}
          strokeWidth={9}
          strokeColor={"#008000"}
          percent={Number(`${Number(progress) * 100}`)}
        />
      </div>
    </TypographyLink>
  );
};

const ProjectStatus = ({
  statusColor,
  statusName,
  className = "",
}: IProjectStatusProps) =>
  statusName && (
    <Tooltip title={statusName} className="max-w-xs" placement="top">
      <Typography
        className={
          "text-[10px] text-center font-semibold rounded-sm min-w-[72px] leading-4 px-1.5 block truncate first:ml-0 ml-2 " +
          className
        }
        style={{
          color: statusColor,
          background: `${statusColor}2e`,
        }}
      >
        {statusName}
      </Typography>
    </Tooltip>
  );

export default memo(SelectProject);
