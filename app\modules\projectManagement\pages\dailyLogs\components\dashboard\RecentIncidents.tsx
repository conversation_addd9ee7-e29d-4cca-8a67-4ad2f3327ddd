import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
import { getGModuleByKey } from "~/zustand";
import { defaultConfig } from "~/data";
import {
  useAppDLDispatch,
  useAppDLSelector,
} from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { fetchDashData } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { sanitizeString } from "~/helpers/helper";

// atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { AvatarGroup } from "~/shared/components/atoms/avatarGroup";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";

const RecentIncidents = () => {
  const { _t } = useTranslation();
  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.incident_module
  );
  const dispatch = useAppDLDispatch();
  const {
    isDashLoading,
    recent_incidents,
    recent_incidents_last_refresh_time,
  }: IDailyLogIntlState = useAppDLSelector((state) => state.dashboard);

  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IRecentIncidents[]>([]);
  useEffect(() => {
    if (!isCashLoading && recent_incidents) {
      setRowData(recent_incidents);
    }
  }, [recent_incidents, isCashLoading]);
  const columnDefs = [
    {
      headerName: _t("Date"),
      maxWidth: 135,
      minWidth: 135,
      field: "incident_date",
      suppressMenu: true,
      cellRenderer: (params: IDLIncidentTableCellRenderer) => {
        return params?.data?.incident_date ? (
          <DateTimeCard format="date" date={params?.data?.incident_date} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("People Involved"),
      field: "employees",
      minWidth: 120,
      maxWidth: 120,
      headerClass: "ag-header-center",
      suppressMenu: true,
      cellRenderer: (params: IDLIncidentTableCellRenderer) => {
        const incidentEmployee = params?.data?.employees || [];
        return incidentEmployee ? (
          <AvatarGroup
            max={{
              count: 1,
              style: {
                color: "#223558",
                backgroundColor: "#ECF1F9",
              },
            }}
            size={24}
            className="flex justify-center"
            prefixCls="multi-avatar-scroll"
          >
            {incidentEmployee.length > 0
              ? incidentEmployee.map(
                  (items: IIncidentsEmployee, index: number) => {
                    const dName = HTMLEntities.decode(
                      sanitizeString(items.display_name)
                    );
                    return (
                      <div
                        key={index}
                        className={`flex items-center ${
                          index === 0 ? "" : "gap-2 py-0.5 px-1"
                        }`}
                      >
                        {index === 0 ? (
                          <Tooltip title={dName} placement="top">
                            <div>
                              <AvatarProfile
                                user={{
                                  name: dName,
                                  image: items.image,
                                }}
                                iconClassName="text-[11px] font-semibold"
                              />
                            </div>
                          </Tooltip>
                        ) : (
                          <div className="p-1 flex items-center gap-1">
                            <AvatarProfile
                              user={{
                                name: dName,
                                image: items.image,
                              }}
                              iconClassName="text-[11px] font-semibold"
                            />
                            <Typography className="">{_t(dName)}</Typography>
                          </div>
                        )}
                      </div>
                    );
                  }
                )
              : "-"}
          </AvatarGroup>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Classification"),
      field: "classification",
      minWidth: 109,
      flex: 2,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: IDLIncidentTableCellRenderer) => {
        const incidentTypeName = HTMLEntities.decode(
          sanitizeString(data?.incident_type_name)
        );
        return (
          <Tooltip title={incidentTypeName}>
            <Typography className="table-tooltip-text">
              {incidentTypeName}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "incident_type_name",
      minWidth: 120,
      flex: 2,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: ({ data }: IDLIncidentTableCellRenderer) => {
        const incidentTypeName = HTMLEntities.decode(
          sanitizeString(data?.txn_type_display_name)
        );
        return incidentTypeName ? (
          <Tooltip title={incidentTypeName}>
            <Typography className="table-tooltip-text">
              {incidentTypeName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const handleRefreshWidget = async () => {
    setIsCashLoading(true);
    setRowData([]);
    await dispatch(fetchDashData({ refreshType: "recent_incidents" }));
    setIsCashLoading(false);
  };
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-incidents.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={module?.plural_name || ""}
        showRefreshIcon={true}
        refreshIconTooltip={recent_incidents_last_refresh_time}
        onClickRefresh={handleRefreshWidget}
        isRefreshing={isCashLoading}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            key={isDashLoading ? "loading" : "loaded"}
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            noRowsOverlayComponent={
              isDashLoading || isCashLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};

export default RecentIncidents;
