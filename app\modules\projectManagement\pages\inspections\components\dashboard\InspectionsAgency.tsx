import { useEffect, useRef, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import { type GridApi, type GridReadyEvent } from "ag-grid-community";
import { getGConfig } from "~/zustand";
import { useInAppDispatch, useInAppSelector } from "../../redux/store";
import { fetchInspectionsDashboardApi } from "../../redux/action/inspectionDashAction";
import { sanitizeString } from "~/helpers/helper";

const InspectionsAgency = () => {
  const { _t } = useTranslation();
  const { module_name }: GConfig = getGConfig();
  const dispatch = useInAppDispatch();
  const gridApiRef = useRef<GridApi | null>(null);
  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  const [isCashLoading, setisCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IInspectionByAgencyData[]>([]);

  const { get_inspections_by_agency, isDashboardLoading } = useInAppSelector(
    (state) => state.inspectionsDashboard
  );

  useEffect(() => {
    if (!isCashLoading && get_inspections_by_agency?.data) {
      setRowData(get_inspections_by_agency?.data);
    }
  }, [get_inspections_by_agency, isCashLoading]);

  const handleClickRefresh = async () => {
    setisCashLoading(true);
    setRowData([]);
    await dispatch(
      fetchInspectionsDashboardApi({
        refresh_type: "get_inspections_by_agency",
      })
    );
    setisCashLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t(`Agency`),
      field: "inspection_agency_name",
      minWidth: 130,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IInspectionByAgencyData }) => {
        const { data } = params;
        return data.inspection_agency_name ? (
          <Tooltip
            title={HTMLEntities.decode(
              sanitizeString(data.inspection_agency_name)
            )}
          >
            <Typography className="table-tooltip-text">
              {HTMLEntities.decode(sanitizeString(data.inspection_agency_name))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    // {
    //   headerName: _t(`Inspector Name`),
    //   field: "inspection_name",
    //   minWidth: 130,
    //   flex: 2,
    //   suppressMenu: true,
    //   headerClass: "ag-header-left",
    //   cellClass: "ag-cell-left",
    //   cellRenderer: () => {
    //     const inspectionName = "Kathryn Murphy";
    //     return (
    //       <Tooltip title={inspectionName}>
    //         <Typography className="table-tooltip-text">
    //           {inspectionName}
    //         </Typography>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      headerName: "#",
      field: "agency_count",
      minWidth: 100,
      maxWidth: 100,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: { data: IInspectionByAgencyData }) => {
        const { data } = params;
        return data.agency_count ? (
          <Tooltip title={data.agency_count}>
            <Typography className="table-tooltip-text">
              {data.agency_count}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];

  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-most-used-equipment.svg`}
    />
  );

  return (
    <>
      <DashboardCardHeader
        title={_t(`${module_name} by Agency`)}
        showRefreshIcon={true}
        isRefreshing={isCashLoading}
        refreshIconTooltip={get_inspections_by_agency?.last_refres_time}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px]">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            onGridReady={onGridReady}
            rowData={rowData}
            key={isDashboardLoading ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isCashLoading || isDashboardLoading ? noRowsOverlay : noData
            }
          />
        </div>
      </div>
    </>
  );
};
export default InspectionsAgency;
