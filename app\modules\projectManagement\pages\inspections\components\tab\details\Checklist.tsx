// Hook
import { Number, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
// Other
import { useInspectionLogDetail } from "../../../hooks/useInspectionDetails";
import { getStatusForField } from "~/shared/utils/helper/common";
import { useInAppDispatch, useInAppSelector } from "../../../redux/store";
import { useEffect, useRef, useState } from "react";
import {
  addLoadingStatus,
  updateInspectionDetail,
} from "../../../redux/slices/inspectionDetailsSlice";
import { useParams } from "@remix-run/react";
import { addUpdateInspectionCheckList } from "../../../redux/action/inspectionDetailsAction";
import delay from "lodash/delay";
import { CHECKLIST_DATA } from "../../../utils/constants";

const Checklist = ({
  task,
  taskIndex,
  isLoadingTaskDragAndDropInSection,
  checklistContainerRef,
  loadingProps,
}: IInspectionCheckListProps) => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams(); // This type already declare.
  const { isLoadingCheckBox, setIsLoadingCheckBox } = loadingProps;
  const [todoTask, setTodoTask] = useState(CHECKLIST_DATA);
  const [isStatusUpdatingLoading, setIsStatusUpdatingLoading] = useState(false);
  const dispatch = useInAppDispatch();
  const { details, isScrollDownToDetail } = useInAppSelector(
    (state) => state.inspectionDetails
  );
  const notesRef = useRef<HTMLTextAreaElement | null>(null);
  const newInputRef = useRef<HTMLTextAreaElement | null>(null);

  useEffect(() => {
    const currentTaskFromStore = details.checklist.find(
      (t) => t.item_id === task.item_id
    );
    setTodoTask(currentTaskFromStore || task);
  }, [task, details.checklist]);

  useEffect(() => {
    if (!isScrollDownToDetail) {
      dispatch(
        addLoadingStatus({
          field: `inspectionTask${taskIndex}`,
          status: "button",
        })
      );
    }
  }, [taskIndex]);

  useEffect(() => {
    setTodoTask(task);
  }, [task]);

  const {
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    onFocusUpdateFieldStatus,
    handleChangeFieldStatus,
    onBlurUpdateFieldStatus,
    loadingStatus,
    isReadOnly,
    inputVals,
    updateDataInStore,
  } = useInspectionLogDetail();

  const addEditCheckList = async (
    data: IAddUpdateInspectionCheckList,
    isStatusUpdating: boolean
  ) => {
    const field = `inspectionTask${taskIndex}`;
    setIsStatusUpdatingLoading(isStatusUpdating);
    setIsLoadingCheckBox((prev) => ({ ...prev, [data.item_id]: true }));
    if (!isStatusUpdating) {
      handleChangeFieldStatus({
        field: field,
        status: "loading",
        action: "API",
      });
    }

    setTodoTask((prev) => ({ ...prev, ...data }));

    const updatedCheckList = details.checklist
      .map((taskone) => {
        if (data.item_id === taskone.item_id) {
          return {
            ...taskone,
            ...data,
          };
        } else {
          return {
            ...taskone,
          };
        }
      })
      .filter((taskone) => taskone.task);

    const updatedDetails = {
      ...inputVals,
      checklist: [...updatedCheckList, { ...CHECKLIST_DATA }],
    };
    dispatch(updateInspectionDetail(updatedDetails));

    const updateRes = (await addUpdateInspectionCheckList({
      id: Number(id),
      checklist: [{ ...data }],
    })) as IAddUpdateInspectionCheckListApiRes;

    if (updateRes?.success) {
      const updatedTask = updateRes.data[0];
      const finalCheckList = updatedCheckList
        .map((taskone) => {
          if (data.item_id === taskone.item_id) {
            return { ...taskone, ...data, ...updatedTask };
          }
          return taskone;
        })
        .filter((taskone) => taskone.task);

      const finalDetails = {
        ...inputVals,
        checklist: finalCheckList.some((t) => t.item_id === 0)
          ? finalCheckList
          : [...finalCheckList, { ...CHECKLIST_DATA }],
      };
      dispatch(updateInspectionDetail(finalDetails));
      if (!isStatusUpdating) {
        handleChangeFieldStatus({
          field: field,
          status: "success",
          action: "API",
        });
      }
    } else {
      dispatch(updateInspectionDetail(inputVals));

      setTodoTask(
        details.checklist.find((t) => t.item_id === task.item_id) || task
      );
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
    }
    delay(() => {
      setIsStatusUpdatingLoading(false);
      setIsLoadingCheckBox((prev) => ({ ...prev, [data.item_id]: false }));
      if (!isStatusUpdating) {
        handleChangeFieldStatus({
          field: field,
          status: "button",
          action: "API",
        });
      }
    }, 1000);
  };

  const focusNextChecklistItem = (currentElement: HTMLTextAreaElement) => {
    const currentLi = currentElement.closest("li");
    if (currentLi) {
      let nextLi = currentLi.nextElementSibling;
      setTimeout(() => {
        if (!nextLi) {
          nextLi = currentLi.nextElementSibling;
        }
        setTimeout(() => {
          if (nextLi) {
            const nextTextArea = nextLi.querySelector("textarea");
            if (nextTextArea) {
              (nextTextArea as HTMLTextAreaElement).focus();
            }
          }
        }, 2000);
      }, 50);
    }
  };

  const decodedTask = HTMLEntities.decode(sanitizeString(todoTask.task || ""));
  const truncatedTask =
    decodedTask.length > 500 ? decodedTask.substring(0, 500) : decodedTask;

  return (
    <li
      key={`${todoTask.item_id}`}
      className="bg-white shadow-[0px_3px_20px] shadow-black/5 px-2.5 py-0.5 rounded flex items-center justify-between dark:bg-dark-400 dark:text-white/90"
    >
      <div className="relative">
        <ButtonWithTooltip
          icon="fa-solid fa-grip-dots"
          tooltipTitle={_t("Move")}
          tooltipPlacement="top"
          iconClassName={`w-3.5 h-3.5 drag-handle ${
            todoTask.item_id === 0
              ? "undraggable cursor-not-allowed opacity-50"
              : ""
          }`}
          className={`cursor-move ${
            todoTask.item_id === 0
              ? "undraggable cursor-not-allowed opacity-50"
              : ""
          }`}
          onClick={() => {}}
          disabled={
            isReadOnly ||
            isStatusUpdatingLoading ||
            Object.values(isLoadingCheckBox).some((isLoading) => isLoading) ||
            Object.values(isLoadingTaskDragAndDropInSection).some(
              (isLoading) => isLoading
            )
          }
        />
        {/*for developer spinner */}
        {/* <div className="w-[18px] h-[18px] bg-white absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center rounded-[5px]">
            <FontAwesomeIcon
            className="h-[18px] w-[18px] text-primary-900 fa-spin"
            icon="fa-duotone fa-solid fa-spinner-third"
            />
            </div> */}
        {Object.keys(isLoadingTaskDragAndDropInSection).find(
          (key) => isLoadingTaskDragAndDropInSection[Number(key)] === true
        ) == task.item_id.toString() && (
          <div className="w-[18px] h-[18px] bg-white absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-center rounded-[5px]">
            <FontAwesomeIcon
              className="h-[18px] w-[18px] text-primary-900 fa-spin"
              icon="fa-duotone fa-solid fa-spinner-third"
            />
          </div>
        )}
      </div>
      <div className="flex gap-2 items-center w-[calc(100%-32px)]">
        <CustomCheckBox
          // className="pt-0.5"
          className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]"
          checked={todoTask?.status?.toString() === "1"}
          disabled={
            isReadOnly ||
            !todoTask.task ||
            todoTask.item_id === 0 ||
            Object.values(isLoadingCheckBox).some((isLoading) => isLoading)
          }
          onChange={(e) => {
            setTodoTask({ ...todoTask, status: e.target.checked ? 1 : 0 });
            addEditCheckList(
              {
                item_id: todoTask.item_id,
                task: todoTask.task,
                status: e.target.checked ? 1 : 0,
                sort_item_order_no:
                  task.item_id === 0 ? taskIndex + 1 : task.sort_order,
              },
              true
            );
          }}
          loadingProps={{
            isLoading: isStatusUpdatingLoading,
            className: "bg-[#ffffff]",
          }}
        />
        <TextAreaField
          ref={task.item_id === 0 ? newInputRef : notesRef}
          placeholder={_t("Add Checklist Item")}
          labelPlacement="left"
          className="h-[30px]"
          editInline={true}
          iconView={true}
          readOnly={
            isReadOnly ||
            Object.values(isLoadingTaskDragAndDropInSection).some(
              (isLoading) => isLoading
            )
          }
          value={truncatedTask}
          onClickStsIcon={() => {
            notesRef.current?.focus();
          }}
          fixStatus={getStatusForField(
            loadingStatus,
            `inspectionTask${taskIndex}`
          )}
          onFocus={() => {
            onFocusUpdateFieldStatus({
              field: `inspectionTask${taskIndex}`,
            });
          }}
          onMouseEnter={() => {
            onMouseEnterUpdateFieldStatus({
              field: `inspectionTask${taskIndex}`,
            });
          }}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
            const inputValue = e.target.value.trimStart();

            setTodoTask({ ...todoTask, task: inputValue });
          }}
          onMouseLeaveDiv={() => {
            onMouseLeaveUpdateFieldStatus({
              field: `inspectionTask${taskIndex}`,
            });
          }}
          onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
            if (e.key === "Tab" || (e.key === "Enter" && !e.shiftKey)) {
              e.preventDefault(); // Prevent new line
              const value = HTMLEntities.encode(
                e?.currentTarget?.value?.trim()
              );
              if (value !== task.task) {
                addEditCheckList(
                  {
                    item_id: task.item_id,
                    task: value,
                    status: task.status,
                    sort_item_order_no:
                      task.item_id === 0 ? taskIndex + 1 : task.sort_order,
                  },
                  false
                );
              } else {
                updateDataInStore({
                  checklist: details.checklist,
                });
                if (!isScrollDownToDetail) {
                  onBlurUpdateFieldStatus({
                    field: `inspectionTask${taskIndex}`,
                  });
                }
              }

              // focusNextChecklistItem(e.currentTarget);
              if (!isScrollDownToDetail) {
                focusNextChecklistItem(e.currentTarget);
              }
            }
          }}
          onBlur={(e: React.FocusEvent<HTMLTextAreaElement>) => {
            const nextFocused = e.relatedTarget as HTMLElement | null;

            // 🚫 Don't continue if next focus is outside checklist container
            if (
              nextFocused &&
              checklistContainerRef.current &&
              !checklistContainerRef.current.contains(nextFocused)
            ) {
              return;
            }

            if (e.relatedTarget && e.relatedTarget.tagName === "TEXTAREA") {
              return;
            }
            const value = HTMLEntities.encode(e?.target?.value.trim());
            if (value !== task.task) {
              addEditCheckList(
                {
                  item_id: task.item_id,
                  task: value,
                  status: task.status,
                  sort_item_order_no:
                    task.item_id === 0 ? taskIndex + 1 : task.sort_order,
                },
                false
              );
            } else {
              updateDataInStore({
                checklist: details.checklist,
              });
              if (!isScrollDownToDetail) {
                onBlurUpdateFieldStatus({
                  field: `inspectionTask${taskIndex}`,
                });
              }
            }

            // focusNextChecklistItem(e.target);
            // ✅ Only focus next item if still inside checklist
            if (
              !isScrollDownToDetail &&
              nextFocused &&
              checklistContainerRef.current?.contains(nextFocused)
            ) {
              focusNextChecklistItem(e.target);
            }
          }}
        />
      </div>
    </li>
  );
};

export default Checklist;
