import { Suspense, useEffect, useMemo, useState } from "react";

import { Spin } from "~/shared/components/atoms/spin";

import { data, useParams, useRevalidator } from "@remix-run/react";
import { fieldStatus } from "~/modules/financials/pages/bills/utils/constants";
import {
  fetchTermData,
  getAccountDetails,
  updateBillDetailAPI,
} from "~/modules/financials/pages/bills/redux/action/billDetailAction";
import delay from "lodash/delay";

import BillDetailsTopBar from "~/modules/financials/pages/bills/components/tab/BillDetailsTopBar";
import {
  useAppBillDispatch,
  useAppBillSelector,
} from "~/modules/financials/pages/bills/redux/store";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getCommonSidebarCollapse, getGConfig, useGModules } from "~/zustand";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import BillDetailsTab from "~/modules/financials/pages/bills/components/tab/BillDetailsTab";
import FilesTab from "~/modules/financials/pages/bills/components/tab/details/FilesTab";
import BillNotes from "~/modules/financials/pages/bills/components/tab/details/BillNotes";
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
import { updatebillDetail } from "~/modules/financials/pages/bills/redux/slices/billDetailSlice";
import dayjs from "dayjs";
import isEmpty from "lodash/isEmpty";




const ManageBillDetails = () => {
  const [selectedProject, setSelectedProject] = useState<
    IProject | undefined | null
  >();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    module_id = 0,
    module_access = "no_access",
    singular_name,
  } = currentModule || {};
  const dispatch = useAppBillDispatch();
  let isSingleSelect: boolean = true;
  const revalidator = useRevalidator();
  const { tab, id: bill_id }: RouteParams = useParams();
  const [{ loading, response }, setData] = useState<IBillDetailState>({
    loading: true,
    response: {},
  });
  const [accountDetails, setAccountDetails] = useState<IAccountDetails | null>(
    null
  );
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [inputValues, setInputValues] = useState<IBillDetailData | undefined>(
    {} as unknown as IBillDetailData
  );

  const { checkModuleAccessByKey } = useGModules();
  const gConfig: GConfig = getGConfig();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(gConfig.module_key) === "read_only",
    [gConfig.module_key]
  );

  const { billDetail, isBillDetailLoading }: IBillDetailsInitialState =
    useAppBillSelector((state) => state.billDetails);

  useEffect(() => {
    dispatch(
      getAccountDetails({
        types: [CFConfig.bill_key_id],
        moduleId: 78,
      })
    ).then((response) => {
      if (response) {
        setAccountDetails(response as unknown as IAccountDetails);
      }
    });
  }, [CFConfig.bill_key_id]);

  const handleChangeFieldStatus = ({
    field,
    status,
    action,
  }: IExpenseFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleUpdateField = async (
    data: IBillDetailsFields,
    extraParams: IBillDetailsFields = {}
  ) => {
    const field = Object.keys(data)[0] as keyof IBillDetailData;
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateData = !!data?.ref_po
      ? { ...data, ref_po: data?.ref_po === "0" ? 0 : data?.ref_po }
      : { ...data };
    const updateRes = (await updateBillDetailAPI({
      custom_bill_id: HTMLEntities.encode(
        billDetail?.data?.custom_bill_id
      ).trim(),
      supplier_id: billDetail?.data?.supplier_id,
      ...(bill_id && { bill_id: Number(bill_id) }),
      module_id,
      ...updateData,

      ...(data.project_id && { project_id: Number(data.project_id) }),
    })) as IBillDetailResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      let newData =
        field === "ref_po" ? { ...data, ...(updateRes?.data || {}) } : data;

      if (newData.order_date) {
        newData.order_date = dayjs(newData.order_date).format(
          CFConfig.day_js_date_format
        );
      }
      if (newData.due_date) {
        newData.due_date = dayjs(newData.due_date).format(
          CFConfig.day_js_date_format
        );
      }
      const updatedData = !isEmpty(extraParams)
        ? { ...newData, ...extraParams }
        : newData;
      dispatch(updatebillDetail(updatedData));
      setData((prevData) => ({
        ...prevData,
        response: {
          data: {
            ...prevData.response.data,
            ...newData,
          },
        },
      }));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: response?.data?.[field] });
      notification.error({
        description: updateRes.message,
      });
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const fetchBillDetails = () => {
    revalidator.revalidate();
    setData((prevData) => ({
      ...prevData,
      isBillDetailLoading: true,
    }));
    setTimeout(() => {
      setData((prevData) => ({
        ...prevData,
        isBillDetailLoading: false,
      }));
    }, 3000);
  };

  useEffect(() => {
    if (billDetail?.data) {
      setSelectedProject({
        id: billDetail?.data?.project_id,
        project_name: billDetail?.data?.project_name,
      } as IProject);
      setInputValues(billDetail?.data as IBillDetailData);
    }
  }, [billDetail?.data]);

  const renderContent = () => {
    switch (tab) {
      case "details":
      case undefined:
        return (
          <>
            <BillDetailsTab
              handleUpdateField={handleUpdateField}
              inputValues={inputValues}
              setInputValues={setInputValues}
              handleChangeFieldStatus={handleChangeFieldStatus}
              loadingStatus={loadingStatus}
              isReadOnly={module_access === "read_only"}
            />
          </>
        );

      case "files":
        return <FilesTab />;

      case "notes":
        return <BillNotes />;

      default:
        return <></>;
    }
  };

  return (
    <>
      <div
        className={`ease-in-out duration-300 w-full overflow-y-auto ${
          sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
        }`}
      >
        <BillDetailsTopBar
          sidebarCollapse={false}
          setIsSelectProjectOpen={setIsSelectProjectOpen}
          handleChangeFieldStatus={handleChangeFieldStatus}
          handleUpdateField={handleUpdateField}
          selectedProject={selectedProject}
          loadingStatus={loadingStatus}
          setInputValues={setInputValues}
          inputValues={inputValues}
          isBillDetailLoading={isBillDetailLoading}
          fetchBillDetails={fetchBillDetails}
        />

        {!isBillDetailLoading && (
          <ReadOnlyPermissionMsg
            className="p-4 pt-0"
            view={module_access === "read_only"}
          />
        )}

        <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
          <div
            className={`px-[15px] pb-[15px] ${
              module_access === "read_only"
                ? window.ENV.PAGE_IS_IFRAME
                  ? "md:min-h-[calc(100dvh-198px)] min-h-[calc(100dvh-256px)]"
                  : "md:min-h-[calc(100dvh-341px)] min-h-[calc(100dvh-408px)]"
                : window.ENV.PAGE_IS_IFRAME
                ? "md:min-h-[calc(100dvh-164px)] min-h-[calc(100dvh-210px)]"
                : "md:min-h-[calc(100dvh-305px)] min-h-[calc(100dvh-360px)]"
            }`}
          >
            {isBillDetailLoading ? (
              <Spin
                className={`flex items-center justify-center ${
                  window.ENV.PAGE_IS_IFRAME
                    ? "md:h-[calc(100vh-161px)] h-[calc(100vh-205px)]"
                    : "md:h-[calc(100vh-304px)] h-[calc(100vh-357px)]"
                }`}
              />
            ) : (
              <>{renderContent()}</>
            )}
          </div>
          {!!(bill_id && module_id) && (
            <TimeLineFooter
              data={{
                addedDate: billDetail?.data?.date_added || "",
                addedTime: billDetail?.data?.time_added || "",
                addedBy: billDetail?.data?.emp_username || "",
                moduleId: module_id,
                recordId: Number(bill_id),
                qbDateAdded: billDetail?.data?.qb_date_added || "",
                qbTimeAdded: billDetail?.data?.qb_time_added || "",
                quickbookUserId: billDetail?.data?.company_bill_id  || 0,
                moduleName: singular_name || "Bill",
              }}
              isSynced={
                billDetail?.data?.quickbook_bill_id !== 0 &&
                !!billDetail?.data?.quickbook_bill_id
                  ? `#${billDetail?.data?.quickbook_bill_id}`
                  : ""
              }
              sidebarCollapse={sidebarCollapse}
              isLoading={isBillDetailLoading}
              hrefLinkDetail={"bill"}
              nameId={"txnId"}
            />
          )}
        </div>

        {isSelectProjectOpen && (
          <SelectProject
            isSingleSelect={isSingleSelect}
            open={isSelectProjectOpen}
            setOpen={setIsSelectProjectOpen}
            selectedProjects={
              selectedProject?.id > 0 && selectedProject?.project_name
                ? [selectedProject]
                : []
            }
            onProjectSelected={(data) => {
              if (data.length) {
                if (
                  Object.keys(data[0]).length &&
                  String(data[0].id) !== String(billDetail?.data?.project_id)
                ) {
                  setSelectedProject(data[0]);
                  handleUpdateField(
                    {
                      project_id: Number(data[0].id),
                      show_client_access: data[0].show_client_access || "",
                    },
                    {
                      pro_id: Number(data[0].id),
                      project_name: data[0].project_name || "",
                      project_type: data[0].project_type_key || "",
                      project_type_name: data[0]?.project_type_name || "",
                      project_billed_to: data[0].billed_to || "",
                    }
                  );
                }
              } else {
                setSelectedProject([]);
                handleUpdateField(
                  {
                    project_id: 0,
                    show_client_access: "",
                  },
                  {
                    pro_id: 0,
                    project_name: "",
                    project_type: "",
                    project_type_name: "",
                    project_billed_to: "",
                  }
                );
              }
            }}
            genericProjects="project,opportunity"
          />
        )}
      </div>
    </>
  );
};

export default ManageBillDetails;
