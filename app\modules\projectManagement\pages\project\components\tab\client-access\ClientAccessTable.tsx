import { ValueSetterParams } from "ag-grid-community";

// atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
// Hook
import { useTranslation } from "~/hook";
import { type RadioChangeEvent } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { Number, sanitizeString } from "~/helpers/helper";
import {
  sendEmailForClientPortal,
  updateClientAccessContact,
} from "../../../redux/action/projectClientAccessAction";
import { updateProjectDetail } from "../../../redux/slices/proDetailsSlice";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { useBoolean } from "../../../hook/use-boolean";
import dayjs from "dayjs";
import { Spin } from "~/shared/components/atoms/spin";

const ClientAccessTable: React.FC<IClientAccessProps> = ({
  isClientAccessEnabled,
}) => {
  const { _t } = useTranslation();
  const { details } = useAppProSelector((state) => state.proDetails);
  const dispatch = useAppProDispatch();
  const [loadingKey, setLoadingKey] = useState<Record<string, boolean>>();

  const { isReadOnly } = useProjectDetail();

  const { onTrue, onFalse, bool } = useBoolean();

  const contacts = useMemo(() => {
    const users = details.client_access_contacts?.map((contact) => {
      const abc = details?.client_access_users?.find(
        (user) => contact.contact_id === user.contact_id
      );
      return {
        access_id: abc?.access_id,
        access_type: abc?.access_type,
        company_id: abc?.company_id,
        contact_id: contact?.contact_id,
        customer_email: contact.email,
        customer_name: contact.customer_name,
        date_added: abc?.date_added,
        date_modified: abc?.date_modified,
        has_access: abc?.has_access,
        is_additional_contact: contact.is_additional_contact,
        last_logged_in: abc?.last_logged_in,
        last_login_date: abc?.last_login_date,
        last_login_time: abc?.last_login_time,
        last_login_track: abc?.last_login_track,
        password: abc?.password,
        project_id: abc?.project_id,
        user_id: abc?.user_id,
        username: abc?.username,
      };
    });
    return users?.filter((user) => user !== undefined && user) ?? [];
  }, [details.client_access_contacts, details?.client_access_users]);

  useEffect(() => {
    if (contacts.length) {
      onTrue();
    }
  }, [contacts]);

  const updateClientAccess = useCallback(
    async (
      val: number,
      key: keyof TClientAccessUsers,
      contact: TClientAccessUsers
    ) => {
      setLoadingKey((prev) => ({ ...prev, [contact?.contact_id]: true }));
      let cData = {
        access_id: contact?.access_id,
        contact_id: contact?.contact_id,
        is_additional_contact: contact?.is_additional_contact,
        ...(key === "has_access" && {
          has_access: val,
          access_type: val === 0 ? 0 : 1,
        }),
        ...(key === "access_type" && { access_type: val, has_access: 1 }),
      };

      const payload = {
        client_user_json: JSON.stringify(cData),
      };

      const updateRes = (await updateClientAccessContact(
        details?.id?.toString() ?? "",
        payload
      )) as IUpdateClientAccessContactApiRes;

      if (updateRes.success) {
        const newUser = updateRes.data?.client_users;
        const resWithUpdatedTime = {
          ...newUser,
          last_login_date: dayjs(newUser.last_login_date).format("YYYY-MM-DD"),
          last_login_time: dayjs(newUser.last_login_time).format("hh:mm A"),
        };

        const updatedContacts = contacts.map((c) => {
          if (c.contact_id?.toString() === contact.contact_id?.toString()) {
            return {
              ...contact,
              ...resWithUpdatedTime,
            };
          }
          return c;
        });

        dispatch(updateProjectDetail({ client_access_users: updatedContacts }));
      } else {
        notification.error({
          description: updateRes.message || "Faile to update",
        });
        dispatch(updateProjectDetail({ client_access_users: contacts }));
      }

      setLoadingKey((prev) => ({ ...prev, [contact?.contact_id]: false }));
    },
    [details?.id, contacts]
  );

  const getLoginLink = useCallback((username: string, password: string) => {
    var popup_url = window.ENV.CLIENT_PANEL_URL + "force-login.php";
    popup_url += "?e=" + encodeURIComponent(username) + "";
    popup_url += "&p=" + encodeURIComponent(btoa(password)) + "";
    return popup_url;
  }, []);

  const sendEmailHandler = useCallback(async (access_id: number) => {
    setLoadingKey((prev) => ({ ...prev, [access_id]: true }));
    const emailRes = await sendEmailForClientPortal({
      access_id: !isNaN(access_id) ? Number(access_id) : 0,
    });

    if (!emailRes.success) {
      notification.error({
        description: emailRes.message || "Failed to send email",
      });
    }
    setLoadingKey((prev) => ({ ...prev, [access_id]: false }));
  }, []);

  const columnDefs = [
    {
      headerName: "",
      field: "has_access",
      minWidth: 50,
      maxWidth: 50,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      editable: ({ data }: IClientAccessCellRendereParams) =>
        !isReadOnly && isClientAccessEnabled && !loadingKey?.[data?.contact_id],
      cellRenderer: "agCheckboxCellRenderer",
      cellEditor: "agCheckboxCellEditor",
      valueGetter: ({ data }: IClientAccessCellRendereParams) => {
        return data?.has_access == 1;
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          let nVal = params.newValue;

          const updatedData = {
            ...params.data,
            has_access: nVal,
            ...(nVal === true && {
              last_login_date: "",
              last_login_time: "",
            }),
          };

          params.node.setData(updatedData);

          updateClientAccess(nVal === true ? 1 : 0, "has_access", params.data);
        }
        return true;
      },
    },
    {
      headerName: _t(""),
      minWidth: 150,
      field: "",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      flex: 2,
      cellRenderer: (params: IClientAccessCellRendereParams) => {
        const { data } = params;
        const name = _t(
          HTMLEntities.decode(sanitizeString(data.customer_name)) ?? ""
        );

        return name ? (
          <Tooltip title={name}>
            <Typography className="text-13 text-[#212529]">{name}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t(""),
      minWidth: 300,
      maxWidth: 300,
      field: "",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IClientAccessCellRendereParams) => {
        const { data } = params;

        if (Number(params.data?.has_access) === 0) return null;

        return data?.last_login_date && data?.last_login_time ? (
          <div className="flex items-center gap-2">
            <Typography className="text-13 !text-primary-900 font-semibold">
              {_t("Last Login:")}
            </Typography>
            <DateTimeCard
              format="datetime"
              date={data?.last_login_date ?? ""}
              time={data?.last_login_time ?? ""}
            />
          </div>
        ) : (
          <></>
        );
      },
    },
    {
      headerName: _t(""),
      minWidth: 310,
      maxWidth: 310,
      field: "",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IClientAccessCellRendereParams) => {
        const { data } = params;

        const radioVal =
          Number(data?.has_access) === 0
            ? ""
            : data?.access_type === 1
            ? "full_access"
            : "read_only";

        return (
          <div className="flex items-center gap-2 relative before:absolute before:w-px before:h-full before:min-h-[32px] before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] before:left-0 before:top-1/2 before:-translate-y-1/2">
            <Typography className="text-13 !text-primary-900 font-semibold pl-3">
              {_t("Access:")}
            </Typography>
            <RadioGroupList
              formInputClassName="!p-0"
              disabled={
                isReadOnly ||
                !isClientAccessEnabled ||
                loadingKey?.[data?.contact_id]
              }
              view="row"
              onChange={(e: RadioChangeEvent) => {
                const val = e.target.value === "full_access" ? 1 : 0;
                updateClientAccess(val, "access_type", data);
              }}
              value={radioVal}
              options={[
                {
                  label: "Full Access",
                  value: "full_access",
                },
                {
                  label: "Read Only",
                  value: "read_only",
                },
              ]}
            />
          </div>
        );
      },
    },
    {
      headerName: _t(""),
      minWidth: 104,
      maxWidth: 104,
      field: "",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: (params: IClientAccessCellRendereParams) => {
        const { data } = params;

        return (
          <div className="relative flex items-center before:absolute before:w-px before:h-full before:min-h-[32px] before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)] before:left-0 before:top-1/2 before:-translate-y-1/2 leading-5">
            <div className="w-7">
              {Number(data?.has_access) === 1 &&
                !isReadOnly &&
                isClientAccessEnabled &&
                (loadingKey?.[data?.access_id] ? (
                  <Spin
                    size="small"
                    className="w-4 h-4 text-primary-900 mx-1.5 cursor-pointer"
                  />
                ) : (
                  <Tooltip title={_t("Email Login Details")}>
                    <FontAwesomeIcon
                      className={`w-4 h-4 text-primary-900 mx-1.5 ${
                        loadingKey?.[data?.access_id]
                          ? "cursor-progress pointer-events-none"
                          : "cursor-pointer"
                      } `}
                      icon="fa-regular fa-envelope"
                      onClick={() => {
                        if (!!data?.customer_email) {
                          sendEmailHandler(Number(data?.access_id));
                        } else {
                          notification.error({
                            description: _t(
                              "The contact does not have an email address associated with their directory record.This is required to send them the credentials."
                            ),
                          });
                        }
                      }}
                    />
                  </Tooltip>
                ))}
            </div>
            <div className="w-7">
              {Number(data?.has_access) === 1 &&
                data?.username &&
                data?.password && (
                  <Tooltip title={_t("Preview")}>
                    <FontAwesomeIcon
                      className="w-4 h-4 text-primary-900 mx-1.5 cursor-pointer"
                      icon="fa-solid fa-eye"
                      onClick={() => {
                        const link = getLoginLink(
                          data?.username,
                          data?.password
                        );
                        window.open(link, "_blank");
                      }}
                    />
                  </Tooltip>
                )}
            </div>
            <Tooltip
              title={_t(
                "For security reasons, the clients password is not shared with anyone except the client and it is sent to the email address in their Directory record. An email address is required to receive login credentials."
              )}
            >
              <FontAwesomeIcon
                className="w-4 h-4 text-primary-900 mx-1.5 cursor-pointer"
                icon="fa-regular fa-circle-info"
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <CollapseSingleTable
        title={_t("Client Access")}
        activeKey={bool ? ["1"] : []}
        onChange={(val) => {
          val.length ? onTrue() : onFalse();
        }}
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table table-no-header"
                headerHeight={0}
                columnDefs={columnDefs}
                rowData={contacts}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-pj-client-access.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />
    </>
  );
};

export default ClientAccessTable;
